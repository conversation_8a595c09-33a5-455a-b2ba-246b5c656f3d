#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6  (若用 gcc10 编译则将 gcc-8.3.0 改为 gcc-10.3.0)
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/common_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.common_api_test.TestFlowFunc.test_xxx

import copy
import unittest
import math
import random
import json
import base64
import struct
from itertools import chain
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.decorators import parallel, async_retrieve, async_enrich, async_mix
from dragonfly.common_leaf_util import set_env

# LeafService.I_AM_MASTER_DRIVER = True

def encode_bytes(data: bytes) -> str:
  """
  PB bytes field need to be base64-encoded in json format. 
  """
  return base64.b64encode(data).decode('utf-8')

def str2bytes(data: str) -> bytes:
  return bytes(data, 'utf-8')

def int2bytes(data: int) -> bytes:
  return struct.pack('<q', data)

def float2bytes(data: float) -> bytes:
  return struct.pack('<d', data)

def gen_key_sign(item_type: int, item_id: int) -> int:
  return item_type << 56 | item_id

class TestFlowFunc(unittest.TestCase):
  # proto file 初始化只会执行一次，所以在 service 初始化的地方进行注册动态 proto
  __service = LeafService(kess_name="grpc_CommonLeafTest") \
    .register_proto(
      "temp.proto",
      """
        syntax = "proto3";
        package ks.reco;

        message TempMessage {
          double score1 = 1;
          double score2 = 2;
        }""") \
    .register_proto(
      "temp2.proto",
      """
        syntax = "proto3";
        package ks.reco;

        message TempMessageSecond {
          int64 id = 1;
          TempMessage msg = 2;
        }""",
      ["temp.proto"]) \
    .register_proto(
      "test_enum.proto",
      """
        syntax = "proto3";
        package ks.reco;
        message TestEnum {
          enum FrontRequestType {
            UNKNOWN_REQUEST    = 0;
            INFEED_REQUEST     = 1;
            SPLASH_REQUEST     = 2;
          }
        }""") \
    .register_proto(
      "test_sub_message.proto",
      """
          syntax = "proto3";
          package ks.reco;
          message SubTestMessage {
            bool bool_type = 1;
            TestEnum.FrontRequestType enum_type = 2;
            int32 int32_type = 3;
            uint32 uint32_type = 4;
            int64 int64_type = 5;
            uint64 uint64_type = 6;
            float float_type = 7;
            double double_type = 8;
            string string_type = 9;
            repeated bool bool_type_r = 10;
            repeated TestEnum.FrontRequestType enum_type_r = 11;
            repeated int32 int32_type_r = 12;
            repeated uint32 uint32_type_r = 13;
            repeated int64 int64_type_r = 14;
            repeated uint64 uint64_type_r = 15;
            repeated float float_type_r = 16;
            repeated double double_type_r = 17;
            repeated string string_type_r = 18;
          }""",
      ["test_enum.proto"])  \
    .register_proto(
      "test_build_from_protobuf_message.proto",
      """
        syntax = "proto3";
        package ks.reco;
        message TestBuildFromProtobufMessage {
          SubTestMessage sub_message = 1;
          repeated SubTestMessage sub_message_r = 2;
        }""",
      ["test_enum.proto", "test_sub_message.proto"])

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__temp_service = copy.deepcopy(cls.__service)
    cls.__temp_service.add_leaf_flows(leaf_flows=[flow])
    return cls.__temp_service.executor()

  def test_if_else(self):
    def test_abnormal_if_detection():
      with set_env(CHECK_ABNORMAL_IF_EXPR="true"):
        with self.assertRaises(Exception):
          flow = LeafFlow(name="test_abnormal_if_detection_error") \
            .if_("condition0 == 0") \
            .else_if_("((condition1 and condition2)) and condition2 ~= nil)") \
            .end_()

        flow = LeafFlow(name="test_abnormal_if_detection_normal") \
          .if_("condition", check_expr=False) \
          .else_if_("enable_launch_photo_tolist > 0 and (((pushPhotoId or 0) > 1) or ((sharePhotoId or 0) > 1))") \
          .else_if_("string.match(perf_bucket_name, bucket_name)") \
          .else_if_("1 or false") \
          .else_if_("(condition and condition > 0)") \
          .else_if_("condition", check_expr=False) \
          .end_()

      with set_env(CHECK_ABNORMAL_IF_EXPR="false"):
        flow = LeafFlow(name="test_abnormal_if_detection_disabled") \
            .if_("condition0") \
            .end_()

    test_abnormal_if_detection()
    flow = LeafFlow(name="test_if_else") \
      .if_("sw1 == 1") \
        .set_attr_value(common_attrs=[dict(name="aaa", value=1, type="int")]) \
      .else_if_("sw2 == 1") \
        .if_("sw22 == 1") \
          .set_attr_value(common_attrs=[dict(name="aaa", value=22, type="int")]) \
        .else_() \
          .set_attr_value(common_attrs=[dict(name="aaa", value=20, type="int")]) \
        .end_() \
      .else_if_("sw3 == 1") \
        .switch_("sw33") \
          .case_(0, 1) \
            .set_attr_value(common_attrs=[dict(name="aaa", value=31, type="int")]) \
          .case_(2) \
            .set_attr_value(common_attrs=[dict(name="aaa", value=32, type="int")]) \
          .default_() \
            .set_attr_value(common_attrs=[dict(name="aaa", value=30, type="int")]) \
        .end_() \
      .else_() \
        .set_attr_value(common_attrs=[dict(name="aaa", value=0, type="int")]) \
      .end_()

    leaf = self.__init_service(flow)
    leaf["sw1"] = 1
    leaf["sw2"] = 1
    leaf["sw22"] = 1
    leaf["sw3"] = 1
    leaf["sw33"] = 0
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 1)

    leaf.reset()
    leaf["sw1"] = 0
    leaf["sw2"] = 1
    leaf["sw22"] = 1
    leaf["sw3"] = 1
    leaf["sw33"] = 0
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 22)

    leaf.reset()
    leaf["sw1"] = 0
    leaf["sw2"] = 1
    leaf["sw22"] = 0
    leaf["sw3"] = 1
    leaf["sw33"] = 0
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 20)

    leaf.reset()
    leaf["sw1"] = 0
    leaf["sw2"] = 0
    leaf["sw22"] = 0
    leaf["sw3"] = 1
    leaf["sw33"] = 0
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 31)

    leaf.reset()
    leaf["sw3"] = 1
    leaf["sw33"] = 1
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 31)

    leaf.reset()
    leaf["sw3"] = 1
    leaf["sw33"] = 2
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 32)

    leaf.reset()
    leaf["sw3"] = 1
    leaf["sw33"] = 3
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 30)

    leaf.reset()
    leaf["sw1"] = 0
    leaf["sw2"] = 0
    leaf["sw3"] = 0
    leaf.run("test_if_else")
    self.assertEqual(leaf["aaa"], 0)


  def test_target_item(self):
    flow = LeafFlow(name="test_target_item") \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit1",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_int_attr": "{{target_int}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit2",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_string_attr": "{{target_string}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit3",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_int_attr": "{{target_int_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit4",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_string_attr": "{{target_string_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit5",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_string_list_attr": "{{target_string_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit6",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_int_list_attr": "{{target_int_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit7",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_empty_int_list_attr": "{{target_int_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit8",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_int_attr": "{{empty_target_int_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit9",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "empty_target_int_attr": 3 }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit10",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "empty_target_int_attr": "{{empty_target_int_list}}" }
      ) \
      .set_attr_value(
        item_attrs=[
          {
            "name": "hit11",
            "type": "int",
            "value": 1
          }
        ],
        target_item={ "target_string_attr": ["{{target_string}}", "22"] }
      ) \



    leaf = self.__init_service(flow)
    leaf["target_int"] = 1
    leaf["target_string"] = "12"
    leaf["target_int_list"] = [1,2]
    leaf["target_string_list"] = ["12", "22"]

    item1 = leaf.add_item(1)
    item2 = leaf.add_item(2)
    item3 = leaf.add_item(3)
    
    item1['hit1'] = 0
    item2['hit1'] = 0
    item3['hit1'] = 0
    item1['hit2'] = 0
    item2['hit2'] = 0
    item3['hit2'] = 0
    item1['hit3'] = 0
    item2['hit3'] = 0
    item3['hit3'] = 0
    item1['hit4'] = 0
    item2['hit4'] = 0
    item3['hit4'] = 0
    item1['hit5'] = 0
    item2['hit5'] = 0
    item3['hit5'] = 0
    item1['hit6'] = 0
    item2['hit6'] = 0
    item3['hit6'] = 0
    
    item1['target_int_attr'] = 1
    item2['target_int_attr'] = 2
    item3['target_int_attr'] = 3

    item1['target_string_attr'] = "12"
    item2['target_string_attr'] = "22"
    item3['target_string_attr'] = "32"
    
    item1['target_int_list_attr'] = [1,2,3]
    item2['target_int_list_attr'] = [1]
    item3['target_string_list_attr'] = ["12", "22"]

    leaf.run("test_target_item")

    for item in leaf.items:
      if item.item_key == 1:
        self.assertEqual(item["hit1"], 1)
        self.assertEqual(item["hit2"], 1)
        self.assertEqual(item["hit3"], 1)
        self.assertEqual(item["hit4"], 1)
        self.assertEqual(item["hit5"], 0)
        self.assertEqual(item["hit6"], 0)
        self.assertEqual(item["hit7"], None)
        self.assertEqual(item["hit8"], None)
        self.assertEqual(item["hit9"], None)
        self.assertEqual(item["hit10"], None)
        self.assertEqual(item["hit11"], 1)
      if item.item_key == 2:
        self.assertEqual(item["hit1"], 0)
        self.assertEqual(item["hit2"], 0)
        self.assertEqual(item["hit3"], 1)
        self.assertEqual(item["hit4"], 1)
        self.assertEqual(item["hit5"], 0)
        self.assertEqual(item["hit6"], 0)
        self.assertEqual(item["hit7"], None)
        self.assertEqual(item["hit8"], None)
        self.assertEqual(item["hit9"], None)
        self.assertEqual(item["hit10"], None)
        self.assertEqual(item["hit11"], 1)
      if item.item_key == 3:
        self.assertEqual(item["hit1"], 0)
        self.assertEqual(item["hit2"], 0)
        self.assertEqual(item["hit3"], 0)
        self.assertEqual(item["hit4"], 0)
        self.assertEqual(item["hit5"], 0)
        self.assertEqual(item["hit6"], 0)
        self.assertEqual(item["hit7"], None)
        self.assertEqual(item["hit8"], None)
        self.assertEqual(item["hit9"], None)
        self.assertEqual(item["hit10"], None)
        self.assertEqual(item["hit11"], None)

  def test_pack_item_attr(self):
    flow = LeafFlow(name="test_pack_item_attr") \
        .pack_item_attr(
          item_source = {
            "common_attr": ["meow_finish_play_list_reverse",]
          },
          mappings = [{
            "from_item_attr": "meow_drama_id_attr",
            "to_common_attr": "meow_finish_play_drama_id_list_reverse",
            "pack_if": "is_drama_attr"
          }]
        ) \

    leaf = self.__init_service(flow)
    leaf["meow_finish_play_list_reverse"] = [1, 2, 3]
    for i in range(1, 5):
      item = leaf.add_item(i)
      if i % 2 == 1:
        item["is_drama_attr"] = i
      item["meow_drama_id_attr"] = i + 100

    leaf.run("test_pack_item_attr")
    self.assertEqual(leaf["meow_finish_play_drama_id_list_reverse"], [101, 103])

  def test_aggregate_list_attr(self):
    flow = LeafFlow(name="test_aggregate_list_attr") \
        .fake_retrieve(num=10, reason=100) \
        .set_attr_value(
          common_attrs=[
            {
              "name": "user_info_bytes",
              "type": "string",
              "value": ""
            }
          ],
          item_attrs=[
            {
              "name": "photo_emb",
              "type": "double_list",
              "value": [0.3,0.4,0.5]
            },
            {
              "name": "follow_aid",
              "type": "int_list",
              "value": [6]
            }
          ]
        ) \
        .aggregate_list_attr(
          mappings = [
          {
            "from_attr": "photo_emb",
            "to_attr": "emb_size",
            "aggregator": "count"
          },
          {
            "from_attr": "photo_emb",
            "to_attr": "max_emb",
            "aggregator": "max"
          },
          {
            "from_attr": "follow_aid",
            "to_attr": "min_aid",
            "aggregator": "min"
          },
          ]
        ) \
        .aggregate_list_attr(
          for_common = True,
          mappings = [{
            "from_attr": "user_info_bytes",
            "to_attr": "user_info_bytes_size",
            "aggregator": "count"
          }])
    leaf = self.__init_service(flow)
    leaf.run("test_aggregate_list_attr")

    self.assertEqual(leaf["user_info_bytes_size"], 0)
    for item in leaf.items:
      self.assertEqual(item["emb_size"], 3)
      self.assertEqual(item["max_emb"], 0.5)
      self.assertEqual(item["min_aid"], 6)

  def test_fake_retrieve(self):
    flow = LeafFlow(name="test_fake_retrieve") \
        .fake_retrieve(num=10, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .copy_item_meta_info(save_reason_to_attr="reason") \
        .deduplicate() \
        .count_reco_result(save_count_to="result_num")
    leaf = self.__init_service(flow)
    leaf.run("test_fake_retrieve")
    self.assertEqual(len(leaf.items), 10)
    for item in leaf.items:
      self.assertEqual(item.reason, 100)
      # 先入的写入 _REASON_
      # copy_item_meta_info 后入的写入 reason
      self.assertEqual(item["_REASON_"], 100)
      self.assertEqual(item["reason"], 101)
    self.assertEqual(leaf["result_num"], 10)

  def test_retrieve_by_common_attr(self):
    flow = LeafFlow(name="test_retrieve_by_common_attr") \
        .retrieve_by_common_attr(
          attr="common1",
          reason=110,
          num_limit=5,
        )
    leaf = self.__init_service(flow)
    leaf["common1"] = [1,1,2,3,5,8,13,21,34]
    leaf.run("test_retrieve_by_common_attr")
    self.assertEqual(len(leaf.items), 5)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, leaf["common1"][index])
      self.assertEqual(item.reason, 110)
      self.assertEqual(item["_REASON_"], 110)

  def test_attr_statistic(self):
    flow = LeafFlow(name="test_attr_statistic") \
        .attr_statistic(
          check_point="retrieval_1",
          save_as_json_to="statisticInfo",
          statistic_config=[
            {"attr_name": "is_drama_attr1", "aggregator": "count"},
            {"attr_name": "is_drama_attr2", "aggregator": "count"}])
    leaf = self.__init_service(flow)
    for i in range(1, 14):
      item = leaf.add_item(i)
      item["is_drama_attr1"] = i % 2
      item["is_drama_attr2"] = i % 3
    leaf.run("test_attr_statistic")
    self.assertEqual(json.loads(leaf["statisticInfo"]),
                     {"retrieval_1": {"is_drama_attr2": {"0": 4,"2": 4,"1": 5},"is_drama_attr1": {"0": 6,"1": 7}}})

  def test_old_build_protobuf(self):
    flow = LeafFlow(name="test_old_build_protobuf") \
        .fake_retrieve(num=3, reason=1) \
        .copy_item_meta_info(
          save_item_id_to_attr="Index"
        ) \
        .build_protobuf(
          is_common_attr = True,
          inputs=[
            { "attr_name": "cascade_pctr", "path": "context_info.cascade_pctr" },
            { "attr_name": "cascade_pltr", "path": "context_info.cascade_pltr" },
            { "attr_name": "cascade_pwtr", "path": "context_info.cascade_pwtr" },
            { "attr_name": "cascade_plvtr", "path": "context_info.cascade_plvtr" },
          ],
          output_attr="reco_photo_info",
          class_name="ks::reco::RecoPhotoInfo",
        ).enrich_with_protobuf(
          from_extra_var = "reco_photo_info",
          attrs = [
            dict(path="context_info.cascade_pctr", name="copy_pctr"),
            dict(path="context_info.cascade_pltr", name="copy_pltr"),
            dict(path="context_info.cascade_pwtr", name="copy_pwtr"),
            dict(path="context_info.cascade_plvtr", name="copy_plvtr"),
          ]
        )

    leaf = self.__init_service(flow)
    leaf["cascade_pctr"] = 1.0
    leaf["cascade_pltr"] = 2.0
    leaf["cascade_pwtr"] = 1.5
    leaf["cascade_plvtr"] = 2.5
    leaf.run("test_old_build_protobuf")
    self.assertEqual(leaf["cascade_pctr"], leaf["copy_pctr"])
    self.assertEqual(leaf["cascade_pltr"], leaf["copy_pltr"])
    self.assertEqual(leaf["cascade_pwtr"], leaf["copy_pwtr"])
    self.assertEqual(leaf["cascade_plvtr"], leaf["copy_plvtr"])

  def assertAlmostFloatingListEqual(self, res1, res2, delta):
    self.assertEqual(len(res1), len(res2))
    for i in range(len(res1)):
      self.assertAlmostEqual(res1[i], res2[i], delta=delta)

  def test_enrich_by_protobuf_skip_unset_field(self):
    flow = LeafFlow(name="test_enrich_by_protobuf_skip_unset_field") \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
              "field_type": "trivial_field",
              "build_list": [
                {"from_attr": "int64_repeated_input", "to_path":"int64_type_r"},
                {"from_attr": "string_single_input", "to_path": "string_type"},
              ]
            }
          ],
          as_string=False,
          output_attr="single_sub_test_message",
          use_dynamic_proto=True
        ) \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            {
              "field_type": "trivial_field",
              "build_list": [
                {"from_attr": "single_sub_test_message", "to_path": "sub_message_r", "append": True},
              ]
            }
          ],
          as_string=False,
          output_attr="test_empty_message",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var="test_empty_message",
          attrs=[
            {"name": "repeated_double_with_default", "path": "sub_message.double_type_r", "skip_unset_field": False},
            {"name": "repeated_float_wo_default", "path": "sub_message.float_type_r", "skip_unset_field": True}
          ],
          is_common_attr=True,
        ) \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            {
              "field_type": "trivial_field",
              "build_list": [
                {"from_attr": "single_sub_test_message", "to_path": "sub_message"},
              ]
            }
          ],
          as_string=False,
          output_attr="test_filled_message",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var="test_filled_message",
          attrs=[
            {"name": "repeated_int64", "path": "sub_message.int64_type_r", "skip_unset_field": True},
            {"name": "single_string", "path": "sub_message.string_type", "skip_unset_field": True},
          ],
          is_common_attr=True,
        )
    
    leaf = self.__init_service(flow)
    # 测试 skip_unset_field 逻辑正确
    leaf["int64_repeated_input"] = [1, 2, 3, 4]
    leaf["string_single_input"] = "hello world"
    leaf.run("test_enrich_by_protobuf_skip_unset_field")

    # 未设置 skip_unset_field 应该返回空 list
    self.assertEqual(leaf["repeated_double_with_default"], [])
    # 设置了 skip_unset_field 应该返回 None
    self.assertIsNone(leaf["repeated_float_wo_default"])
    # 确认设置的值无误
    self.assertEqual(leaf["repeated_int64"], leaf["int64_repeated_input"])
    self.assertEqual(leaf["single_string"], leaf["string_single_input"])

  def test_build_from_protobuf(self):
    flow = LeafFlow(name="test_build_from_protobuf") \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "int64_type", "to_path":"uint64_type"},
                  { "from_attr": "uint64_type", "to_path":"int64_type"},
                  { "from_attr": "float_type", "to_path":"float_type"},
                  { "from_attr": "double_type", "to_path":"double_type"},
                  { "from_attr": "string_type", "to_path":"string_type"},
                ]
            }
          ],
          as_string=False,
          output_attr="common_single_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "common_single_field",
          attrs = [
            { "name": "single_bool_type", "path":"bool_type"},
            { "name": "single_enum_type", "path":"enum_type"},
            { "name": "single_int32_type", "path":"int32_type"},
            { "name": "single_uint32_type", "path":"uint32_type"},
            { "name": "single_int64_type", "path":"uint64_type"},
            { "name": "single_uint64_type", "path":"int64_type"},
            { "name": "single_float_type", "path":"float_type"},
            { "name": "single_double_type", "path":"double_type"},
            { "name": "single_string_type", "path":"string_type"},
          ]
        ) \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "int64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "uint64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="common_repeated_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "common_repeated_field",
          attrs = [
            { "name": "repeated_bool_type_r", "path":"bool_type_r"},
            { "name": "repeated_enum_type_r", "path":"enum_type_r"},
            { "name": "repeated_int32_type_r", "path":"int32_type_r"},
            { "name": "repeated_uint32_type_r", "path":"uint32_type_r"},
            { "name": "repeated_int64_type_r", "path":"uint64_type_r"},
            { "name": "repeated_uint64_type_r", "path":"int64_type_r"},
            { "name": "repeated_float_type_r", "path":"float_type_r"},
            { "name": "repeated_double_type_r", "path":"double_type_r"},
            { "name": "repeated_string_type_r", "path":"string_type_r"},
          ]
        ) \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "common_single_field", "from_path": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "common_single_field", "from_path": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "common_single_field", "from_path": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "common_single_field", "from_path": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "common_single_field", "from_path": "uint64_type", "to_path":"uint64_type"},
                  { "from_attr": "common_single_field", "from_path": "int64_type", "to_path":"int64_type"},
                  { "from_attr": "common_single_field", "from_path": "double_type", "to_path":"double_type"},
                  { "from_attr": "common_single_field", "from_path": "float_type", "to_path":"float_type"},
                  { "from_attr": "common_single_field", "from_path": "string_type", "to_path":"string_type"},
                ]
            },
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "common_repeated_field", "from_path": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "uint64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "int64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "common_repeated_field", "from_path": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="common_sub_message_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "common_sub_message_field",
          attrs = [
            { "name": "sub_message_bool_type", "path":"bool_type"},
            { "name": "sub_message_enum_type", "path":"enum_type"},
            { "name": "sub_message_int32_type", "path":"int32_type"},
            { "name": "sub_message_uint32_type", "path":"uint32_type"},
            { "name": "sub_message_int64_type", "path":"uint64_type"},
            { "name": "sub_message_uint64_type", "path":"int64_type"},
            { "name": "sub_message_float_type", "path":"float_type"},
            { "name": "sub_message_double_type", "path":"double_type"},
            { "name": "sub_message_string_type", "path":"string_type"},
            { "name": "sub_message_bool_type_r", "path":"bool_type_r"},
            { "name": "sub_message_enum_type_r", "path":"enum_type_r"},
            { "name": "sub_message_int32_type_r", "path":"int32_type_r"},
            { "name": "sub_message_uint32_type_r", "path":"uint32_type_r"},
            { "name": "sub_message_int64_type_r", "path":"uint64_type_r"},
            { "name": "sub_message_uint64_type_r", "path":"int64_type_r"},
            { "name": "sub_message_float_type_r", "path":"float_type_r"},
            { "name": "sub_message_double_type_r", "path":"double_type_r"},
            { "name": "sub_message_string_type_r", "path":"string_type_r"},
          ]
        ) \
        .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "common_sub_message_field", "to_path":"sub_message"},
                  { "from_attr": "common_sub_message_field", "to_path":"sub_message_r", "append":True}
                ]
            }
          ],
          as_string=False,
          output_attr="common_build_from_protobuf_message",
          use_dynamic_proto=True
        ) \
        .debug_log(
          common_attrs=["common_single_field", "common_repeated_field", "common_sub_message_field", "common_build_from_protobuf_message"]
        )

    leaf = self.__init_service(flow)
    # 测试 common attr 所有字段逻辑
    leaf["bool_type"] = 1
    leaf["enum_type"] = 2
    leaf["int32_type"] = 3
    leaf["uint32_type"] = 4
    leaf["int64_type"] = 5
    leaf["uint64_type"] = 6
    leaf["float_type"] = 2.1
    leaf["double_type"] = 2.2
    leaf["string_type"] = "test_string"

    leaf["bool_type_r"] = [1,0]
    leaf["enum_type_r"] = [2,4,5]
    leaf["int32_type_r"] = [2,4,5]
    leaf["uint32_type_r"] = [2,4,5]
    leaf["int64_type_r"] = [2,4,5]
    leaf["uint64_type_r"] = [2,4,5]
    leaf["float_type_r"] = [2.4,4.5,5.6]
    leaf["double_type_r"] = [2.4,4.5,5.6]
    leaf["string_type_r"] = ["test_string","test"]
    leaf.run("test_build_from_protobuf")
    # 比较单值字段
    self.assertEqual(leaf["bool_type"], leaf["single_bool_type"])
    self.assertEqual(leaf["enum_type"], leaf["single_enum_type"])
    self.assertEqual(leaf["int32_type"], leaf["single_int32_type"])
    self.assertEqual(leaf["uint32_type"], leaf["single_uint32_type"])
    self.assertEqual(leaf["int64_type"], leaf["single_int64_type"])
    self.assertEqual(leaf["uint64_type"], leaf["single_uint64_type"])
    self.assertAlmostEqual(leaf["float_type"], leaf["single_float_type"], delta=0.1)
    self.assertAlmostEqual(leaf["double_type"], leaf["single_double_type"], delta=0.1)
    self.assertEqual(leaf["string_type"], leaf["single_string_type"])
    # # 比较多值字段
    self.assertListEqual(leaf["bool_type_r"], leaf["repeated_bool_type_r"])
    self.assertListEqual(leaf["enum_type_r"], leaf["repeated_enum_type_r"])
    self.assertListEqual(leaf["int32_type_r"], leaf["repeated_int32_type_r"])
    self.assertListEqual(leaf["uint32_type_r"], leaf["repeated_uint32_type_r"])
    self.assertListEqual(leaf["int64_type_r"], leaf["repeated_int64_type_r"])
    self.assertListEqual(leaf["uint64_type_r"], leaf["repeated_uint64_type_r"])
    self.assertAlmostFloatingListEqual(leaf["float_type_r"], leaf["repeated_float_type_r"], delta=0.1)
    self.assertAlmostFloatingListEqual(leaf["double_type_r"], leaf["repeated_double_type_r"], delta=0.1)
    self.assertListEqual(leaf["string_type_r"], leaf["repeated_string_type_r"])
    # # 比较 pb 结果
    self.assertEqual(leaf["bool_type"], leaf["sub_message_bool_type"])
    self.assertEqual(leaf["enum_type"], leaf["sub_message_enum_type"])
    self.assertEqual(leaf["int32_type"], leaf["sub_message_int32_type"])
    self.assertEqual(leaf["uint32_type"], leaf["sub_message_uint32_type"])
    self.assertEqual(leaf["int64_type"], leaf["sub_message_int64_type"])
    self.assertEqual(leaf["uint64_type"], leaf["sub_message_uint64_type"])
    self.assertAlmostEqual(leaf["float_type"], leaf["sub_message_float_type"], delta=0.1)
    self.assertAlmostEqual(leaf["double_type"], leaf["sub_message_double_type"], delta=0.1)
    self.assertEqual(leaf["string_type"], leaf["sub_message_string_type"])

    self.assertListEqual(leaf["bool_type_r"], leaf["sub_message_bool_type_r"])
    self.assertListEqual(leaf["enum_type_r"], leaf["sub_message_enum_type_r"])
    self.assertListEqual(leaf["int32_type_r"], leaf["sub_message_int32_type_r"])
    self.assertListEqual(leaf["uint32_type_r"], leaf["sub_message_uint32_type_r"])
    self.assertListEqual(leaf["int64_type_r"], leaf["sub_message_int64_type_r"])
    self.assertListEqual(leaf["uint64_type_r"], leaf["sub_message_uint64_type_r"])
    self.assertAlmostFloatingListEqual(leaf["float_type_r"], leaf["sub_message_float_type_r"], delta=0.1)
    self.assertAlmostFloatingListEqual(leaf["double_type_r"], leaf["sub_message_double_type_r"], delta=0.1)
    self.assertListEqual(leaf["string_type_r"], leaf["sub_message_string_type_r"])
  
  def test_build_protobuf_from_item_attr(self):
    flow = LeafFlow(name="test_build_protobuf_from_item_attr") \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "int64_type", "to_path":"uint64_type"},
                  { "from_attr": "uint64_type", "to_path":"int64_type"},
                  { "from_attr": "float_type", "to_path":"float_type"},
                  { "from_attr": "double_type", "to_path":"double_type"},
                  { "from_attr": "string_type", "to_path":"string_type"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_single_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "item_single_field",
          attrs = [
            { "name": "single_bool_type", "path":"bool_type"},
            { "name": "single_enum_type", "path":"enum_type"},
            { "name": "single_int32_type", "path":"int32_type"},
            { "name": "single_uint32_type", "path":"uint32_type"},
            { "name": "single_int64_type", "path":"uint64_type"},
            { "name": "single_uint64_type", "path":"int64_type"},
            { "name": "single_float_type", "path":"float_type"},
            { "name": "single_double_type", "path":"double_type"},
            { "name": "single_string_type", "path":"string_type"},
          ],
          is_common_attr=False
        ) \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "int64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "uint64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_repeated_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "item_repeated_field",
          attrs = [
            { "name": "repeated_bool_type_r", "path":"bool_type_r"},
            { "name": "repeated_enum_type_r", "path":"enum_type_r"},
            { "name": "repeated_int32_type_r", "path":"int32_type_r"},
            { "name": "repeated_uint32_type_r", "path":"uint32_type_r"},
            { "name": "repeated_int64_type_r", "path":"uint64_type_r"},
            { "name": "repeated_uint64_type_r", "path":"int64_type_r"},
            { "name": "repeated_float_type_r", "path":"float_type_r"},
            { "name": "repeated_double_type_r", "path":"double_type_r"},
            { "name": "repeated_string_type_r", "path":"string_type_r"},
          ],
          is_common_attr=False
        ) \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "item_single_field", "from_path": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "item_single_field", "from_path": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "item_single_field", "from_path": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "item_single_field", "from_path": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "item_single_field", "from_path": "uint64_type", "to_path":"uint64_type"},
                  { "from_attr": "item_single_field", "from_path": "int64_type", "to_path":"int64_type"},
                  { "from_attr": "item_single_field", "from_path": "double_type", "to_path":"double_type"},
                  { "from_attr": "item_single_field", "from_path": "float_type", "to_path":"float_type"},
                  { "from_attr": "item_single_field", "from_path": "string_type", "to_path":"string_type"},
                ]
            },
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "item_repeated_field", "from_path": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "uint64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "int64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_sub_message_field",
          use_dynamic_proto=True
        ) \
        .enrich_with_protobuf(
          from_extra_var = "item_sub_message_field",
          attrs = [
            { "name": "sub_message_bool_type", "path":"bool_type"},
            { "name": "sub_message_enum_type", "path":"enum_type"},
            { "name": "sub_message_int32_type", "path":"int32_type"},
            { "name": "sub_message_uint32_type", "path":"uint32_type"},
            { "name": "sub_message_int64_type", "path":"uint64_type"},
            { "name": "sub_message_uint64_type", "path":"int64_type"},
            { "name": "sub_message_float_type", "path":"float_type"},
            { "name": "sub_message_double_type", "path":"double_type"},
            { "name": "sub_message_string_type", "path":"string_type"},
            { "name": "sub_message_bool_type_r", "path":"bool_type_r"},
            { "name": "sub_message_enum_type_r", "path":"enum_type_r"},
            { "name": "sub_message_int32_type_r", "path":"int32_type_r"},
            { "name": "sub_message_uint32_type_r", "path":"uint32_type_r"},
            { "name": "sub_message_int64_type_r", "path":"uint64_type_r"},
            { "name": "sub_message_uint64_type_r", "path":"int64_type_r"},
            { "name": "sub_message_float_type_r", "path":"float_type_r"},
            { "name": "sub_message_double_type_r", "path":"double_type_r"},
            { "name": "sub_message_string_type_r", "path":"string_type_r"},
          ],
          is_common_attr=False
        ) \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "item_sub_message_field", "to_path":"sub_message"},
                  { "from_attr": "item_sub_message_field", "to_path":"sub_message_r", "append":True}
                ]
            }
          ],
          as_string=False,
          output_attr="item_build_from_protobuf_message",
          use_dynamic_proto=True
        ) \
        .debug_log(
          item_attrs=["item_single_field", "item_repeated_field", "item_sub_message_field", "item_build_from_protobuf_message"]
        )

    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    # 测试 common attr 所有字段逻辑
    item1["bool_type"] = 1
    item1["enum_type"] = 2
    item1["int32_type"] = 3
    item1["uint32_type"] = 4
    item1["int64_type"] = 5
    item1["uint64_type"] = 6
    item1["float_type"] = 2.1
    item1["double_type"] = 2.2
    item1["string_type"] = "test_string"

    item1["bool_type_r"] = [1,0]
    item1["enum_type_r"] = [2,4,5]
    item1["int32_type_r"] = [2,4,5]
    item1["uint32_type_r"] = [2,4,5]
    item1["int64_type_r"] = [2,4,5]
    item1["uint64_type_r"] = [2,4,5]
    item1["float_type_r"] = [2.4,4.5,5.6]
    item1["double_type_r"] = [2.4,4.5,5.6]
    item1["string_type_r"] = ["test_string","test"]
    leaf.run("test_build_protobuf_from_item_attr")
    # 比较单值字段
    self.assertEqual(item1["bool_type"], item1["single_bool_type"])
    self.assertEqual(item1["enum_type"], item1["single_enum_type"])
    self.assertEqual(item1["int32_type"], item1["single_int32_type"])
    self.assertEqual(item1["uint32_type"], item1["single_uint32_type"])
    self.assertEqual(item1["int64_type"], item1["single_int64_type"])
    self.assertEqual(item1["uint64_type"], item1["single_uint64_type"])
    self.assertAlmostEqual(item1["float_type"], item1["single_float_type"], delta=0.1)
    self.assertAlmostEqual(item1["double_type"], item1["single_double_type"], delta=0.1)
    self.assertEqual(item1["string_type"], item1["single_string_type"])
    # # 比较多值字段
    self.assertListEqual(item1["bool_type_r"], item1["repeated_bool_type_r"])
    self.assertListEqual(item1["enum_type_r"], item1["repeated_enum_type_r"])
    self.assertListEqual(item1["int32_type_r"], item1["repeated_int32_type_r"])
    self.assertListEqual(item1["uint32_type_r"], item1["repeated_uint32_type_r"])
    self.assertListEqual(item1["int64_type_r"], item1["repeated_int64_type_r"])
    self.assertListEqual(item1["uint64_type_r"], item1["repeated_uint64_type_r"])
    self.assertAlmostFloatingListEqual(item1["float_type_r"], item1["repeated_float_type_r"], delta=0.1)
    self.assertAlmostFloatingListEqual(item1["double_type_r"], item1["repeated_double_type_r"], delta=0.1)
    self.assertListEqual(item1["string_type_r"], item1["repeated_string_type_r"])
    # # 比较 pb 结果
    self.assertEqual(item1["bool_type"], item1["sub_message_bool_type"])
    self.assertEqual(item1["enum_type"], item1["sub_message_enum_type"])
    self.assertEqual(item1["int32_type"], item1["sub_message_int32_type"])
    self.assertEqual(item1["uint32_type"], item1["sub_message_uint32_type"])
    self.assertEqual(item1["int64_type"], item1["sub_message_int64_type"])
    self.assertEqual(item1["uint64_type"], item1["sub_message_uint64_type"])
    self.assertAlmostEqual(item1["float_type"], item1["sub_message_float_type"], delta=0.1)
    self.assertAlmostEqual(item1["double_type"], item1["sub_message_double_type"], delta=0.1)
    self.assertEqual(item1["string_type"], item1["sub_message_string_type"])

    self.assertListEqual(item1["bool_type_r"], item1["sub_message_bool_type_r"])
    self.assertListEqual(item1["enum_type_r"], item1["sub_message_enum_type_r"])
    self.assertListEqual(item1["int32_type_r"], item1["sub_message_int32_type_r"])
    self.assertListEqual(item1["uint32_type_r"], item1["sub_message_uint32_type_r"])
    self.assertListEqual(item1["int64_type_r"], item1["sub_message_int64_type_r"])
    self.assertListEqual(item1["uint64_type_r"], item1["sub_message_uint64_type_r"])
    self.assertAlmostFloatingListEqual(item1["float_type_r"], item1["sub_message_float_type_r"], delta=0.1)
    self.assertAlmostFloatingListEqual(item1["double_type_r"], item1["sub_message_double_type_r"], delta=0.1)
    self.assertListEqual(item1["string_type_r"], item1["sub_message_string_type_r"])

  def test_build_protobuf_by_repeated_msg(self):
    flow = LeafFlow(name="test_build_protobuf_by_repeated_msg") \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "int64_type", "to_path":"uint64_type"},
                  { "from_attr": "uint64_type", "to_path":"int64_type"},
                  { "from_attr": "float_type", "to_path":"float_type"},
                  { "from_attr": "double_type", "to_path":"double_type"},
                  { "from_attr": "string_type", "to_path":"string_type"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_single_field",
          use_dynamic_proto=True
        ) \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "trivial_field",
                "build_list": [
                  { "from_attr": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "int64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "uint64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_repeated_field",
          use_dynamic_proto=True
        ) \
        .build_from_protobuf(
          is_common_attr=False,
          class_name="ks::reco::SubTestMessage",
          inputs=[
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "item_single_field", "from_path": "bool_type", "to_path":"bool_type"},
                  { "from_attr": "item_single_field", "from_path": "enum_type", "to_path":"enum_type"},
                  { "from_attr": "item_single_field", "from_path": "int32_type", "to_path":"int32_type"},
                  { "from_attr": "item_single_field", "from_path": "uint32_type", "to_path":"uint32_type"},
                  { "from_attr": "item_single_field", "from_path": "uint64_type", "to_path":"uint64_type"},
                  { "from_attr": "item_single_field", "from_path": "int64_type", "to_path":"int64_type"},
                  { "from_attr": "item_single_field", "from_path": "double_type", "to_path":"double_type"},
                  { "from_attr": "item_single_field", "from_path": "float_type", "to_path":"float_type"},
                  { "from_attr": "item_single_field", "from_path": "string_type", "to_path":"string_type"},
                ]
            },
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::SubTestMessage",
                "build_list": [
                  { "from_attr": "item_repeated_field", "from_path": "bool_type_r", "to_path":"bool_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "enum_type_r", "to_path":"enum_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "int32_type_r", "to_path":"int32_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "uint32_type_r", "to_path":"uint32_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "uint64_type_r", "to_path":"uint64_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "int64_type_r", "to_path":"int64_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "double_type_r", "to_path":"double_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "float_type_r", "to_path":"float_type_r"},
                  { "from_attr": "item_repeated_field", "from_path": "string_type_r", "to_path":"string_type_r"},
                ]
            }
          ],
          as_string=False,
          output_attr="item_sub_message_field",
          use_dynamic_proto=True
        ) \
        .build_protobuf(
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            { "item_attr": "item_sub_message_field", "path": "sub_message_r", "append": True },
          ],
          use_dynamic_proto=True,
          output_common_attr="source_repeated_message_ptr",
        ) \
         .build_from_protobuf(
          is_common_attr=True,
          class_name="ks::reco::TestBuildFromProtobufMessage",
          inputs=[
            {
                "field_type": "pb_field",
                "class_name": "ks::reco::TestBuildFromProtobufMessage",
                "build_list": [
                  { "from_attr": "source_repeated_message_ptr", "from_path": "sub_message_r", "to_path":"sub_message_r"}
                ]
            }
          ],
          as_string=False,
          output_attr="dest_repeated_message_ptr",
          use_dynamic_proto=True
        ) \
        .debug_log(
          common_attrs=["source_repeated_message_ptr", "dest_repeated_message_ptr"]
        )

    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    item2 = leaf.add_item(2)
    # 测试 common attr 所有字段逻辑
    # item1 赋值
    item1["bool_type"] = 1
    item1["enum_type"] = 2
    item1["int32_type"] = 3
    item1["uint32_type"] = 4
    item1["int64_type"] = 5
    item1["uint64_type"] = 6
    item1["float_type"] = 2.1
    item1["double_type"] = 2.2
    item1["string_type"] = "test_string"

    item1["bool_type_r"] = [1,0]
    item1["enum_type_r"] = [2,4,5]
    item1["int32_type_r"] = [2,4,5]
    item1["uint32_type_r"] = [2,4,5]
    item1["int64_type_r"] = [2,4,5]
    item1["uint64_type_r"] = [2,4,5]
    item1["float_type_r"] = [2.4,4.5,5.6]
    item1["double_type_r"] = [2.4,4.5,5.6]
    item1["string_type_r"] = ["test_string","test"]
    # item2 赋值
    # 测试 common attr 所有字段逻辑
    item2["bool_type"] = 1
    item2["enum_type"] = 2
    item2["int32_type"] = 3
    item2["uint32_type"] = 4
    item2["int64_type"] = 5
    item2["uint64_type"] = 6
    item2["float_type"] = 2.1
    item2["double_type"] = 2.2
    item2["string_type"] = "test_string"

    item2["bool_type_r"] = [1,0]
    item2["enum_type_r"] = [2,4,5]
    item2["int32_type_r"] = [2,4,5]
    item2["uint32_type_r"] = [2,4,5]
    item2["int64_type_r"] = [2,4,5]
    item2["uint64_type_r"] = [2,4,5]
    item2["float_type_r"] = [2.4,4.5,5.6]
    item2["double_type_r"] = [2.4,4.5,5.6]
    item2["string_type_r"] = ["test_string","test"]
    leaf.run("test_build_protobuf_by_repeated_msg")

  def test_parse_protobuf_from_string(self):
    flow = LeafFlow(name="test_parse_protobuf_from_string") \
        .fake_retrieve(num=3, reason=1) \
        .copy_item_meta_info(
          save_item_id_to_attr="Index"
        ) \
        .build_protobuf(
          inputs=[
            { "common_attr": "cascade_pctr", "path": "context_info.cascade_pctr" },
            { "common_attr": "cascade_pltr", "path": "context_info.cascade_pltr" },
            { "common_attr": "cascade_pwtr", "path": "context_info.cascade_pwtr" },
            { "common_attr": "cascade_plvtr", "path": "context_info.cascade_plvtr" },
            { "item_attr": "Index", "path": "final_rerank_index", "append": True },
          ],
          output_common_attr="reco_photo_info",
          class_name="ks::reco::RecoPhotoInfo",
        ) \
        .serialize_protobuf_message(
          from_common_attr = "reco_photo_info",
          serialize_to_common_attr = "reco_photo_info_str"
        ) \
        .parse_protobuf_from_string(
            input_attr="reco_photo_info_str",
            output_attr="reco_photo_info_new",
            class_name="ks::reco::RecoPhotoInfo",
        ) \
        .enrich_with_protobuf(
          from_extra_var = "reco_photo_info_new",
          attrs = [
            dict(path="context_info.cascade_pctr", name="copy_pctr"),
            dict(path="context_info.cascade_pltr", name="copy_pltr"),
            dict(path="context_info.cascade_pwtr", name="copy_pwtr"),
            dict(path="context_info.cascade_plvtr", name="copy_plvtr"),
            dict(path="final_rerank_index", name="copy_rerank_indexes"),
          ]
        )

    leaf = self.__init_service(flow)
    leaf["cascade_pctr"] = 1.0
    leaf["cascade_pltr"] = 2.0
    leaf["cascade_pwtr"] = 1.5
    leaf["cascade_plvtr"] = 2.5
    leaf["rerank_indexes"] = [1,2,3]
    leaf.run("test_parse_protobuf_from_string")
    self.assertEqual(leaf["cascade_pctr"], leaf["copy_pctr"])
    self.assertEqual(leaf["cascade_pltr"], leaf["copy_pltr"])
    self.assertEqual(leaf["cascade_pwtr"], leaf["copy_pwtr"])
    self.assertEqual(leaf["cascade_plvtr"], leaf["copy_plvtr"])
    self.assertEqual(len(leaf["rerank_indexes"]), len(leaf["copy_rerank_indexes"]))
    for i in range(0, len(leaf["rerank_indexes"])):
      self.assertEqual(leaf["rerank_indexes"][i], leaf["copy_rerank_indexes"][i])

  def test_inject_protobuf_bytes_string(self):
    # 测试 int64, int32, float, double, string 类型的字段注入
    # message OverseaTagResult {
    #     uint64 video_id = 1;
    #     uint32 classification_index = 2;
    #     float classification_prob = 3;
    #     double erotic_prob = 26; //性感概率
    #     string ocr_black_hit_word = 45;
    # }
    flow = LeafFlow(name = "test_inject_protobuf_bytes_string") \
      .build_protobuf(
        class_name="ksib::reco::OverseaTagResult",
        inputs=[],
        as_string=True,
        output_common_attr="oversea_tag_result_str",
      ) \
      .inject_protobuf_bytes(
          from_common_attr = "oversea_tag_result_str",
          append_msgs=[
              {
                  "common_attr": "int64_val",
                  "field_type": "int64",
                  "field": 1
              },
              {
                  "common_attr": "int32_val",
                  "field_type": "int",
                  "field": 2
              },
              {
                  "common_attr": "float_val",
                  "field_type": "float",
                  "field": 3
              },
              {
                  "common_attr": "double_val",
                  "field_type": "double",
                  "field": 26
              },
              {
                  "common_attr": "string_val",
                  "field": 45
              }
          ],
          output_common_attr = "oversea_tag_result_str"
      ) \
      .parse_protobuf_from_string(
          input_attr="oversea_tag_result_str",
          output_attr="oversea_tag_result_pb",
          class_name="ksib::reco::OverseaTagResult",
      ) \
      .enrich_with_protobuf(
          from_extra_var = "oversea_tag_result_pb",
          attrs = [
              dict(path="video_id", name="video_id"),
              dict(path="classification_index", name="classification_index"),
              dict(path="classification_prob", name="classification_prob"),
              dict(path="erotic_prob", name="erotic_prob"),
              dict(path="ocr_black_hit_word", name="ocr_black_hit_word"),
          ]
      )
    leaf = self.__init_service(flow)
    leaf["int64_val"] = 123123123123
    leaf["int32_val"] = 2147483600
    # 10000 + 1 / 1024
    leaf["float_val"] = 10000.0009765625
    leaf["double_val"] = 1.123123123111123
    leaf["string_val"] = "123123123123"
    leaf.run("test_inject_protobuf_bytes_string")
    self.assertEqual(leaf["int64_val"], leaf["video_id"])
    self.assertEqual(leaf["int32_val"], leaf["classification_index"])
    self.assertEqual(leaf["float_val"], leaf["classification_prob"])
    self.assertEqual(leaf["double_val"], leaf["erotic_prob"])
    self.assertEqual(leaf["string_val"], leaf["ocr_black_hit_word"])

  def test_register_proto(self):

    flow = LeafFlow(name="test_register_proto") \
        .build_protobuf(
          use_dynamic_proto=True,
          class_name="ks::reco::TempMessage",
          inputs=[
            { "common_attr": "score1", "path": "score1" },
            { "common_attr": "score2", "path": "score2" },
          ],
          output_common_attr="temp_message",
        ) \
        .build_protobuf(
          use_dynamic_proto=True,
          class_name="ks::reco::TempMessageSecond",
          inputs=[
            { "common_attr": "id", "path": "id" },
            { "common_attr": "temp_message", "path": "msg" },
          ],
          output_common_attr="temp_message_two",
        ) \
        .serialize_protobuf_message(
          from_common_attr = "temp_message_two",
          serialize_to_common_attr = "temp_message_two_str"
        ) \
        .parse_protobuf_from_string(
          use_dynamic_proto=True,
          input_attr="temp_message_two_str",
          output_attr="temp_message_two_pb_new",
          class_name="ks.reco.TempMessageSecond",
        ) \
        .enrich_with_protobuf(
          from_extra_var = "temp_message_two_pb_new",
          attrs = [
            dict(name="parsed_score2", path="msg.score2"),
            dict(name="parsed_score1", path="msg.score1"),
            dict(name="parseed_id", path="id"),
          ]
        )
    leaf = self.__init_service(flow)
    leaf["score1"] = 1.1
    leaf["score2"] = 1.2
    leaf["id"] = 462158
    leaf.run("test_register_proto")
    self.assertEqual(leaf["score1"], leaf["parsed_score1"])
    self.assertEqual(leaf["score2"], leaf["parsed_score2"])
    self.assertEqual(leaf["id"], leaf["parseed_id"])

  def test_switch_case(self):
    flow = LeafFlow(name="test_switch_case") \
        .switch_("user_type") \
          .case_(1, 2) \
            .gen_common_attr_by_lua(
              attr_map={
                "test_attr": "user_type",
              }
            ) \
          .case_(3) \
            .gen_common_attr_by_lua(
              attr_map={
                "test_attr": "333",
              }
            ) \
          .default_() \
            .gen_common_attr_by_lua(
              attr_map={
                "test_attr": "\"default\"",
              }
            ) \
        .end_switch_()

    leaf = self.__init_service(flow)

    leaf["user_type"] = 4
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], "default")

    leaf["user_type"] = 1
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], leaf["user_type"])

    leaf["user_type"] = 3
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], 333)

    leaf["user_type"] = 1.1
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], "default")

    leaf["user_type"] = 1.0
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], 1.0)

    leaf["user_type"] = "aa"
    leaf.run("test_switch_case")
    self.assertEqual(leaf["test_attr"], "default")

  def test_copy_item_meta_info(self):
    flow = LeafFlow(name="test_copy_item_meta_info") \
        .fake_retrieve(num=10, reason=100, reset_item_type=1) \
        .copy_item_meta_info(
          save_item_key_to_attr="save_item_key_to_attr",
          save_item_id_to_attr="save_item_id_to_attr",
          save_item_type_to_attr="save_item_type_to_attr",
          save_reason_to_attr="save_reason_to_attr",
          save_score_to_attr="save_score_to_attr",
          save_in_browse_set_to_attr="save_in_browse_set_to_attr",
          save_item_seq_to_attr="save_item_seq_to_attr",
        ) 

    leaf = self.__init_service(flow)
    leaf.run("test_copy_item_meta_info")
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item["save_item_key_to_attr"])
      self.assertEqual(item.item_id, item["save_item_id_to_attr"])
      self.assertEqual(item.item_type, item["save_item_type_to_attr"])
      self.assertEqual(1, item["save_item_type_to_attr"])
      self.assertEqual(item.reason, item["save_reason_to_attr"])
      self.assertEqual(item.score, item["save_score_to_attr"])
      self.assertEqual(0, item["save_in_browse_set_to_attr"])
      self.assertEqual(index, item["save_item_seq_to_attr"])

  def test_copy_user_meta_info(self):
    flow = LeafFlow(name="test_copy_user_meta_info") \
        .copy_user_meta_info(
          save_user_id_to_attr="save_user_id_to_attr",
          save_device_id_to_attr="save_device_id_to_attr",
          save_request_type_to_attr="save_request_type_to_attr",
          save_browse_set_size_to_attr="save_browse_set_size_to_attr",
          save_request_time_to_attr="save_request_time_to_attr"
        ) 

    leaf = self.__init_service(flow)
    leaf.user_id = 10086
    leaf.device_id = 'abcdef'
    leaf.request_time = 1590931639821
    leaf.browse_set = [1, 2, 3, 4, 5]

    leaf.run("test_copy_user_meta_info")

    self.assertEqual(leaf["save_user_id_to_attr"], 10086)
    self.assertEqual(leaf["save_device_id_to_attr"], "abcdef")
    self.assertEqual(leaf["save_request_time_to_attr"], 1590931639821)
    self.assertEqual(leaf["save_browse_set_size_to_attr"], 5)

  def test_mark_attr_readonly(self):
    flow = LeafFlow(name="test_mark_attr_readonly") \
        .mark_attr_readonly(
          common_attrs=["aaa", "bbb"],
          item_attrs=["ccc", "ddd"],
        ) \
        .set_attr_value(
          no_overwrite=False,
          common_attrs=[{
            "name": "aaa",
            "type": "int",
            "value": 2
          }, {
            "name": "bbb",
            "type": "string",
            "value": "zzz"
          }
          ],
          item_attrs=[{
            "name": "ccc",
            "type": "int",
            "value": 200
          }, {
            "name": "ddd",
            "type": "string",
            "value": "zzz"
          }]
        )

    leaf = self.__init_service(flow)
    leaf["aaa"] = 1
    leaf["bbb"] = "xxx"
    item = leaf.add_item(1)
    item["ccc"] = 100
    item["ddd"] = "yyy"

    leaf.run("test_mark_attr_readonly")

    self.assertEqual(leaf["aaa"], 1)
    self.assertEqual(leaf["bbb"], "xxx")
    self.assertEqual(item["ccc"], 100)
    self.assertEqual(item["ddd"], "yyy")


  def test_count_item_attr(self):
    flow = LeafFlow(name="test_count_item_attr") \
        .count_item_attr(
          counters = [{
            "check_attr_name": "author_id",
            "output_attr_name": "author_browsed_count",
            # 统计 author_id 在 recent_browsed_authors 中出现的次数
            "check_values": ["{{recent_browsed_authors}}", 2, 999],
          }]
        )

    leaf = self.__init_service(flow)
    for i in range(0, 100):
      item = leaf.add_item(i)
      item["author_id"] = [i % 5, i % 5]
    leaf["recent_browsed_authors"] = [0, 2]
    leaf.run("test_count_item_attr")
    for index, item in enumerate(leaf.items):
      if index % 5 == 0:
        self.assertEqual(item["author_browsed_count"], 2)
      elif index % 5 == 2:
        self.assertEqual(item["author_browsed_count"], 4)
      else:
        self.assertEqual(item["author_browsed_count"], 0)

  def test_deduplicate(self):
    flow = LeafFlow(name="test_deduplicate") \
        .fake_retrieve(num=10, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .deduplicate(
          reason_priority_list=[101, 100],
          append_reason_to="reasons",
          save_dup_count_to="dup_count"
        )
    leaf = self.__init_service(flow)
    leaf.run("test_deduplicate")
    self.assertEqual(len(leaf.items), 10)
    for item in leaf.items:
      # 先入的写入 _REASON_
      self.assertEqual(item["_REASON_"], 100)
      self.assertEqual(item.reason, 101)
      self.assertEqual(item["reasons"], [100, 101])
      self.assertEqual(item["dup_count"], 2)

    flow = LeafFlow(name="test_deduplicate_least") \
        .fake_retrieve(num=10, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .deduplicate(
        lower_reason_priority_list=[101, 100],
        append_reason_to="reasons",
        save_dup_count_to="dup_count"
    )
    leaf = self.__init_service(flow)
    leaf.run("test_deduplicate_least")
    self.assertEqual(len(leaf.items), 10)
    for item in leaf.items:
      # 先入的写入 _REASON_
      self.assertEqual(item["_REASON_"], 100)
      self.assertEqual(item.reason, 101)
      self.assertEqual(item["reasons"], [100, 101])
      self.assertEqual(item["dup_count"], 2)

    flow = LeafFlow(name="test_deduplicate_least_2") \
        .fake_retrieve(num=10, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .deduplicate(
        lower_reason_priority_list=[100],
        append_reason_to="reasons",
        save_dup_count_to="dup_count"
    )
    leaf = self.__init_service(flow)
    leaf.run("test_deduplicate_least_2")
    self.assertEqual(len(leaf.items), 10)
    for item in leaf.items:
      # 先入的写入 _REASON_
      self.assertEqual(item["_REASON_"], 100)
      self.assertEqual(item.reason, 101)
      self.assertEqual(item["reasons"], [100, 101])
      self.assertEqual(item["dup_count"], 2)

    flow = LeafFlow(name="test_deduplicate_least_3") \
        .fake_retrieve(num=10, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .deduplicate(
        reason_priority_list=[101],
        lower_reason_priority_list=[100],
        append_reason_to="reasons",
        save_dup_count_to="dup_count"
    )
    leaf = self.__init_service(flow)
    leaf.run("test_deduplicate_least_3")
    self.assertEqual(len(leaf.items), 10)
    for item in leaf.items:
      # 先入的写入 _REASON_
      self.assertEqual(item["_REASON_"], 100)
      self.assertEqual(item.reason, 101)
      self.assertEqual(item["reasons"], [100, 101])
      self.assertEqual(item["dup_count"], 2)

    flow = LeafFlow(name="test_deduplicate_append_reason_order_to") \
        .fake_retrieve(num=5, reason=100) \
        .fake_retrieve(num=10, reason=101) \
        .fake_retrieve(num=1, reason=102) \
        .fake_retrieve(item_keys=[13, 12, 11], reason=103) \
        .deduplicate(
        reason_priority_list=[100, 101],
        append_reason_to="reasons",
        append_reason_order_to="reason_order"
    )
    leaf = self.__init_service(flow)
    leaf.run("test_deduplicate_append_reason_order_to")
    self.assertEqual(len(leaf.items), 13)
    for item in leaf.items:
      # check order
      if item.item_key == 1:
        self.assertEqual(item["reasons"], [100, 101, 102])
        self.assertEqual(item["reason_order"], [item.item_key, item.item_key, item.item_key])
      elif item.item_key > 1 and item.item_key <= 5:
        self.assertEqual(item["reasons"], [100, 101])
        self.assertEqual(item["reason_order"], [item.item_key, item.item_key])
      elif item.item_key > 5 and item.item_key <= 10:
        self.assertEqual(item["reasons"], [101])
        self.assertEqual(item["reason_order"], [item.item_key])
      elif item.item_key > 10 and item.item_key <= 13:
        self.assertEqual(item["reasons"], [103])
        self.assertEqual(item["reason_order"], [14 - item.item_key])
      else:
        pass

  def test_enrich_attr_by_lua(self):
    flow = LeafFlow(name="test_enrich_attr_by_lua") \
      .enrich_attr_by_lua(
        import_common_attr = ["city", "region"],
        function_for_common = "calculate",
        export_common_attr = ["concat"],
        lua_script = """
          function calculate()
            return city .. region
          end
        """
      )
    leaf = self.__init_service(flow)
    leaf["city"] = "bj"
    leaf["region"] = "hb"
    leaf.run("test_enrich_attr_by_lua")
    self.assertEqual(leaf["concat"], "bjhb")

    flow = LeafFlow(name="test_enrich_attr_by_lua_xxhash") \
      .enrich_attr_by_lua(
        import_common_attr = ["data"],
        function_for_common = "calculate",
        export_common_attr = ["h32", "h64"],
        lua_script = """
          function calculate()
            return util.XXHash32(data), util.XXHash64(data)
          end
        """
      )
    leaf = self.__init_service(flow)
    leaf["data"] = "Hello, world!"
    leaf.run("test_enrich_attr_by_lua_xxhash")
    self.assertEqual(leaf["h32"], 834093149)
    self.assertEqual(leaf["h64"], -755700219241327498) # 对应无符号数：17691043854468224118
    leaf["data"] = 1
    leaf.run("test_enrich_attr_by_lua_xxhash")
    self.assertEqual(leaf["h32"], 149775153)
    self.assertEqual(leaf["h64"], -6977822845260490347) # 对应无符号数: 11468921228449061269
    leaf["data"] = 9999
    leaf.run("test_enrich_attr_by_lua_xxhash")
    self.assertEqual(leaf["h32"], 781163594)
    self.assertEqual(leaf["h64"], -8207105382575226232) # 对应无符号数: 10239638691134325384
    leaf["data"] = 9999999
    leaf.run("test_enrich_attr_by_lua_xxhash")
    self.assertEqual(leaf["h32"], 353506232)
    self.assertEqual(leaf["h64"], 8401761029293667483)
    leaf["data"] = 99999999999
    leaf.run("test_enrich_attr_by_lua_xxhash")
    self.assertEqual(leaf["h32"], 1291971903)
    self.assertEqual(leaf["h64"], 5632993335228209510)

    flow = LeafFlow(name="test_enrich_attr_by_lua_geohash") \
      .enrich_attr_by_lua(
        import_common_attr = ["lat", "lon"],
        function_for_common = "calculate",
        export_common_attr = ["geohash_encoded"],
        lua_script = """
          function calculate()
            return util.GeoHashEncode(lat, lon, 12)
          end
        """
      ) \
      .enrich_attr_by_lua(
        import_common_attr = ["geohash_encoded"],
        function_for_common = "calculate",
        export_common_attr = ["lat_decoded", "lon_decoded"],
        lua_script = """
          function calculate()
            local lat_decoded, lon_decoded = util.GeoHashDecode(geohash_encoded)
            return lat_decoded, lon_decoded
          end
        """
      ) 

    # 验证数据来源：http://geohash.org/ezs42e44yx96
    leaf = self.__init_service(flow)
    leaf["lat"] = 42.6
    leaf["lon"] = -5.6
    leaf.run("test_enrich_attr_by_lua_geohash")
    print(f'lat_decoded: {leaf["lat_decoded"]}, lon_decoded: {leaf["lon_decoded"]}')
    self.assertEqual(leaf["geohash_encoded"], "ezs42e44yx96") 
    self.assertAlmostEqual(leaf["lat_decoded"], leaf["lat"])
    self.assertAlmostEqual(leaf["lon_decoded"], leaf["lon"])

  def test_truncate_by_attr(self):
    flow = LeafFlow(name="test_truncate_by_attr") \
      .set_attr_value(
        common_attrs = [
          {"name": "bucket2_limit_num", "type": "int", "value": 4},
          {"name": "bucket2_ratio", "type": "double", "value": 0.5},
          {"name": "bucket3_limit_num", "type": "int", "value": 5},
        ]
      ) \
      .truncate_by_attr(
      attr_name="bucket_name",
      size_limit=12,
      allow_overflow=True,
      queues=[
        {
          "attr_value": 1,
          "ratio": 1.0,
          "limit": 2
        },
        {
          "attr_value": 2,
          "ratio": "{{bucket2_ratio}}",
          "limit": "{{bucket2_limit_num}}"
        },
        {
          "attr_value": 3,
          "limit": "{{bucket3_limit_num}}"
        },
      ],
    )

    leaf = self.__init_service(flow)
    for i in range(0, 24):
      item = leaf.add_item(i)
      item["bucket_name"] = i // 6

    result_item = [0, 1, 2, 6, 7, 12, 13, 14, 18, 19, 20, 21, 22]
    leaf.run("test_truncate_by_attr")
    self.assertEqual(len(leaf.items), 13)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])


  def test_diversify_by_rules(self):
    flow = LeafFlow(name="test_diversify_by_rules") \
      .diversify_by_rules(
      max_satisfied_pick=10,
      top_priority=8,
      rules = [
        # 对作者 id 做滑动窗口 6 出 1 打散
        dict(priority=6, window_size=6, max_num=1, attr_name="author_id_attr"),
        # 在结果集的前 10 个结果中强插 2 个直播短视频， 注意该情况下非直播短视频的 "is_living" 为缺省状态
        dict(priority=2, window_type="top", window_size=10, min_num=2, attr_name="is_living"),
        # 以上规则配置中，第一条规则的优先级高于第二条规则 (6 > 2), 规则引擎会优先保证第一条规则被满足
      ]
    )

    leaf = self.__init_service(flow)
    for i in range(0, 14):
      item = leaf.add_item(i)
      item["author_id_attr"] = i // 2
      if i >= 10:
        item["is_living"] = 1

    result_item = [0, 2, 4, 6, 8, 10, 1, 3, 5, 12, 7, 9, 11, 13]
    leaf.run("test_diversify_by_rules")
    self.assertEqual(len(leaf.items), 14)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])


  def test_intermix(self):
    flow = LeafFlow(name="test_intermix") \
      .intermix(
      mix_on_attr = "category",
      # 1: 视频，0: 直播，该 pattern 表示每 2 个视频跟 1 个直播，无限往复下去
      mix_pattern = [1, 1, 0],
    )

    leaf = self.__init_service(flow)
    for i in range(0, 10):
      item = leaf.add_item(i)
      item["category"] = i // 3

    result_item = [3, 4, 0, 5, 1, 2, 6, 7, 8, 9]
    categorys = [1, 1, 0, 1, 0, 0, 2, 2, 2, 3]
    leaf.run("test_intermix")
    self.assertEqual(len(leaf.items), 10)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])
      self.assertEqual(item["category"], categorys[index])


  def test_filter_by_item_results(self):
    flow_a = LeafFlow(name="flowa")
    flow_b = LeafFlow(name="flowb")
    flow = LeafFlow(name="test_filter_by_item_results") \
      .enrich_by_sub_flow(sub_flow = flow_a, save_results_to="sub_flow_a_result", target_reason=[1]) \
      .enrich_by_sub_flow(sub_flow = flow_b, save_results_to="sub_flow_b_result", target_reason=[2]) \
      .filter_by_item_results(
      remove_if_not_in=["sub_flow_a_result","sub_flow_b_result"]
    )

    leaf = self.__init_service(flow)
    for i in range(0, 8):
      reason = 0
      if i <= 3:
        reason = 1
      if i >= 7:
        reason = 2
      leaf.add_item(i, reason, 0.0)

    result_item = [0, 1, 2, 3, 7]
    leaf.run("test_filter_by_item_results")
    self.assertEqual(len(leaf.items), 5)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])

  def test_rotate(self):
    flow = LeafFlow(name="test_rotate") \
      .rotate(head=-1)

    leaf = self.__init_service(flow)
    for i in range(0, 6):
      leaf.add_item(i)

    result_item = [5, 0, 1, 2, 3, 4]
    leaf.run("test_rotate")
    self.assertEqual(len(leaf.items), 6)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])


  def test_variant(self):
    flow = LeafFlow(name="test_variant") \
      .variant(
      variant_config = {
        # 按 author_id 打散
        "author_id_attr": {
          # 打散窗口一般设置为一屏或一刷的数目
          "decay_window_size": 4,
          # 设置为 2 表示不允许重复，只能出现 1 次
          "decay_occurrent_times": 2,
          # 打散衰减系数
          "decay_rate": 0.1,
        }
      }
    )

    leaf = self.__init_service(flow)
    for i in range(0, 20):
      item = leaf.add_item(i)
      item["author_id_attr"] = i // 3

    result_item = [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11, 12, 15, 18, 13, 14, 16, 19, 17]
    author_id = [0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 4, 4, 5, 6, 5]
    leaf.run("test_variant")
    self.assertEqual(len(leaf.items), 20)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])
      self.assertEqual(item["author_id_attr"], author_id[index])


  def test_force_insert(self):
    flow = LeafFlow(name="test_force_insert") \
      .force_insert(
      # 在 int item_attr "promote_to_position" 中指定要强插的位置
      position_from_attr = "promote_to_position",
      # 将被强插的 item 的 reason 重置为 666
      reset_reason = 666
    )

    leaf = self.__init_service(flow)
    position = 0
    for i in range(0, 10):
      item = leaf.add_item(i)
      if i % 3 == 0:
        item["promote_to_position"] = position
        position += 1

    result_item = [0, 3, 6, 9, 1, 2, 4, 5, 7, 8]
    reasons = [666, 666, 666, 666, 0, 0, 0, 0, 0, 0]
    leaf.run("test_force_insert")
    self.assertEqual(len(leaf.items), 10)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])
      self.assertEqual(item.reason, reasons[index])


  def test_truncate_by_reason(self):
    flow = LeafFlow(name="test_truncate_by_reason") \
      .truncate_by_reason(
      size_limit = 25,
    )

    leaf = self.__init_service(flow)
    for i in range(0, 100):
      leaf.add_item(i, i // 10, 0.0)
    result_item = [0, 1, 10, 11, 20, 21, 30, 31, 40, 41, 50, 51, 60, 61, 70, 71, 80, 81, 90, 91]
    leaf.run("test_truncate_by_reason")
    self.assertEqual(len(leaf.items), 20)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])



  @unittest.skip("shuffle 结果随机无法验证")
  def test_shuffle(self):
    pass

  def test_limit(self):
    flow = LeafFlow(name="test_limit") \
      .limit(5, backfill_to={"partner_flag": 3}) \
      .count_reco_result(save_count_to="result_num")

    leaf = self.__init_service(flow)
    for i in range(0, 100):
      item = leaf.add_item(i)
      if i >= 10:
        item["partner_flag"] = 1
    result_item = [0, 1, 2, 3, 4, 10, 11, 12]
    leaf.run("test_limit")
    self.assertEqual(len(leaf.items), 8)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, result_item[index])


  def test_filter_by_attr(self):
    flow = LeafFlow(name="test_filter_by_attr") \
      .filter_by_attr(
        attr_name="like_score",
        remove_if="<",
        compare_to=1,
        remove_if_attr_missing=True,
      ) \
      .count_reco_result(save_count_to="result_num")

    leaf = self.__init_service(flow)
    for i in range(0, 100):
      item = leaf.add_item(i)
      if i >= 10:
        item["like_score"] = i % 5
    leaf.run("test_filter_by_attr")
    self.assertEqual(leaf["result_num"], 72)

  def test_filter_by_browse_set(self):
    flow = LeafFlow(name="test_filter_by_browse_set") \
      .filter_by_browse_set(
        save_filtered_items_to_common_attr="browse_set_filtered_items"
      )

    leaf = self.__init_service(flow)
    leaf.browse_set = [1, 3, 5, 7, 9]
    for i in range(0, 100):
      leaf.add_item(i % 50)
    leaf.run("test_filter_by_browse_set")
    self.assertEqual(len(leaf.items), 90)
    self.assertListEqual(leaf["browse_set_filtered_items"], [1, 3, 5, 7, 9, 1, 3, 5, 7, 9])

    flow = LeafFlow(name="test_filter_by_browse_set_with_attr") \
      .filter_by_browse_set(check_id_in_attr="aid")

    leaf = self.__init_service(flow)
    leaf.browse_set = [1, 3, 5, 7, 9]
    item_keys = list(range(0, 10))
    for i in item_keys:
      item = leaf.add_item(i * 1000)
      item["aid"] = i
    leaf.run("test_filter_by_browse_set_with_attr")
    self.assertListEqual([item.item_key for item in leaf.items],
                         [x * 1000 for x in item_keys if x not in leaf.browse_set])

    flow = LeafFlow(name="test_filter_by_browse_set_with_list_attr") \
      .filter_by_browse_set(check_id_in_attr="aid")

    leaf = self.__init_service(flow)
    leaf.browse_set = [1, 5, 9]
    item_keys = list(range(0, 10))
    for i in item_keys:
      item = leaf.add_item(i * 1000)
      item["aid"] = [i, i+1]
    leaf.run("test_filter_by_browse_set_with_list_attr")
    self.assertListEqual([item.item_key for item in leaf.items],
                         [x * 1000 for x in item_keys if x not in leaf.browse_set and x+1 not in leaf.browse_set])


  def test_filter_by_common_attr(self):
    flow = LeafFlow(name="test_filter_by_common_attr") \
      .filter_by_common_attr(
        common_attr=["unwanted_items"]
      ) \
      .count_reco_result(save_count_to="result_num1") \
      .filter_by_common_attr(
        common_attr=["unwanted_items_for_pardon"],
        pardon_num=5,
      ) \
      .count_reco_result(save_count_to="result_num2") \
      .filter_by_common_attr(
        common_attr=["wanted_items"],
        pardon_num=5,
        exclude=False
      ) \
      .count_reco_result(save_count_to="result_num3") \
      .filter_by_common_attr(
        common_attr=["wanted_items"],
        exclude=False
      ) \
      .count_reco_result(save_count_to="result_num4") \
      .filter_by_common_attr(
        common_attr=["unwanted_items_for_cancel"],
        cancel_num=5
      ) \
      .count_reco_result(save_count_to="result_num5") \
      .filter_by_common_attr(
        common_attr=["unwanted_items_for_cancel"],
        cancel_num=3
      ) \
      .count_reco_result(save_count_to="result_num6")

    leaf = self.__init_service(flow)
    leaf["unwanted_items"] = [1, 3, 5, 7, 9]
    leaf["wanted_items"] = [2, 4, 6, 8, 10]
    leaf["unwanted_items_for_pardon"] = [11, 12, 13, 14, 15]
    leaf["unwanted_items_for_cancel"] = [2, 4, 6]
    for i in range(0, 100):
      item = leaf.add_item(i % 50)
    leaf.run("test_filter_by_common_attr")
    self.assertEqual(leaf["result_num1"], 90)
    self.assertEqual(leaf["result_num2"], 85)
    self.assertEqual(leaf["result_num3"], 15)
    self.assertEqual(leaf["result_num4"], 10)
    self.assertEqual(leaf["result_num5"], 10)
    self.assertEqual(leaf["result_num6"], 4)

  def test_filter_by_rule(self):
    def join_rule(rules,join):
      return {"join":join,"filters":rules}

    def gen_rule(attr,compare_to,remove_if, remove_if_attr_missing=False,enable=None):
      rule = {
        "attr_name": attr,
        "compare_to": compare_to,
        "remove_if": remove_if,
        "remove_if_attr_missing": remove_if_attr_missing
      }
      if enable != None:
        rule["enable"] = enable

      return rule
    
    def gen_item_filter_rule_rule(attr,compare_to_item,remove_if):
      return {
          "attr_name": attr,
          "compare_to_item_attr": compare_to_item,
          "remove_if": remove_if,
        }
    
    test_queue = [
      {"rule":gen_rule("p_int", 5, "==")},
      {"rule":gen_rule("p_int", 3, ">")},
      {"rule":gen_rule("p_int", "{{common_int}}", "==")},
      {"rule":gen_rule("p_int", "{{return common_int + 1}}", "==")},
      {"rule":gen_rule("p_double", "{{common_str}}", "==")},
      {"rule":gen_rule("p_str", "{{common_str}}", "==")},
      {"rule":gen_rule("p_int", [1,2,3,4], "in")},
      {"rule":gen_rule("p_int", [1,2,3,4], "not in")},
      {"rule":gen_rule("p_int", "{{int_list}}", "in")},
      {"rule":gen_rule("p_int_list", 1, "contain")},
      {"rule":gen_rule("p_int_list", [1,2], "contain")},
      {"rule":gen_rule("p_int_list", 1, "not contain")},
      {"rule":gen_rule("p_str_list", ["1", "sth", "winter"], "contain")},
      {"rule":gen_rule("p_str_list", "{{str_list}}", "not contain")},
      {"rule":gen_rule("p_int_list", [1,2,3], "intersect")},
      {"rule":gen_rule("p_int_list", "{{int_list}}", "not intersect")},
      {"rule":gen_rule("p_str_list", ["1", "sth", "winter"], "intersect")},
      {"rule":gen_rule("p_str_list", "{{str_list}}", "not intersect")},
      {"rule":join_rule([gen_rule("p_int", 3, ">"), gen_rule("p_int", 8, "<")], "and")},
      {"rule":join_rule([gen_rule("p_str_count", "5", "=="), gen_rule("p_double", 3.0, "<=")], "or")},
      {"rule":join_rule([gen_rule("p_int", i, "==") for i in range(3,8)], "or")},
      {"rule":join_rule([gen_rule("p_int_null", -1, "==", True), gen_rule("p_double", 7.0, "<=")], "and")},
      {"rule":join_rule([join_rule([gen_rule("p_int", 3, ">"), gen_rule("p_int", 8, "<")], "and"), gen_rule("p_double", 1.0, "==")], "or")},
      {"rule":join_rule([join_rule([gen_rule("p_int", 3, ">"), gen_rule("p_int", 8, "<")], "and"), gen_rule("p_double", 1.0, "==")], "or"), "pardon_num":2},
      {"rule":join_rule([join_rule([gen_rule("p_int", 3, ">"), gen_rule("p_int", 8, "<")], "and"), gen_rule("p_double", 1.0, "==")], "or"), "cancel_num":6},
      {"rule":{"attr_name": "p_int_null", "remove_if_attr_missing":True}},
      {"rule":gen_item_filter_rule_rule("p_int", "p_compare_to_int", "==")},
      {"rule":gen_item_filter_rule_rule("p_double", "p_compare_to_double", ">")},
      {"rule":gen_item_filter_rule_rule("p_str_list2", "p_compare_to_str_list", "contain")},
      {"rule":join_rule([join_rule([gen_rule("p_int", 3, ">"), gen_rule("p_int", 6, "<")], "and"), gen_item_filter_rule_rule("p_double", "p_compare_to_double", "<")], "or")},
      {"rule":gen_rule("p_int_list", [1,2,3,4], "not in")},
      {"rule":gen_rule("p_int", 5, "==", enable="{{enable_false}}")},
      {"rule":gen_rule("p_int", 5, "!=", enable="{{enable_true}}")},
      {"rule":join_rule([gen_rule("p_int", 5, "==", enable="{{enable_false}}") for _ in range(4)], "and")},
      {"rule":join_rule([gen_rule("p_int", 1, "==", enable="{{enable_false}}"), 
                         gen_rule("p_int", 5, "==")], "and") },
      {"rule":join_rule([gen_rule("p_int", 1, "==", enable="{{enable_true}}"), 
                         gen_rule("p_int", 5, "==", enable="{{enable_false}}")], "and") },
      {"rule":join_rule([gen_rule("p_int", 1, "==", enable="{{enable_false}}"), 
                         gen_rule("p_int", 5, "==", enable="{{enable_true}}")], "or") },
    ]
    assert_item_indexes = [
      [0,1,2,3,4,6,7,8,9],
      [0,1,2,3],
      [0,1,2,4,5,6,7,8,9],
      [0,1,2,3,5,6,7,8,9],
      [0,1,2,3,4,5,6,7,8,9],
      [],
      [0,5,6,7,8,9],
      [1,2,3,4],
      [0,5,6,7,8,9],
      [2,3,4,5,6,7,8,9],
      [2,3,4,5,6,7,8,9],
      [0,1],
      [0,1,2,3,4,5,6,7,8,9],
      [],
      [4,5,6,7,8,9],
      [0,1,2,3,4],
      [0,2,3,4,5,6,7,8,9],
      [1],
      [0,1,2,3,8,9],
      [4,6,7,8,9],
      [0,1,2,8,9],
      [0,2,4,6,8,9],
      [0,2,3,8,9],
      [0,1,2,3,4,8,9],
      [0,1,2,3,4,5,6,7,8,9],
      [0,2,4,6,8],
      [0,1,2,4,5,6,7,8,9],
      [0,1,2,3,4,5,6,7],
      [1,2,4,5,7,8],
      [7,8,9],
      [1,2],
      [0,1,2,3,4,5,6,7,8,9],
      [5],
      [0,1,2,3,4,5,6,7,8,9],
      [0,1,2,3,4,6,7,8,9],
      [0,2,3,4,5,6,7,8,9],
      [0,1,2,3,4,6,7,8,9],
    ]
    
    def test_flow(test_service, idx, config, correct_result):
      flow = LeafFlow(name="test_filter_by_rule"+str(idx)) \
        .filter_by_rule(**config)

      leaf = test_service.__init_service(flow)
      leaf["threshold"] = 8
      leaf["common_int"] = 3
      leaf["common_double"] = 7.0
      leaf["common_str"] = "str"
      leaf["int_list"] = [1,2,3,4]
      leaf["str_list"] = ["1", "for", "unit", "test"]
      leaf["enable_true"] = True
      leaf["enable_false"] = False

      for i in range(10):
        item = leaf.add_item(i)
        item["p_int"] = i
        item["p_double"] = float(i)
        item["p_str"] = "str"
        item["p_str_count"] = str(i)
        if i%2==0:
          item["p_int_null"] = i
        item["p_int_list"] = [i, i+1, i+2]
        item["p_str_list"] = [str(i)]
        item["p_compare_to_int"] = 3
        item["p_compare_to_double"] = 7.0
        item["p_compare_to_int_list"] = [1,2,3,4]
        item["p_compare_to_str_list"] = ["for", "unit", "test"]
        if i%3==0:
          item["p_str_list2"] = [str(i), "for", "unit", "test"]

      leaf.run("test_filter_by_rule"+str(idx))
      print(config)
      self.assertEqual([item["p_int"] for item in leaf.items], correct_result)
    
    for idx in range(len(test_queue)):
      test_flow(self, idx, test_queue[idx], assert_item_indexes[idx])

    flow = LeafFlow(name="test_filter_by_rule_same_config") \
    .filter_by_rule(
      rule = {
        "attr_name":"p_int_list",
        "compare_to":"{{test_list}}",
        "remove_if": "not intersect"
      },
    ) \
    .set_attr_value(
      no_overwrite=False,
      common_attrs=[
        {
          "name": "test_list",
          "type": "int_list",
          "value": []
        }
      ],
    ) \
    .filter_by_rule(
      rule = {
        "attr_name":"p_int_list",
        "compare_to":"{{test_list}}",
        "remove_if": "not intersect"
      },
    )
    leaf = self.__init_service(flow)
    leaf["test_list"] = [1]
    for i in range(10):
      item = leaf.add_item(i)
      item["p_int_list"] = [1,19,1,1]
    leaf.run("test_filter_by_rule_same_config")
    self.assertEqual(len(leaf.items), 0)

  def test_base64(self):
    flow = LeafFlow(name="test_base64") \
      .base64(
        mode="encode",
        is_common_attr=True,
        input_attr="str_list",
        output_attr="str_list_encoded",
      ) \
      .base64(
        mode="decode",
        is_common_attr=True,
        input_attr="str_list_encoded",
        output_attr="str_list_decoded",
      )

    leaf = self.__init_service(flow)
    leaf["str_list"] = ["666", "777"]
    leaf.run("test_base64")
    self.assertListEqual(leaf["str_list"], leaf["str_list_decoded"])

  def test_zstd(self):
    flow = LeafFlow(name="test_zstd") \
      .zstd(
        mode="compress",
        compression_level = -5,
        input_common_attr="str",
        output_common_attr="str_compressed",
      ) \
      .zstd(
        mode="decompress",
        input_common_attr="str_compressed",
        output_common_attr="str_decompressed",
      )
    leaf = self.__init_service(flow)
    leaf["str"] = "asdfasdf"
    leaf.run("test_zstd")
    self.assertEqual(leaf["str"], leaf["str_decompressed"])
  
  def test_string_format(self):
    flow = LeafFlow(name="test_string_format") \
      .string_format(
        is_common_attr=True,
        format_string="%lu\t%s",
        input_attrs=["primary_key", "batched_samples_encoded"],
        output_attr="batched_samples_kv",
      ) \
      .string_format(
        is_common_attr=False,
        format_string="%lu\t%lu",
        input_attrs=["tag", "click"],
        output_attr="click_kv",
      )

    leaf = self.__init_service(flow)
    leaf["primary_key"] = 666
    leaf["batched_samples_encoded"] = "abcde"
    item = leaf.add_item(111)
    item["tag"], item["click"] = -1, 1
    leaf.run("test_string_format")
    self.assertEqual(leaf["batched_samples_kv"], "666\tabcde")
    self.assertEqual(item["click_kv"], "18446744073709551615\t1")

  def test_str_format(self):
    flow = LeafFlow(name="test_str_format") \
      .string_format(
        is_common_attr=True,
        format_string="%lu\t%s",
        input_attrs=["primary_key", "batched_samples_encoded"],
        output_attr="batched_samples_kv",
      ) \
      .string_format(
        is_common_attr=False,
        format_string="%lu\t%lu",
        input_attrs=["tag", "click"],
        output_attr="click_kv",
      )

    leaf = self.__init_service(flow)
    leaf["primary_key"] = 666
    leaf["batched_samples_encoded"] = "abcde"
    item = leaf.add_item(111)
    item["tag"], item["click"] = -1, 1
    leaf.run("test_str_format")
    self.assertEqual(leaf["batched_samples_kv"], "666\tabcde")
    self.assertEqual(item["click_kv"], "18446744073709551615\t1")



  def test_get_distance_with_lua(self):
    def get_distance(lat_a, lon_a, lat_b, lon_b):
      # approximate radius of earth in km
      R = 6373.0

      lat1 = math.radians(lat_a)
      lon1 = math.radians(lon_a)
      lat2 = math.radians(lat_b)
      lon2 = math.radians(lon_b)

      dlon = lon2 - lon1
      dlat = lat2 - lat1

      a = math.sin(dlat / 2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2)**2
      c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

      return R * c

    def random_lat_lon():
      return (random.uniform(-90, 90), random.uniform(-180, 80))

    test_input = [
      (*random_lat_lon(), *random_lat_lon()) for idx in range(100)
    ]

    test_cases = [
      (f"distance_{idx}", f"util.GetDistance({lat_a}, {lon_a}, {lat_b}, {lon_b})", get_distance(lat_a, lon_a, lat_b, lon_b))
      for idx, (lat_a, lon_a, lat_b, lon_b) in enumerate(test_input)
    ]

    flow = LeafFlow(name="test_get_distance_with_lua") \
      .gen_common_attr_by_lua(attr_map={key: expr for key, expr, _ in test_cases})

    leaf = self.__init_service(flow)
    leaf.run("test_get_distance_with_lua")
    for key, expr, val in test_cases:
      self.assertAlmostEqual(leaf[key], val, delta=20, msg=f"output of '{expr}' is unexpected")

  def test_enrich_with_json_common(self):
    flow = LeafFlow(name="test_enrich_with_json") \
      .enrich_with_json(
        import_attr="input_json_str",
        attrs=[
          dict(name="json", path="root", output_type="json_string"),
          dict(name="json_list", path="root", output_type="json_string_list"),
          dict(name="int_val", path="root.int_val", output_type="int64"),
          dict(name="int_list_val", path="root.int_val", output_type="int64_list"),
          dict(name="float_val", path="root.float_val", output_type="double"),
          dict(name="float_list_val", path="root.float_val", output_type="double_list"),
          dict(name="string_val", path="root.string_val", output_type="string"),
          dict(name="string_list_val", path="root.string_val", output_type="string_list"),
          dict(name="no_such_val", path="root.no_such_attr", output_type="json_string_list"),
        ],
        is_common_attr=True,
      )

    leaf = self.__init_service(flow)

    json_obj1 = {
      "int_val": 1,
      "float_val": 1.1,
      "string_val": "alpha",
    }

    json_obj2 = {
      "int_val": 3,
      "float_val": 2.2,
      "string_val": "beta",
    }

    invalid_json_obj = {
      "int_val": "invalid",
      "float_val": "invalid",
      "string_val": -1,
    }

    root_objs = [json_obj1, json_obj2, invalid_json_obj]

    leaf["input_json_str"] = json.dumps({"root": root_objs})
    leaf.run("test_enrich_with_json")

    self.assertEqual(json.loads(leaf["json"]), json_obj1)
    self.assertEqual([json.loads(j) for j in leaf["json_list"]], root_objs)
    self.assertEqual(leaf["int_val"], 1)
    self.assertEqual(leaf["int_list_val"], [1, 3])
    self.assertEqual(leaf["float_val"], 1.1)
    self.assertEqual(leaf["float_list_val"], [1.1, 2.2])
    self.assertEqual(leaf["string_val"], "alpha")
    self.assertEqual(leaf["string_list_val"], ["alpha", "beta"])
    self.assertEqual(leaf["no_such_val"], None)

  def test_encrypted_id_for_common_attr(self):
    flow = LeafFlow(name="test_encrypted_id_for_common_attr") \
      .encrypted_id(mode="decrypt_photo_id", is_common_attr=True,
                    input_attr="encrypted_photo_id", output_attr="decrypted_photo_id") \
      .encrypted_id(mode="encrypt_photo_id", is_common_attr=True,
                    input_attr="decrypted_photo_id", output_attr="encrypted_photo_id2") \
      .encrypted_id(mode="decrypt_live_id", is_common_attr=True,
                    input_attr="encrypted_live_id", output_attr="decrypted_live_id") \
      .encrypted_id(mode="encrypt_live_id", is_common_attr=True,
                    input_attr="decrypted_live_id", output_attr="encrypted_live_id2") \
      .encrypted_id(mode="decrypt_photo_id", is_common_attr=True,
                    input_attr="encrypted_photo_id_list", output_attr="decrypted_photo_id_list") \
      .encrypted_id(mode="encrypt_photo_id", is_common_attr=True,
                    input_attr="decrypted_photo_id_list", output_attr="encrypted_photo_id_list2") \
      .encrypted_id(mode="decrypt_live_id", is_common_attr=True,
                    input_attr="encrypted_live_id_list", output_attr="decrypted_live_id_list") \
      .encrypted_id(mode="encrypt_live_id", is_common_attr=True,
                    input_attr="decrypted_live_id_list", output_attr="encrypted_live_id_list2") \
      .encrypted_id(mode="decrypt_id", is_common_attr=True,
                    input_attr="encrypted_mix_id1", output_attr="decrypted_mix_id1") \
      .encrypted_id(mode="decrypt_id", is_common_attr=True,
                    input_attr="encrypted_mix_id2", output_attr="decrypted_mix_id2") \
      .encrypted_id(mode="decrypt_id", is_common_attr=True,
                    input_attr="encrypted_mix_id_list", output_attr="decrypted_mix_id_list")

    leaf = self.__init_service(flow)

    leaf["encrypted_photo_id"] = "5219672038057953952"
    leaf["encrypted_live_id"] = "LwbKeucqSnA"
    leaf["encrypted_photo_id_list"] = ["5219672038057953952", "5221079413377787863"]
    leaf["encrypted_live_id_list"] = ["LwbKeucqSnA", "-8X6x84adeI"]
    leaf["encrypted_mix_id1"] = "5219672038057953952"
    leaf["encrypted_mix_id2"] = "LwbKeucqSnA"
    leaf["encrypted_mix_id_list"] = ["5219672038057953952", "5221079413377787863", "LwbKeucqSnA", "-8X6x84adeI"]

    leaf.run("test_encrypted_id_for_common_attr")

    self.assertEqual(leaf["decrypted_photo_id"], 69605266109)
    self.assertEqual(leaf["encrypted_photo_id2"], 5219672038057953952)
    self.assertEqual(leaf["decrypted_live_id"], 9274845166)
    self.assertEqual(leaf["encrypted_live_id2"], "LwbKeucqSnA")
    self.assertEqual(leaf["decrypted_photo_id_list"], [69605266109, 69768587368])
    self.assertEqual(leaf["encrypted_photo_id_list2"], [5219672038057953952, 5221079413377787863])
    self.assertEqual(leaf["decrypted_live_id_list"], [9274845166, 9277967843])
    self.assertEqual(leaf["encrypted_live_id_list2"], ["LwbKeucqSnA", "-8X6x84adeI"])
    self.assertEqual(leaf["decrypted_mix_id1"], 69605266109)
    self.assertEqual(leaf["decrypted_mix_id2"], 9274845166)
    self.assertEqual(leaf["decrypted_mix_id_list"], [69605266109, 69768587368, 9274845166, 9277967843])

  def test_encrypted_id_for_item_attr(self):
    flow = LeafFlow(name="test_encrypted_id_for_item_attr") \
      .encrypted_id(mode="decrypt_photo_id",
                    input_attr="encrypted_photo_id", output_attr="decrypted_photo_id") \
      .encrypted_id(mode="encrypt_photo_id",
                    input_attr="decrypted_photo_id", output_attr="encrypted_photo_id2") \
      .encrypted_id(mode="decrypt_live_id",
                    input_attr="encrypted_live_id", output_attr="decrypted_live_id") \
      .encrypted_id(mode="encrypt_live_id",
                    input_attr="decrypted_live_id", output_attr="encrypted_live_id2") \
      .encrypted_id(mode="decrypt_photo_id",
                    input_attr="encrypted_photo_id_list", output_attr="decrypted_photo_id_list") \
      .encrypted_id(mode="encrypt_photo_id",
                    input_attr="decrypted_photo_id_list", output_attr="encrypted_photo_id_list2") \
      .encrypted_id(mode="decrypt_live_id",
                    input_attr="encrypted_live_id_list", output_attr="decrypted_live_id_list") \
      .encrypted_id(mode="encrypt_live_id",
                    input_attr="decrypted_live_id_list", output_attr="encrypted_live_id_list2") \
      .encrypted_id(mode="decrypt_id",
                    input_attr="encrypted_mix_id1", output_attr="decrypted_mix_id1") \
      .encrypted_id(mode="decrypt_id",
                    input_attr="encrypted_mix_id2", output_attr="decrypted_mix_id2") \
      .encrypted_id(mode="decrypt_id",
                    input_attr="encrypted_mix_id_list", output_attr="decrypted_mix_id_list")

    leaf = self.__init_service(flow)

    item = leaf.add_item(0)

    item["encrypted_photo_id"] = "5219672038057953952"
    item["encrypted_live_id"] = "LwbKeucqSnA"
    item["encrypted_photo_id_list"] = ["5219672038057953952", "5221079413377787863"]
    item["encrypted_live_id_list"] = ["LwbKeucqSnA", "-8X6x84adeI"]
    item["encrypted_mix_id1"] = "5219672038057953952"
    item["encrypted_mix_id2"] = "LwbKeucqSnA"
    item["encrypted_mix_id_list"] = ["5219672038057953952", "5221079413377787863", "LwbKeucqSnA", "-8X6x84adeI"]

    leaf.run("test_encrypted_id_for_item_attr")

    self.assertEqual(item["decrypted_photo_id"], 69605266109)
    self.assertEqual(item["encrypted_photo_id2"], 5219672038057953952)
    self.assertEqual(item["decrypted_live_id"], 9274845166)
    self.assertEqual(item["encrypted_live_id2"], "LwbKeucqSnA")
    self.assertEqual(item["decrypted_photo_id_list"], [69605266109, 69768587368])
    self.assertEqual(item["encrypted_photo_id_list2"], [5219672038057953952, 5221079413377787863])
    self.assertEqual(item["decrypted_live_id_list"], [9274845166, 9277967843])
    self.assertEqual(item["encrypted_live_id_list2"], ["LwbKeucqSnA", "-8X6x84adeI"])
    self.assertEqual(item["decrypted_mix_id1"], 69605266109)
    self.assertEqual(item["decrypted_mix_id2"], 9274845166)
    self.assertEqual(item["decrypted_mix_id_list"], [69605266109, 69768587368, 9274845166, 9277967843])

  def test_select_list_values_for_common(self):
    flow = LeafFlow(name="test_select_list_values_for_common") \
      .select_list_values(
        index_attr = "index",
        list_values = [
          {"from": "int_list", "to": "sub_int_list"},
          {"from": "float_list", "to": "sub_float_list"},
          {"from": "str_list", "to": "sub_str_list"},
        ],
        is_common_attr=True) \
     .select_list_values(
        index_attr = "single_index",
        list_values = [
          {"from": "int_list", "to": "sub_int"},
          {"from": "float_list", "to": "sub_float"},
          {"from": "str_list", "to": "sub_str"},
        ],
        is_common_attr=True)\
     .select_list_values(
        index_attr = "range_index",
        list_values = [
          {"from": "int_list", "to": "sub_int_range"},
          {"from": "float_list", "to": "sub_float_range"},
          {"from": "str_list", "to": "sub_str_range"},
        ],
        is_common_attr=True,
        is_range=True)

    leaf = self.__init_service(flow)

    leaf["index"] = [0, 3, 2, 1]
    leaf["single_index"] = 1
    leaf["int_list"] = [1, 2, 3, 4, 5]
    leaf["float_list"] = [1.1, 2.2, 3.3, 4.4, 5.5]
    leaf["str_list"] = ["alpha", "beta", "gamma", "blabla", "eta"]
    leaf["range_index"] = [1, 3]

    leaf.run("test_select_list_values_for_common")

    self.assertEqual(leaf["sub_int_list"], [1, 4, 3, 2])
    self.assertEqual(leaf["sub_float_list"], [1.1, 4.4, 3.3, 2.2])
    self.assertEqual(leaf["sub_str_list"], ["alpha", "blabla", "gamma", "beta"])

    self.assertEqual(leaf["sub_int"], 2)
    self.assertEqual(leaf["sub_float"], 2.2)
    self.assertEqual(leaf["sub_str"], "beta")

    self.assertEqual(leaf["sub_int_range"], [2, 3])
    self.assertEqual(leaf["sub_float_range"], [2.2, 3.3])
    self.assertEqual(leaf["sub_str_range"], ["beta", "gamma"])

  def test_select_list_values_for_item(self):
    flow = LeafFlow(name="test_select_list_values_for_item") \
      .select_list_values(
        index_attr = "index",
        list_values = [
          {"from": "int_list", "to": "sub_int_list"},
          {"from": "float_list", "to": "sub_float_list"},
          {"from": "str_list", "to": "sub_str_list"},
        ],
        is_common_attr=False) \
      .select_list_values(
        index_attr = "single_index",
        list_values = [
          {"from": "int_list", "to": "sub_int"},
          {"from": "float_list", "to": "sub_float"},
          {"from": "str_list", "to": "sub_str"},
        ],
        is_common_attr=False)

    leaf = self.__init_service(flow)

    item = leaf.add_item(0)

    item["index"] = [0, 3, 2, 1]
    item["single_index"] = 1
    item["int_list"] = [1, 2, 3, 4, 5]
    item["float_list"] = [1.1, 2.2, 3.3, 4.4, 5.5]
    item["str_list"] = ["alpha", "beta", "gamma", "blabla", "eta"]

    leaf.run("test_select_list_values_for_item")

    self.assertEqual(item["sub_int_list"], [1, 4, 3, 2])
    self.assertEqual(item["sub_float_list"], [1.1, 4.4, 3.3, 2.2])
    self.assertEqual(item["sub_str_list"], ["alpha", "blabla", "gamma", "beta"])

    self.assertEqual(item["sub_int"], 2)
    self.assertEqual(item["sub_float"], 2.2)
    self.assertEqual(item["sub_str"], "beta")

  def test_split_string(self):
    flow = LeafFlow(name="test_split_string") \
      .split_string(
        input_common_attr="input_common",
        output_common_attr="output_common",
        delimiters="",
      )\
      .split_string(
        input_item_attr="input_item",
        output_item_attr="output_item",
        delimiters="",
        parse_to_int=True,
      ) \
      .split_string(
        input_common_attr="input_common2",
        output_common_attr="output_common2",
        delimiters=",",
      )\
      .split_string(
        input_item_attr="input_item2",
        output_item_attr="output_item2",
        delimiters=" ",
        parse_to_double=True,
      ) \
      .split_string(
        input_common_attr="input_common3",
        output_common_attr="output_common3",
        delimiters=",",
        skip_empty_tokens=True,
        trim_spaces=True,
        strip_whitespaces=True,
      )

    leaf = self.__init_service(flow)

    leaf["input_common"] = "中文1c"
    leaf["input_common2"] = "中,文,1c"
    leaf["input_common3"] = " aa, bb, ,,  ,  cc "
    item1=leaf.add_item(1)
    item1["input_item"] = "按char分割后存为int 1"
    item1["input_item2"] = "0.1 0.2 1 "
    item2=leaf.add_item(2)
    item2["input_item"] = "123"
    item2["input_item2"] = "123"


    leaf.run("test_split_string")
    self.assertEqual(leaf["output_common"], ['中','文','1','c'])
    self.assertEqual(leaf["output_common2"], ['中','文','1c'])
    self.assertEqual(leaf["output_common3"], ['aa','bb','cc'])

    for item in leaf.items:
      if item.item_key == 1:
        self.assertEqual(item["output_item"], [1])
        self.assertEqual(item["output_item2"], [0.1, 0.2, 1])
      if item.item_key == 2:
        self.assertEqual(item["output_item"], [1,2,3])
        self.assertEqual(item["output_item2"], [123])


  def test_copy_attr(self):
    flow = LeafFlow(name="test_copy_attr") \
      .copy_attr(
        attrs=[
          {"from_common": "common_input", "to_common": "common_to_common"},
          {"from_common": "common_input2", "to_item": "common_to_item"},
          {"from_item": "item_input", "to_common": "item_to_common"},
          {"from_item": "item_input2", "to_item": "item_to_item"},
        ]
      ) 

    leaf = self.__init_service(flow)
    leaf["common_input"] = 111
    leaf["common_input2"] = ["a", "b", "c"]

    item1 = leaf.add_item(1)
    item2 = leaf.add_item(2)
    item3 = leaf.add_item(3)

    item1['item_input'] = 0.1
    item2['item_input'] = 0.2
    item3['item_input'] = 0.3

    item1['item_input2'] = [0.1, 0.2, 0.3]
    item2['item_input2'] = [0.4, 0.5, 0.6]
    item3['item_input2'] = [0.1, 0.1, -0.1]

    leaf.run("test_copy_attr")

    self.assertEqual(leaf["common_to_common"], 111)

    self.assertEqual(item1["common_to_item"], ["a", "b", "c"])
    self.assertEqual(item2["common_to_item"], ["a", "b", "c"])
    self.assertEqual(item3["common_to_item"], ["a", "b", "c"])

    self.assertEqual(leaf["item_to_common"], 0.1)

    self.assertEqual(item1["item_to_item"], [0.1, 0.2, 0.3])
    self.assertEqual(item2["item_to_item"], [0.4, 0.5, 0.6])
    self.assertEqual(item3["item_to_item"], [0.1, 0.1, -0.1])

  def test_copy_attr_with_overwrite(self):
    flow = LeafFlow(name="test_copy_attr_with_overwrite") \
      .copy_attr(
        attrs=[
          {"from_common": "common_input", "to_common": "common_to_common"},
          {"from_common": "common_input_fallback", "to_common": "common_to_common", "overwrite": False},
          {"from_common": "common_input2", "to_item": "common_to_item"},
          {"from_common": "common_input2_fallback", "to_item": "common_to_item", "overwrite": False},
          {"from_item": "item_input", "to_common": "item_to_common"},
          {"from_item": "item_input_fallback", "to_common": "item_to_common", "overwrite": False},
          {"from_item": "item_input2", "to_item": "item_to_item"},
          {"from_item": "item_input2_fallback", "to_item": "item_to_item", "overwrite": False},
          {"from_item": "item_input3", "to_item": "item_to_item3"},
          {"from_item": "item_input3_fallback", "to_item": "item_to_item3", "overwrite": False}
        ]
      )

    leaf = self.__init_service(flow)
    leaf["common_input"] = 111
    leaf["common_input_fallback"] = 222

    leaf["common_input2"] = ["a", "b", "c"]
    leaf["common_input2_fallback"] = ["abc"]

    item1 = leaf.add_item(1)
    item2 = leaf.add_item(2)
    item3 = leaf.add_item(3)

    item1['item_input'] = 0.1
    item1['item_input_fallback'] = 0.11
    item2['item_input'] = 0.2
    item2['item_input_fallback'] = 0.22
    item3['item_input'] = 0.3
    item3['item_input_fallback'] = 0.33

    item1['item_input2'] = [0.1, 0.2, 0.3]
    item2['item_input2_fallback'] = [0.456]
    item3['item_input2'] = [0.7, 0.8, 0.9]
    item3['item_input2_fallback'] = [0.789]

    item1['item_input3'] = 1
    item2['item_input3_fallback'] = 2
    item3['item_input3'] = 3
    item3['item_input3_fallback'] = 4

    leaf.run("test_copy_attr_with_overwrite")

    self.assertEqual(leaf["common_to_common"], 111)

    self.assertEqual(item1["common_to_item"], ["a", "b", "c"])
    self.assertEqual(item2["common_to_item"], ["a", "b", "c"])
    self.assertEqual(item3["common_to_item"], ["a", "b", "c"])

    self.assertEqual(leaf["item_to_common"], 0.1)

    self.assertEqual(item1["item_to_item"], [0.1, 0.2, 0.3])
    self.assertEqual(item2["item_to_item"], [0.456])
    self.assertEqual(item3["item_to_item"], [0.7, 0.8, 0.9])

    self.assertEqual(item1["item_to_item3"], 1)
    self.assertEqual(item2["item_to_item3"], 2)
    self.assertEqual(item3["item_to_item3"], 3)

  def test_delegate_retrieve(self):
    rpc_mocker = self.__service.rpc_mocker()

    # data for mock response
    item_id = [10, 11, 12, 13, 14, 15, 16, 17]
    item_type = [0, 2, 0, 0, 1, 1, 1, 2]

    int_list = [2, 3, 132, 4, 1, 23, 34, 4]
    double_list = [0.1, 23, 0.199999, 0.234, 0.56, 0.56, 4.23, -5.4]
    string_list = ["plastic", "poet", "back", "cake", "reader", "hospital", "atomic", "tall"]

    packed_int_list = [[1, 3, 4], [67], [], [2, 3], [5],[-3], [], [789]]
    packed_double_list = [[0.56], [1, 3, 4], [], [0.56], [], [657], [4.0, 3, 21], [789.999]]
    packed_string_list = [["call"], ["boat", "sea", "paid"], ["factor"], ["weak"], \
                          ["swung"], ["ourselves", "blue"], ["about"], ["dropped"]]

    # construct mock response
    resp = dict(
      item = [],
      common_attr = [],
      item_attr = {}
    )
    for i in range(len(item_id)):
      item_attr = dict(
        type = "FLOAT_ATTR",
        name = encode_bytes(str2bytes('ctr')),
        floatValue = double_list[i],
      )
      item = dict(
        item_id = item_id[i],
        item_type = item_type[i],
        item_attr = [item_attr],
      )
      resp["item"].append(item)

    common_attr = dict(
      type = "INT_ATTR",
      name = encode_bytes(str2bytes('int_common_attr')),
      int_value = 1
    )
    resp["common_attr"].append(common_attr)

    item_attr = dict(
      item_keys = [],
      attr_values = []
    )
    for i in range(len(item_id)):
      item_key = gen_key_sign(item_type[i], item_id[i])
      item_attr["item_keys"].append(item_key)

    int_item_attr = dict(
      name = "int_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in int_list)),
      value_type = "INT64"
    )
    item_attr["attr_values"].append(int_item_attr)

    double_item_attr = dict(
      name = "double_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in double_list)),
      value_type = "FLOAT64"
    )
    item_attr["attr_values"].append(double_item_attr)

    string_item_attr = dict(
      name = "string_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in string_list)),
      value_type = "STRING",
      value_length = [7,4,4,4,6,8,6,4]
    )
    item_attr["attr_values"].append(string_item_attr)

    int_list_item_attr = dict(
      name = "int_list_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in list(chain.from_iterable(packed_int_list)))),
      value_type = "INT64_LIST",
      value_length = [3,1,0,2,1,1,0,1]
    )
    item_attr["attr_values"].append(int_list_item_attr)

    double_list_item_attr = dict(
      name = "double_list_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in list(chain.from_iterable(packed_double_list)))),
      value_type = "FLOAT64_LIST",
      value_length = [1,3,0,1,0,1,3,1]
    )
    item_attr["attr_values"].append(double_list_item_attr)

    string_list_item_attr = dict(
      name = "string_list_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in list(chain.from_iterable(packed_string_list)))),
      value_type = "STRING_LIST",
      value_length = [1,4,3,4,3,4,1,6,1,4,1,5,2,9,4,1,5,1,7]
    )
    item_attr["attr_values"].append(string_list_item_attr)

    resp["item_attr"] = item_attr
    
    rpc_mocker.mock_rpc_response(service_name="grpc_DelegateRetrieveTest", response_json_str=json.dumps(resp))
    
    flow = LeafFlow(name="test_delegate_retrieve") \
      .delegate_retrieve(
        kess_service = "grpc_DelegateRetrieveTest",
        recv_common_attrs = ["int_common_attr"],
        recv_item_attrs = ["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"],
        use_packed_item_attr = True,
      )
    leaf = self.__init_service(flow)
    leaf.run("test_delegate_retrieve")
    self.assertEqual(leaf["int_common_attr"], 1)

    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, item_id[index])
      self.assertEqual(item.item_type, item_type[index])
      self.assertAlmostEqual(item["ctr"], double_list[index],6)
      self.assertEqual(item["int_item_attr"], int_list[index])
      self.assertEqual(item["double_item_attr"], double_list[index])
      self.assertEqual(item["string_item_attr"], string_list[index])
      self.assertListEqual(item["int_list_item_attr"], packed_int_list[index])
      self.assertListEqual(item["double_list_item_attr"], packed_double_list[index])
      self.assertListEqual(item["string_list_item_attr"], packed_string_list[index])

  def test_set_attr_default_value(self):
    flow = LeafFlow(name="test_set_attr_default_value") \
      .set_attr_default_value(
        item_attrs=[
          {"name": "int_attr", "type": "int","value": 1, "clear_existing_value": True},
          {"name": "double_attr", "type": "double","value": 0.3},
          {"name": "string_attr", "type": "string","value": "", "clear_existing_value": True},
          {"name": "int_list_attr", "type": "int_list","value": [3,4,5]},
          {"name": "double_list_attr", "type": "double_list","value": [0.3,4,5]},
          {"name": "string_list_attr", "type": "string_list","value": ["cat", "dog"]},
          {"name": "dynamic_int_attr", "type": "int","value": "{{test_int_attr}}"},
          {"name": "dynamic_double_attr", "type": "double","value": "{{test_float_attr}}"},
          {"name": "dynamic_string_attr", "type": "string","value": "{{test_string_attr}}"},
          {"name": "dynamic_int_list_attr", "type": "int_list","value": "{{test_int_list_attr}}"},
          {"name": "dynamic_double_list_attr", "type": "double_list","value": "{{test_float_list_attr}}"},
          {"name": "dynamic_string_list_attr", "type": "string_list","value": "{{test_string_list_attr}}"},
          {"name": "dynamic_empty_int_attr", "type": "int","value": "{{test_empty_int_attr}}"},
          {"name": "dynamic_empty_double_attr", "type": "double","value": "{{test_empty_float_attr}}"},
          {"name": "dynamic_empty_string_attr", "type": "string","value": "{{test_empty_string_attr}}"},
          {"name": "dynamic_empty_int_list_attr", "type": "int_list","value": "{{test_empty_int_list_attr}}"},
          {"name": "dynamic_empty_double_list_attr", "type": "double_list","value": "{{test_empty_float_list_attr}}"},
          {"name": "dynamic_empty_string_list_attr", "type": "string_list","value": "{{test_empty_string_list_attr}}"},  
          {"name": "empty_dynamic_int_attr", "type": "int","value": "{{test_int_attr}}"},
          {"name": "empty_dynamic_double_attr", "type": "double","value": "{{test_float_attr}}"},
          {"name": "empty_dynamic_string_attr", "type": "string","value": "{{test_string_attr}}"},
          {"name": "empty_dynamic_int_list_attr", "type": "int_list","value": "{{test_int_list_attr}}"},
          {"name": "empty_dynamic_double_list_attr", "type": "double_list","value": "{{test_float_list_attr}}"},
          {"name": "empty_dynamic_string_list_attr", "type": "string_list","value": "{{test_string_list_attr}}"},    
        ]
      )\

    leaf = self.__init_service(flow)
    leaf["test_int_attr"] = 4
    leaf["test_float_attr"] = 3.4
    leaf["test_string_attr"] = "test_string_attr"
    leaf["test_int_list_attr"] = [4,4,5]
    leaf["test_float_list_attr"] = [4,4.5,4.2]
    leaf["test_string_list_attr"] = ["23","23","444"]
    item1 = leaf.add_item(1)
    item1["int_attr"] = 2
    item1["double_attr"] = 0.1
    item1["string_attr"] = "poet"
    item1["int_list_attr"] = [6, 6]
    item1["double_list_attr"] = [0.0, 0.0]
    item1["string_list_attr"] = [""]
    item1["dynamic_int_attr"] = 2
    item1["dynamic_double_attr"] = 0.1
    item1["dynamic_string_attr"] = "poet"
    item1["dynamic_int_list_attr"] = [6, 6]
    item1["dynamic_double_list_attr"] = [0.0, 0.0]
    item1["dynamic_string_list_attr"] = [""]
    item1["dynamic_empty_int_attr"] = 22
    item1["dynamic_empty_double_attr"] = 0.11
    item1["dynamic_empty_string_attr"] = "poet1"
    item1["dynamic_empty_int_list_attr"] = [6, 61]
    item1["dynamic_empty_double_list_attr"] = [0.0, 1.0, 1]
    item1["dynamic_empty_string_list_attr"] = ["123"]

    leaf.add_item(2)
    leaf.add_item(3)
    leaf.run("test_set_attr_default_value")
    for item in leaf.items:
      if item.item_key == 1:
        self.assertEqual(item["int_attr"], 1)
        self.assertEqual(item["double_attr"], 0.1)
        self.assertEqual(item["string_attr"], "")
        self.assertEqual(item["int_list_attr"], [6, 6])
        self.assertEqual(item["double_list_attr"], [0.0, 0.0])
        self.assertEqual(item["string_list_attr"], [""])
        self.assertEqual(item["dynamic_int_attr"], 2)
        self.assertEqual(item["dynamic_double_attr"], 0.1)
        self.assertEqual(item["dynamic_string_attr"], "poet")
        self.assertEqual(item["dynamic_int_list_attr"], [6, 6])
        self.assertEqual(item["dynamic_double_list_attr"], [0.0, 0.0])
        self.assertEqual(item["dynamic_string_list_attr"], [""])
        self.assertEqual(item["dynamic_empty_int_attr"], 22)
        self.assertEqual(item["dynamic_empty_double_attr"], 0.11)
        self.assertEqual(item["dynamic_empty_string_attr"], "poet1")
        self.assertEqual(item["dynamic_empty_int_list_attr"], [6, 61])
        self.assertEqual(item["dynamic_empty_double_list_attr"], [0.0, 1.0, 1])
        self.assertEqual(item["dynamic_empty_string_list_attr"], ["123"])
        self.assertEqual(item["empty_dynamic_int_attr"], 4)
        self.assertEqual(item["empty_dynamic_double_attr"], 3.4)
        self.assertEqual(item["empty_dynamic_string_attr"], "test_string_attr")
        self.assertEqual(item["empty_dynamic_int_list_attr"], [4,4,5])
        self.assertEqual(item["empty_dynamic_double_list_attr"], [4,4.5,4.2])
        self.assertEqual(item["empty_dynamic_string_list_attr"], ["23","23","444"])
      else:
        self.assertEqual(item["int_attr"], 1)
        self.assertEqual(item["double_attr"], 0.3)
        self.assertEqual(item["string_attr"], "")
        self.assertEqual(item["int_list_attr"], [3,4,5])
        self.assertEqual(item["double_list_attr"], [0.3,4,5])
        self.assertEqual(item["string_list_attr"], ["cat", "dog"])
        self.assertEqual(item["dynamic_int_attr"], 4)
        self.assertEqual(item["dynamic_double_attr"], 3.4)
        self.assertEqual(item["dynamic_string_attr"], "test_string_attr")
        self.assertEqual(item["dynamic_int_list_attr"], [4,4,5])
        self.assertEqual(item["dynamic_double_list_attr"], [4,4.5,4.2])
        self.assertEqual(item["dynamic_string_list_attr"], ["23","23","444"])
        self.assertEqual(item["dynamic_empty_int_attr"], None)
        self.assertEqual(item["dynamic_empty_double_attr"], None)
        self.assertEqual(item["dynamic_empty_string_attr"], None)
        self.assertEqual(item["dynamic_empty_int_list_attr"], None)
        self.assertEqual(item["dynamic_empty_double_list_attr"], None)
        self.assertEqual(item["dynamic_empty_string_list_attr"], None)
        self.assertEqual(item["empty_dynamic_int_attr"], 4)
        self.assertEqual(item["empty_dynamic_double_attr"], 3.4)
        self.assertEqual(item["empty_dynamic_string_attr"], "test_string_attr")
        self.assertEqual(item["empty_dynamic_int_list_attr"], [4,4,5])
        self.assertEqual(item["empty_dynamic_double_list_attr"], [4,4.5,4.2])
        self.assertEqual(item["empty_dynamic_string_list_attr"], ["23","23","444"])
  
  def test_retrieve_by_ann_embedding(self):
    pass

  def test_delegate_enrich(self):
    item_id = [10, 11, 12, 13, 14, 15, 16, 17]
    item_type = [0, 2, 0, 0, 1, 1, 1, 2]

    # data for mock response
    int_list = [4, 3, 132, 4, 1, 23, 34, 4]
    double_list = [0.3, 23, 0.199999, 0.234, 0.56, 0.56, 4.23, -5.4]
    string_list = ["rabbit", "poet", "back", "cake", "reader", "hospital", "atomic", "tall"]

    packed_int_list = [[-1, 3, 4], [67], [], [2, 3], [5],[-3], [], [789]]
    packed_double_list = [[0.66], [1, 3, 4], [], [0.56], [], [657], [4.0, 3, 21], [789.999]]
    packed_string_list = [["for"], ["boat", "sea", "paid"], ["factor"], ["weak"], \
                          ["swung"], ["ourselves", "blue"], ["about"], ["dropped"]]

    # construct mock response
    resp = dict(
      item = [],
      common_attr = [],
      item_attr = {}
    )
    for i in range(len(item_id)):
      item_attr = dict(
        type = "FLOAT_ATTR",
        name = encode_bytes(str2bytes('ctr')),
        floatValue = double_list[i],
      )
      item = dict(
        item_id = item_id[i],
        item_type = item_type[i],
        item_attr = [item_attr],
      )
      resp["item"].append(item)

    common_attr = dict(
      type = "INT_ATTR",
      name = encode_bytes(str2bytes('int_common_attr')),
      int_value = 1
    )
    resp["common_attr"].append(common_attr)

    item_attr = dict(
      item_keys = [],
      attr_values = []
    )
    for i in range(len(item_id)):
      item_key = gen_key_sign(item_type[i], item_id[i])
      item_attr["item_keys"].append(item_key)

    int_item_attr = dict(
      name = "int_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in int_list)),
      value_type = "INT64"
    )
    item_attr["attr_values"].append(int_item_attr)

    double_item_attr = dict(
      name = "double_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in double_list)),
      value_type = "FLOAT64"
    )
    item_attr["attr_values"].append(double_item_attr)

    string_item_attr = dict(
      name = "string_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in string_list)),
      value_type = "STRING",
      value_length = [6,4,4,4,6,8,6,4]
    )
    item_attr["attr_values"].append(string_item_attr)

    int_list_item_attr = dict(
      name = "int_list_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in list(chain.from_iterable(packed_int_list)))),
      value_type = "INT64_LIST",
      value_length = [3,1,0,2,1,1,0,1]
    )
    item_attr["attr_values"].append(int_list_item_attr)

    double_list_item_attr = dict(
      name = "double_list_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in list(chain.from_iterable(packed_double_list)))),
      value_type = "FLOAT64_LIST",
      value_length = [1,3,0,1,0,1,3,1]
    )
    item_attr["attr_values"].append(double_list_item_attr)

    string_list_item_attr = dict(
      name = "string_list_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in list(chain.from_iterable(packed_string_list)))),
      value_type = "STRING_LIST",
      value_length = [1,3,3,4,3,4,1,6,1,4,1,5,2,9,4,1,5,1,7]
    )
    item_attr["attr_values"].append(string_list_item_attr)

    resp["item_attr"] = item_attr
    
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_DelegateEnrichTest", response_json_str=json.dumps(resp))
    
    flow = LeafFlow(name="test_delegate_enrich") \
      .delegate_enrich(
        kess_service = "grpc_DelegateEnrichTest",
        recv_common_attrs = ["int_common_attr"],
        recv_item_attrs = ["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"],
        use_packed_item_attr = "{{dynamic_common_attr}}",
      )
    leaf = self.__init_service(flow)

    leaf["dynamic_common_attr"] = True
    for index, id in enumerate(item_id):
      leaf.add_item_by_type(item_type[index], id)

    leaf.run("test_delegate_enrich")
    self.assertEqual(leaf["int_common_attr"], 1)

    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, item_id[index])
      self.assertEqual(item.item_type, item_type[index])
      self.assertAlmostEqual(item["ctr"], double_list[index],6)
      self.assertEqual(item["int_item_attr"], int_list[index])
      self.assertEqual(item["double_item_attr"], double_list[index])
      self.assertEqual(item["string_item_attr"], string_list[index])
      self.assertListEqual(item["int_list_item_attr"], packed_int_list[index])
      self.assertListEqual(item["double_list_item_attr"], packed_double_list[index])
      self.assertListEqual(item["string_list_item_attr"], packed_string_list[index])

    flow = LeafFlow(name="test_delegate_enrich_item_tables") \
      .retrieve_by_common_attr(attr="item_in_table1", reason=1, item_table = "table1") \
      .retrieve_by_common_attr(attr="item_in_table2", reason=2, item_table = "table2") \
      .delegate_enrich(
        kess_service = "grpc_DelegateEnrichTest",
        recv_item_attrs = ["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"],
        use_packed_item_attr = True,
        item_from_tables = ["table1", "table2"],
      ) \
      .log_debug_info(for_debug_request_only=False,item_attrs=["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"], item_table="table2")
    leaf = self.__init_service(flow)
    leaf["item_in_table1"] = [10, 2<<56|11, 12]
    leaf["item_in_table2"] = [13, 1<<56|14, 1<<56|15, 1<<56|16, 2<<56|17]

    leaf.run("test_delegate_enrich_item_tables") # ctr 为 kuiba 类型，暂时不支持

  @unittest.skip("to be fix")
  def test_enrich_by_generic_grpc(self):
    # TODO（caohongjin）：后面补充单测
    pass

  def test_enrich_attr_by_light_function(self):
    flow = LeafFlow(name = "test_enrich_attr_by_light_function") \
      .enrich_attr_by_light_function(
        import_common_attr = [
          { "name": "common_attr1", "as": "cascade_prerank_pctr_weight" },
          { "name": "common_attr2", "as": "cascade_prerank_pltr_weight" },
        ],
        import_item_attr = [
          { "name": "item_attr1", "as": "cascade_prerank_pctr" },
          { "name": "item_attr2", "as": "cascade_prerank_pltr" },
        ],
        export_item_attr = [
          { "name": "cascade_prerank_score", "as": "item_attr_output" },
        ],
        function_name = "CalPreRankScore",
        class_name = "ExploreLightFunctionSetV2",
      )

    leaf = self.__init_service(flow)
    leaf["common_attr1"] = 2.0
    leaf["common_attr2"] = 1.0

    item = leaf.add_item(0)
    item["item_attr1"] = 2.0
    item["item_attr2"] = 2.0

    leaf.run("test_enrich_attr_by_light_function")

    self.assertEqual(item["item_attr_output"], 6.0)

  def test_normalize_attr(self):
    flow = LeafFlow(name = "test_normalize_attr") \
      .normalize_attr(
        input_attr="score",
        output_attr="score_scaled",
        mode="min_max_scale",
        default_val=0.0,
        eps=0.000001
      )

    leaf = self.__init_service(flow)
    item_ids = [10, 11, 12, 13, 14, 15, 16]
    scores = [0.95, 0.93, 0.92, 0.82, 0.85, 0.3, 0.2]
    scores_scaled = [1.0, 0.9733333333333333, 0.96, 0.8266666666666665, 0.8666666666666666, 0.1333333333333333, 0.0]
    for i in range(len(item_ids)):
      item = leaf.add_item(item_ids[i])  
      item["score"] = scores[i]

    leaf.run("test_normalize_attr")
    for index, item in enumerate(leaf.items):
      self.assertAlmostEqual(item["score_scaled"], scores_scaled[index])

  # 代码测试同 test_get_item_attr_by_distributed_common_index
  @unittest.skip("skip ut for distributed index")
  def test_get_item_attr_by_distributed_index(self):
    pass

  # 代码测试同 test_get_item_attr_by_distributed_common_index
  @unittest.skip("skip ut for distributed index")
  def test_get_merchant_item_attr_by_distributed_index(self):
    pass

  # 代码测试同 test_get_item_attr_by_distributed_common_index
  @unittest.skip("skip ut for distributed index")
  def test_get_item_attr_by_distributed_new_photo_info_index(self):
    pass

  # 代码测试同 test_get_item_attr_by_distributed_common_index
  @unittest.skip("skip ut for distributed index")
  def test_get_merchant_living_item_attr_by_distributed_index(self):
    pass

  # 代码测试同 test_get_item_attr_by_distributed_common_index
  @unittest.skip("skip ut for distributed index")
  def test_get_item_attr_by_distributed_kuiba_predict_item_index(self):
    pass

  @unittest.skip("skip ut for distributed flat index, cause bus error")
  def test_get_item_attr_by_distributed_common_index(self):
    rpc_mocker = self.__service.rpc_mocker()
    photo_info_response = '{"ret_code": 0,"photo_info": [{"item_id": "2089820330939868064","item_info": "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","item_version": "0"}]}'

    rpc_mocker.mock_rpc_response(service_name="grpc_merchantLive1ppDistributedIndex", method_name="/ks.reco.DistributedPhotoInfoService/GetPhotoInfo",
                                 response_json_str=json.dumps(json.loads(photo_info_response)))

    int_v = -1
    double_v = 0.6233766
    double_list_v = [0.67242366,0.6797486,0.7316964,0.7641584,0.77844745,0.7810599,0.8681659,0.95]
    int_list_v = [92212100,92218369,92203126,92213656,92221830,92218334,92219478,92200398]
    string_v = "Abu Zaby"
    string_list_v = ["YEM","LBY","PSE","QAT","SAU","KWT","IRQ","DZA","TUN","JOR","BHR","SYR","MRT","OMN","ARE","EGY","LBN","SDN","MAR"]
    attrs_config = [
      {"name": "p_op_class_id", "type": "int"},
      {"name": "p_ksib_mmu_photo_tag_all_id", "type": "int_list"},
      {"name": "emp_xtr_score_v2", "type": "float"},
      {"name": "p_photo_region", "type": "string"},
      {"name": "p_ksib_mmu_photo_tag_all_prob", "type": "float_list"},
      {"name": "a_me_allowed_country", "type": "string_list"}
    ]

    sub_flow = LeafFlow(name="distributed_index_subflow") \
      .retrieve_by_common_attr(attr="items", reason=999) \
      .get_item_attr_by_distributed_common_index(
      use_dynamic_photo_store=True,
      photo_store_kconf_key="reco.distributedIndex.merchant1ppPhotoStoreConfig",
      attrs=attrs_config,
      debug_log=True
    )

    # 测试 sub_flow 传出的能力
    flow2 = LeafFlow(name="test_get_item_attr_by_distributed_common_index") \
      .retrieve_by_sub_flow(sub_flow=sub_flow, pass_common_attrs=["items"], merge_item_attrs=[
      "p_op_class_id", "p_ksib_mmu_photo_tag_all_id", "emp_xtr_score_v2",
      "p_photo_region", "p_ksib_mmu_photo_tag_all_prob", "a_me_allowed_country"
    ], debug_log=True)

    leaf = self.__init_service(flow2)
    leaf["items"] = [2089820330939868064]
    leaf.run("test_get_item_attr_by_distributed_common_index")
    self.assertEqual(len(leaf.items), 1)
    for item in leaf.items:
      self.assertEqual(item["p_op_class_id"], int_v)
      self.assertEqual(item["p_ksib_mmu_photo_tag_all_id"], int_list_v)
      self.assertAlmostEqual(item["emp_xtr_score_v2"], double_v)
      self.assertEqual(item["p_photo_region"], string_v)
      self.assertEqual(item["a_me_allowed_country"], string_list_v)
      for index, value in enumerate(item["p_ksib_mmu_photo_tag_all_prob"]):
        self.assertAlmostEqual(value, double_list_v[index])

  @unittest.skip("skip ut for distributed flat index, cause bus error")
  def test_get_item_attr_by_distributed_flat_index(self):
    rpc_mocker = self.__service.rpc_mocker()
    photo_info_response = '{"ret_code": 0,"photo_info": [{"item_id": "2089820330939868064","item_info": "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","item_version": "0"}]}'


    rpc_mocker.mock_rpc_response(service_name="grpc_merchantLive1ppDistributedIndex", method_name="/ks.reco.DistributedPhotoInfoService/GetPhotoInfo",
                                 response_json_str=json.dumps(json.loads(photo_info_response)))

    int_v = -1
    double_v = 0.6233766
    double_list_v = [0.67242366,0.6797486,0.7316964,0.7641584,0.77844745,0.7810599,0.8681659,0.95]
    int_list_v = [92212100,92218369,92203126,92213656,92221830,92218334,92219478,92200398]
    string_v = "Abu Zaby"
    string_list_v = ["YEM","LBY","PSE","QAT","SAU","KWT","IRQ","DZA","TUN","JOR","BHR","SYR","MRT","OMN","ARE","EGY","LBN","SDN","MAR"]
    attrs_config = [
      {"name": "p_op_class_id", "type": "int"},
      {"name": "p_ksib_mmu_photo_tag_all_id", "type": "int_list"},
      {"name": "emp_xtr_score_v2", "type": "float"},
      {"name": "p_photo_region", "type": "string"},
      {"name": "p_ksib_mmu_photo_tag_all_prob", "type": "float_list"},
      {"name": "a_me_allowed_country", "type": "string_list"}
    ]

    # 测试基本类型数据的读取
    flow = LeafFlow(name="flat_index_test") \
      .get_item_attr_by_distributed_flat_index(
      photo_store_kconf_key="reco.distributedIndex.merchant1ppPhotoStoreConfig",
      attrs=attrs_config,
      debug_log=True
    ).log_debug_info(item_attrs=["aId"], for_debug_request_only=False)

    leaf = self.__init_service(flow)
    leaf.add_item(2089820330939868064)
    leaf.run("flat_index_test")
    self.assertEqual(len(leaf.items), 1)
    for item in leaf.items:
      self.assertEqual(item["p_op_class_id"], int_v)
      self.assertEqual(item["p_ksib_mmu_photo_tag_all_id"], int_list_v)
      self.assertAlmostEqual(item["emp_xtr_score_v2"], double_v)
      self.assertEqual(item["p_photo_region"], string_v)
      self.assertEqual(item["a_me_allowed_country"], string_list_v)
      for index, value in enumerate(item["p_ksib_mmu_photo_tag_all_prob"]):
        self.assertAlmostEqual(value, double_list_v[index])

    sub_flow = LeafFlow(name="flat_index_subflow") \
      .retrieve_by_common_attr(attr="items", reason=999) \
      .get_item_attr_by_distributed_flat_index(
      photo_store_kconf_key="reco.distributedIndex.merchant1ppPhotoStoreConfig",
      attrs=attrs_config,
      debug_log=True
    )

    # 测试 sub_flow 传出的能力
    flow2 = LeafFlow(name="flat_index_test2") \
      .retrieve_by_sub_flow(sub_flow=sub_flow, pass_common_attrs=["items"], merge_item_attrs=[
      "p_op_class_id", "p_ksib_mmu_photo_tag_all_id", "emp_xtr_score_v2",
      "p_photo_region", "p_ksib_mmu_photo_tag_all_prob", "a_me_allowed_country"
    ], debug_log=True)

    leaf = self.__init_service(flow2)
    leaf["items"] = [2089820330939868064]
    leaf.run("flat_index_test2")
    self.assertEqual(len(leaf.items), 1)
    for item in leaf.items:
      self.assertEqual(item["p_op_class_id"], int_v)
      self.assertEqual(item["p_ksib_mmu_photo_tag_all_id"], int_list_v)
      self.assertAlmostEqual(item["emp_xtr_score_v2"], double_v)
      self.assertEqual(item["p_photo_region"], string_v)
      self.assertEqual(item["a_me_allowed_country"], string_list_v)
      for index, value in enumerate(item["p_ksib_mmu_photo_tag_all_prob"]):
        self.assertAlmostEqual(value, double_list_v[index])


    sub_flow2 = LeafFlow(name="flat_index_subflow2") \
      .enrich_attr_by_lua(
      import_item_attr = ["p_op_class_id", "p_ksib_mmu_photo_tag_all_id", "emp_xtr_score_v2",
                          "p_photo_region", "p_ksib_mmu_photo_tag_all_prob", "a_me_allowed_country"],
      function_for_item = "calculate",
      export_item_attr = ["new_p_op_class_id", "new_p_ksib_mmu_photo_tag_all_id", "new_emp_xtr_score_v2",
                          "new_p_photo_region", "new_p_ksib_mmu_photo_tag_all_prob", "new_a_me_allowed_country"],
      lua_script = """
        function calculate(seq, item_key, reason, score)
          return p_op_class_id, p_ksib_mmu_photo_tag_all_id, emp_xtr_score_v2, p_photo_region, p_ksib_mmu_photo_tag_all_prob, a_me_allowed_country
        end
      """,
      debug_log=True
    )


    # 测试主 flow 向 sub_flow 传入的能力
    flow3 = LeafFlow(name="flat_index_test3") \
      .get_item_attr_by_distributed_flat_index(
      photo_store_kconf_key="reco.distributedIndex.merchant1ppPhotoStoreConfig",
      attrs=attrs_config,
      debug_log=True
    ) \
      .enrich_by_sub_flow(sub_flow=sub_flow2, pass_item_attrs=[
      "p_op_class_id", "p_ksib_mmu_photo_tag_all_id", "emp_xtr_score_v2",
      "p_photo_region", "p_ksib_mmu_photo_tag_all_prob", "a_me_allowed_country"
    ], merge_item_attrs=[
      "new_p_op_class_id", "new_p_ksib_mmu_photo_tag_all_id", "new_emp_xtr_score_v2",
      "new_p_photo_region", "new_p_ksib_mmu_photo_tag_all_prob", "new_a_me_allowed_country"
    ], debug_log=True)
    leaf = self.__init_service(flow3)
    leaf.add_item(2089820330939868064)
    leaf.run("flat_index_test3")
    self.assertEqual(len(leaf.items), 1)
    for item in leaf.items:
      self.assertEqual(item["new_p_op_class_id"], int_v)
      self.assertEqual(item["new_p_ksib_mmu_photo_tag_all_id"], int_list_v)
      self.assertAlmostEqual(item["new_emp_xtr_score_v2"], double_v)
      self.assertEqual(item["new_p_photo_region"], string_v)
      self.assertEqual(item["new_a_me_allowed_country"], string_list_v)
      for index, value in enumerate(item["new_p_ksib_mmu_photo_tag_all_prob"]):
        self.assertAlmostEqual(value, double_list_v[index])

  def test_retrieve_by_sub_flow(self):

    @async_retrieve()
    def sub_flow_a(flow):
        flow.retrieve_by_common_attr(attr="item_list_a", reason=1)
        flow.set_attr_value(
          item_attrs=[
            {"name": "sub_item", "type": "string", "value": "a"}
          ],
        )
        flow.set_attr_default_value(
          item_attrs=[
              {"name": "default_sub_int", "type": "int", "value": 17},
              {"name": "default_sub_double", "type": "double", "value": 3.14},
              {"name": "default_sub_string", "type": "string", "value": "aaa"},
              {"name": "default_sub_int_list",
               "type": "int_list", "value": [1, 2, 3, 4, 5]},
              {"name": "default_sub_double_list",
               "type": "double_list", "value": [3.14, 2.22, 315]},
              {"name": "default_sub_string_list",
               "type": "string_list", "value": ["abc", "def", "ggg"]}
          ],
        )

    merge_item_attrs=[{"name": "default_sub_concat_int", "copy_mode": "CONCAT"},
                      {"name": "default_sub_min_int", "copy_mode": "MIN"},
                      {"name": "default_sub_max_double", "copy_mode": "MAX"},
                      {"name": "default_sub_sum_double", "copy_mode": "SUM"},
                      "default_sub_int", "default_sub_double", "default_sub_string", "default_sub_int_list",
                      "default_sub_double_list", "default_sub_string_list", "sub_item"]
    @async_retrieve(deduplicate_results=True, merge_item_attrs=merge_item_attrs)
    def sub_flow_b(flow):
        flow.retrieve_by_common_attr(attr="item_list_b", reason=2)
        flow.set_attr_value(
          item_attrs=[
            {"name": "sub_item", "type": "string", "value": "b"}
          ],
        )
        flow.set_attr_default_value(
          item_attrs=[
              {"name": "default_sub_concat_int", "type": "int", "value": 11},
              {"name": "default_sub_min_int", "type": "int", "value": 5},
              {"name": "default_sub_max_double", "type": "double", "value": 5.5},
              {"name": "default_sub_sum_double", "type": "double", "value": 5.5},
              {"name": "default_sub_int", "type": "int", "value": 17},
              {"name": "default_sub_double", "type": "double", "value": 3.14},
              {"name": "default_sub_string", "type": "string", "value": "aaa"},
              {"name": "default_sub_int_list",
               "type": "int_list", "value": [1, 2, 3, 4, 5]},
              {"name": "default_sub_double_list",
               "type": "double_list", "value": [3.14, 2.22, 315]},
              {"name": "default_sub_string_list",
               "type": "string_list", "value": ["abc", "def", "ggg"]}
          ],
        )
    
    @async_retrieve(merge_and_overwrite=True, deduplicate_results=True, retrieve_num=1, merge_item_attrs=merge_item_attrs)
    def sub_flow_c(flow):
        flow.retrieve_by_common_attr(attr="item_list_c", reason=3)
        flow.set_attr_value(
          item_attrs=[
            {"name": "sub_item", "type": "string", "value": "c"}
          ],
        )
        flow.set_attr_default_value(
          item_attrs=[
              {"name": "default_sub_concat_int", "type": "int", "value": 22},
              {"name": "default_sub_min_int", "type": "int", "value": 22},
              {"name": "default_sub_max_double", "type": "double", "value": 22.7},
              {"name": "default_sub_sum_double", "type": "double", "value": 22.3},
              {"name": "default_sub_int", "type": "int", "value": 17},
              {"name": "default_sub_double", "type": "double", "value": 3.14},
              {"name": "default_sub_string", "type": "string", "value": "aaa"},
              {"name": "default_sub_int_list",
               "type": "int_list", "value": [1, 2, 3, 4, 5]},
              {"name": "default_sub_double_list",
               "type": "double_list", "value": [3.14, 2.22, 315]},
              {"name": "default_sub_string_list",
               "type": "string_list", "value": ["abc", "def", "ggg"]}
          ],
        )
        

    checklist = ["a", "a", "a", "a", "c", "c", "c", "c", "c"]
    reason_list = [1, 1, 1, 1, 2, 2, 2, 2, 3]
    default_sub_concat_int_list = [None, None, None, None, [11, 22], [11, 22], [11, 22], [11, 22], [22]]
    default_sub_min_int_list = [None, None, None, None, 5, 5, 5, 5, 22]
    default_sub_max_double_list = [None, None, None, None, 22.7, 22.7, 22.7, 22.7, 22.7]
    default_sub_sum_double_list = [None, None, None, None, 27.8, 27.8, 27.8, 27.8, 22.3]

    flow = LeafFlow(name="test_retrieve_by_sub_flow")
    sub_flow_a(flow)
    sub_flow_b(flow)
    sub_flow_c(flow)
    flow.set_attr_default_value(
          item_attrs=[
              {"name": "default_sub_int", "type": "int", "value": 0},
              {"name": "default_sub_double", "type": "double", "value": 0.0},
              {"name": "default_sub_string", "type": "string", "value": ""},
              {"name": "default_sub_int_list",
               "type": "int_list", "value": [0]},
              {"name": "default_sub_double_list",
               "type": "double_list", "value": [0.0]},
              {"name": "default_sub_string_list",
               "type": "string_list", "value": [""]}
          ],
    )
    flow.copy_item_meta_info(save_reason_to_attr="reason")
    flow.log_debug_info(
        item_attrs=["sub_item", "default_sub_string",
                    "default_sub_int", "default_sub_double", "default_sub_int_list", "default_sub_double_list", "default_sub_string_list",
                    "default_sub_concat_int", "default_sub_min_int", "default_sub_max_double", "default_sub_sum_double"],
        for_debug_request_only=False
    )

    leaf = self.__init_service(flow)
    leaf["item_list_a"] = [1, 2, 3, 4]
    leaf["item_list_b"] = [5, 6, 7, 8]
    leaf["item_list_c"] = [5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10]
    leaf.run("test_retrieve_by_sub_flow")
    self.assertEqual(len(leaf.items), 9)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["sub_item"], checklist[index])
      self.assertEqual(item["reason"], reason_list[index])
      self.assertEqual(item["_REASON_"], reason_list[index])
      self.assertEqual(item["default_sub_concat_int"], default_sub_concat_int_list[index])
      self.assertEqual(item["default_sub_min_int"], default_sub_min_int_list[index])
      self.assertEqual(item["default_sub_max_double"], default_sub_max_double_list[index])
      self.assertEqual(item["default_sub_sum_double"], default_sub_sum_double_list[index])
      self.assertEqual(item["default_sub_string"], "aaa")
      self.assertEqual(item["default_sub_int"], 17)
      self.assertEqual(item["default_sub_double"], 3.14)
      self.assertEqual(item["default_sub_int_list"], [1, 2, 3, 4, 5])
      self.assertEqual(item["default_sub_double_list"], [3.14, 2.22, 315])
      self.assertEqual(item["default_sub_string_list"], ["abc", "def", "ggg"])
    test_item = leaf.add_item(11)
    self.assertEqual(test_item["default_sub_string"], "")
    self.assertEqual(test_item["default_sub_int"], 0)
    self.assertEqual(test_item["default_sub_double"], 0.0)
    self.assertEqual(test_item["default_sub_int_list"], [0])
    self.assertEqual(test_item["default_sub_double_list"], [0.0])
    self.assertEqual(test_item["default_sub_string_list"], [""])

  def test_enrich_by_sub_flow(self):

    @async_enrich(target_reason=[1])
    def enrich_sub_flow_a(flow):
      flow.set_attr_value(
      item_attrs=[
        {"name": "sub_item", "type": "string", "value": "a"}
      ],
    )

    @async_enrich(target_reason=[2])
    def enrich_sub_flow_b(flow):
      flow.set_attr_value(
      item_attrs=[
        {"name": "sub_item", "type": "string", "value": "b"}
      ],
    )

    flow = LeafFlow(name="test_enrich_by_sub_flow")
    flow.retrieve_by_common_attr(attr="item_list_a", reason=1)
    flow.retrieve_by_common_attr(attr="item_list_b", reason=2)
    enrich_sub_flow_a(flow)
    enrich_sub_flow_b(flow)
    flow.log_debug_info(
      item_attrs=["sub_item"]
    )

    checklist = ["a", "a", "a", "a", "b", "b", "b", "b"]

    leaf = self.__init_service(flow)
    leaf["item_list_a"] = [1, 2, 3, 4]
    leaf["item_list_b"] = [5, 6, 7, 8]
    leaf.run("test_enrich_by_sub_flow")
    for index, item in enumerate(leaf.items):
      self.assertAlmostEqual(item["sub_item"], checklist[index])

  def test_truncate(self):
    flow = LeafFlow(name='test_truncate1')\
      .truncate(size_limit=3, backfill_to={"partner_flag": 1})
    leaf = self.__init_service(flow)
    partner_f = [0, 0, 0, 1, 0]
    for i in range(5):
      item = leaf.add_item(i)
      item["partner_flag"] = partner_f[i]
    leaf.run("test_truncate1")
    self.assertEqual(len(leaf.items), 4)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, index)

    flow = LeafFlow(name='test_truncate2')\
      .truncate(size_limit=200)
    leaf = self.__init_service(flow)
    for i in range(300):
      item = leaf.add_item(i)
    leaf.run("test_truncate2")
    self.assertEqual(len(leaf.items), 200)

    flow = LeafFlow(name='test_truncate3') \
      .truncate(size_limit=2,target_item={"flag": 1})
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i % 2 == 1:
        item["flag"] = 1
    item_list=[0,1,2,3,4,6,8]
    leaf.run("test_truncate3")
    for i,item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])

    flow = LeafFlow(name='test_truncate4') \
      .truncate(size_limit=2,target_item={"flag": [0,1,2,3,4,5,7,8]})
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i % 2 == 1:
        item["flag"] = i
    item_list=[0,1,2,3,4,6,8,9]
    leaf.run("test_truncate4")
    for i,item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])

  
  def test_enrich_norm_and_discrete(self):
    flow = LeafFlow(name="test_enrich_norm_and_discrete") \
      .enrich_norm_and_discrete(
        is_common_attr=False,
        input_attr='xtr',
        output_norm_attr='norm',
        output_discrete_attr="discrete",
        quantile_list=[0, 4, 6, 9]
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["xtr"] = i

    leaf.run("test_enrich_norm_and_discrete")

    attr_norm = [0.0, 0.0833, 0.1666, 0.25, 0.3333, 0.5, 0.6666, 0.7777, 0.8888, 1.0]
    attr_discrete = [0, 0, 0, 0, 1, 1, 2, 2, 2, 2]
    for index, item in enumerate(leaf.items):
      self.assertAlmostEqual(item['norm'], attr_norm[index], 3)
      self.assertEqual(item['discrete'], attr_discrete[index])

  def test_retrieve_by_common_attrs(self):
    flow = LeafFlow(name="test_retrieve_by_common_attrs") \
      .retrieve_by_common_attrs(
        attrs=[
          {"name": "like_list", "reason": 991, "num_limit": 2}, 
          {"name": "click_list", "reason": 992, "num_limit": 2}
        ],
        total_limit=3,
        exclude_items_in_attr="exclude_list",
      )
    leaf = self.__init_service(flow)

    leaf["like_list"] = [1, 2, 3, 4]
    leaf["click_list"] = [5, 6, 7, 8]
    leaf["exclude_list"] = [1]
    leaf.run("test_retrieve_by_common_attrs")

    item_key_list = [2, 3, 5]
    self.assertEqual(len(leaf.items), 3)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_key_list[index])

  def test_set_default_value(self):
    flow = LeafFlow(name="test_set_default_value") \
      .set_default_value(
        common_attrs=[
          {"name": "int_common","type": "int","value": 3},
          {"name": "int_common","type": "double","value": 6.6},
          {"name": "double_common","type": "double","value": 6.6},
          {"name": "string_common","type": "string","value": "pull"},
          {"name": "int_list_common","type": "int_list","value": [1, 1]},
          {"name": "double_list_common","type": "double_list","value": [-3, 0.2]},
          {"name": "string_list_common","type": "string_list","value": ["moon", "dream"]},
        ],
        item_attrs=[
          {"name": "int_item","type": "int","value": 1},
          {"name": "double_item","type": "double","value": 0.5},
          {"name": "string_item","type": "string","value": ""},
          {"name": "int_list_item","type": "int_list","value": [3,4,5]},
          {"name": "double_list_item","type": "double_list","value": [0.73,1.1,-2.3]},
          {"name": "string_list_item","type": "string_list","value": ["pop", "push"]},
        ]
      )
    leaf = self.__init_service(flow)
    leaf["int_common"] = 9
    item1 = leaf.add_item(1)
    item1["int_item"] = 2
    item1["double_item"] = 0.1
    item1["string_item"] = "poet"
    item1["int_list_item"] = [6, 6]
    item1["double_list_item"] = [0.0, 0.0]
    item1["string_list_item"] = [""]
    leaf.add_item(2)

    leaf.run("test_set_default_value")
    self.assertEqual(leaf["int_common"], 3)
    self.assertEqual(leaf["double_common"], 6.6)
    self.assertEqual(leaf["string_common"], "pull")
    self.assertEqual(leaf["int_list_common"], [1, 1])
    self.assertEqual(leaf["double_list_common"], [-3, 0.2])
    self.assertEqual(leaf["string_list_common"],["moon", "dream"])
    for item in leaf.items:
      self.assertEqual(item["int_item"], 1)
      self.assertEqual(item["double_item"], 0.5)
      self.assertEqual(item["string_item"], "")
      self.assertEqual(item["int_list_item"], [3,4,5])
      self.assertEqual(item["double_list_item"], [0.73,1.1,-2.3])
      self.assertEqual(item["string_list_item"], ["pop", "push"])

  def test_pack_common_attr(self):
    flow = LeafFlow(name="test_pack_common_attr") \
      .pack_common_attr(
        input_common_attrs = ["like_list", "click_list"],
        output_common_attr = "author_list",
        limit_num = 7,
      )
    leaf = self.__init_service(flow)
    leaf["like_list"] = [12, 13, 3, 1, 5]
    leaf["click_list"] = [2, 13]
    leaf.run("test_pack_common_attr")

    out_list = [12, 13, 3, 1, 5, 2, 13]
    self.assertEqual(leaf["author_list"], out_list)

  def test_sort(self):
    flow = LeafFlow(name="test_sort1") \
      .sort(score_from_attr="emp_score", stable_sort=True, desc=False)
    leaf = self.__init_service(flow)
    score_list = [1,4,5,76,5,65,7,45,2,-9]
    for i in range(10):
      item = leaf.add_item(i)
      item["emp_score"] = score_list[i]

    leaf.run("test_sort1")
    item_list = [9, 0, 8, 1, 2, 4, 6, 7, 5, 3]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])

    flow = LeafFlow(name="test_sort2") \
      .sort(score_from_attr="emp_score")
    leaf = self.__init_service(flow)
    score_list = [1,4,5,76,6,65,7,45,2,-9]
    for i in range(10):
      item = leaf.add_item(i)
      item["emp_score"] = score_list[i]

    leaf.run("test_sort2")
    item_list = [3, 5, 7, 6, 4, 2, 1, 8, 0, 9]
    item_score_list = [76, 65, 45, 7, 6, 5, 4, 2, 1, -9]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])
      self.assertEqual(item.score, item_score_list[i])
      self.assertEqual(item["_SCORE_"], item_score_list[i])
    
    flow = LeafFlow(name="test_sort3") \
      .sort(score_from_attr="emp_score", update_score=False)
    leaf = self.__init_service(flow)
    score_list = [1,4,5,76,6,65,7,45,2,-9]
    for i in range(10):
      item = leaf.add_item(i)
      item["emp_score"] = score_list[i]

    leaf.run("test_sort3")
    item_list = [3, 5, 7, 6, 4, 2, 1, 8, 0, 9]
    item_score_list = [76, 65, 45, 7, 6, 5, 4, 2, 1, -9]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])
      self.assertEqual(item.score, 0.0)
      self.assertEqual(item["_SCORE_"], item_score_list[i])
    
    flow = LeafFlow(name="test_sort_multi") \
      .sort(score_from_attr=["emp_score_1", "emp_score_2"])
    leaf = self.__init_service(flow)
    score_list_1 = [1,1,1,2,2,2,2,2,1,1]
    score_list_2 = [1,4,5,76,6,65,7,45,2,-9]
    for i in range(10):
      item = leaf.add_item(i)
      item["emp_score_1"] = score_list_1[i]
      item["emp_score_2"] = score_list_2[i]

    leaf.run("test_sort_multi")
    item_list = [3, 5, 7, 6, 4, 2, 1, 8, 0, 9]
    item_score_list = [2, 2, 2, 2, 2, 1, 1, 1, 1, 1]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])
      self.assertEqual(item.score, item_score_list[i])
      self.assertEqual(item["_SCORE_"], item_score_list[i])
    
    flow = LeafFlow(name="test_sort_multi_no_score") \
      .sort(score_from_attr=["emp_score_1", "emp_score_2"], update_score=False)
    leaf = self.__init_service(flow)
    score_list_1 = [1,1,1,2,2,2,2,2,1,1]
    score_list_2 = [1,4,5,76,6,65,7,45,2,-9]
    for i in range(10):
      item = leaf.add_item(i)
      item["emp_score_1"] = score_list_1[i]
      item["emp_score_2"] = score_list_2[i]

    leaf.run("test_sort_multi_no_score")
    item_list = [3, 5, 7, 6, 4, 2, 1, 8, 0, 9]
    item_score_list = [2, 2, 2, 2, 2, 1, 1, 1, 1, 1]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, item_list[i])
      self.assertEqual(item.score, 0.0)
      self.assertEqual(item["_SCORE_"], item_score_list[i])

  def test_gen_ensemble_seq_num(self):
    flow = LeafFlow(name="test_gen_ensemble_seq_num") \
      .gen_ensemble_seq_num(ensemble_attrs=["score1"], output_attr_postfix="_n", start_seq=1, stable_sort=True) \
      .gen_ensemble_seq_num(ensemble_attrs=["score2"], output_attr_postfix="_n", start_seq=1, stable_sort=True, desc=False)

    leaf = self.__init_service(flow)
    score1 = [1.7, 2.3, 3.0, 4.0, 8.0, 7.0, 6.6, 5.0]
    score1_list = [8, 7, 6, 5, 1, 2, 3, 4]
    score2 = [4.5, 3.3, 2.1, 1.0, 5.7, 6.0, 7.0, 8.0]
    score2_list = [4, 3, 2, 1, 5, 6, 7, 8]
    for i in range(8):
      item = leaf.add_item(i)
      item["score1"] = score1[i]
      item["score2"] = score2[i]

    leaf.run("test_gen_ensemble_seq_num")
    for i, item in enumerate(leaf.items):
      self.assertEqual(item["score1_n"], score1_list[i])
      self.assertEqual(item["score2_n"], score2_list[i])


  def test_transform_item_attr(self):
    flow = LeafFlow(name="test_transform_item_attr") \
      .transform_item_attr(
        mappings = [{
          "check_attr_name": "cluster_id",
          "check_attr_type": "int",
          "output_attr_name": "in_browsed_cluster",
          "output_attr_type": "int",
          "output_default_value": -1,
          "rules": [{
            "check_values": ["{{recent_browsed_cluster}}"],
            "output_value": 1,
          }]
        }]
      )
    leaf = self.__init_service(flow)
    leaf["recent_browsed_cluster"] = [i for i in range(98, 105)]
    for i in range(10):
      item = leaf.add_item(i)
      item["cluster_id"] = i + 100

    leaf.run("test_transform_item_attr")
    in_browsed_list = [1,1,1,1,1,-1,-1,-1,-1,-1]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item["in_browsed_cluster"], in_browsed_list[i])

  def test_dispatch_common_attr(self):
    flow = LeafFlow(name="test_dispatch_common_attr") \
      .dispatch_common_attr(
        from_common_attr = "common_list",
        to_item_attr = "item_attr",
      ) \
      .dispatch_common_attr(
        dispatch_config = [{
          "from_common_attr" : "common_list1",
          "to_item_attr" : "item_attr1",
        },{
          "from_common_attr" : "common_list2",
          "to_item_attr" : "item_attr2",
        }]
      ).dispatch_common_attr(
        from_common_attr = "common_list3",
        to_item_attr = "item_attr3",
        by_list_size = 1,
      ) \
      .dispatch_common_attr(
        dispatch_config = [{
          "from_common_attr" : "common_list4",
          "to_item_attr" : "item_attr4",
          "by_list_size" : 3,
        }]
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
    attr_list = [1,2,3,4,10,3,4,5,56,9]
    leaf["common_list"] = attr_list
    leaf["common_list1"] = attr_list
    leaf["common_list2"] = attr_list
    leaf["common_list3"] = attr_list
    leaf["common_list4"] = attr_list

    leaf.run("test_dispatch_common_attr")
    for i, item in enumerate(leaf.items):
      self.assertEqual(item["item_attr"], attr_list[i])
      self.assertEqual(item["item_attr1"], attr_list[i])
      self.assertEqual(item["item_attr2"], attr_list[i])
      self.assertListEqual(item["item_attr3"], [attr_list[i]])
      if i < math.ceil(len(attr_list) / 3):
        self.assertListEqual(item["item_attr4"], attr_list[i*3:(i+1)*3])
      else:
        self.assertIsNone(item["item_attr4"])

  def test_calc_weighted_sum(self):
    flow = LeafFlow(name="test_calc_weighted_sum") \
      .calc_weighted_sum(
        channels = [
          { "name": "pctr", "weight": 1 },
          { "name": "pltr", "weight": 0.5 },
          { "name": "pftr", "weight": 0.6 },
        ],
        output_item_attr = "final_score",
      )
    leaf = self.__init_service(flow)
    for i in range(5):
      item = leaf.add_item(i)
      item["pctr"] = i / 10
      item["pltr"] = (i + 1)/10
      item["pftr"] = (i + 2)/10
    leaf.run("test_calc_weighted_sum")
    final_list = [0.17, 0.38, 0.59, 0.8, 1.01]
    for index, item in enumerate(leaf.items):
      self.assertAlmostEqual(item["final_score"], final_list[index], 6)

  def test_get_common_attr_from_redis(self):
    redis_mocker = self.__service.redis_mocker()
    redis_mocker.mock_redis_response(cluster_names=["aaa","bbb"], responses=[['1', 'abc'],['from', 'bbb']])
    flow = LeafFlow(name="test_get_common_attr_from_redis") \
      .get_common_attr_from_redis(
        cluster_name = "aaa",
        redis_params = [
          {
            "redis_key": "not used",
            "output_attr_name": "output1",
            "redis_value_type": "string",
            "output_attr_type": "string",
          },
          {
            "redis_key": "not used",
            "output_attr_name": "output2",
            "redis_value_type": "string",
            "output_attr_type": "string",
          }
        ]
      ) \
      .get_common_attr_from_redis(
        cluster_name = "bbb",
        redis_params = [
          {
            "redis_key": "not used",
            "output_attr_name": "output3",
            "redis_value_type": "string",
            "output_attr_type": "string",
          },
          {
            "redis_key": "not used",
            "output_attr_name": "output4",
            "redis_value_type": "string",
            "output_attr_type": "string",
          }
        ]
      )
    leaf = self.__init_service(flow)
    
    leaf.run("test_get_common_attr_from_redis")
    self.assertEqual(leaf["output1"], '1')
    self.assertEqual(leaf["output2"], 'abc')
    self.assertEqual(leaf["output3"], 'from')
    self.assertEqual(leaf["output4"], 'bbb')


  def test_get_item_attr_from_redis(self):
    redis_mocker = self.__service.redis_mocker()
    redis_mocker.mock_redis_response(cluster_names=["aaa","bbb"], responses=[["testaaa"],["testbbb1", "testbbb2"]])
    flow = LeafFlow(name="test_get_item_attr_from_redis") \
      .get_item_attr_from_redis(
      cluster_name = "aaa",
      redis_key_from="redis_key",
      save_value_to="redis_value",
    ) \
      .get_item_attr_from_redis(
      cluster_name = "bbb",
      attrs_config=[{"key_attr": "key1", "value_attr": "value1"},
                    {"key_attr": "key2", "value_attr": "value2"}],
      debug_log=True
    )
    leaf = self.__init_service(flow)

    item = leaf.add_item(123)
    item["redis_key"] = "a"
    item["key1"] = "b"
    item["key2"] = "c"

    leaf.run("test_get_item_attr_from_redis")
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["redis_value"], 'testaaa')
      self.assertEqual(item["value1"], 'testbbb1')
      self.assertEqual(item["value2"], 'testbbb2')

  def test_retrieve_by_redis(self):
    redis_mocker = self.__service.redis_mocker()
    redis_mocker.mock_redis_response(cluster_names=["aaa"], responses=[["38680800976_0.99_cat,38670790701_0.38_dog,38650272587_0.33_pig"]])
    flow = LeafFlow(name="test_retrieve_by_redis") \
      .retrieve_by_redis(
      reason = 100,
      retrieve_num = 1000,
      cluster_name = "aaa",
      key_from_attr = "key",
      item_separator = ",",
      attr_separator = "_",
      append_src_key_to_attr = "append_src_key_to_attr",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )

    leaf = self.__init_service(flow)
    leaf["key"] = "abc"

    ctr=[0.99, 0.38, 0.33]
    tag=["cat", "dog", "pig"]
    leaf.run("test_retrieve_by_redis")
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["emp_ctr"], ctr[index])
      self.assertEqual(item["tag"], tag[index])
      self.assertEqual(item["append_src_key_to_attr"], ["abc"])

  def test_arrange_by_sub_flow(self):

    @parallel(partition_size=4)
    def sub_test(flow):
      flow.set_attr_value(
        item_attrs=[
          {"name": "sub_int", "type": "int", "value": 17},
          {"name": "sub_double", "type": "double", "value": 3.14},
          {"name": "sub_string", "type": "string", "value": "aaa"},
          {"name": "sub_int_list", "type": "int_list", "value": [1,2,3,4,5]},
          {"name": "sub_double_list", "type": "double_list", "value": [3.14, 2.22, 315]},
          {"name": "sub_string_list", "type": "string_list", "value": ["abc", "def", "ggg"]}
        ],
        common_attrs=[
          {"name": "sub_common_int", "type": "int", "value": 16},
        ]
      )

    flow = LeafFlow(name="test_arrange_by_sub_flow").retrieve_by_common_attr(attr="item_list", reason=1)
    sub_test(flow)
    flow.log_debug_info(
        item_attrs=["sub_string", "sub_int", "sub_double", "sub_int_list", "sub_double_list", "sub_string_list"],
      common_attrs=["sub_common_int"],
    )

    leaf = self.__init_service(flow)
    leaf["item_list"] = [1, 2, 3, 4, 5, 6, 7, 8]
    leaf.run("test_arrange_by_sub_flow")
    self.assertEqual(leaf["sub_common_int"], 16)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["sub_string"], "aaa")
      self.assertEqual(item["sub_int"], 17)
      self.assertEqual(item["sub_double"], 3.14)
      self.assertEqual(item["sub_int_list"], [1, 2, 3, 4, 5])
      self.assertEqual(item["sub_double_list"], [3.14, 2.22, 315])
      self.assertEqual(item["sub_string_list"], ["abc", "def", "ggg"])

    sub_flow = LeafFlow(name="sub_a")
    sub_flow.pack_item_attr(
      item_source = {
        "reco_results": True,
      },
      mappings = [{
        "aggregator": "concat",
        "from_item_attr": "aid",
        "to_common_attr": "tmp_common_attr",
      }]
    ) \
    .dispatch_common_attr(
      from_common_attr = "tmp_common_attr",
      to_item_attr = "out_item_attr",
    )

    main_flow = LeafFlow(name="main")
    main_flow.arrange_by_sub_flow(sub_flow=sub_flow, group_by="aid",pass_item_attrs=["aid"], merge_item_attrs=["out_item_attr"]) 

    leaf = self.__init_service(main_flow)
    for i in range(15):
      item = leaf.add_item(i)
      if i < 10:
        item["aid"] = 1 if i < 5 else 2
    
    leaf.run("main")
    for item in leaf.items:
      if item.item_key < 10:
        self.assertEqual(item["out_item_attr"], 1 if item.item_key < 5 else 2)
      else:
        self.assertEqual(item["out_item_attr"], None)

  def test_partition(self):
    flow = LeafFlow(name="test_partition") \
      .partition(
        target={
          "category": "food",
          "tag": [2, 4]
        }
      )

    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["category"] = "food" if i % 2 == 0 else "drink"
      item["tag"] = i % 5

    leaf.run("test_partition")
    expected_list = [2, 4, 0, 1, 3, 5, 6, 7, 8, 9]
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, expected_list[i])

  def test_perf(self):
    flow = LeafFlow(name="test_perf") \
      .enrich_attr_by_lua(
        import_common_attr = ["common1"],
        export_common_attr = ["common2"],
        import_item_attr = ["item1"],
        export_item_attr = ["item2"],
        function_for_common = "calculate",
        function_for_item = "calc",
        lua_script = '''
          function calculate()
            return common1
          end
          function calc()
            return item1
          end
        ''', 
        debug_log=True,
      )\
      .delegate_retrieve(
        kess_service = "grpc_DelegateRetrieveTest",
        recv_common_attrs = ["int_common_attr"],
        recv_item_attrs = ["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"],
        use_packed_item_attr = True,
        debug_log=True,
      )

    leaf = self.__init_service(flow)
    leaf["common1"] = "aaa"
    item1 = leaf.add_item(1)
    item1["item1"] = "111"
    item_key_list = [2,3,4,5,6]
    for item_key in item_key_list:
      leaf.add_item(item_key)
    leaf.run("test_perf")
    self.assertEqual(leaf["common2"], "aaa")

  @unittest.skip("to be fix")
  def test_get_remote_embedding_lite_v2(self):
    flow = LeafFlow(name="test_get_remote_embedding_raw_lite") \
      .get_remote_embedding_lite_v2(
        kess_service='grpc_itemid_20w_clusterid',
        shard_num=1,
        id_converter={"type_name": "plainIdConverter"},
        query_source_type='item_attr',
        input_attr_name='goods_id',
        output_attr_name='mmu_vr_clusterid',
        is_raw_data=True,
        raw_data_type='uint32',
        size=1
      )

    leaf = self.__init_service(flow)
    goods_ids = [3759842681075,3760461806167,3758605000824,3757986579397,3759581975458,]
    for i in range(1):
      item = leaf.add_item(i)
      item["goods_id"] = goods_ids

    leaf.run("test_get_remote_embedding_raw_lite")
    #TODO(qianlei): 补充实际单测逻辑

  def test_pack_multi_item_attrs(self, **kwargs):
    # mock running data
    attr_num = 5
    item_num = 10

    int_attr_names = [f"int_attr{aidx}" for aidx in range(attr_num)]
    int_attr_values = list()
    for iidx in range(item_num):
      values = [iidx * 10 + aidx  for aidx in range(attr_num)]
      int_attr_values.append(values)

    float_attr_names = [f"float_attr{aidx}" for aidx in range(attr_num)]
    float_attr_values = list()
    for iidx in range(item_num):
      values = [iidx * 10.0 + aidx * 1.1  for aidx in range(attr_num)]
      float_attr_values.append(values)

    string_attr_names = [f"string_attr{aidx}" for aidx in range(attr_num)]
    string_attr_values = list()
    for iidx in range(item_num):
      values = [f"s{iidx * 10 + aidx}"  for aidx in range(attr_num)]
      string_attr_values.append(values)

    # cases
    cases_int_rowmajor=list()
    for iidx in range(item_num):
      for aidx in range(attr_num): cases_int_rowmajor.append(int_attr_values[iidx][aidx])
    cases_int_colmajor=list()
    for aidx in range(attr_num):
      for iidx in range(item_num): cases_int_colmajor.append(int_attr_values[iidx][aidx])

    cases_float_rowmajor=list()
    for iidx in range(item_num):
      for aidx in range(attr_num): cases_float_rowmajor.append(float_attr_values[iidx][aidx])
    cases_float_colmajor=list()
    for aidx in range(attr_num):
      for iidx in range(item_num): cases_float_colmajor.append(float_attr_values[iidx][aidx])

    cases_string_rowmajor=list()
    for iidx in range(item_num):
      for aidx in range(attr_num): cases_string_rowmajor.append(string_attr_values[iidx][aidx])
    cases_string_colmajor=list()
    for aidx in range(attr_num):
      for iidx in range(item_num): cases_string_colmajor.append(string_attr_values[iidx][aidx])

    #flow
    flow = LeafFlow(name="test_pack_multi_item_attrs")\
        .pack_multi_item_attrs(
          pack_mode = "concat_rowmajor",
          from_attrs = int_attr_names,
          to_attr = "int_rowmajor",
          default_value = 0,
        )\
        .pack_multi_item_attrs(
          pack_mode = "concat_colmajor",
          from_attrs = int_attr_names,
          to_attr = "int_colmajor",
          default_value = 0,
        ) \
        .pack_multi_item_attrs(
          pack_mode = "concat_rowmajor",
          from_attrs = float_attr_names,
          to_attr = "float_rowmajor",
          default_value = 0.0,
        )\
        .pack_multi_item_attrs(
          pack_mode = "concat_colmajor",
          from_attrs = float_attr_names,
          to_attr = "float_colmajor",
          default_value = 0.0,
        ) \
        .pack_multi_item_attrs(
          pack_mode = "concat_rowmajor",
          from_attrs = string_attr_names,
          to_attr = "string_rowmajor",
          default_value = "",
        )\
        .pack_multi_item_attrs(
          pack_mode = "concat_colmajor",
          from_attrs = string_attr_names,
          to_attr = "string_colmajor",
          default_value = "",
        )

    # run flow
    leaf = self.__init_service(flow)
    for iidx in range(item_num):
      item = leaf.add_item(iidx+1)
      for aidx in range(attr_num):
        item[int_attr_names[aidx]] = int_attr_values[iidx][aidx]
        item[float_attr_names[aidx]] = float_attr_values[iidx][aidx]
        item[string_attr_names[aidx]] = string_attr_values[iidx][aidx]
    leaf.run("test_pack_multi_item_attrs")

    # assert
    self.assertEqual(len(leaf["int_rowmajor"]), len(cases_int_rowmajor))
    self.assertEqual(len(leaf["int_colmajor"]), len(cases_int_colmajor))
    for i in range(len(cases_int_rowmajor)): self.assertEqual(leaf["int_rowmajor"][i], cases_int_rowmajor[i])
    for i in range(len(cases_int_colmajor)): self.assertEqual(leaf["int_colmajor"][i], cases_int_colmajor[i])

    self.assertEqual(len(leaf["float_rowmajor"]), len(cases_float_rowmajor))
    self.assertEqual(len(leaf["float_colmajor"]), len(cases_float_colmajor))
    for i in range(len(cases_float_rowmajor)): self.assertEqual(leaf["float_rowmajor"][i], cases_float_rowmajor[i])
    for i in range(len(cases_float_colmajor)): self.assertEqual(leaf["float_colmajor"][i], cases_float_colmajor[i])

    self.assertEqual(len(leaf["string_rowmajor"]), len(cases_string_rowmajor))
    self.assertEqual(len(leaf["string_colmajor"]), len(cases_string_colmajor))
    for i in range(len(cases_string_rowmajor)): self.assertEqual(leaf["string_rowmajor"][i], cases_string_rowmajor[i])
    for i in range(len(cases_string_colmajor)): self.assertEqual(leaf["string_colmajor"][i], cases_string_colmajor[i])

  def test_enrich_attr_by_json(self):
    flow = LeafFlow(name="test_enrich_attr_by_json") \
      .enrich_attr_by_json(
        json_attr = "str_json",
        json_configs = [{
          "json_path": "{{json_path_common_attr}}",
          "export_common_attr": "double_attr",
          "default_value": 0.0
        },{
          "json_path": "double_list_attr",
          "export_common_attr": "double_list_attr",
          "default_value": []
        },{
          "json_path": "{{json_path_item_attr}}",
          "export_item_attr": "str_item_attr",
          "default_value": ""
        },
        ]
      ).enrich_attr_by_json(
        json_attr = "id_map",
        json_configs = [{
          "json_path": "{{pid}}",
          "export_item_attr": "count",
          "default_value": 0
        },
        ]
      )

    leaf = self.__init_service(flow)
    leaf["json_path_common_attr"] = "double_attr"
    leaf["str_json"] = '{"double_list_attr":[0.300000,0.900000],"str_list_attr":["test","list"],"double_attr":0.300000,"aid1":"111","aid2":"222"}'
    leaf["id_map"] = '{"111": 111111, "222": 222222}'

    item1 = leaf.add_item(1)
    item1["json_path_item_attr"] = "aid1"
    item1["pid"] = 111
    item2 = leaf.add_item(2)
    item2["json_path_item_attr"] = "aid2"
    item2["pid"] = 222

    leaf.run("test_enrich_attr_by_json")
    self.assertAlmostEqual(leaf["double_attr"], 0.3)
    self.assertAlmostEqual(leaf["double_list_attr"], [0.3, 0.9])
    for item in leaf.items:
      if item.item_key == 1:
        self.assertEqual(item["str_item_attr"], "111")
        self.assertEqual(item["count"], 111111)
      else:
        self.assertEqual(item["str_item_attr"], "222")
        self.assertEqual(item["count"], 222222)

    flow = LeafFlow(name="test_enrich_attr_by_json.from_item_json_to_common") \
      .enrich_attr_by_json(
        json_attr = "str_json",
        json_from_item_attr = True,
        json_configs = [{
            "json_path": "[].field1",
            "export_common_attr": "field1_list",
          },{
            "json_path": "[].field2",
            "export_common_attr": "field2_list",
            "default_value": ""
          },{
            "json_path": "[].field3",
            "export_common_attr": "field3_list",
            "default_value": "v"
          }
        ]
      )

    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    item1["str_json"] = '[{"field1":"f1","field2":"f2"},{"field1":"f3","field3":"f4"}]'
    item2 = leaf.add_item(2)
    item2["str_json"] = '[{"field2":"f5","field3":"f6"},{"field1":"f7","field3":"f8"}]'

    leaf.run("test_enrich_attr_by_json.from_item_json_to_common")
    self.assertEqual(leaf["field1_list"], ["f1", "f3", "f7"])
    self.assertEqual(leaf["field2_list"], ["f2", "", "f5", ""])
    self.assertEqual(leaf["field3_list"], ["v", "f4", "f6", "f8"])

    flow = LeafFlow(name="test_enrich_attr_by_json.from_item_json_to_item") \
      .enrich_attr_by_json(
        json_attr = "str_json",
        json_from_item_attr = True,
        json_configs = [{
            "json_path": "field1",
            "export_item_attr": "field1_copy",
          },{
            "json_path": "field2",
            "export_item_attr": "field2_copy",
            "default_value": ""
          },{
            "json_path": "field3",
            "export_item_attr": "field3_copy",
            "default_value": "v"
          }
        ]
      )

    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    item1["str_json"] = '{"field1":"f1","field2":"f2"}'
    item2 = leaf.add_item(2)
    item2["str_json"] = '{"field2":"f3","field3":"f4"}'

    leaf.run("test_enrich_attr_by_json.from_item_json_to_item")

    self.assertEqual(leaf.items[0]["field1_copy"], "f1")
    self.assertEqual(leaf.items[0]["field2_copy"], "f2")
    self.assertEqual(leaf.items[0]["field3_copy"], "v")
    self.assertEqual(leaf.items[1]["field1_copy"], None)
    self.assertEqual(leaf.items[1]["field2_copy"], "f3")
    self.assertEqual(leaf.items[1]["field3_copy"], "f4")

  def test_attr_get_and_set(self):
    flow = LeafFlow(name="test_attr_get_and_set")
    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    #int
    leaf["int_value"] = 17
    self.assertEqual(leaf["int_value"], 17)
    item1["int_value"] = 29
    self.assertEqual(item1["int_value"], 29)
    #float
    leaf["float_value"] = 0.26
    self.assertEqual(leaf["float_value"], 0.26)
    item1["float_value"] = 0.55
    self.assertEqual(item1["float_value"], 0.55)
    #string
    leaf["string_value"] = "test_string"
    self.assertEqual(leaf["string_value"], "test_string")
    item1["string_value"] = "test_item"
    self.assertEqual(item1["string_value"], "test_item")
    #int_list
    int_list = [1, 3, 5, 7, 9]
    leaf["int_list_value"] = int_list
    self.assertEqual(leaf["int_list_value"], int_list)
    item1["int_list_value"] = int_list
    self.assertEqual(item1["int_list_value"], int_list)
    #float_list
    float_list = [0.77, 9999.15, 0]
    leaf["float_list_value"] = float_list
    self.assertEqual(leaf["float_list_value"], float_list)
    item1["float_list_value"] = float_list
    self.assertEqual(item1["float_list_value"], float_list)
    #string_list
    string_list = ["", "test_string", "aaaaaaaaabbbbbbbbbbaaaaaaa"]
    leaf["string_list_value"] = string_list
    self.assertEqual(leaf["string_list_value"], string_list)
    item1["string_list_value"] = string_list
    self.assertEqual(item1["string_list_value"], string_list)

  def test_subflow_attr_pass(self):
    lua_script = """
          function common_func()
            local int_value = int_value or nil
            local float_value = float_value or nil
            local string_value = string_value or nil
            local int_list_value = int_list_value or nil
            local float_list_value = float_list_value or nil
            local string_list_value = string_list_value or nil
            return int_value, float_value, string_value, int_list_value, float_list_value, string_list_value
          end
          function item_func(seq, item_key, reason, score)
            local int_value = int_value or nil
            local float_value = float_value or nil
            local string_value = string_value or nil
            local int_list_value = int_list_value or nil
            local float_list_value = float_list_value or nil
            local string_list_value = string_list_value or nil
            return int_value, float_value, string_value, int_list_value, float_list_value, string_list_value
          end
        """
    attrs = ["int_value", "float_value", "string_value", "int_list_value", "float_list_value", "string_list_value"]
    attrs_a = ["int_value_a", "float_value_a", "string_value_a", "int_list_value_a", "float_list_value_a", "string_list_value_a"]
    attrs_b = ["int_value_b", "float_value_b", "string_value_b", "int_list_value_b", "float_list_value_b", "string_list_value_b"]
    #attrs_c = ["int_value_c", "float_value_c", "string_value_c", "int_list_value_c", "float_list_value_c", "string_list_value_c"]
    attrs_d = ["int_value_d", "float_value_d", "string_value_d", "int_list_value_d", "float_list_value_d", "string_list_value_d"]
    attrs_d_alias = [{"name": attr, "as": f"{attr}.alias"} for attr in attrs_d]
    sub_flow_a = LeafFlow(name="enrich_sub_flow") \
      .enrich_attr_by_lua(
        import_common_attr=attrs,
        export_common_attr=attrs_a,
        import_item_attr=attrs,
        export_item_attr=attrs_a,
        function_for_common="common_func",
        function_for_item="item_func",
        lua_script=lua_script
    )
    sub_flow_b = LeafFlow(name="enrich_sub_flow2") \
      .enrich_attr_by_lua(
      import_common_attr=attrs,
      export_common_attr=attrs_b,
      import_item_attr=attrs,
      export_item_attr=attrs_b,
      function_for_common="common_func",
      function_for_item="item_func",
      lua_script=lua_script
    )
    sub_flow_d = LeafFlow(name="enrich_sub_flow3") \
      .enrich_attr_by_lua(
      import_common_attr=attrs,
      export_common_attr=attrs_d,
      import_item_attr=attrs,
      export_item_attr=attrs_d,
      function_for_common="common_func",
      function_for_item="item_func",
      lua_script=lua_script
    )

    flow = LeafFlow(name="test_subflow_attr_pass") \
      .enrich_by_sub_flow(
      sub_flow=sub_flow_a,
      pass_common_attrs=attrs,
      pass_item_attrs=attrs,
      merge_item_attrs=attrs_a,
      merge_common_attrs=attrs_a,
    ).enrich_by_sub_flow(
      sub_flow=sub_flow_b,
      pass_common_attrs=attrs,
      pass_item_attrs=attrs,
      merge_item_attrs=attrs_b,
      merge_common_attrs=attrs_b,
      deep_copy=True
    ).enrich_by_sub_flow(
      sub_flow=sub_flow_d,
      pass_common_attrs=attrs,
      pass_item_attrs=attrs,
      merge_item_attrs=attrs_d_alias,
      merge_common_attrs=attrs_d_alias,
      deep_copy=True
    )


    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    leaf["int_value"] = 17
    item1["int_value"] = 29
    leaf["float_value"] = 0.26
    item1["float_value"] = 0.55
    leaf["string_value"] = "test_string"
    item1["string_value"] = "test_item"
    int_list = [1, 3, 5, 7, 9]
    leaf["int_list_value"] = int_list
    item1["int_list_value"] = int_list
    float_list = [0.77, 9999.15, 0]
    leaf["float_list_value"] = float_list
    item1["float_list_value"] = float_list
    string_list = ["", "test_string", "aaaaaaaaabbbbbbbbbbaaaaaaa"]
    leaf["string_list_value"] = string_list
    item1["string_list_value"] = string_list

    leaf.run("test_subflow_attr_pass")
    self.assertEqual(leaf["int_value_a"], 17)
    self.assertEqual(item1["int_value_a"], 29)
    self.assertEqual(leaf["float_value_a"], 0.26)
    self.assertEqual(item1["float_value_a"], 0.55)
    self.assertEqual(leaf["string_value_a"], "test_string")
    self.assertEqual(item1["string_value_a"], "test_item")
    self.assertEqual(leaf["int_list_value_a"], int_list)
    self.assertEqual(item1["int_list_value_a"], int_list)
    self.assertEqual(leaf["float_list_value_a"], float_list)
    self.assertEqual(item1["float_list_value_a"], float_list)
    self.assertEqual(leaf["string_list_value_a"], string_list)
    self.assertEqual(item1["string_list_value_a"], string_list)
    self.assertEqual(leaf["int_value_b"], 17)
    self.assertEqual(item1["int_value_b"], 29)
    self.assertEqual(leaf["float_value_b"], 0.26)
    self.assertEqual(item1["float_value_b"], 0.55)
    self.assertEqual(leaf["string_value_b"], "test_string")
    self.assertEqual(item1["string_value_b"], "test_item")
    self.assertEqual(leaf["int_list_value_b"], int_list)
    self.assertEqual(item1["int_list_value_b"], int_list)
    self.assertEqual(leaf["float_list_value_b"], float_list)
    self.assertEqual(item1["float_list_value_b"], float_list)
    self.assertEqual(leaf["string_list_value_b"], string_list)
    self.assertEqual(item1["string_list_value_b"], string_list)
    self.assertEqual(leaf["int_value_d.alias"], 17)
    self.assertEqual(item1["int_value_d.alias"], 29)
    self.assertEqual(leaf["float_value_d.alias"], 0.26)
    self.assertEqual(item1["float_value_d.alias"], 0.55)
    self.assertEqual(leaf["string_value_d.alias"], "test_string")
    self.assertEqual(item1["string_value_d.alias"], "test_item")
    self.assertEqual(leaf["int_list_value_d.alias"], int_list)
    self.assertEqual(item1["int_list_value_d.alias"], int_list)
    self.assertEqual(leaf["float_list_value_d.alias"], float_list)
    self.assertEqual(item1["float_list_value_d.alias"], float_list)
    self.assertEqual(leaf["string_list_value_d.alias"], string_list)
    self.assertEqual(item1["string_list_value_d.alias"], string_list)

  def test_parse_protobuf_list_from_common_attr(self):
    flow = LeafFlow(name="test_parse_protobuf_list_from_common_attr")
    flow.build_protobuf(
      inputs=[
        { "common_attr": "pay_time1", "path": "pay_time" },
      ],
      output_common_attr="BuyerStat1",
      class_name="ks.reco.BuyerStat",
      as_string=True
    ).build_protobuf(
      inputs=[
        { "common_attr": "pay_time2", "path": "pay_time" },
      ],
      output_common_attr="BuyerStat2",
      class_name="ks.reco.BuyerStat",
      as_string=True
    ).build_protobuf(
      inputs=[
        { "common_attr": "pay_time3", "path": "pay_time" },
      ],
      output_common_attr="BuyerStat3",
      class_name="ks.reco.BuyerStat",
      as_string=True
    ).pack_common_attr(
      input_common_attrs = ["BuyerStat1", "BuyerStat2", "BuyerStat3"],
      output_common_attr = "BuyerStat_list",
      limit_num = 10,
    ).parse_protobuf_list_from_common_attr(
      input_common_attr="BuyerStat_list",
      class_name="ks.reco.BuyerStat",
      attrs=[
        {"name":"pay_time_list", "path":"pay_time"}]
    )

    leaf = self.__init_service(flow)
    leaf["pay_time1"] = 123
    leaf["pay_time2"] = 555
    leaf["pay_time3"] = 990
    leaf.run("test_parse_protobuf_list_from_common_attr")
    self.assertEqual(leaf["pay_time_list"], [123, 555, 990])


  def test_set_attr_value(self):

    flow = LeafFlow(name="test_set_attr_value").set_attr_value(
        item_attrs=[
          {"name": "sub_int", "type": "int", "value": 17},
          {"name": "sub_double", "type": "double", "value": 3.14},
          {"name": "sub_string", "type": "string", "value": "aaa"},
          {"name": "sub_int_list", "type": "int_list", "value": [1,2,3,4,5]},
          {"name": "sub_double_list", "type": "double_list", "value": [3.14, 2.22, 315]},
          {"name": "sub_string_list", "type": "string_list", "value": ["abc", "def", "ggg"]}
        ],
        common_attrs=[
          {"name": "int", "type": "int", "value": 17},
          {"name": "double", "type": "double", "value": 3.14},
          {"name": "string", "type": "string", "value": "aaa"},
          {"name": "int_list", "type": "int_list", "value": [1,2,3,4,5]},
          {"name": "double_list", "type": "double_list", "value": [3.14, 2.22, 315]},
          {"name": "string_list", "type": "string_list", "value": ["abc", "def", "ggg"]}
        ]
      )

    leaf = self.__init_service(flow)
    leaf["item_list"] = [1, 2, 3, 4, 5, 6, 7, 8]
    leaf.run("test_set_attr_value")
    self.assertEqual(leaf["string"], "aaa")
    self.assertEqual(leaf["int"], 17)
    self.assertEqual(leaf["double"], 3.14)
    self.assertEqual(leaf["int_list"], [1,2,3,4,5])
    self.assertEqual(leaf["double_list"], [3.14, 2.22, 315])
    self.assertEqual(leaf["string_list"], ["abc", "def", "ggg"])
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["sub_string"], "aaa")
      self.assertEqual(item["sub_int"], 17)
      self.assertEqual(item["sub_double"], 3.14)
      self.assertEqual(item["sub_int_list"], [1,2,3,4,5])
      self.assertEqual(item["sub_double_list"], [3.14, 2.22, 315])
      self.assertEqual(item["sub_string_list"], ["abc", "def", "ggg"])

  def test_dump_context(self):
    flow = LeafFlow(name="test_dump_context", item_table="test_table") \
      .retrieve_by_common_attr(attr="item_list", reason=1) \
      .copy_item_meta_info(
      save_item_id_to_attr="id",
      save_item_seq_to_attr="seq",
    ) \
      .dump_context(
      include_item_results=True,
      item_attrs="*",
      dump_as_table=True,
      dump_to_attr="context"
    ) \
      .dump_context(
      include_item_results=True,
      item_attrs=["id"],
      dump_as_table=True,
      dump_to_attr="context2"
    ) \
      .parse_protobuf_from_string(
      input_attr="context",
      output_attr="table_info",
      class_name="ks.platform.DataTable",
    ) \
      .enrich_with_protobuf(
      from_extra_var="table_info",
      attrs=[
        dict(name="parse_items",
             path="item_list.item_key",
             repeat_limit={"item_list.item_key": 10}),
        "name"
      ]
    )

    leaf = self.__init_service(flow)
    leaf["item_list"] = [5, 6, 7, 8]
    leaf.run("test_dump_context")

    self.assertEqual(leaf["parse_items"], [5, 6, 7, 8])
    self.assertEqual(leaf["name"], "test_table")

  def test_check_tail_number(self):
    flow = LeafFlow(name="test_check_tail_number") \
    .check_tail_number(
        kconf_key = 'reco.debug.notExistTailnumberKconfForTest',
        test_value = '{{user_id}}',
        output_to = 'is_sampled_user'
    ) 

    leaf = self.__init_service(flow)
    leaf["user_id"] = 6666100
    leaf.run("test_check_tail_number")
    self.assertEqual(leaf["is_sampled_user"], 0)
  
  def test_select_sign(self):
    flow = LeafFlow(name="test_select_sign") \
    .select_sign(
      input_slot_attr = "common_slots",
      input_sign_attr = "common_signs",
      select_slots = [100],
      output_sign_attrs = ["slot_100_bak"],
      is_common_attr = True
    )

    leaf = self.__init_service(flow)
    leaf["common_slots"] = [100, 200, 100, 200]
    leaf["common_signs"] = [12, 23, 34, 45]
    leaf.run("test_select_sign")
    self.assertEqual(leaf["slot_100_bak"], [12,34])

  def test_shuffle_list_attr(self):
    flow = LeafFlow(name="test_shuffle_list_attr") \
    .shuffle_list_attr(
        common_attr = "int_list_common_attr",
        item_attr = "string_list_item_attr"
    )

    leaf = self.__init_service(flow)
    leaf["int_list_common_attr"] = [1, 2]
    for i in range(5):
      item = leaf.add_item(i)
      item["string_list_item_attr"] = ["a", "b", "c"]
    leaf.run("test_shuffle_list_attr")
    self.assertEqual(len(leaf["int_list_common_attr"]), 2)
    self.assertIn(1, leaf["int_list_common_attr"])
    self.assertIn(2, leaf["int_list_common_attr"])
    for item in leaf.items:
      self.assertEqual(len(item["string_list_item_attr"]), 3)
      self.assertIn("a", item["string_list_item_attr"])
      self.assertIn("b", item["string_list_item_attr"])
      self.assertIn("c", item["string_list_item_attr"])

    flow = LeafFlow(name="test_shuffle_list_attr2") \
    .shuffle_list_attr(
        common_attr = "int_list_common_attr",
    )

    leaf = self.__init_service(flow)
    leaf["int_list_common_attr"] = [1, 2]
    leaf.run("test_shuffle_list_attr2")
    self.assertEqual(len(leaf["int_list_common_attr"]), 2)
    self.assertIn(1, leaf["int_list_common_attr"])
    self.assertIn(2, leaf["int_list_common_attr"])

  def test_mix_by_sub_flow(self):
    with set_env(CHECK_TALBE_DEPENDENCY='true'):
      @async_mix()
      def pack(flow):
        flow \
          .log_debug_info(item_attrs=["id", "tag"], for_debug_request_only=False, item_table="merchant") \
          .pack_item_attr_to_item_attr(from_item_attrs=["id", "tag"], to_item_attr="goods", default_val=0, debug_log=True, item_table="merchant") \
          .pack_item_attr_to_item_attr(from_item_attrs=["id", "tag"], to_item_attr="goods", default_val=0, debug_log=True, item_table="merchant2") \
          .retrieve_by_common_attr(attr="list2", reason=2, item_table="merchant_live", debug_log=True) \
          .copy_item_meta_info(save_reason_to_attr="aId", item_table="merchant_live")

      flow = LeafFlow(name="test_combine_by_sub_flow") \
        .retrieve_by_common_attr(attr="list1", reason=1, item_table="merchant", debug_log=True) \
        .copy_item_meta_info(save_item_id_to_attr="id", save_reason_to_attr="tag", item_table="merchant", debug_log=True) \
        .retrieve_by_common_attr(attr="list1", reason=1, item_table="merchant2", debug_log=True) \
        .copy_item_meta_info(save_item_id_to_attr="id", save_reason_to_attr="tag", item_table="merchant2", debug_log=True)

      pack(flow)

      flow.pack_item_attr(item_source={"reco_results": True}, mappings=[
          {"from_item_attr": "id", "to_common_attr": "merchant_id_list"},
          {"from_item_attr": "tag", "to_common_attr": "merchant_tag_list"},
          {"from_item_attr": "goods", "to_common_attr": "merchant_goods_list"}], item_table="merchant") \
        .pack_item_attr(item_source={"reco_results": True}, mappings=[
          {"from_item_attr": "id", "to_common_attr": "merchant2_id_list"},
          {"from_item_attr": "tag", "to_common_attr": "merchant2_tag_list"},
          {"from_item_attr": "goods", "to_common_attr": "merchant2_goods_list"}], item_table="merchant2") \
        .pack_item_attr(item_source={"reco_results": True}, mappings=[
          {"from_item_attr": "aId", "to_common_attr": "merchantlive_aId_list"},
          {"from_item_attr": "_REASON_", "to_common_attr": "merchantlive_reason_list"}], item_table="merchant_live") \
        .log_debug_info(common_attrs=["merchant_id_list", "merchant_tag_list", "merchant_goods_list", "merchantlive_aId_list", "merchantlive_reason_list", "merchant2_id_list", "merchant2_tag_list", "merchant2_goods_list"])

      leaf = self.__init_service(flow)
      leaf["list1"] = [1,2,3]
      leaf["list2"] = [4,5,6]
      leaf.run("test_combine_by_sub_flow")

      self.assertEqual(leaf["merchant_id_list"], [1,2,3])
      self.assertEqual(leaf["merchant2_id_list"], [1,2,3])
      self.assertEqual(leaf["merchant_tag_list"], [1,1,1])
      self.assertEqual(leaf["merchant2_tag_list"], [1,1,1])
      self.assertEqual(leaf["merchant_goods_list"], [1,1,2,1,3,1])
      self.assertEqual(leaf["merchant2_goods_list"], [1,1,2,1,3,1])
      self.assertEqual(leaf["merchantlive_aId_list"], [2,2,2])
      self.assertEqual(leaf["merchantlive_reason_list"], [2,2,2])


  def test_pack_item_attr_to_item_attr(self):
    flow = LeafFlow(name="test_pack_item_attr_to_item_attr") \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["a", "b", "c"],
      to_item_attr = "out_list",
      default_val = 0,
    )
    leaf = self.__init_service(flow)
    for i in range(2):
      item = leaf.add_item(i)
      item["a"] = i
      item["b"] = i + 1
    leaf.run("test_pack_item_attr_to_item_attr")
    for item in leaf.items:
      if item.item_key == 0:
        self.assertEqual(item["out_list"], [0, 1, 0])
      else:
        self.assertEqual(item["out_list"], [1, 2, 0])

    flow = LeafFlow(name="test_pack_item_attr_to_item_attr2") \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["a", "b", "c"],
      to_item_attr = "out_list",
      default_val = 0,
      limit_num = 1,
    )
    leaf = self.__init_service(flow)
    for i in range(2):
      item = leaf.add_item(i)
      item["a"] = i
      item["b"] = i + 1
    leaf.run("test_pack_item_attr_to_item_attr2")
    for item in leaf.items:
      if item.item_key == 0:
        self.assertEqual(item["out_list"], [0])
      else:
        self.assertEqual(item["out_list"], [1])

    flow = LeafFlow(name="test_pack_item_attr_to_item_attr3") \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["int_a", "int_b", "int_c"],
      to_item_attr = "int_out_list1",
      default_val = [-1, -2],
      fill_default_val = False
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["int_a", "int_b", "int_c"],
      to_item_attr = "int_out_list2",
      default_val = [-1, -2],
      fill_default_val = True 
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["double_a", "double_b", "double_c"],
      to_item_attr = "double_out_list1",
      default_val = [1.1, 1.2],
      fill_default_val = False
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["double_a", "double_b", "double_c"],
      to_item_attr = "double_out_list2",
      default_val = [1.1, 1.2],
      fill_default_val = True 
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["str_a", "str_b", "str_c"],
      to_item_attr = "str_out_list1",
      default_val = ["aa", "bb"],
      fill_default_val = False
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["str_a", "str_b", "str_c"],
      to_item_attr = "str_out_list2",
      default_val = ["aa", "bb"],
      fill_default_val = True 
    )

    leaf = self.__init_service(flow)
    for i in range(2):
      item = leaf.add_item(i)
      item["int_a"] = [i, i+1]
      item["int_c"] = [i+2, i+3]
      item["double_a"] = [float(i), float(i+1)]
      item["double_c"] = [float(i+2), float(i+3)]
      item["str_a"] = [str(i), str(i+1)]
      item["str_c"] = [str(i+2), str(i+3)]
    leaf.run("test_pack_item_attr_to_item_attr3")
    for item in leaf.items:
      if item.item_key == 0:
        self.assertEqual(item["int_out_list1"], [0, 1, 2, 3])
        self.assertEqual(item["int_out_list2"], [0, 1, -1, -2, 2, 3])
        self.assertEqual(item["double_out_list1"], [0.0, 1.0, 2.0, 3.0])
        self.assertEqual(item["double_out_list2"], [0.0, 1.0, 1.1, 1.2, 2.0, 3.0])
        self.assertEqual(item["str_out_list1"], ["0", "1", "2", "3"])
        self.assertEqual(item["str_out_list2"], ["0", "1", "aa", "bb", "2", "3"])
      else:
        self.assertEqual(item["int_out_list1"], [1, 2, 3, 4])
        self.assertEqual(item["int_out_list2"], [1, 2, -1, -2, 3, 4])
        self.assertEqual(item["double_out_list1"], [1.0, 2.0, 3.0, 4.0])
        self.assertEqual(item["double_out_list2"], [1.0, 2.0, 1.1, 1.2, 3.0, 4.0])
        self.assertEqual(item["str_out_list1"], ["1", "2", "3", "4"])
        self.assertEqual(item["str_out_list2"], ["1", "2", "aa", "bb", "3", "4"])

    flow = LeafFlow(name="test_pack_item_attr_to_item_attr4") \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["int_a", "int_b", "int_c"],
      to_item_attr = "int_out_list1",
      default_val = [-1, -2],
      dedup_to_item_attr = True
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["str_a", "str_b", "str_c"],
      to_item_attr = "str_out_list1",
      default_val = ["aa", "bb"],
      dedup_to_item_attr = True
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["int_a"],
      to_item_attr = "int_out_list2",
      default_val = [-1, -2],
      aggregator = "copy",
      dedup_to_item_attr = True
    ) \
   .pack_item_attr_to_item_attr(
      from_item_attrs = ["str_a"],
      to_item_attr = "str_out_list2",
      default_val = ["aa", "bb"],
      aggregator = "copy",
      dedup_to_item_attr = True
    ) \
    .pack_item_attr_to_item_attr(
      from_item_attrs = ["int_b"],
      to_item_attr = "int_out_list3",
      default_val = [-1, -1],
      aggregator = "copy",
      dedup_to_item_attr = True
    ) \
   .pack_item_attr_to_item_attr(
      from_item_attrs = ["str_b"],
      to_item_attr = "str_out_list3",
      default_val = ["aa", "aa"],
      aggregator = "copy",
      dedup_to_item_attr = True
    ) \

    leaf = self.__init_service(flow)
    for i in range(2):
      item = leaf.add_item(i)
      item["int_a"] = [i, i]
      item["int_c"] = [i+1, i+1]
      item["str_a"] = [str(i), str(i)]
      item["str_c"] = [str(i+1), str(i+1)]
    leaf.run("test_pack_item_attr_to_item_attr4")
    for item in leaf.items:
      if item.item_key == 0:
        self.assertEqual(item["int_out_list1"], [0, -1, -2, 1])
        self.assertEqual(item["str_out_list1"], ["0", "aa", "bb", "1"])
        self.assertEqual(item["int_out_list2"], [0])
        self.assertEqual(item["str_out_list2"], ["0"])
        self.assertEqual(item["int_out_list3"], [-1])
        self.assertEqual(item["str_out_list3"], ["aa"])
      else:
        self.assertEqual(item["int_out_list1"], [1, -1, -2, 2])
        self.assertEqual(item["str_out_list1"], ["1", "aa", "bb", "2"])
        self.assertEqual(item["int_out_list2"], [1])
        self.assertEqual(item["str_out_list2"], ["1"])
        self.assertEqual(item["int_out_list3"], [-1])
        self.assertEqual(item["str_out_list3"], ["aa"])

  def test_aggregate_mapping_list(self):
    flow = LeafFlow(name="test_aggregate_mapping_list") \
    .aggregate_mapping_list(
      key_list_attr = "seller_id_list",
      match_key_in_item_attr = "seller_id",
      aggregate_config = [
        { "value_list_attr" : "buy_item_price_list", "aggregator" : "sum", "save_result_to" : "buy_item_price_sum"},
        { "value_list_attr" : "pay_time_list", "aggregator" : "max", "save_result_to" : "seller_max_buy_time"}],
      save_count_to = "match_count")

    leaf = self.__init_service(flow)
    leaf["seller_id_list"] = [7001, 7002, 7001, 7002]
    leaf["buy_item_price_list"] = [100, 300, 5005, 11111]
    leaf["pay_time_list"] = [110001, 110003, 1001, 220002]

    item1 = leaf.add_item(1)
    item1["seller_id"] = 7001
    item2 = leaf.add_item(2)
    item2["seller_id"] = 7002

    leaf.run("test_aggregate_mapping_list")

    buy_item_price_sum = [5105, 11411]
    seller_max_buy_time = [110001, 220002]
    for index, item in enumerate(leaf.items):
      self.assertEqual(item["buy_item_price_sum"], buy_item_price_sum[index])
      self.assertEqual(item["seller_max_buy_time"], seller_max_buy_time[index])
      self.assertEqual(item["seller_max_buy_time"], seller_max_buy_time[index])
      self.assertEqual(item["match_count"], 2)


  def test_limit_by_reason(self):
    # 1
    flow = LeafFlow(name="test_limit_by_reason") \
      .fake_retrieve(num=10, reason=100) \
      .fake_retrieve(num=10, reason=101) \
      .limit_by_reason(
        reason_limit = 5,
      )

    leaf = self.__init_service(flow)
    leaf.run("test_limit_by_reason")
    reason_100_count = 0
    reason_101_count = 0
    for item in leaf.items:
      if item.reason == 100:
        reason_100_count += 1
      elif item.reason == 101:
        reason_101_count += 1
    self.assertEqual(reason_100_count, 5)
    self.assertEqual(reason_101_count, 5)

    # 2
    flow = LeafFlow(name="test_limit_by_reason2") \
      .fake_retrieve(num=10, reason=100) \
      .fake_retrieve(num=10, reason=101) \
      .limit_by_reason(
        reason_limit = 5,
        reason_config = [
          {
            "reason": 100,
            "limit": 8,
          },
        ],
      )

    leaf = self.__init_service(flow)
    leaf.run("test_limit_by_reason2")
    reason_100_count = 0
    reason_101_count = 0
    for item in leaf.items:
      if item.reason == 100:
        reason_100_count += 1
      elif item.reason == 101:
        reason_101_count += 1
    self.assertEqual(reason_100_count, 8)
    self.assertEqual(reason_101_count, 5)

    # 3
    flow = LeafFlow(name="test_limit_by_reason3") \
      .fake_retrieve(num=10, reason=100) \
      .fake_retrieve(num=10, reason=101) \
      .limit_by_reason(
        size_limit = 16,
        reason_limit = 5,
      )

    leaf = self.__init_service(flow)
    leaf.run("test_limit_by_reason3")
    reason_100_count = 0
    reason_101_count = 0
    for item in leaf.items:
      if item.reason == 100:
        reason_100_count += 1
      elif item.reason == 101:
        reason_101_count += 1
    self.assertEqual(reason_100_count, 8)
    self.assertEqual(reason_101_count, 8)

  def test_calc_ensemble_score(self):
    flow = LeafFlow(name = "test_calc_ensemble_score") \
      .calc_ensemble_score(
          channels = [
              { "name": "pctr", "weight": 1.0 },
              { "name": "pltr", "weight": 1.0 },
              { "name": "pftr", "weight": 1.0 },
          ],
          regulator = 1.0,
          smooth = 1.0,
          output_attr = "ensemble_score",
      ) \
      .sort(score_from_attr="ensemble_score")
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["pctr"] =  i / 10
      item["pltr"] =  (10 - i) / 10
      item["pftr"] =  (i % 2) / 10
    leaf.run("test_calc_ensemble_score")
    self.assertEqual([item.item_key for item in leaf.items], [1, 9, 0, 3, 8, 7, 5, 2, 6, 4])

  def test_get_kconf_params(self):
    flow = LeafFlow(name="test_get_kconf_params") \
      .get_kconf_params(
        kconf_configs = [{
          "kconf_key": "reco.debug.notExistGetKconfParamsForTest",
          "export_common_attr": "out_common_attr",
          "json_path": "aaa.bbb",
          "default_value": [1.0, 2, 2.0]
        },{
          "kconf_key": "reco.debug.notExistGetKconfParamsForTest",
          "export_item_attr": "out_item_attr",
          "json_path": "{{in_item_attr}}",
          "default_value": ["a", "b"]
        },{
          "kconf_key": "reco.debug.notExistGetKconfParamsForTest",
          "export_common_attr": "out_default_attr",
          "json_path": "{{no_this_attr}}",
          "default_value": 2
        }]
      ) 
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["in_item_attr"] = "not_exsit_kconf_key"
    leaf.run("test_get_kconf_params")
    self.assertEqual(leaf["out_common_attr"], [1.0, 2.0, 2.0])
    for item in leaf.items:
      self.assertEqual(item["out_item_attr"], ["a", "b"])

    leaf = self.__init_service(flow)
    leaf.run("test_get_kconf_params")
    self.assertEqual(leaf["out_default_attr"], 2)

  def test_pack_attr_to_bytes(self):
    flow = LeafFlow(name="test_pack_attr_to_bytes") \
      .pack_attr_to_bytes(
        is_common=True,
        schema=[
          dict(attr_name="common_int1", dtype="int8"),
          dict(attr_name="common_int2", dtype="int16"),
          dict(attr_name="common_int3", dtype="int32"),
          dict(attr_name="common_int4", dtype="int64"),
          dict(attr_name="common_float1", dtype="fp32"),
          dict(attr_name="common_float2", dtype="fp64"),
          dict(attr_name="common_byte1", dtype="bytes"),
          dict(attr_name="common_int_list1", dtype="int8"),
          dict(attr_name="common_int_list2", dtype="int16"),
          dict(attr_name="common_int_list3", dtype="int32"),
          dict(attr_name="common_int_list4", dtype="int64"),
          dict(attr_name="common_float_list1", dtype="fp32"),
          dict(attr_name="common_float_list2", dtype="fp64"),
          dict(attr_name="common_byte_list1", dtype="bytes"),
        ],
        output_attr_name="output_common_bytes") \
      .pack_attr_to_bytes(
        is_common=False,
        schema=[
          dict(attr_name="item_int1", dtype="int8"),
          dict(attr_name="item_int2", dtype="int16"),
          dict(attr_name="item_int3", dtype="int32"),
          dict(attr_name="item_int4", dtype="int64"),
          dict(attr_name="item_float1", dtype="fp32"),
          dict(attr_name="item_float2", dtype="fp64"),
          dict(attr_name="item_byte1", dtype="bytes"),
          dict(attr_name="item_int_list1", dtype="int8"),
          dict(attr_name="item_int_list2", dtype="int16"),
          dict(attr_name="item_int_list3", dtype="int32"),
          dict(attr_name="item_int_list4", dtype="int64"),
          dict(attr_name="item_float_list1", dtype="fp32"),
          dict(attr_name="item_float_list2", dtype="fp64"),
          dict(attr_name="item_byte_list1", dtype="bytes"),
        ],
        output_attr_name="output_item_bytes")

    leaf = self.__init_service(flow)
    leaf["common_int1"] = 1
    leaf["common_int2"] = 2
    leaf["common_int3"] = 3
    leaf["common_int4"] = 4
    leaf["common_float1"] = 1.1
    leaf["common_float2"] = 2.2
    leaf["common_byte1"] = "common_byte1"
    leaf["common_int_list1"] = [1, 2]
    leaf["common_int_list2"] = [2, 3]
    leaf["common_int_list3"] = [3, 4]
    leaf["common_int_list4"] = [4, 5]
    leaf["common_float_list1"] = [1.1, 2.2]
    leaf["common_float_list2"] = [2.2, 3.3]
    leaf["common_byte_list1"] = ["common", "byte", "list", "1"]
    for i in range(10):
      item = leaf.add_item(i)
      item["item_int1"] = 1
      item["item_int2"] = 2
      item["item_int3"] = 3
      item["item_int4"] = 4
      item["item_float1"] = 1.1
      item["item_float2"] = 2.2
      item["item_byte1"] = "item_byte1"
      item["item_int_list1"] = [1, 2]
      item["item_int_list2"] = [2, 3]
      item["item_int_list3"] = [3, 4]
      item["item_int_list4"] = [4, 5]
      item["item_float_list1"] = [1.1, 2.2]
      item["item_float_list2"] = [2.2, 3.3]
      item["item_byte_list1"] = ["item", "byte", "list", "1"]

    leaf.run("test_pack_attr_to_bytes")

    self.assertEqual(leaf.get_bytes("output_common_bytes"), struct.pack("<bhiqfd", 1, 2, 3, 4, 1.1, 2.2) + b"common_byte1" + struct.pack("<bbhhiiqqffdd", 1, 2, 2, 3, 3, 4, 4, 5, 1.1, 2.2, 2.2, 3.3) + b"commonbytelist1")
    for item in leaf.items:
      self.assertEqual(item.get_bytes("output_item_bytes"), struct.pack("<bhiqfd", 1, 2, 3, 4, 1.1, 2.2) + b"item_byte1" + struct.pack("<bbhhiiqqffdd", 1, 2, 2, 3, 3, 4, 4, 5, 1.1, 2.2, 2.2, 3.3) + b"itembytelist1")

  def test_unpack_bytes_to_attr(self):
    flow = LeafFlow(name="test_unpack_bytes_to_attr") \
      .unpack_bytes_to_attr(
        is_common=True,
        is_array=False,
        source_attr_name="input_common_bytes",
        schema=[
          dict(attr_name="common_int1", dtype="int8"),
          dict(attr_name="common_int2", dtype="int16"),
          dict(attr_name="common_int3", dtype="int32"),
          dict(attr_name="common_int4", dtype="int64"),
          dict(attr_name="common_float1", dtype="fp32"),
          dict(attr_name="common_float2", dtype="fp64"),
        ]) \
      .unpack_bytes_to_attr(
        is_common=True,
        is_array=True,
        source_attr_name="input_common_bytes_list",
        schema=[
          dict(attr_name="common_int1_list", dtype="int8"),
          dict(attr_name="common_int2_list", dtype="int16"),
          dict(attr_name="common_int3_list", dtype="int32"),
          dict(attr_name="common_int4_list", dtype="int64"),
          dict(attr_name="common_float1_list", dtype="fp32"),
          dict(attr_name="common_float2_list", dtype="fp64"),
        ]) \
      .unpack_bytes_to_attr(
        is_common=False,
        is_array=False,
        source_attr_name="input_item_bytes",
        schema=[
          dict(attr_name="item_int1", dtype="int8"),
          dict(attr_name="item_int2", dtype="int16"),
          dict(attr_name="item_int3", dtype="int32"),
          dict(attr_name="item_int4", dtype="int64"),
          dict(attr_name="item_float1", dtype="fp32"),
          dict(attr_name="item_float2", dtype="fp64"),
        ]) \
      .unpack_bytes_to_attr(
        is_common=False,
        is_array=True,
        source_attr_name="input_item_bytes_list",
        schema=[
          dict(attr_name="item_int1_list", dtype="int8"),
          dict(attr_name="item_int2_list", dtype="int16"),
          dict(attr_name="item_int3_list", dtype="int32"),
          dict(attr_name="item_int4_list", dtype="int64"),
          dict(attr_name="item_float1_list", dtype="fp32"),
          dict(attr_name="item_float2_list", dtype="fp64"),
        ])

    leaf = self.__init_service(flow)
    input_common_bytes = struct.pack("<bhiqfd", 1, 2, 3, 4, 1.1, 2.2)
    leaf["input_common_bytes"] = input_common_bytes
    input_common_bytes_list = input_common_bytes + input_common_bytes + input_common_bytes
    leaf["input_common_bytes_list"] = input_common_bytes_list
    
    
    input_item_bytes = struct.pack("<bhiqfd", 1, 2, 3, 4, 1.1, 2.2)
    input_item_bytes_list = input_item_bytes + input_item_bytes + input_item_bytes
    for i in range(10):
      item = leaf.add_item(i)
      item['input_item_bytes'] = input_item_bytes
      item['input_item_bytes_list'] = input_item_bytes_list

    leaf.run("test_unpack_bytes_to_attr")

    self.assertEqual(leaf["common_int1"], 1)
    self.assertEqual(leaf["common_int2"], 2)
    self.assertEqual(leaf["common_int3"], 3)
    self.assertEqual(leaf["common_int4"], 4)
    self.assertEqual(round(leaf["common_float1"], 3), 1.1)
    self.assertEqual(round(leaf["common_float2"], 3), 2.2)
    self.assertEqual(leaf["common_int1_list"], [1, 1, 1])
    self.assertEqual(leaf["common_int2_list"], [2, 2, 2])
    self.assertEqual(leaf["common_int3_list"], [3, 3, 3])
    self.assertEqual(leaf["common_int4_list"], [4, 4, 4])
    self.assertEqual([round(v, 3) for v in leaf["common_float1_list"]], [1.1, 1.1, 1.1])
    self.assertEqual([round(v, 3) for v in leaf["common_float2_list"]], [2.2, 2.2, 2.2])
    for item in leaf.items:
      self.assertEqual(item["item_int1"], 1)
      self.assertEqual(item["item_int2"], 2)
      self.assertEqual(item["item_int3"], 3)
      self.assertEqual(item["item_int4"], 4)
      self.assertEqual(round(item["item_float1"], 3), 1.1)
      self.assertEqual(round(item["item_float2"], 3), 2.2)

      self.assertEqual(item["item_int1_list"], [1, 1, 1])
      self.assertEqual(item["item_int2_list"], [2, 2, 2])
      self.assertEqual(item["item_int3_list"], [3, 3, 3])
      self.assertEqual(item["item_int4_list"], [4, 4, 4])
      self.assertEqual([round(v, 3) for v in item["item_float1_list"]], [1.1, 1.1, 1.1])
      self.assertEqual([round(v, 3) for v in item["item_float2_list"]], [2.2, 2.2, 2.2])
    
    

  def test_enrich_by_wasm(self):
    flow = LeafFlow(name = "test_enrich_by_wasm") \
      .enrich_by_wasm(
        wat = """
        (module
          (func (export "f1") (param i64) (result i64)
            local.get 0
            i64.const 100
            i64.add)
        )
        """,
        calls = [
          {
            "name": "f1",
            "params": [ "int_in1", ],
            "results": [ "int_out1", ],
          },
        ],
      )
    leaf = self.__init_service(flow)
    leaf["int_in1"] = 42
    leaf.run("test_enrich_by_wasm")
    self.assertEqual(leaf["int_out1"], 142)

    flow = LeafFlow(name = "test_enrich_by_wasm2") \
      .enrich_by_wasm(
        wat = """
        (module
          (type (;0;) (func))
          (type (;1;) (func (param i32) (result i32)))
          (func $__wasm_call_ctors (type 0))
          (func $alloc (type 1) (param i32) (result i32)
            (local i32 i32 i32 i32 i32)
            i32.const 0
            local.set 1
            i32.const 0
            i32.load offset=1024
            local.set 2
            memory.size
            local.set 3
            block  ;; label = @1
              local.get 0
              i32.const 3
              i32.add
              i32.const -4
              i32.and
              local.tee 0
              local.get 2
              i32.const 66672
              i32.add
              local.tee 4
              i32.const -1
              i32.xor
              i32.gt_u
              br_if 0 (;@1;)
              block  ;; label = @2
                local.get 4
                local.get 0
                i32.add
                local.tee 5
                local.get 3
                i32.const 16
                i32.shl
                local.tee 3
                i32.le_u
                br_if 0 (;@2;)
                local.get 5
                local.get 3
                i32.sub
                i32.const 65535
                i32.add
                i32.const 16
                i32.shr_u
                memory.grow
                i32.const -1
                i32.eq
                br_if 1 (;@1;)
                i32.const 0
                i32.load offset=1024
                local.set 2
              end
              i32.const 0
              local.get 2
              local.get 0
              i32.add
              i32.store offset=1024
              local.get 4
              local.set 1
            end
            local.get 1)
          (func $init (type 0)
            i32.const 0
            i32.const 0
            i32.store offset=1024)
          (func $common_attrs_example (type 0)
            (local i32 i32 i32 i32 i32 i32 i32 i32)
            i32.const 0
            i32.const 0
            i64.load offset=1032
            i64.store offset=1040
            i32.const 0
            i32.const 0
            f64.load offset=1048
            f64.store offset=1056
            i32.const 0
            i32.load offset=1024
            local.set 0
            i32.const 0
            i32.load offset=1064
            i32.load
            local.set 1
            memory.size
            local.set 2
            i32.const 0
            local.set 3
            block  ;; label = @1
              local.get 1
              i32.const 7
              i32.add
              i32.const -4
              i32.and
              local.tee 1
              local.get 0
              i32.const 66672
              i32.add
              local.tee 4
              i32.const -1
              i32.xor
              i32.gt_u
              br_if 0 (;@1;)
              block  ;; label = @2
                local.get 4
                local.get 1
                i32.add
                local.tee 5
                local.get 2
                i32.const 16
                i32.shl
                local.tee 2
                i32.le_u
                br_if 0 (;@2;)
                i32.const 0
                local.set 3
                local.get 5
                local.get 2
                i32.sub
                i32.const 65535
                i32.add
                i32.const 16
                i32.shr_u
                memory.grow
                i32.const -1
                i32.eq
                br_if 1 (;@1;)
                i32.const 0
                i32.load offset=1024
                local.set 0
              end
              i32.const 0
              local.get 0
              local.get 1
              i32.add
              i32.store offset=1024
              local.get 4
              local.set 3
            end
            local.get 3
            i32.const 0
            i32.load offset=1064
            local.tee 0
            i32.load
            i32.store
            i32.const 0
            local.get 3
            i32.store offset=1068
            block  ;; label = @1
              local.get 0
              i32.load
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              i32.const 4
              local.set 3
              loop  ;; label = @2
                i32.const 0
                i32.load offset=1068
                local.get 3
                i32.add
                local.get 0
                local.get 3
                i32.add
                i32.load8_u
                i32.store8
                local.get 3
                i32.const -3
                i32.add
                local.set 1
                local.get 3
                i32.const 1
                i32.add
                local.set 3
                local.get 1
                i32.const 0
                i32.load offset=1064
                local.tee 0
                i32.load
                i32.lt_s
                br_if 0 (;@2;)
              end
            end
            i32.const 0
            i32.load offset=1024
            local.set 3
            i32.const 0
            i32.load offset=1072
            i32.load align=1
            local.set 0
            memory.size
            local.set 4
            i32.const 0
            local.set 5
            block  ;; label = @1
              local.get 0
              i32.const 3
              i32.shl
              i32.const 4
              i32.or
              local.tee 0
              local.get 3
              i32.const 66672
              i32.add
              local.tee 1
              i32.const -1
              i32.xor
              i32.gt_u
              br_if 0 (;@1;)
              block  ;; label = @2
                local.get 1
                local.get 0
                i32.add
                local.tee 2
                local.get 4
                i32.const 16
                i32.shl
                local.tee 4
                i32.le_u
                br_if 0 (;@2;)
                i32.const 0
                local.set 5
                local.get 2
                local.get 4
                i32.sub
                i32.const 65535
                i32.add
                i32.const 16
                i32.shr_u
                memory.grow
                i32.const -1
                i32.eq
                br_if 1 (;@1;)
                i32.const 0
                i32.load offset=1024
                local.set 3
              end
              i32.const 0
              local.get 3
              local.get 0
              i32.add
              i32.store offset=1024
              local.get 1
              local.set 5
            end
            local.get 5
            i32.const 0
            i32.load offset=1072
            local.tee 6
            i32.load align=1
            i32.store align=1
            i32.const 0
            local.get 5
            i32.store offset=1076
            block  ;; label = @1
              local.get 6
              i32.load align=1
              local.tee 3
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              local.get 3
              i32.const 3
              i32.and
              local.set 4
              i32.const 0
              local.set 2
              block  ;; label = @2
                local.get 3
                i32.const 4
                i32.lt_u
                br_if 0 (;@2;)
                local.get 3
                i32.const -4
                i32.and
                local.set 7
                i32.const 0
                local.set 3
                i32.const 0
                local.set 2
                loop  ;; label = @3
                  local.get 5
                  local.get 3
                  i32.add
                  local.tee 0
                  i32.const 4
                  i32.add
                  local.get 6
                  local.get 3
                  i32.add
                  local.tee 1
                  i32.const 4
                  i32.add
                  i64.load align=1
                  i64.store align=1
                  local.get 0
                  i32.const 12
                  i32.add
                  local.get 1
                  i32.const 12
                  i32.add
                  i64.load align=1
                  i64.store align=1
                  local.get 0
                  i32.const 20
                  i32.add
                  local.get 1
                  i32.const 20
                  i32.add
                  i64.load align=1
                  i64.store align=1
                  local.get 0
                  i32.const 28
                  i32.add
                  local.get 1
                  i32.const 28
                  i32.add
                  i64.load align=1
                  i64.store align=1
                  local.get 3
                  i32.const 32
                  i32.add
                  local.set 3
                  local.get 7
                  local.get 2
                  i32.const 4
                  i32.add
                  local.tee 2
                  i32.ne
                  br_if 0 (;@3;)
                end
              end
              local.get 4
              i32.eqz
              br_if 0 (;@1;)
              local.get 6
              local.get 2
              i32.const 3
              i32.shl
              i32.const 4
              i32.or
              local.tee 0
              i32.add
              local.set 3
              local.get 5
              local.get 0
              i32.add
              local.set 0
              loop  ;; label = @2
                local.get 0
                local.get 3
                i64.load align=1
                i64.store align=1
                local.get 3
                i32.const 8
                i32.add
                local.set 3
                local.get 0
                i32.const 8
                i32.add
                local.set 0
                local.get 4
                i32.const -1
                i32.add
                local.tee 4
                br_if 0 (;@2;)
              end
            end
            i32.const 0
            i32.load offset=1024
            local.set 3
            i32.const 0
            i32.load offset=1080
            i32.load align=1
            local.set 0
            memory.size
            local.set 4
            i32.const 0
            local.set 5
            block  ;; label = @1
              local.get 0
              i32.const 3
              i32.shl
              i32.const 4
              i32.or
              local.tee 0
              local.get 3
              i32.const 66672
              i32.add
              local.tee 1
              i32.const -1
              i32.xor
              i32.gt_u
              br_if 0 (;@1;)
              block  ;; label = @2
                local.get 1
                local.get 0
                i32.add
                local.tee 2
                local.get 4
                i32.const 16
                i32.shl
                local.tee 4
                i32.le_u
                br_if 0 (;@2;)
                i32.const 0
                local.set 5
                local.get 2
                local.get 4
                i32.sub
                i32.const 65535
                i32.add
                i32.const 16
                i32.shr_u
                memory.grow
                i32.const -1
                i32.eq
                br_if 1 (;@1;)
                i32.const 0
                i32.load offset=1024
                local.set 3
              end
              i32.const 0
              local.get 3
              local.get 0
              i32.add
              i32.store offset=1024
              local.get 1
              local.set 5
            end
            local.get 5
            i32.const 0
            i32.load offset=1080
            local.tee 6
            i32.load align=1
            i32.store align=1
            i32.const 0
            local.get 5
            i32.store offset=1084
            block  ;; label = @1
              local.get 6
              i32.load align=1
              local.tee 3
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              local.get 3
              i32.const 3
              i32.and
              local.set 4
              i32.const 0
              local.set 2
              block  ;; label = @2
                local.get 3
                i32.const 4
                i32.lt_u
                br_if 0 (;@2;)
                local.get 3
                i32.const -4
                i32.and
                local.set 7
                i32.const 0
                local.set 3
                i32.const 0
                local.set 2
                loop  ;; label = @3
                  local.get 5
                  local.get 3
                  i32.add
                  local.tee 0
                  i32.const 4
                  i32.add
                  local.get 6
                  local.get 3
                  i32.add
                  local.tee 1
                  i32.const 4
                  i32.add
                  f64.load align=1
                  f64.store align=1
                  local.get 0
                  i32.const 12
                  i32.add
                  local.get 1
                  i32.const 12
                  i32.add
                  f64.load align=1
                  f64.store align=1
                  local.get 0
                  i32.const 20
                  i32.add
                  local.get 1
                  i32.const 20
                  i32.add
                  f64.load align=1
                  f64.store align=1
                  local.get 0
                  i32.const 28
                  i32.add
                  local.get 1
                  i32.const 28
                  i32.add
                  f64.load align=1
                  f64.store align=1
                  local.get 3
                  i32.const 32
                  i32.add
                  local.set 3
                  local.get 7
                  local.get 2
                  i32.const 4
                  i32.add
                  local.tee 2
                  i32.ne
                  br_if 0 (;@3;)
                end
              end
              local.get 4
              i32.eqz
              br_if 0 (;@1;)
              local.get 6
              local.get 2
              i32.const 3
              i32.shl
              i32.const 4
              i32.or
              local.tee 0
              i32.add
              local.set 3
              local.get 5
              local.get 0
              i32.add
              local.set 0
              loop  ;; label = @2
                local.get 0
                local.get 3
                f64.load align=1
                f64.store align=1
                local.get 3
                i32.const 8
                i32.add
                local.set 3
                local.get 0
                i32.const 8
                i32.add
                local.set 0
                local.get 4
                i32.const -1
                i32.add
                local.tee 4
                br_if 0 (;@2;)
              end
            end)
          (func $item_attrs_example (type 0)
            (local i32 i32 i32 i32 i32 i32 i32 i32 i32 i32)
            i32.const 0
            local.set 0
            block  ;; label = @1
              i32.const 0
              i32.load offset=1088
              local.tee 1
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              local.get 1
              i32.const 3
              i32.and
              local.set 2
              i32.const 0
              i32.load offset=1096
              local.set 3
              i32.const 0
              i32.load offset=1092
              local.set 4
              block  ;; label = @2
                local.get 1
                i32.const 4
                i32.lt_u
                br_if 0 (;@2;)
                local.get 1
                i32.const -4
                i32.and
                local.set 5
                i32.const 0
                local.set 6
                i32.const 0
                local.set 0
                loop  ;; label = @3
                  local.get 3
                  local.get 6
                  i32.add
                  local.tee 7
                  local.get 4
                  local.get 6
                  i32.add
                  local.tee 8
                  i64.load
                  i64.store
                  local.get 7
                  i32.const 8
                  i32.add
                  local.get 8
                  i32.const 8
                  i32.add
                  i64.load
                  i64.store
                  local.get 7
                  i32.const 16
                  i32.add
                  local.get 8
                  i32.const 16
                  i32.add
                  i64.load
                  i64.store
                  local.get 7
                  i32.const 24
                  i32.add
                  local.get 8
                  i32.const 24
                  i32.add
                  i64.load
                  i64.store
                  local.get 6
                  i32.const 32
                  i32.add
                  local.set 6
                  local.get 5
                  local.get 0
                  i32.const 4
                  i32.add
                  local.tee 0
                  i32.ne
                  br_if 0 (;@3;)
                end
              end
              block  ;; label = @2
                local.get 2
                i32.eqz
                br_if 0 (;@2;)
                local.get 4
                local.get 0
                i32.const 3
                i32.shl
                local.tee 7
                i32.add
                local.set 6
                local.get 3
                local.get 7
                i32.add
                local.set 7
                loop  ;; label = @3
                  local.get 7
                  local.get 6
                  i64.load
                  i64.store
                  local.get 6
                  i32.const 8
                  i32.add
                  local.set 6
                  local.get 7
                  i32.const 8
                  i32.add
                  local.set 7
                  local.get 2
                  i32.const -1
                  i32.add
                  local.tee 2
                  br_if 0 (;@3;)
                end
              end
              local.get 1
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              local.get 1
              i32.const 3
              i32.and
              local.set 2
              i32.const 0
              local.set 0
              i32.const 0
              i32.load offset=1104
              local.set 3
              i32.const 0
              i32.load offset=1100
              local.set 4
              block  ;; label = @2
                local.get 1
                i32.const 4
                i32.lt_u
                br_if 0 (;@2;)
                local.get 1
                i32.const -4
                i32.and
                local.set 5
                i32.const 0
                local.set 6
                i32.const 0
                local.set 0
                loop  ;; label = @3
                  local.get 3
                  local.get 6
                  i32.add
                  local.tee 7
                  local.get 4
                  local.get 6
                  i32.add
                  local.tee 8
                  f64.load
                  f64.store
                  local.get 7
                  i32.const 8
                  i32.add
                  local.get 8
                  i32.const 8
                  i32.add
                  f64.load
                  f64.store
                  local.get 7
                  i32.const 16
                  i32.add
                  local.get 8
                  i32.const 16
                  i32.add
                  f64.load
                  f64.store
                  local.get 7
                  i32.const 24
                  i32.add
                  local.get 8
                  i32.const 24
                  i32.add
                  f64.load
                  f64.store
                  local.get 6
                  i32.const 32
                  i32.add
                  local.set 6
                  local.get 5
                  local.get 0
                  i32.const 4
                  i32.add
                  local.tee 0
                  i32.ne
                  br_if 0 (;@3;)
                end
              end
              block  ;; label = @2
                local.get 2
                i32.eqz
                br_if 0 (;@2;)
                local.get 4
                local.get 0
                i32.const 3
                i32.shl
                local.tee 7
                i32.add
                local.set 6
                local.get 3
                local.get 7
                i32.add
                local.set 7
                loop  ;; label = @3
                  local.get 7
                  local.get 6
                  f64.load
                  f64.store
                  local.get 6
                  i32.const 8
                  i32.add
                  local.set 6
                  local.get 7
                  i32.const 8
                  i32.add
                  local.set 7
                  local.get 2
                  i32.const -1
                  i32.add
                  local.tee 2
                  br_if 0 (;@3;)
                end
              end
              local.get 1
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              i32.const 0
              i32.load offset=1024
              local.set 0
              i32.const 0
              local.set 2
              loop  ;; label = @2
                i32.const 0
                i32.load offset=1108
                local.get 2
                i32.const 2
                i32.shl
                local.tee 7
                i32.add
                i32.load
                i32.load
                local.set 8
                memory.size
                local.set 6
                block  ;; label = @3
                  block  ;; label = @4
                    local.get 8
                    i32.const 7
                    i32.add
                    i32.const -4
                    i32.and
                    local.tee 3
                    local.get 0
                    i32.const 66672
                    i32.add
                    local.tee 4
                    i32.const -1
                    i32.xor
                    i32.le_u
                    br_if 0 (;@4;)
                    i32.const 0
                    local.set 6
                    br 1 (;@3;)
                  end
                  block  ;; label = @4
                    local.get 4
                    local.get 3
                    i32.add
                    local.tee 5
                    local.get 6
                    i32.const 16
                    i32.shl
                    local.tee 6
                    i32.le_u
                    br_if 0 (;@4;)
                    local.get 5
                    local.get 6
                    i32.sub
                    i32.const 65535
                    i32.add
                    i32.const 16
                    i32.shr_u
                    memory.grow
                    local.set 5
                    i32.const 0
                    local.set 6
                    i32.const 0
                    i32.load offset=1024
                    local.set 0
                    local.get 5
                    i32.const -1
                    i32.eq
                    br_if 1 (;@3;)
                  end
                  i32.const 0
                  local.get 0
                  local.get 3
                  i32.add
                  local.tee 0
                  i32.store offset=1024
                  local.get 4
                  local.set 6
                end
                i32.const 0
                i32.load offset=1112
                local.get 7
                i32.add
                local.get 6
                i32.store
                i32.const 0
                i32.load offset=1112
                local.get 7
                i32.add
                i32.load
                local.get 8
                i32.store
                block  ;; label = @3
                  local.get 8
                  i32.const 1
                  i32.lt_s
                  br_if 0 (;@3;)
                  local.get 8
                  i32.const 1
                  i32.and
                  local.set 3
                  i32.const 0
                  local.set 6
                  block  ;; label = @4
                    local.get 8
                    i32.const 1
                    i32.eq
                    br_if 0 (;@4;)
                    local.get 8
                    i32.const -2
                    i32.and
                    local.set 8
                    i32.const 0
                    local.set 6
                    loop  ;; label = @5
                      i32.const 0
                      i32.load offset=1112
                      local.get 7
                      i32.add
                      i32.load
                      local.get 6
                      i32.add
                      i32.const 4
                      i32.add
                      i32.const 0
                      i32.load offset=1108
                      local.get 7
                      i32.add
                      i32.load
                      local.get 6
                      i32.add
                      i32.const 4
                      i32.add
                      i32.load8_u
                      i32.store8
                      i32.const 0
                      i32.load offset=1112
                      local.get 7
                      i32.add
                      i32.load
                      local.get 6
                      i32.add
                      i32.const 5
                      i32.add
                      i32.const 0
                      i32.load offset=1108
                      local.get 7
                      i32.add
                      i32.load
                      local.get 6
                      i32.add
                      i32.const 5
                      i32.add
                      i32.load8_u
                      i32.store8
                      local.get 8
                      local.get 6
                      i32.const 2
                      i32.add
                      local.tee 6
                      i32.ne
                      br_if 0 (;@5;)
                    end
                  end
                  local.get 3
                  i32.eqz
                  br_if 0 (;@3;)
                  i32.const 0
                  i32.load offset=1112
                  local.get 7
                  i32.add
                  i32.load
                  local.get 6
                  i32.add
                  i32.const 4
                  i32.add
                  i32.const 0
                  i32.load offset=1108
                  local.get 7
                  i32.add
                  i32.load
                  local.get 6
                  i32.add
                  i32.const 4
                  i32.add
                  i32.load8_u
                  i32.store8
                end
                local.get 2
                i32.const 1
                i32.add
                local.tee 2
                i32.const 0
                i32.load offset=1088
                local.tee 6
                i32.lt_s
                br_if 0 (;@2;)
              end
              local.get 6
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              i32.const 0
              i32.load offset=1024
              local.set 9
              i32.const 0
              local.set 1
              loop  ;; label = @2
                i32.const 0
                i32.load offset=1116
                local.get 1
                i32.const 2
                i32.shl
                local.tee 7
                i32.add
                i32.load
                i32.load align=1
                local.set 6
                memory.size
                local.set 8
                block  ;; label = @3
                  block  ;; label = @4
                    local.get 6
                    i32.const 3
                    i32.shl
                    i32.const 4
                    i32.or
                    local.tee 2
                    local.get 9
                    i32.const 66672
                    i32.add
                    local.tee 0
                    i32.const -1
                    i32.xor
                    i32.le_u
                    br_if 0 (;@4;)
                    i32.const 0
                    local.set 8
                    br 1 (;@3;)
                  end
                  block  ;; label = @4
                    local.get 0
                    local.get 2
                    i32.add
                    local.tee 3
                    local.get 8
                    i32.const 16
                    i32.shl
                    local.tee 8
                    i32.le_u
                    br_if 0 (;@4;)
                    local.get 3
                    local.get 8
                    i32.sub
                    i32.const 65535
                    i32.add
                    i32.const 16
                    i32.shr_u
                    memory.grow
                    local.set 3
                    i32.const 0
                    local.set 8
                    i32.const 0
                    i32.load offset=1024
                    local.set 9
                    local.get 3
                    i32.const -1
                    i32.eq
                    br_if 1 (;@3;)
                  end
                  i32.const 0
                  local.get 9
                  local.get 2
                  i32.add
                  local.tee 9
                  i32.store offset=1024
                  local.get 0
                  local.set 8
                end
                i32.const 0
                i32.load offset=1120
                local.get 7
                i32.add
                local.get 8
                i32.store
                i32.const 0
                i32.load offset=1120
                local.get 7
                i32.add
                i32.load
                local.tee 3
                local.get 6
                i32.store align=1
                block  ;; label = @3
                  local.get 6
                  i32.const 1
                  i32.lt_s
                  br_if 0 (;@3;)
                  local.get 6
                  i32.const 3
                  i32.and
                  local.set 2
                  i32.const 0
                  local.set 0
                  i32.const 0
                  i32.load offset=1116
                  local.get 7
                  i32.add
                  i32.load
                  local.set 4
                  block  ;; label = @4
                    local.get 6
                    i32.const 4
                    i32.lt_u
                    br_if 0 (;@4;)
                    local.get 6
                    i32.const -4
                    i32.and
                    local.set 5
                    i32.const 0
                    local.set 6
                    i32.const 0
                    local.set 0
                    loop  ;; label = @5
                      local.get 3
                      local.get 6
                      i32.add
                      local.tee 7
                      i32.const 4
                      i32.add
                      local.get 4
                      local.get 6
                      i32.add
                      local.tee 8
                      i32.const 4
                      i32.add
                      i64.load align=1
                      i64.store align=1
                      local.get 7
                      i32.const 12
                      i32.add
                      local.get 8
                      i32.const 12
                      i32.add
                      i64.load align=1
                      i64.store align=1
                      local.get 7
                      i32.const 20
                      i32.add
                      local.get 8
                      i32.const 20
                      i32.add
                      i64.load align=1
                      i64.store align=1
                      local.get 7
                      i32.const 28
                      i32.add
                      local.get 8
                      i32.const 28
                      i32.add
                      i64.load align=1
                      i64.store align=1
                      local.get 6
                      i32.const 32
                      i32.add
                      local.set 6
                      local.get 5
                      local.get 0
                      i32.const 4
                      i32.add
                      local.tee 0
                      i32.ne
                      br_if 0 (;@5;)
                    end
                  end
                  local.get 2
                  i32.eqz
                  br_if 0 (;@3;)
                  local.get 4
                  local.get 0
                  i32.const 3
                  i32.shl
                  local.tee 7
                  i32.add
                  i32.const 4
                  i32.add
                  local.set 6
                  local.get 3
                  local.get 7
                  i32.add
                  i32.const 4
                  i32.add
                  local.set 7
                  loop  ;; label = @4
                    local.get 7
                    local.get 6
                    i64.load align=1
                    i64.store align=1
                    local.get 6
                    i32.const 8
                    i32.add
                    local.set 6
                    local.get 7
                    i32.const 8
                    i32.add
                    local.set 7
                    local.get 2
                    i32.const -1
                    i32.add
                    local.tee 2
                    br_if 0 (;@4;)
                  end
                end
                local.get 1
                i32.const 1
                i32.add
                local.tee 1
                i32.const 0
                i32.load offset=1088
                local.tee 6
                i32.lt_s
                br_if 0 (;@2;)
              end
              local.get 6
              i32.const 1
              i32.lt_s
              br_if 0 (;@1;)
              i32.const 0
              i32.load offset=1024
              local.set 9
              i32.const 0
              local.set 1
              loop  ;; label = @2
                i32.const 0
                i32.load offset=1124
                local.get 1
                i32.const 2
                i32.shl
                local.tee 7
                i32.add
                i32.load
                i32.load align=1
                local.set 6
                memory.size
                local.set 8
                block  ;; label = @3
                  block  ;; label = @4
                    local.get 6
                    i32.const 3
                    i32.shl
                    i32.const 4
                    i32.or
                    local.tee 2
                    local.get 9
                    i32.const 66672
                    i32.add
                    local.tee 0
                    i32.const -1
                    i32.xor
                    i32.le_u
                    br_if 0 (;@4;)
                    i32.const 0
                    local.set 8
                    br 1 (;@3;)
                  end
                  block  ;; label = @4
                    local.get 0
                    local.get 2
                    i32.add
                    local.tee 3
                    local.get 8
                    i32.const 16
                    i32.shl
                    local.tee 8
                    i32.le_u
                    br_if 0 (;@4;)
                    local.get 3
                    local.get 8
                    i32.sub
                    i32.const 65535
                    i32.add
                    i32.const 16
                    i32.shr_u
                    memory.grow
                    local.set 3
                    i32.const 0
                    local.set 8
                    i32.const 0
                    i32.load offset=1024
                    local.set 9
                    local.get 3
                    i32.const -1
                    i32.eq
                    br_if 1 (;@3;)
                  end
                  i32.const 0
                  local.get 9
                  local.get 2
                  i32.add
                  local.tee 9
                  i32.store offset=1024
                  local.get 0
                  local.set 8
                end
                i32.const 0
                i32.load offset=1128
                local.get 7
                i32.add
                local.get 8
                i32.store
                i32.const 0
                i32.load offset=1128
                local.get 7
                i32.add
                i32.load
                local.tee 3
                local.get 6
                i32.store align=1
                block  ;; label = @3
                  local.get 6
                  i32.const 1
                  i32.lt_s
                  br_if 0 (;@3;)
                  local.get 6
                  i32.const 3
                  i32.and
                  local.set 2
                  i32.const 0
                  local.set 0
                  i32.const 0
                  i32.load offset=1124
                  local.get 7
                  i32.add
                  i32.load
                  local.set 4
                  block  ;; label = @4
                    local.get 6
                    i32.const 4
                    i32.lt_u
                    br_if 0 (;@4;)
                    local.get 6
                    i32.const -4
                    i32.and
                    local.set 5
                    i32.const 0
                    local.set 6
                    i32.const 0
                    local.set 0
                    loop  ;; label = @5
                      local.get 3
                      local.get 6
                      i32.add
                      local.tee 7
                      i32.const 4
                      i32.add
                      local.get 4
                      local.get 6
                      i32.add
                      local.tee 8
                      i32.const 4
                      i32.add
                      f64.load align=1
                      f64.store align=1
                      local.get 7
                      i32.const 12
                      i32.add
                      local.get 8
                      i32.const 12
                      i32.add
                      f64.load align=1
                      f64.store align=1
                      local.get 7
                      i32.const 20
                      i32.add
                      local.get 8
                      i32.const 20
                      i32.add
                      f64.load align=1
                      f64.store align=1
                      local.get 7
                      i32.const 28
                      i32.add
                      local.get 8
                      i32.const 28
                      i32.add
                      f64.load align=1
                      f64.store align=1
                      local.get 6
                      i32.const 32
                      i32.add
                      local.set 6
                      local.get 5
                      local.get 0
                      i32.const 4
                      i32.add
                      local.tee 0
                      i32.ne
                      br_if 0 (;@5;)
                    end
                  end
                  local.get 2
                  i32.eqz
                  br_if 0 (;@3;)
                  local.get 4
                  local.get 0
                  i32.const 3
                  i32.shl
                  local.tee 7
                  i32.add
                  i32.const 4
                  i32.add
                  local.set 6
                  local.get 3
                  local.get 7
                  i32.add
                  i32.const 4
                  i32.add
                  local.set 7
                  loop  ;; label = @4
                    local.get 7
                    local.get 6
                    f64.load align=1
                    f64.store align=1
                    local.get 6
                    i32.const 8
                    i32.add
                    local.set 6
                    local.get 7
                    i32.const 8
                    i32.add
                    local.set 7
                    local.get 2
                    i32.const -1
                    i32.add
                    local.tee 2
                    br_if 0 (;@4;)
                  end
                end
                local.get 1
                i32.const 1
                i32.add
                local.tee 1
                i32.const 0
                i32.load offset=1088
                i32.lt_s
                br_if 0 (;@2;)
              end
            end)
          (memory (;0;) 2)
          (global $__stack_pointer (mut i32) (i32.const 66672))
          (global (;1;) i32 (i32.const 66672))
          (global (;2;) i32 (i32.const 1032))
          (global (;3;) i32 (i32.const 1040))
          (global (;4;) i32 (i32.const 1048))
          (global (;5;) i32 (i32.const 1056))
          (global (;6;) i32 (i32.const 1064))
          (global (;7;) i32 (i32.const 1068))
          (global (;8;) i32 (i32.const 1072))
          (global (;9;) i32 (i32.const 1076))
          (global (;10;) i32 (i32.const 1080))
          (global (;11;) i32 (i32.const 1084))
          (global (;12;) i32 (i32.const 1088))
          (global (;13;) i32 (i32.const 1096))
          (global (;14;) i32 (i32.const 1092))
          (global (;15;) i32 (i32.const 1104))
          (global (;16;) i32 (i32.const 1100))
          (global (;17;) i32 (i32.const 1108))
          (global (;18;) i32 (i32.const 1112))
          (global (;19;) i32 (i32.const 1116))
          (global (;20;) i32 (i32.const 1120))
          (global (;21;) i32 (i32.const 1124))
          (global (;22;) i32 (i32.const 1128))
          (global (;23;) i32 (i32.const 1024))
          (global (;24;) i32 (i32.const 1132))
          (global (;25;) i32 (i32.const 1136))
          (global (;26;) i32 (i32.const 66672))
          (global (;27;) i32 (i32.const 1024))
          (global (;28;) i32 (i32.const 131072))
          (global (;29;) i32 (i32.const 0))
          (global (;30;) i32 (i32.const 1))
          (export "memory" (memory 0))
          (export "__wasm_call_ctors" (func $__wasm_call_ctors))
          (export "alloc" (func $alloc))
          (export "__heap_base" (global 1))
          (export "init" (func $init))
          (export "common_attrs_example" (func $common_attrs_example))
          (export "ca_in1" (global 2))
          (export "ca_out1" (global 3))
          (export "ca_in2" (global 4))
          (export "ca_out2" (global 5))
          (export "ca_in3" (global 6))
          (export "ca_out3" (global 7))
          (export "ca_in4" (global 8))
          (export "ca_out4" (global 9))
          (export "ca_in5" (global 10))
          (export "ca_out5" (global 11))
          (export "item_attrs_example" (func $item_attrs_example))
          (export "item_num" (global 12))
          (export "ia_out1" (global 13))
          (export "ia_in1" (global 14))
          (export "ia_out2" (global 15))
          (export "ia_in2" (global 16))
          (export "ia_in3" (global 17))
          (export "ia_out3" (global 18))
          (export "ia_in4" (global 19))
          (export "ia_out4" (global 20))
          (export "ia_in5" (global 21))
          (export "ia_out5" (global 22))
          (export "__dso_handle" (global 23))
          (export "__data_end" (global 24))
          (export "__stack_low" (global 25))
          (export "__stack_high" (global 26))
          (export "__global_base" (global 27))
          (export "__heap_end" (global 28))
          (export "__memory_base" (global 29))
          (export "__table_base" (global 30)))
        """,
        input_attrs = [
          { "name": "ca_in1", "value_type": "i64" },
          { "name": "ca_in2", "value_type": "f64" },
          { "name": "ca_in3", "value_type": "string" },
          { "name": "ca_in4", "value_type": "i64_list" },
          { "name": "ca_in5", "value_type": "f64_list" },
          { "name": "ia_in1", "type": "item_attr", "value_type": "i64" },
          { "name": "ia_in2", "type": "item_attr", "value_type": "f64" },
          { "name": "ia_in3", "type": "item_attr", "value_type": "string" },
          { "name": "ia_in4", "type": "item_attr", "value_type": "i64_list" },
          { "name": "ia_in5", "type": "item_attr", "value_type": "f64_list" },
        ],
        output_attrs = [
          { "name": "ca_out1", "value_type": "i64" },
          { "name": "ca_out2", "value_type": "f64" },
          { "name": "ca_out3", "value_type": "string" },
          { "name": "ca_out4", "value_type": "i64_list" },
          { "name": "ca_out5", "value_type": "f64_list" },
          { "name": "ia_out1", "type": "item_attr", "value_type": "i64" },
          { "name": "ia_out2", "type": "item_attr", "value_type": "f64" },
          { "name": "ia_out3", "type": "item_attr", "value_type": "string" },
          { "name": "ia_out4", "type": "item_attr", "value_type": "i64_list" },
          { "name": "ia_out5", "type": "item_attr", "value_type": "f64_list" },
        ],
        calls = [
          { "name": "common_attrs_example" },
          { "name": "item_attrs_example" },
        ],
      )
    
    leaf = self.__init_service(flow)
    leaf["ca_in1"] = 42
    leaf["ca_in2"] = 42.5
    leaf["ca_in3"] = "hello"
    leaf["ca_in4"] = [1, 2, 3, 4, 5]
    leaf["ca_in5"] = [1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5]

    for i in range(3):
      item = leaf.add_item(i + 10)
      item["ia_in1"] = i
      item["ia_in2"] = i + 10.5
      item["ia_in3"] = "hello" + str(i)
      item["ia_in4"] = [1, 2] + [i + 10]
      item["ia_in5"] = [1.5, 2.5, 3.5] + [i + 10.5]

    leaf.run("test_enrich_by_wasm2")
    self.assertEqual(leaf["ca_out1"], 42)
    self.assertEqual(leaf["ca_out2"], 42.5)
    self.assertEqual(leaf["ca_out3"], "hello")
    self.assertEqual(leaf["ca_out4"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["ca_out5"], [1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5])

    for item in leaf.items:
      if item.item_key == 11:
        self.assertEqual(item["ia_out1"], 1)
        self.assertEqual(item["ia_out2"], 11.5)
        self.assertEqual(item["ia_out3"], "hello1")
        self.assertEqual(item["ia_out4"], [1, 2, 11])
        self.assertEqual(item["ia_out5"], [1.5, 2.5, 3.5, 11.5])

  def test_cast_attr_type(self):
    cast_types = ["int", "string", "float"]
    from_types = ["int", "string", "float", "int_list", "string_list", "float_list"]
    flow = (
      LeafFlow(name="test_cast_attr_type")
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": to_type,
            "from_common_attr": f"cast_input_{from_type}_common_attr",
            "to_common_attr": f"cast_output_{from_type}_to_{to_type}_common_attr",
          } for to_type in cast_types for from_type in from_types
        ] + [
          {
            "to_type": to_type,
            "from_item_attr": f"cast_intput_{from_type}_item_attr",
            "to_item_attr": f"cast_output_{from_type}_to_{to_type}_item_attr",
          } for to_type in cast_types for from_type in from_types
        ]
      )
    )
    leaf = self.__init_service(flow)
    leaf["cast_input_int_common_attr"] = 13
    leaf["cast_input_float_common_attr"] = 13.5
    leaf["cast_input_string_common_attr"] = "138"
    leaf["cast_input_int_list_common_attr"] = [1, 2, 3]
    leaf["cast_input_float_list_common_attr"] = [1.51, 2.51, 3.51]
    leaf["cast_input_string_list_common_attr"] = ["3", "4", "5"]
    for i in range(1, 10):
      item = leaf.add_item(i)
      item["cast_intput_int_item_attr"] = i
      item["cast_intput_float_item_attr"] = i + 0.5
      item["cast_intput_string_item_attr"] = str(i)
      item["cast_intput_int_list_item_attr"] = [1, 2, i]
      item["cast_intput_float_list_item_attr"] = [1.51, 2.51, i + 0.51]
      item["cast_intput_string_list_item_attr"] = ["3", "4", str(i)]
    leaf.run("test_cast_attr_type")
    self.assertEqual(leaf["cast_output_int_to_int_common_attr"], 13)
    self.assertEqual(leaf["cast_output_int_to_float_common_attr"], 13.0)
    self.assertEqual(leaf["cast_output_int_to_string_common_attr"], "13")
    self.assertEqual(leaf["cast_output_float_to_int_common_attr"], 13)
    self.assertEqual(leaf["cast_output_float_to_float_common_attr"], 13.5)
    self.assertEqual(leaf["cast_output_float_to_string_common_attr"].startswith("13.5"), True)
    self.assertEqual(leaf["cast_output_string_to_int_common_attr"], 138)
    self.assertEqual(leaf["cast_output_string_to_float_common_attr"], 138.0)
    self.assertEqual(leaf["cast_output_string_to_string_common_attr"], "138")
    self.assertEqual(leaf["cast_output_int_list_to_int_common_attr"], [1, 2, 3])
    self.assertEqual(leaf["cast_output_int_list_to_float_common_attr"], [1.0, 2.0, 3.0])
    self.assertEqual(leaf["cast_output_int_list_to_string_common_attr"], ["1", "2", "3"])
    self.assertEqual(leaf["cast_output_float_list_to_int_common_attr"], [1, 2, 3])
    self.assertEqual(leaf["cast_output_float_list_to_float_common_attr"], [1.51, 2.51, 3.51])
    self.assertEqual(leaf["cast_output_float_list_to_string_common_attr"][0].startswith("1.5"), True)
    self.assertEqual(leaf["cast_output_float_list_to_string_common_attr"][1].startswith("2.5"), True)
    self.assertEqual(leaf["cast_output_float_list_to_string_common_attr"][2].startswith("3.5"), True)
    self.assertEqual(leaf["cast_output_string_list_to_int_common_attr"], [3, 4, 5])
    self.assertEqual(leaf["cast_output_string_list_to_float_common_attr"], [3.0, 4.0, 5.0])
    self.assertEqual(leaf["cast_output_string_list_to_string_common_attr"], ["3", "4", "5"])
    for item in leaf.items:
      self.assertEqual(item["cast_output_int_to_int_item_attr"], item.item_key)
      self.assertEqual(item["cast_output_int_to_float_item_attr"], item.item_key + 0.0)
      self.assertEqual(item["cast_output_int_to_string_item_attr"], str(item.item_key))
      self.assertEqual(item["cast_output_float_to_int_item_attr"], item.item_key)
      self.assertEqual(item["cast_output_float_to_float_item_attr"], item.item_key + 0.5)
      self.assertEqual(item["cast_output_float_to_string_item_attr"].startswith(str(item.item_key + 0.5)), True)

      self.assertEqual(item["cast_output_string_to_int_item_attr"], item.item_key)
      self.assertEqual(item["cast_output_string_to_float_item_attr"], item.item_key + 0.0)
      self.assertEqual(item["cast_output_string_to_string_item_attr"], str(item.item_key))
      self.assertEqual(item["cast_output_int_list_to_int_item_attr"], [1, 2, item.item_key])
      self.assertEqual(item["cast_output_int_list_to_float_item_attr"], [1.0, 2.0, item.item_key + 0.0])
      self.assertEqual(item["cast_output_int_list_to_string_item_attr"], ["1", "2", str(item.item_key)])
      self.assertEqual(item["cast_output_float_list_to_int_item_attr"], [1, 2, item.item_key])
      self.assertEqual(item["cast_output_float_list_to_float_item_attr"], [1.51, 2.51, item.item_key + 0.51])
      self.assertEqual(item["cast_output_float_list_to_string_item_attr"][0].startswith("1.5"), True)
      self.assertEqual(item["cast_output_float_list_to_string_item_attr"][1].startswith("2.5"), True)
      self.assertEqual(item["cast_output_float_list_to_string_item_attr"][2].startswith(str(item.item_key + 0.5)), True)
      self.assertEqual(item["cast_output_string_list_to_int_item_attr"], [3, 4, item.item_key])
      self.assertEqual(item["cast_output_string_list_to_float_item_attr"], [3.0, 4.0, item.item_key + 0.0])
      self.assertEqual(item["cast_output_string_list_to_string_item_attr"], ["3", "4", str(item.item_key)])

  def test_calc_by_formula1(self):
    flow = LeafFlow(name="test_calc_by_formula1") \
      .gen_common_attr_by_lua(attr_map={
        "new_user": "1"
      }) \
      .copy_item_meta_info(save_item_id_to_attr="iid") \
      .calc_by_formula1(
        kconf_key = "reco.live2.formula1Test",
        import_common_attr=[
          {"name": "nebula", "as": "is_nebula", "default_val": 1},
          {"name": "new_user", "as": "is_new_user"}
        ],
        import_item_attr = [
          {"name": "iid", "as":"item_id"}
        ],
        export_formula_value = [
          {"name": "score", "as": "f1_score"}
        ],
        abtest_biz_name = "THANOS_RECO",
        user_id = 2148408760,
        device_id = "A9E17B96-3F1D-484E-9804-20CDFE9372C5"
      )
    leaf = self.__init_service(flow)
    leaf.run("test_calc_by_formula1")
    for item in leaf.items:
      self.assertEqual(item["f1_score"], 30)

    flow = LeafFlow(name="test_calc_by_formula1_2") \
      .gen_common_attr_by_lua(attr_map={
        "is_new_user": "1"
      }) \
      .copy_item_meta_info(save_item_id_to_attr="iid") \
      .calc_by_formula1(
        import_common_attr = ["is_new_user"],
        import_item_attr = ["t"],
        base_json = {
          "scenario": "scenario_pg_test2",
          "parameter": {
            "base": {
              "click_alpha": 0,
              "click_beta": 2.3,
              "like_alpha": 0.3,
              "like_beta": 2.6,
              "extra_alpha": 1,
              "ctr": 9.8,
              "click_omega": 1,
              "is_new_user==1": {
                "click_alpha": 0.6,
                "click_omega": 4
              },
              "is_new_user==2": {
                "click_alpha": 9.9
              }
            }
          },
          "formula": {
            "score": "click_omega",
            "s_int": "click_beta",
          },
          "debug_log": {
            "enable": True,
          },
          "perf_sample_rate": 0.001
        },
        export_formula_value = [
          {"name": "score", "as": "f1_score"},
          {"name": "s_int", "as": "f1_int", "to_int": True}
        ],
      )
    leaf = self.__init_service(flow)
    for i in range(5):
      item = leaf.add_item(i)
      item["t"] = 1
    leaf.run("test_calc_by_formula1_2")
    for item in leaf.items:
      self.assertEqual(item["f1_score"], 5.0)
      self.assertEqual(item["f1_int"], 2)

  def test_select_item(self):
    flow = LeafFlow(name="test_select_item") \
      .count_reco_result(
        save_count_to="out",
        select_item = {
          "attr_name": "p_int",
          "select_if": "<=",
          "compare_to": 1,
          "limit": 4
        },
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i < 5:
        item["p_int"] = 1
      else:
        item["p_int"] = 2
    leaf.run("test_select_item")
    self.assertEqual(leaf["out"], 4)

    flow = LeafFlow(name="test_select_item2") \
      .count_reco_result(
        save_count_to="out",
        select_item = {
          "join": "and",
          "filters": [{
            "attr_name": "p_double",
            "select_if": "<=",
            "compare_to": 8
          }, {
            "attr_name": "p_double",
            "select_if": ">=",
            "compare_to": 3
          }],
          "limit": "{{limit_num}}",
        },
      )
    leaf = self.__init_service(flow)
    leaf['limit_num'] = 5
    for i in range(20):
      item = leaf.add_item(i)
      item["p_double"] = 1.0* i
    leaf.run("test_select_item2")
    self.assertEqual(leaf["out"], 5)

    flow = LeafFlow(name="test_select_item3") \
      .count_reco_result(
        save_count_to="out",
        select_item = {
          "attr_name": "p_int",
          "select_if": "not null",
        }
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i < 3:
        item["p_int"] = 1
    leaf.run("test_select_item3")
    self.assertEqual(leaf["out"], 3)

    flow = LeafFlow(name="test_select_item4") \
      .count_reco_result(
        save_count_to="out",
        select_item = {
          "attr_name": "p_int",
          "select_if": "is null",
        }
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i < 7:
        item["p_int"] = 2.1
    leaf.run("test_select_item4")
    self.assertEqual(leaf["out"], 3)

    flow = LeafFlow(name="test_select_item5") \
      .deduplicate(
        select_item = {
            "attr_name": "item_author_id",
            "compare_to": 0,
            "select_if": "!=",
            "select_if_attr_missing": False
        },
        on_item_attr='item_id'
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["item_author_id"] = 0 if i < 5 else 2
      item["item_id"] = 1
    leaf.run("test_select_item5")
    self.assertEqual(len(leaf.items), 6)
    
    flow = LeafFlow(name="test_select_item6") \
      .count_reco_result(
        save_count_to="out",
        select_item = {
          "attr_name": "item_id_list",
          "compare_to": [1, 2],
          "select_if": "not in",
        }
      )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["item_id_list"] = [i, i+1] if i % 2 == 1 else []
    leaf.run("test_select_item6")
    self.assertEqual(leaf["out"], 4)

    flow = LeafFlow(name="test_select_item7") \
      .count_reco_result(
        save_count_to="out1",
        select_item = {
          "attr_name": "item_id_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb1}}",
              "upper_bound": "{{ub1}}"
            }
          },
          "select_if": "in",
        }
      ) \
     .count_reco_result(
        save_count_to="out2",
        select_item = {
          "attr_name": "item_score_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb2}}",
              "upper_bound": "{{ub2}}"
            }
          },
          "select_if": "in",
        }
      ) \
     .count_reco_result(
        save_count_to="out3",
        select_item = {
          "attr_name": "item_score",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb2}}",
              "upper_bound": "{{ub2}}"
            }
          },
          "select_if": "in",
        }
      ) \
     .count_reco_result(
        save_count_to="out4",
        select_item = {
          "attr_name": "item_score",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb2}}",
            }
          },
          "select_if": "in",
        }
      ) \
      .count_reco_result(
        save_count_to="out5",
        select_item = {
          "attr_name": "item_score",
          "compare_to": {
            "range" : {
              "upper_bound": "{{ub2}}"
            }
          },
          "select_if": "in",
        }
      ) \
      .count_reco_result(
        save_count_to="out6",
        select_item = {
          "attr_name": "item_name_list",
          "compare_to": ["1", "2", "3", "4", "5"],
          "select_if": "in",
        }
      ) \
      .count_reco_result(
        save_count_to="out7",
        select_item = {
          "attr_name": "item_id_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb1}}",
              "upper_bound": "{{ub1}}"
            }
          },
          "select_if": "intersect",
        }
      ) \
      .count_reco_result(
        save_count_to="out8",
        select_item = {
          "attr_name": "item_id_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb1}}",
              "upper_bound": "{{ub1}}"
            }
          },
          "select_if": "not intersect",
        }
      ) \
      .count_reco_result(
        save_count_to="out9",
        select_item = {
          "attr_name": "item_score_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb2}}",
              "upper_bound": "{{ub2}}"
            }
          },
          "select_if": "intersect",
        }
      ) \
      .count_reco_result(
        save_count_to="out10",
        select_item = {
          "attr_name": "item_score_list",
          "compare_to": {
            "range" : {
              "lower_bound": "{{lb2}}",
              "upper_bound": "{{ub2}}"
            }
          },
          "select_if": "not intersect",
        }
      ) \
      .count_reco_result(
        save_count_to="out11",
        select_item = {
          "attr_name": "item_id",
          "compare_to": [1,3,5,7],
          "select_if": "in",
        }
      ) \
      .count_reco_result(
        save_count_to="out12",
        select_item = {
          "attr_name": "item_id",
          "compare_to": [1,3,5,7],
          "select_if": "not in",
        }
      ) \
      .count_reco_result(
        save_count_to="out13",
        select_item = {
          "attr_name": "item_name_list",
          "compare_to": ["1","2","3","4","5"],
          "select_if": "in",
        }
      ) \
      .count_reco_result(
        save_count_to="out14",
        select_item = {
          "attr_name": "item_name_list",
          "compare_to": ["1","2","3","4","5"],
          "select_if": "not in",
        }
      ) \
  
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      item["item_id_list"] = [i, i+1, i+2, i+3]
      item["item_score_list"] = [1.0*i, 1.0*i+1, 1.0*i+2, 1.0*i+3]
      item["item_score"] = 1.0*i
      item["item_name_list"] = [str(i), str(i+1), str(i+2), str(i+3)]
      item["item_id"] = i

    leaf["lb1"] = 1
    leaf["ub1"] = 6
    leaf["lb2"] = 0.9 
    leaf["ub2"] = 5.9

    leaf.run("test_select_item7")

    self.assertEqual(leaf["out1"], 2)
    self.assertEqual(leaf["out2"], 2)
    self.assertEqual(leaf["out3"], 5)
    self.assertEqual(leaf["out4"], 9)
    self.assertEqual(leaf["out5"], 6)
    self.assertEqual(leaf["out6"], 2)
    self.assertEqual(leaf["out7"], 6)
    self.assertEqual(leaf["out8"], 4)
    self.assertEqual(leaf["out9"], 6)
    self.assertEqual(leaf["out10"], 4)
    self.assertEqual(leaf["out11"], 4)
    self.assertEqual(leaf["out12"], 6)
    self.assertEqual(leaf["out13"], 2)
    self.assertEqual(leaf["out14"], 8)

  def test_union_table(self):
    with set_env(CHECK_TALBE_DEPENDENCY='true'):
      flow = LeafFlow(name="test_union_table") \
        .union_table(
        from_table="sub_photo",
        to_table="photo",
        copy_attrs=["author_id",
                    {"name": "score", "copy_mode": "MAX"},
                    {"name": "reason", "copy_mode": "CONCAT"}]
      ).log_debug_info(item_attrs=["author_id", "score",  "reason"], for_debug_request_only=False, item_table="photo")

      leaf = self.__init_service(flow)
      leaf.set_main_table(table_name="photo")
      author_list = [1001, 1002, 1003]
      score_list = [5.7, 100.1, 0.55]
      for i in range(3):
        item = leaf.add_item(i+1)
        item["author_id"] = author_list[i]
        item["score"] = score_list[i]
        item["reason"] =  1

      score_list = [90.1, 2.5, 3.5]
      leaf.set_main_table(table_name="sub_photo")
      for i in range(3):
        item = leaf.add_item(i+3)
        item["author_id"] = author_list[i]
        item["score"] = score_list[i]
        item["reason"] =  2

      leaf.run("test_union_table")
      leaf.set_main_table(table_name="photo")

      item_list = [1,2,3, 3, 4, 5]
      author_list = [1001, 1002, 1001,1001, 1002, 1003]
      score_list = [5.7, 100.1, 90.1,90.1, 2.5, 3.5]
      reason_list = [[1], [1], [1,2],[1,2], [2], [2]]

      self.assertEqual(len(leaf.items), 6)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item.item_key, item_list[i])
        self.assertEqual(item["author_id"], author_list[i])
        self.assertEqual(item["score"], score_list[i])
        self.assertEqual(item["reason"], reason_list[i])

  def test_group_by(self):
    with set_env(CHECK_TALBE_DEPENDENCY='true'):
      flow = LeafFlow(name="test_group_by") \
        .group_by(
        from_table="photo",
        to_table="hetu_table",
        by_attr="hetu_tag_id",
        concat_source_table_key_as="photo_id_list",
        copy_attrs=[{"name": "author", "copy_mode": "CONCAT", "as": "author_list"},
                    {"name": "reason", "copy_mode": "CONCAT", "as": "reason_list"},
                    {"name": "caption_segment", "copy_mode": "CONCAT", "as": "concat_caption_segment"}]
      )
      leaf = self.__init_service(flow)
      leaf.set_main_table(table_name="photo")
      hetu_id_list = [14942050, 14941137, 14942050, 14942050, 14941137, 14943000]
      reason_list = [1,1,1,2,2,2]
      author_list = [1001, 1002, 1003, 1003, 1003, 1004]
      caption_segment_list = [["a", "b"], [""], ["c", "d"], [""], ["e"], ["f", "g"]]
      for i in range(6):
        item = leaf.add_item(i+1)
        item["hetu_tag_id"] = hetu_id_list[i]
        item["reason"] = reason_list[i]
        item["author"] = author_list[i]
        item["caption_segment"] = caption_segment_list[i]

      leaf.run("test_group_by")

      item_list = [14942050, 14941137, 14943000]
      pid_list = [[1,3,4],[2,5], [6]]
      authors_list = [[1001,1003,1003], [1002,1003], [1004]]
      reasons_list =  [[1,1,2], [1,2], [2]]
      concat_caption_segment =  [["a", "b", "c", "d", ""], ["", "e"], ["f", "g"]]

      leaf.set_main_table(table_name="hetu_table")
      self.assertEqual(len(leaf.items), 3)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item.item_key, item_list[i])
        self.assertEqual(item["photo_id_list"], pid_list[i])
        self.assertEqual(item["author_list"], authors_list[i])
        self.assertEqual(item["reason_list"], reasons_list[i])
        self.assertEqual(item["concat_caption_segment"], concat_caption_segment[i])

  def test_left_join_table(self):
    with set_env(CHECK_TALBE_DEPENDENCY='true'):
      flow = LeafFlow(name="test_left_join_table") \
        .left_join_table(
        left_table="photo",
        right_table="author",
        join_attr_left="author_id",
        copy_attrs=["author_fans_count", {"name": "author_score", "as": "new_author_score"}]
      )
      leaf = self.__init_service(flow)

      leaf.set_main_table(table_name="photo")
      item = leaf.add_item(1)
      item["author_id"] = 1001
      item = leaf.add_item(2)
      item["author_id"] = 1002
      item = leaf.add_item(3)
      item["author_id"] = 1001

      leaf.set_main_table(table_name="author")
      item = leaf.add_item(1001)
      item["author_fans_count"] = 10
      item["author_score"] = 12.5
      item = leaf.add_item(1002)
      item["author_fans_count"] = 1000
      item["author_score"] = 50.3
      item = leaf.add_item(1003)
      item["author_fans_count"] = 100000
      item["author_score"] = 99.9

      leaf.run("test_left_join_table")

      fans_counts = [10, 1000, 10]
      author_score = [12.5, 50.3, 12.5]


      leaf.set_main_table(table_name="photo")

      self.assertEqual(len(leaf.items), 3)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item["author_fans_count"], fans_counts[i])
        self.assertEqual(item["new_author_score"], author_score[i])

      flow = LeafFlow(name="test_left_join_table2") \
        .left_join_table(
        left_table="live",
        right_table="goods",
        join_attr_left="item_list",
        copy_attrs=[{"name": "price", "copy_mode": "CONCAT"}, {"name": "category", "copy_mode": "CONCAT"}]
      ).log_debug_info(item_table="live", item_attrs=["item_list", "price", "category"], for_debug_request_only=False)
      leaf = self.__init_service(flow)

      leaf.set_main_table(table_name="live")
      item = leaf.add_item(1)
      item["item_list"] = [1001, 1002, 1003]
      item = leaf.add_item(2)
      item["item_list"] = [1003, 1005]
      item = leaf.add_item(3)
      item["item_list"] = [1004, 1002, 1001]

      leaf.set_main_table(table_name="goods")
      item = leaf.add_item(1001)
      item["price"] = 121.11
      item["category"] = "一支笔"
      item = leaf.add_item(1002)
      item["price"] = 7.5
      item["category"] = "一个苹果"
      item = leaf.add_item(1003)
      item["price"] = 50.0
      item["category"] = "一本书"
      item = leaf.add_item(1004)
      item["price"] = 11.1
      item["category"] = "一支笔"
      item = leaf.add_item(1005)
      item["price"] = 1.1
      item["category"] = "一支笔"

      leaf.run("test_left_join_table2")

      prices = [[121.11, 7.5, 50.0], [50.0, 1.1], [11.1, 7.5, 121.11]]
      categorys = [["一支笔", "一个苹果", "一本书"], ["一本书", "一支笔"], ["一支笔", "一个苹果", "一支笔"]]

      leaf.set_main_table(table_name="live")

      self.assertEqual(len(leaf.items), 3)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item["price"], prices[i])
        self.assertEqual(item["category"], categorys[i])

  def test_create_logic_table(self):
    with set_env(CHECK_TALBE_DEPENDENCY='true'):
      flow = LeafFlow(name="test_create_logic_table") \
        .create_logic_table(
        item_table="photo",
        logic_table="temp_photo",
        select_attr=["author_id", "author_fans_count", "author_score"],
        select_item={
          "attr_name": "author_fans_count",
          "select_if": "<=",
          "compare_to": 10
        },
        remove_selected_items=True
      ).filter_by_rule(
        rule = {
          "attr_name": "author_id",
          "remove_if": "==",
          "compare_to": 2
        },
        item_table="temp_photo"
      ).set_attr_value(
        item_attrs=[
          {
            "name": "author_score",
            "type": "double",
            "value": 1000.1
          }
        ],
        item_table="temp_photo"
      )

      leaf = self.__init_service(flow)

      leaf.set_main_table(table_name="photo")
      item = leaf.add_item(1)
      item["author_id"] = 1
      item["author_fans_count"] = 10
      item["author_score"] = 12.5
      item = leaf.add_item(2)
      item["author_id"] = 1
      item["author_fans_count"] = 1000
      item["author_score"] = 50.3
      item = leaf.add_item(3)
      item["author_id"] = 2
      item["author_fans_count"] = 100000
      item["author_score"] = 99.9

      leaf.run("test_create_logic_table")

      fans_counts = [10, 1000, 100000]
      author_score = [1000.1, 50.3, 99.9]

      leaf.set_main_table(table_name="photo")
      self.assertEqual(len(leaf.items), 2)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item["author_fans_count"], fans_counts[i + 1])
        self.assertEqual(item["author_score"], author_score[i + 1])

      leaf.set_main_table(table_name="temp_photo")
      self.assertEqual(len(leaf.items), 1)
      for i, item in enumerate(leaf.items):
        self.assertEqual(item["author_fans_count"], fans_counts[i])
        self.assertEqual(item["author_score"], author_score[i])



  def test_parse_context(self):
    list1 = [1, 3, 5, 7]
    p_ints = [3, 9, 15, 21]
    p_strs = ["test1", "test3", "test5", "test7"]

    flow = LeafFlow(name="test_parse_context1") \
      .retrieve_by_common_attr(attr="list1", reason=1, item_table="table1") \
      .dispatch_common_attr(
        from_common_attr = "p_ints",
        to_item_attr = "p_int",
        item_table="table1"
      ) \
      .dispatch_common_attr(
        from_common_attr = "p_strs",
        to_item_attr = "p_str",
        item_table="table1"
      ) \
      .dump_context(item_attrs=["p_int", "p_str"], include_item_results=True,
                    dump_to_attr="context", item_table="table1", debug_log=True) \
      .parse_context(extract_item_results=True,
                     extract_item_attrs=["p_int", "p_str"],
                     parse_from_attr="context", debug_log=True) \
      .log_debug_info(item_attrs=["p_int", "p_str"], for_debug_request_only=False)

    leaf = self.__init_service(flow)
    leaf["list1"] = list1
    leaf["p_ints"] = p_ints
    leaf["p_strs"] = p_strs
    leaf.run("test_parse_context1")

    self.assertEqual(len(leaf.items), 4)
    for i, item in enumerate(leaf.items):
      self.assertEqual(item.item_key, list1[i])
      self.assertEqual(item["p_int"], p_ints[i])
      self.assertEqual(item["p_str"], p_strs[i])

    flow2 = LeafFlow(name="test_parse_context2") \
      .retrieve_by_common_attr(attr="list1", reason=1, item_table="table1") \
      .dispatch_common_attr(
      from_common_attr = "p_ints",
      to_item_attr = "p_int",
      item_table="table1"
    ) \
      .dispatch_common_attr(
      from_common_attr = "p_strs",
      to_item_attr = "p_str",
      item_table="table1"
    ) \
      .dump_context(item_attrs=["p_int", "p_str"], include_item_results=True,
                    dump_to_attr="context", item_table="table1", dump_as_table=True, debug_log=True) \
      .parse_context(extract_item_results=True,
                     extract_item_attrs=["p_int", "p_str"],
                     parse_from_attr="context", data_format="data_table", debug_log=True) \
      .log_debug_info(item_attrs=["p_int", "p_str"], for_debug_request_only=False)

    leaf2 = self.__init_service(flow2)
    leaf2["list1"] = list1
    leaf2["p_ints"] = p_ints
    leaf2["p_strs"] = p_strs
    leaf2.run("test_parse_context2")

    self.assertEqual(len(leaf2.items), 4)
    for i, item in enumerate(leaf2.items):
      self.assertEqual(item.item_key, list1[i])
      self.assertEqual(item["p_int"], p_ints[i])
      self.assertEqual(item["p_str"], p_strs[i])

  def test_build_custom_kv_user_info(self):
    flow = LeafFlow(name="test_build_custom_kv_user_info") \
      .build_sample_attr(
        mappings = [
          {
            "from_attr": "field1",
            "to_attr": "field1_ptr",
          }
        ]
      ) \
      .build_protobuf(
        class_name = "kuiba::PredictItem",
        inputs = [
          {
            "common_attr": "field1_ptr",
            "path": "attr",
            "append": True,
          },
        ],
        output_common_attr = "kv_user_info",
      ) \
      .build_custom_kv_user_info(
        collection_name = "test.c1",
        kv_user_info_attr = "kv_user_info",
        custom_user_info_keys = [
          "field1",
        ],
        save_result_to_attr = "custom_kv_user_info_str",
      ) \
      .base64(
        mode = "encode",
        input_attr = "custom_kv_user_info_str",
        output_attr = "base64_str",
      )

    leaf = self.__init_service(flow)
    leaf["field1"] = 12345
    leaf.run("test_build_custom_kv_user_info")
    self.assertGreater(len(leaf["base64_str"]), 0)

  def test_parse_from_custom_kv_user_info(self):
    flow = LeafFlow(name="test_parse_from_custom_kv_user_info") \
      .build_sample_attr(
        mappings = [
          {
            "from_attr": "field1_orig",
            "to_attr": "field1_ptr",
            "rename": "field1",
          }
        ]
      ) \
      .build_protobuf(
        class_name = "kuiba::PredictItem",
        inputs = [
          {
            "common_attr": "field1_ptr",
            "path": "attr",
            "append": True,
          },
        ],
        output_common_attr = "kv_user_info",
      ) \
      .build_custom_kv_user_info(
        collection_name = "test.c1",
        kv_user_info_attr = "kv_user_info",
        custom_user_info_keys = [
          "field1",
        ],
        save_result_to_attr = "custom_kv_user_info_str",
      ) \
      .parse_from_custom_kv_user_info(
        collection_name = "test.c1",
        custom_user_info_from = "custom_kv_user_info_str",
        fields_to_read = [
          "field1",
        ],
      )
    leaf = self.__init_service(flow)
    leaf["field1_orig"] = 123456
    leaf.run("test_parse_from_custom_kv_user_info")
    self.assertEqual(leaf["field1_orig"], leaf["field1"])

  def test_get_item_attr_by_remote_index(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
      "result_offset": base64.b64encode(struct.pack("<i", 0)).decode("utf-8"),
      "int_attr_value": base64.b64encode(struct.pack("<Q", 42)).decode("utf-8"),
      "string_attr_value": base64.b64encode(struct.pack("<i", 0)).decode("utf-8"),
      "attr_type": base64.b64encode(struct.pack("<i", 1)).decode("utf-8"),
    }
    resp_str = json.dumps(resp)
    rpc_mocker.mock_rpc_response(
      service_name="grpc_mocked_test",
      response_json_str=resp_str
    )

    # test normal
    flow = LeafFlow(name="test_get_item_attr_by_remote_index") \
      .get_item_attr_by_remote_index(
        kess_service = "grpc_mocked_test",
        attrs = [
          "attr1",
        ]
      )
    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    self.assertEqual(item1["attr1"], None)
    leaf.run("test_get_item_attr_by_remote_index")
    found = False
    for item in leaf.items:
      if item.item_key == 1:
        found = True
        self.assertEqual(item["attr1"], 42)
    self.assertEqual(found, True)

    #
    # test redirect
    # grpc_common_query_will_redirect_test -> grpc_common_query_redirect_to_test
    # already configured in kconf
    #
    rpc_mocker.mock_rpc_response(
      service_name="grpc_common_query_redirect_to_test",
      response_json_str=resp_str
    )
    flow = LeafFlow(name="test_get_item_attr_by_remote_index_test_redirect") \
      .get_item_attr_by_remote_index(
        kess_service = "grpc_common_query_will_redirect_test",
        attrs = [
          "attr1",
        ]
      )
    leaf = self.__init_service(flow)
    item1 = leaf.add_item(1)
    self.assertEqual(item1["attr1"], None)
    leaf.run("test_get_item_attr_by_remote_index_test_redirect")
    found = False
    for item in leaf.items:
      if item.item_key == 1:
        found = True
        self.assertEqual(item["attr1"], 42)
    self.assertEqual(found, True)

  def test_retrieve_by_remote_index(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
      "key_sign": base64.b64encode(struct.pack("<QQ", 42, 43)).decode("utf-8"),
      "result_offset": base64.b64encode(struct.pack("<ii", 0, 2)).decode("utf-8"),
      "string_attr_value": base64.b64encode(struct.pack("<i", 0)).decode("utf-8"),
    }
    resp_str = json.dumps(resp)
    rpc_mocker.mock_rpc_response(
      service_name="grpc_mocked_test",
      response_json_str=resp_str
    )

    # test normal
    flow = LeafFlow(name="test_retrieve_by_remote_index") \
      .retrieve_by_remote_index(
        kess_service = "grpc_mocked_test",
        querys = [{
          "query": "test_term"
        }],
        default_search_num = 2,
        default_random_search = 1,
        timeout_ms = 100,
        reason = 1,
      )
    leaf = self.__init_service(flow)
    self.assertEqual(len(leaf.items), 0)
    leaf.run("test_retrieve_by_remote_index")
    item_keys = []
    for item in leaf.items:
      item_keys.append(item.item_key)
    self.assertEqual(item_keys, [42, 43])

    #
    # test redirect
    # grpc_common_query_will_redirect_test -> grpc_common_query_redirect_to_test
    # already configured in kconf
    #
    rpc_mocker.mock_rpc_response(
      service_name="grpc_common_query_redirect_to_test",
      shard="s1",
      response_json_str=resp_str
    )
    flow = LeafFlow(name="test_retrieve_by_remote_index_test_redirect") \
      .retrieve_by_remote_index(
        kess_service = "grpc_common_query_will_redirect_test",
        querys = [{
          "query": "test_term"
        }],
        default_search_num = 2,
        default_random_search = 1,
        timeout_ms = 100,
        reason = 1,
      )
    leaf = self.__init_service(flow)
    self.assertEqual(len(leaf.items), 0)
    leaf.run("test_retrieve_by_remote_index_test_redirect")
    item_keys = []
    for item in leaf.items:
      item_keys.append(item.item_key)
    self.assertEqual(item_keys, [42, 43])

  # 代码测试同 test_retrieve_by_remote_index
  @unittest.skip("skip ut for partition storage")
  def test_retrieve_by_remote_colossusdb_index(self):
    pass
  
  # 本地索引不好做单测，暂时跳过
  @unittest.skip("skip ut for local index")
  def test_get_item_attr_by_local_index(self):
    pass

  # 本地索引不好做单测，暂时跳过
  @unittest.skip("skip ut for local index")
  def test_retrieve_by_local_index(self):
    pass

  def test_build_common_reco_response(self):
    rpc_mocker = self.__service.rpc_mocker()

    # data for mock response
    item_id = [10, 11, 12, 13, 14, 15, 16, 17]
    item_type = [0, 2, 0, 0, 1, 1, 1, 2]

    int_list = [2, 3, 132, 4, 1, 23, 34, 4]
    double_list = [0.1, 23, 0.199999, 0.234, 0.56, 0.56, 4.23, -5.4]
    
    resp = dict(
      item = [],
      common_attr = [],
      item_attr = {}
    )
    for i in range(len(item_id)):
      item_attr = dict(
        type = "FLOAT_ATTR",
        name = encode_bytes(str2bytes('ctr')),
        floatValue = double_list[i],
      )
      item = dict(
        item_id = item_id[i],
        item_type = item_type[i],
        item_attr = [item_attr],
      )
      resp["item"].append(item)

    common_attr = dict(
      type = "INT_ATTR",
      name = encode_bytes(str2bytes('int_common_attr')),
      int_value = 1
    )
    resp["common_attr"].append(common_attr)

    item_attr = dict(
      item_keys = [],
      attr_values = []
    )
    for i in range(len(item_id)):
      item_key = gen_key_sign(item_type[i], item_id[i])
      item_attr["item_keys"].append(item_key)

    int_item_attr = dict(
      name = "int_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in int_list)),
      value_type = "INT64"
    )
    item_attr["attr_values"].append(int_item_attr)

    resp["item_attr"] = item_attr
    
    rpc_mocker.mock_rpc_response(service_name="grpc_DelegateRetrieveTest", response_json_str=json.dumps(resp))
    
    flow = LeafFlow(name="test_build_common_reco_response") \
      .delegate_retrieve(
        kess_service = "grpc_DelegateRetrieveTest",
        recv_common_attrs = ["int_common_attr"],
        recv_item_attrs = ["ctr", "int_item_attr"],
      ) \
      .gen_common_attr_by_lua(
        attr_map={
          "build_limit": "3",
        }
      ) \
      .build_common_reco_response(
        common_attrs=["int_common_attr"],
        item_attrs=["ctr", "int_item_attr"],
        to_attr="response_str",
        include_return_attrs_in_request=True,
        item_num="{{build_limit}}"
      ) \
      .truncate(size_limit=0) \
      .retrieve_by_common_reco_response(
        response_attr="response_str",
        recv_item_attrs=["ctr", "int_item_attr"],
        recv_common_attrs=["int_common_attr"]
      )

    leaf = self.__init_service(flow)
    leaf.run("test_build_common_reco_response")

    self.assertEqual(leaf["int_common_attr"], common_attr["int_value"])
    self.assertEqual(len(leaf.items), 3)
    for i in range(len(leaf.items)):
      item = leaf.items[i]
      self.assertEqual(item.item_id, item_id[i])
      self.assertEqual(item.item_type, item_type[i])
      self.assertEqual(item["int_item_attr"], int_list[i])
      self.assertAlmostEqual(item["ctr"], double_list[i], delta=1e-6)


  # userinfo 不好做单测，暂时跳过
  @unittest.skip("skip ut for local index")
  def test_fetch_user_info(self):
    pass

  # idmapping 不好做单测，暂时跳过
  @unittest.skip("skip ut for abtest_mapping_id")
  def test_supplement_abtest_mapping_id(self):
    pass

  def test_reverse_item(self):
    flow = LeafFlow(name="test_reverse_item") \
    .reverse_item()

    leaf = self.__init_service(flow)
    for i in reversed(range(5)):
      item = leaf.add_item(i)
    leaf.run("test_reverse_item")
    self.assertEqual(len(leaf.items), 5)
    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, index)

  def test_pipeline_loop(self):
    flow = LeafFlow(name="test_pipeline_for_loop_1", loop_on="id_list", 
                    loop_index="id_index", loop_value="id", loop_limit=2) \
      .enrich_attr_by_lua(
        import_common_attr = ["id", "id_index", "result"],
        export_common_attr = ["result"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            local id_with_index = tostring(id) .. "@" .. tostring(id_index) .. "|"
            local result = (result or "")
            return id_with_index .. result
          end
        """,
        debug_log = True
      )

    leaf = self.__init_service(flow)
    leaf["id_list"] = [1, 2, 3]
    leaf.run("test_pipeline_for_loop_1")
    self.assertEqual(leaf["result"], "2@1|1@0|")

    flow = LeafFlow(name="test_pipeline_for_loop_2", loop_on="id_list", 
                    loop_index="id_index", loop_value="id", loop_if="break_signal") \
      .enrich_attr_by_lua(
        import_common_attr = ["id", "id_index", "result"],
        export_common_attr = ["result", "break_signal"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            local id_with_index = tostring(id) .. "@" .. tostring(id_index) .. "|"
            local result = (result or "")
            return id_with_index .. result, id_index < 1
          end
        """,
        debug_log = True
      )
    leaf = self.__init_service(flow)
    leaf["id_list"] = [1, 2, 3]
    leaf.run("test_pipeline_for_loop_2")
    self.assertEqual(leaf["result"], "2@1|1@0|")

    flow = LeafFlow(name="test_pipeline_do_while_1", loop_limit = 3, loop_if="break_signal") \
      .enrich_attr_by_lua(
        import_common_attr = ["id_list", "id_index", "result"],
        export_common_attr = ["result", "break_signal", "id_index"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            local id_index = (id_index or 0) + 1
            local id = id_list[id_index]
            local id_with_index = tostring(id) .. "@" .. tostring(id_index) .. "|"
            local result = (result or "")
            return id_with_index .. result, id_index < 2, id_index
          end
        """,
        debug_log = True
      )
    leaf = self.__init_service(flow)
    leaf["id_list"] = [1, 2, 3]
    leaf.run("test_pipeline_do_while_1")
    self.assertEqual(leaf["result"], "2@2|1@1|")
    

  def test_random_stable_shuffle(self):
    flow = LeafFlow(name="test_random_stable_shuffle")
    flow.random_stable_shuffle(
      shuffle_by_reason=True
    )
    leaf = self.__init_service(flow)
    before_map = {}
    after_map = {}
    before_key_list = []
    after_key_list = []
    for i in range(10000):
      item = leaf.add_item(i, int(i/10))
    for item in leaf.items:
      if item.reason not in before_map:
        before_map[item.reason] = []
      before_map[item.reason].append(item.item_key)
      before_key_list.append(item.item_key)
    leaf.run("test_random_stable_shuffle")
    for item in leaf.items:
      if item.reason not in after_map:
        after_map[item.reason] = []
      after_map[item.reason].append(item.item_key)
      after_key_list.append(item.item_key)
    self.assertNotEqual(before_key_list, after_key_list)
    self.assertEqual(before_map, after_map)

  def test_get_author_exp_params(self):
    flow = LeafFlow(name="test_author_exp_params_integer") \
      .get_author_exp_params(
        author_tail = "climb_retrieval_author_tail",
        author_exp_params = [
          {
            "param_name": "invalid_attr_name",
            "attr_name": "author_exp_string_ab_param",
            "attr_type": 'string',
            "default_value": "test"
          },
          {
            "param_name": "invalid_attr_name",
            "attr_name": "author_exp_double_ab_param",
            "attr_type": 'double',
            "default_value": -1.234
          },
          {
            "param_name": "invalid_attr_name",
            "attr_name": "author_exp_bool_ab_param",
            "attr_type": 'bool',
            "default_value": False
          },
          {
            "param_name": "invalid_attr_name",
            "attr_name": "author_exp_int_ab_param",
            "attr_type": 'int',
            "default_value": -10086
          }
        ])
    leaf = self.__init_service(flow)
    for i in range(5):
      item = leaf.add_item(i)
      item["author_tail"] = i
    leaf.run("test_author_exp_params_integer")
    for index, item in enumerate(leaf.items):
      self.assertEqual(-10086, item["author_exp_int_ab_param"])
      self.assertEqual(-1.234, item["author_exp_double_ab_param"])
      self.assertEqual("test", item["author_exp_string_ab_param"])
      self.assertEqual(False, item["author_exp_bool_ab_param"])

  # photo count 单测暂时跳过
  @unittest.skip("skip ut for counter")
  def test_get_rodis_counter(self):
    pass

  # enrich_attr_by_py 不好做单测，暂时跳过
  @unittest.skip("skip ut for enrich_attr_by_py")
  def test_enrich_attr_by_py(self):
    pass
    # flow = LeafFlow(name = "test_enrich_attr_by_py") \
    #   .enrich_attr_by_py(
    #     function_set = ,
    #     py_function = ,
    #   )

  def test_get_rodis_value_no_proxy(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
        "item": [{
            "data": encode_bytes(b"test"),
        }],
    }
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str=json.dumps(resp))

    flow = LeafFlow(name="test_get_rodis_value") \
      .get_rodis_value(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        key_attr = "key",
        value_attr = "value",
      )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536

    leaf.run("test_get_rodis_value")

    self.assertEqual(leaf["value"], "test")
  
  def test_get_rodis_value(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
        "item": [{
            "data": encode_bytes(b"test"),
        }],
    }
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str=json.dumps(resp))

    flow = LeafFlow(name="test_get_rodis_value") \
      .get_rodis_value(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        key_attr = "key",
        value_attr = "value",
      )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536

    leaf.run("test_get_rodis_value")

    self.assertEqual(leaf["value"], "test")
  
  def test_get_rodis_timelist(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
        "item": [{
            "data": encode_bytes(b"test"),
        }],
    }
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str=json.dumps(resp))

    flow = LeafFlow(name="test_get_rodis_timelist") \
      .get_rodis_timelist(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        key_attr = "key",
        value_attr = "value",
        len_limit=1,
        after_ts=0,
        before_ts=0,
        min_len=0,
      )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536

    leaf.run("test_get_rodis_timelist")

    self.assertEqual(leaf["value"], ["test"])

  def test_write_to_rodis(self):
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str='{}')

    flow = LeafFlow(name="test_write_to_rodis") \
      .write_to_rodis(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        key_attr = "key",
        value_attr = "value",
      )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536
    leaf["value"] = "test"

    leaf.run("test_write_to_rodis")

  def test_item_attr_missing_rate(self):
    flow = LeafFlow(name="test_item_attr_missing_rate") \
      .item_attr_missing_rate(
        check = [
        {"item_attr": "cnt", "save_rate_to": "out_common",}]
      ) \

    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      if i % 3:
        item["cnt"] = 1
    leaf.run("test_item_attr_missing_rate")
    self.assertEqual(leaf["out_common"], 0.4)

  def test_reco_encrypted_id_enrich(self):
    flow = LeafFlow(name="test_reco_encrypted_id_enrich") \
      .encrypted_id(
        is_common_attr = True,
        mode = "decrypt_photo_id",
        input_attr = "test_pid_list",
        output_attr = "test_decrypted_pid_list"
      ) \
      .encrypted_id(
        is_common_attr = True,
        reset_errno = True,
        mode = "decrypt_photo_id",
        input_attr = "test_pid_list",
        output_attr = "test_decrypted_pid_list_fixed"
      )
    leaf = self.__init_service(flow)
    leaf["test_pid_list"] = ["1235218977651507413259", "5218977651507413259"]
    leaf.run("test_reco_encrypted_id_enrich")
    self.assertEqual([0, 150107200844438], leaf["test_decrypted_pid_list_fixed"])
    self.assertEqual([0, 0], leaf["test_decrypted_pid_list"])

  @unittest.skip("skip ut for streaming_enrich")
  def test_streaming_enrich(self):
    pass

  @unittest.skip("skip ut for streaming_status")
  def test_streaming_status(self):
    pass

  @unittest.skip("skip ut for send_streaming_response")
  def test_send_streaming_response(self):
    pass

  @unittest.skip("skip ut for lookup_kconf")
  def test_lookup_kconf(self):
    pass
  
  def test_write_to_clotho(self):
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="reco-rodis-gateway-test-clotho", response_json_str='{}')

    flow = LeafFlow(name="test_write_to_clotho") \
      .write_to_clotho(
      kess_name = "reco-rodis-gateway-test-clotho",
      table_name = "clotho_rodis_test",
      token_name = "test_client",
      kconf_key="reco.rodisV1.clotho_rodis_test",
      timeout_ms=100,
      key_attr="key",
      value_attr=[{"from": "value", "to": "kv_1"}],
    )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536
    leaf["value"] = "test"

    leaf.run("test_write_to_clotho")
  
  @unittest.skip("skip ut for get_clotho_value for client doesn't support kess")
  def test_get_clotho_value(self):
    rpc_mocker = self.__service.rpc_mocker()
    resp = {
        "item": [{
            "data": encode_bytes(b"test"),
        }],
    }
    rpc_mocker.mock_rpc_response(service_name="reco-rodis-gateway-test-clotho", response_json_str=json.dumps(resp))

    flow = LeafFlow(name="test_get_clotho_value") \
      .get_clotho_value(
        kess_name = "reco-rodis-gateway-test-clotho",
        table_name = "clotho_rodis_test",
        token_name = "test_client",
        kconf_key="reco.rodisV1.clotho_rodis_test",
        key_attr="key",
        value_attr=[{"from":"kv_1","to":"value"}],
        timeout_ms=200,
      )
    leaf = self.__init_service(flow)

    leaf["key"] = 65536

    leaf.run("test_get_clotho_value")

    self.assertEqual(leaf["value"], "test")

  def test_find_value(self):
    flow = LeafFlow(name="test_find_value") \
      .find_value(
        input = "{{int_list}}",
        value = "{{int_a}}",
        result = "is_found_int_a"
      ) \
      .find_value(
        input = "{{int_list}}",
        value = "{{int_a}}",
        result = "is_found_int_a",
        output_index = "is_found_int_a_index"
      ) \
      .find_value(
        input = "{{int_list}}",
        value = "{{int_b}}",
        result = "is_found_int_b"
      ) \
      .find_value(
        input = "{{int_list}}",
        value = "not_a_int",
        result = "is_found_int_c"
      ) \
      .find_value(
        input = "{{int_list}}",
        value = "{{int_a}}",
        count = True,
        result = "is_found_int_d"
      ) \
      .find_value(
        input = "{{int_list}}",
        value = "{{int_a}}",
        count = True,
        result = "is_found_int_d",
        output_index = "is_found_int_d_index"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = "{{str_a}}",
        result = "is_found_str_a"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = "{{str_a}}",
        result = "is_found_str_a",
        output_index = "is_found_str_a_index"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = "{{str_b}}",
        result = "is_found_str_b"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = 0,
        result = "is_found_str_c"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = "{{str_a}}",
        count = True,
        result = "is_found_str_d"
      ) \
      .find_value(
        input = "{{str_list}}",
        value = "{{str_a}}",
        count = True,
        result = "is_found_str_d",
        output_index = "is_found_str_d_index"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "{{double_a}}",
        result = "is_found_double_a"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "{{double_a}}",
        result = "is_found_double_a",
        output_index = "is_found_double_a_index"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "{{double_b}}",
        result = "is_found_double_b"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "not_a_double",
        result = "is_found_double_c"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "{{double_a}}",
        count = True,
        result = "is_found_double_d"
      ) \
      .find_value(
        input = "{{double_list}}",
        value = "{{double_a}}",
        count = True,
        result = "is_found_double_d",
        output_index = "is_found_double_d_index"
      ) \

    leaf = self.__init_service(flow)
    leaf["int_list"] = [1, 1, 2, 3, 4, 5]
    leaf["int_a"] = 1
    leaf["int_b"] = 10
    leaf["str_list"] = ["1", "1", "2", "3", "4", "5"]
    leaf["str_a"] = "1"
    leaf["str_b"] = "10"
    leaf["double_list"] = [1.1, 1.1, 2.1, 3.1, 4.1, 5.1]
    leaf["double_a"] = 1.1
    leaf["double_b"] = 10.1
    leaf.run("test_find_value")
    self.assertEqual(leaf["is_found_int_a"], 1)
    self.assertEqual(leaf["is_found_int_a_index"], 0)
    self.assertEqual(leaf["is_found_int_b"], 0)
    self.assertEqual(leaf["is_found_int_c"], 0)
    self.assertEqual(leaf["is_found_int_d"], 2)
    self.assertEqual(leaf["is_found_int_d_index"], [0, 1])
    self.assertEqual(leaf["is_found_str_a"], 1)
    self.assertEqual(leaf["is_found_str_a_index"], 0)
    self.assertEqual(leaf["is_found_str_b"], 0)
    self.assertEqual(leaf["is_found_str_c"], 0)
    self.assertEqual(leaf["is_found_str_d"], 2)
    self.assertEqual(leaf["is_found_str_d_index"], [0, 1])
    self.assertEqual(leaf["is_found_double_a"], 1)
    self.assertEqual(leaf["is_found_double_a_index"], 0)
    self.assertEqual(leaf["is_found_double_b"], 0)
    self.assertEqual(leaf["is_found_double_c"], 0)
    self.assertEqual(leaf["is_found_double_d"], 2)
    self.assertEqual(leaf["is_found_double_d_index"], [0, 1])

  @unittest.skip("skip ut for test_get_geoinfo for machine does't have geohash agent")
  def test_get_geoinfo(self):
    pass

    flow = LeafFlow(name="test_get_geoinfo") \
      .get_geoinfo(
           lat_attr = "lat_attr",
           lon_attr = "lon_attr",
           save_bdcode_to_attr = "bdcode",
           save_adcode_to_attr = "adcode",
           save_nation_to_attr = "nation",
           save_province_to_attr = "province",
           save_city_to_attr = "city",
           save_county_to_attr = "county",
           save_bdname_to_attr = "bdname"
       ) \
       .get_geoinfo(
           is_common_attr = False,
           lat_attr = "lat_attr",
           lon_attr = "lon_attr",
           save_bdcode_to_attr = "bdcode",
           save_adcode_to_attr = "adcode",
           save_nation_to_attr = "nation",
           save_province_to_attr = "province",
           save_city_to_attr = "city",
           save_county_to_attr = "county",
           save_bdname_to_attr = "bdname"
       )
  
    leaf = self.__init_service(flow)

    leaf["lat_attr"] = 40.053281
    leaf["lon_attr"] = 116.31185

    item = leaf.add_item(1)
    item["lat_attr"] = leaf["lat_attr"]
    item["lon_attr"] = leaf["lon_attr"]

    leaf.run("test_get_geoinfo")

    self.assertEqual(leaf["bdcode"], 1101080001)
    self.assertEqual(leaf["adcode"], 110108)
    self.assertEqual(leaf["nation"], "中国")
    self.assertEqual(leaf["province"], "北京")
    self.assertEqual(leaf["city"], "北京")
    self.assertEqual(leaf["county"], "海淀区")
    self.assertEqual(leaf["bdname"], "西二旗")
 
    self.assertEqual(item["bdcode"], 1101080001)
    self.assertEqual(item["adcode"], 110108)
    self.assertEqual(item["nation"], "中国")
    self.assertEqual(item["province"], "北京")
    self.assertEqual(item["city"], "北京")
    self.assertEqual(item["county"], "海淀区")
    self.assertEqual(item["bdname"], "西二旗")

  def test_rodis_unique_list_enricher(self):
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str='{}')

    flow = LeafFlow(name="test_rodis_unique_list_enricher") \
      .rodis_unique_list_enricher(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 2,
        timeout_ms = 200,
        key_attr = "key_attr",
        unique_key_attr = "result_unique_key_attr",
        sort_key_attr = "result_sort_key_attr",
        data_attr = "result_data_attr"
      )
    leaf = self.__init_service(flow)
    leaf["key_attr"] = "test_key"
    leaf.run("test_rodis_unique_list_enricher")

  def test_rodis_unique_list_observer(self):
    unique_key_list = ["pid1", "pid2", "pid3"]
    sort_key_list = ["1", "2", "3"]
    data_list = ["1,2,3", "2,3,4", "3,4,5"]
    
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_rodisCamelCase", response_json_str='{}')

    flow = LeafFlow(name="test_rodis_unique_list_observer") \
      .rodis_unique_list_observer(
        kess_name = "grpc_rodisCamelCase",
        domain = "TEST_DOMAIN",
        payload_id = 2,
        timeout_ms = 200,
        key_attr = "key_attr",
        unique_key_attr = "unique_key_attr",
        sort_key_attr = "sort_key_attr",
        data_attr = "data_attr"
      )
    leaf = self.__init_service(flow)
    leaf["key"] = "test_key"
    leaf["unique_key_attr"] = unique_key_list
    leaf["sort_key_attr"] = sort_key_list
    leaf["data_attr"] = data_list
    leaf.run("test_rodis_unique_list_observer")

  # 不方便测试 http
  def test_enrich_common_attr_by_http(self):
    pass

  def test_dot_product(self):
    flow = LeafFlow(name="test_dot_product") \
      .dot_product(
        x_attr = "x_attr_int_list",
        y_attr = "y_attr_int_list",
        y_is_common_attr = True,
        save_result_to = "result1"
      ) \
      .dot_product(
        x_attr = "x_attr_int_list",
        y_attr = "y_attr_double_list",
        y_is_common_attr = True,
        save_result_to = "result2"
      ) \
      .dot_product(
        x_attr = "x_attr_double_list",
        y_attr = "y_attr_int_list",
        y_is_common_attr = True,
        save_result_to = "result3"
      ) \
      .dot_product(
        x_attr = "x_attr_double_list",
        y_attr = "y_attr_double_list",
        y_is_common_attr = True,
        save_result_to = "result4"
      ) \
      .dot_product(
        x_attr = "x_attr_int_list",
        y_attr = "y_attr_int_list",
        save_result_to = "result5"
      ) \
      .dot_product(
        x_attr = "x_attr_int_list",
        y_attr = "y_attr_double_list",
        save_result_to = "result6"
      ) \
      .dot_product(
        x_attr = "x_attr_double_list",
        y_attr = "y_attr_int_list",
        save_result_to = "result7"
      ) \
      .dot_product(
        x_attr = "x_attr_double_list",
        y_attr = "y_attr_double_list",
        save_result_to = "result8"
      )

    leaf = self.__init_service(flow)
    leaf["x_attr_int_list"] = [1, 2, 3, 4, 5]
    leaf["x_attr_double_list"] = [1.1, 1.2, 0.3, 1.5, 2.3]
    leaf["y_attr_int_list"] = [1, 2, 3, 4, 5]
    leaf["y_attr_double_list"] = [1.1, 1.2, 0.3, 1.5, 2.3]
    leaf.run("test_dot_product")
    self.assertEqual(leaf["result1"], 55)
    self.assertAlmostEqual(leaf["result2"], 21.9)
    self.assertAlmostEqual(leaf["result3"], 21.9)

    leaf = self.__init_service(flow)
    leaf["x_attr_int_list"] = [1, 2, 3, 4, 5]
    leaf["x_attr_double_list"] = [1.1, 1.2, 0.3, 1.5, 2.3]
    leaf["y_attr_int_list"] = [1, 2, 3, 4, 5]
    leaf["y_attr_double_list"] = [1.1, 1.2, 0.3, 1.5, 2.3]

    item = leaf.add_item(1)
    item["y_attr_int_list"] = [1, 2, 3, 4, 5]
    item["y_attr_double_list"] = [1.1, 1.2, 0.3, 1.5, 2.3]

    leaf.run("test_dot_product")
    self.assertEqual(leaf["result1"], 55)
    self.assertAlmostEqual(leaf["result2"], 21.9)
    self.assertAlmostEqual(leaf["result3"], 21.9)
    self.assertAlmostEqual(leaf["result4"], 10.28)
    self.assertEqual(item["result5"], 55)
    self.assertAlmostEqual(item["result6"], 21.9)
    self.assertAlmostEqual(item["result7"], 21.9)
    self.assertAlmostEqual(item["result8"], 10.28)

  # 涉及 http 请求
  @unittest.skip("skip ut for retrieve_by_new_elastic_search")
  def test_retrieve_by_new_elastic_search(self):
    pass

  # 涉及 http 请求
  @unittest.skip("skip ut for enrich_by_elastic_search")
  def test_enrich_by_elastic_search(self):
    pass

if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('common_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
