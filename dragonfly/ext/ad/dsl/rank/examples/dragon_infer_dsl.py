###--------------InferServer Dragon DSL 生成脚本-----------------------###
### DSL教学文档: https://docs.corp.kuaishou.com/d/home/<USER>
import os
import sys
import json
import yaml
import argparse
import base64
import collections

import dragonfly.ext.ad.dsl.rank.model_config_util as util
from dragonfly.ext.ad.dsl.rank.model_config_util import WARNING_LOG
from dragonfly.ext.ad.dsl.rank.model_config_util import EXIT_LOG
from dragonfly.ext.ad.dsl.rank.model_config_util import INFO_LOG
from dragonfly.ext.ad.ad_api_mixin import AdApiMixin
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.gsu.gsu_api_mixin import GsuApiMixin
from dragonfly.ext.cofea.cofea_api_mixin import CofeaApiMixin
from dragonfly.ext.offline.offline_api_mixin import OfflineApiMixin
from dragonfly.ext.kuiba.kuiba_api_mixin import KuibaApiMixin
from dragonfly.ext.mio.mio_api_mixin import MioApiMixin
from dragonfly.ext.ad_union_train.ad_union_train_api_mixin import AdUnionTrainApiMixin
from dragonfly.ext.embed_calc.embed_calc_api_mixin import EmbedCalcApiMixin
from dragonfly.ext.uni_predict.uni_predict_api_mixin import UniPredictApiMixin

"""
################ 算法同学根据自身需求修改下方配置 #####################
### 1. Dragon PipeLine 配置, Infer目前预估必要Processor包括 ad_infer_feature、ad_uni_predict_fused、ad_infer_result_v2 三个 Processor，如果需要其他Processor的自行添加，无特殊需求无需改动
#    a. Dragon 入门教程: https://docs.corp.kuaishou.com/d/home/<USER>
#    b. Processor 相关介绍或Dragon框架问题: https://dragonfly.corp.kuaishou.com/#/
### 2. 非Dragon配置，如模型的特殊配置(embedding_min_fill_ratio、max_batching_size)，在 add_leaf_flows 时写在 extra_fields 中, 如果检查发现和默认配置冲突的，将会报错
"""
### 阶段一: 指定自己的dragon仓库路径, XXXXX/dragon 为 Dragon 仓库路径
# 添加 Dragon 环境变量 export PYTHONPATH=XXXXX/dragon:$PYTHONPATH

### 阶段二: 修改模型各自定制化配置 (Demo: 直播 GMV)
embedding_min_fill_ratio = 0.01
model_btq_topic = "live_roi_ln_single_roi_atv_model"  # 模型btq配置
model_outputs = [ "gmv_all", "gmv_front", "gmv_end", "gmv_indirect"]   # 模型输出
base_model_kconf_path = "ad.adPsCloudModels.ad_dsp_sim_gmv_roi_atv_model_v3_old"  # 历史模型kconf配置, 若无则需置为空
model_deploy_info = {  # 模型 hdfs 配置
    "hdfs_root": "/home/<USER>/big_model_rollback/live_roi_ln_single_roi_atv_model", # 模型hdfs路径
    "model_feature": "real_model_feature", # 模型特征文件名
    "model_meta": "real_model.meta", # 模型meta文件名
    "dragon_feature_config" : "dragon_feature_config.json",  # 仅在模型有 dragon 特征时需要配置
    "model_btq_topic": model_btq_topic # 模型btq配置
}
model_config = util.GetInferDefaultConfig(model_deploy_info)  # 获取默认配置

### 阶段三: 定义 predict pipeline(必须), 各 Processor 配置+使用方法参考点击链接搜索
#   https://dragonfly.corp.kuaishou.com/#/wiki/development_guide?id=commonrecorequest-proto-%e5%ae%9a%e4%b9%89
class PredictServerFlow(
    LeafFlow, AdApiMixin, UniPredictApiMixin,
    MioApiMixin, OfflineApiMixin, GsuApiMixin,
    CofeaApiMixin,
    EmbedCalcApiMixin,
    AdUnionTrainApiMixin,
):
    def predict_ad_model(self, **kwargs):
        rowmajor = True
        receive_dnn_model_as_macro_block = True
        close_optimization = False
        outputs = kwargs.pop("outputs", [])
        model_info = kwargs.pop("model_info", {})
        model_config = kwargs.pop("model_config", {})
        btq_topic = kwargs.pop("btq_topic", "")
        outputs = [{"tensor_name": output, "attr_name": output} for output in outputs]
        assert "model_meta" in model_info, "`model_info::model_meta` is required"
        assert "model_feature" in model_info, "`model_info::model_feature` is required"
        assert "hdfs_root" in model_info, "`model_info::hdfs_root` is required"
        executor_batchsizes = [256, 512, 1024]
        self.ad_infer_feature().ad_uni_predict_fused(
            optimizers=["KaiTFUserItemSplit"],
            idt_config={
                "use_common_idt_service": model_config["use_common_idt_service"],
            },
            batching_config={
                "batch_timeout_micros": 10000,
                "max_batch_size": max(executor_batchsizes),
                "max_enqueued_batches": 64,
            },
            executor_config={
                "context_per_device": 10,
            },
            dragon_feature_config=model_config["dragon_feature_config"],
            hdfs_root=model_info["hdfs_root"],
            ps_root=util.GetModelDirPath(model_info["hdfs_root"]),
            model_meta_path=util.GetModelPathInfo(model_info, "model_meta"),
            feature_path=util.GetModelPathInfo(model_info, "model_feature"),
            queue_prefix=btq_topic,
            key=btq_topic,
            rowmajor=rowmajor,
            model_loader_config={
                "enable_fp16": False,
                "force_input_tensor_fp32": True,
                "dynamic_shape": False,
                "dense_format": "kai",
                "receive_dnn_model_as_macro_block": receive_dnn_model_as_macro_block,
                "rowmajor": True,
                "executor_batchsizes": executor_batchsizes,
                "type": "KaiTFExecutedByTensorRTModelLoader",
            },
            outputs=outputs,
            use_bs_fast_feature=model_config.get("use_bs_fast_feature", False),
            emp_service=model_config["emp_service"],
            embedding_storage_config={
                "embedding_node_prefix": model_config.get(
                    "embedding_node_prefix", "embedding"
                ),
                "remove_sign_prefix": model_config.get("remove_sign_prefix", False),
                "kai_nohash_model": model_config.get("kai_nohash_model", False),
                "remap_slot_sign_feature": model_config.get(
                    "remap_slot_sign_feature", {}
                ),
                "embedding_capcity_gb": model_config.get("embedding_capcity_gb", 128),
                "embedding_cache_type": model_config.get("embedding_cache_type", "shm"),
                "embedding_min_fill_ratio": embedding_min_fill_ratio,
            },
            embedding_fetcher_config={
                "in_fea_org_layout": model_config.get("in_fea_org_layout", 1),
                "reordering_feature": model_config.get("reordering_feature", False),
                "exponent_per_bucket": model_config.get("exponent_per_bucket", 12),
                "weights_in_tensor": model_config.get("weights_in_tensor", False),
                "combine_type": model_config.get("combine_type", 0),
            },
            deploy=model_info,
            btq_reader_config={
                "btq_model_reader_thread_num": model_config.get(
                    "btq_model_reader_thread_num", 3
                ),
                "btq_model_read_qps": model_config.get("btq_model_read_qps", 100),
                "btq_model_worker_num": model_config.get("btq_model_worker_num", 8),
            },
            use_idt=model_config.get("use_idt", True),
            instant_rollback=model_config.get("instant_rollback", True),
            grappler_transform_params=model_config.get("grappler_transform_params", "pruning,constfold,arithmetic,dependency"),
        ).ad_infer_result_v2(
            enable_model_validate=True,
            output_value_width=1,
            predict_value_num=model_config.get("predict_value_num", 1),
            neg_sample_rate=1.0,
            is_negative_valid=False,
            outputs=outputs,
            use_common_idt_service=True,
            dragon_log_rate=1000,
            key=btq_topic,
            # 该 Processor 支持定制化 idt 配置
        )
        return self

### 阶段四: 生成PipeLine配置, 模型root层级特殊配置写在 extra_fields 里，其他无需修改
predict = PredictServerFlow(name="predict").predict_ad_model(
    model_config=model_config, outputs=model_outputs, model_info=model_deploy_info, btq_topic=model_btq_topic
)
idt_pipeline = PredictServerFlow(name="common_idt").common_idt()
kscale_pipeline = PredictServerFlow(name="kscale").kscale()
service = LeafService(
    kess_name="grpc_test_infer_server", # 无需改动, KessName 以模型部署时 KaiServing 页面为准
    item_attrs_from_request=["item_miss"],
    common_attrs_from_request=["cmdkey_end_pos", "cmdkey_list", "serialized_features"],
)
# 预估 pipeline 返回的 item attr
service.return_item_attrs(["predict_value", "result_stat"])
service.add_leaf_flows(
    leaf_flows=[predict, kscale_pipeline, idt_pipeline], request_type="predict"
).draw() 
tmp_file = os.path.abspath(__file__).replace(".py", "_tmp.json")
service.build(
    output_file=tmp_file,
    extra_fields={
        "request_type_config": {"predict": ["predict"], "prefetch": ["prefetch"]},
        "output_op_names": model_outputs,
        "enable_local_feature":False,
        "embedding_min_fill_ratio": 0.01,
        "model_max_lag": 7200,
        "kai_model": True,
        "btq_queue_name": model_btq_topic
    },
)

################ 算法同学根据自身需求修改上方配置 #####################

################ 下方为基础配置, 请勿擅动 #####################
# 生成 AD_GPU_DRAGON_PREDICT_SERVER 配置
output_infer_file = os.path.abspath(__file__).replace(".py", ".json")
util.MergeGenerateFile(
    "",
    output_infer_file,
    "INFER",
    tmp_file,
    model_config,
    model_deploy_info,
    base_model_kconf_path
)

os.unlink(tmp_file)
