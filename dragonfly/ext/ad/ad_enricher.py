#!/usr/bin/env python3
"""
filename: ad_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for ad
author: <PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-09-19 13:21:00
"""

from os import path as osp
from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel, extract_attr_names, check_arg
from ...common_leaf_processor import LeafEnricher
from .dsl.rank.model_config_util import PROJECT_SYNC_SERVER_ROOT

class AdFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_feature_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["input_common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["output_common_attrs"])
    return attrs

class AdLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_label_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["input_common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["output_common_attrs"])
    return attrs

class AdCommonPicassoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_picasso_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "input_attr" in self._config:
      attrs.add(self._config.get('input_attr'))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    assert "output_attr" in self._config, f"缺少 picasso 服务特征抽取的输出 attr 配置"
    attrs.add(self._config.get('output_attr'))
    return attrs

class AdFullGoodsGsuSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_full_goods_gsu_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(['picasso_resp_attr','target_aid_attr'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(['output_attr'])
    return attrs

class AdNewFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["input_common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["output_common_attrs"])
    for k in ["success_flag_attr_var", "time_attr"]:
      if k in self._config:
        attrs.add(self._config[k])
    return attrs

class AdTransEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_trans_cofea"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["input_common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class OfflineBucketJoinEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bucket_join"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    for key in ["save_begin_time_ms_to_common_attr", "save_end_time_ms_to_common_attr"]:
      if key in self._config and self._config[key]:
        ret.add(self._config[key])
    return ret

class FusedGsuWithIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fused_gsu_with_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_pid_attr"])
    if "sorted_item_idx_attr" in self._config:
      ret.add(self._config["sorted_item_idx_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "author_id_attr" in self._config:
      ret.add(self._config["author_id_attr"])
    if "tag_attr" in self._config:
      ret.add(self._config["tag_attr"])
    if "play_time_attr" in self._config:
      ret.add(self._config["play_time_attr"])
    if "duration_attr" in self._config:
      ret.add(self._config["duration_attr"])
    if "timestamp_attr" in self._config:
      ret.add(self._config["timestamp_attr"])
    if "channel_attr" in self._config:
      ret.add(self._config["channel_attr"])
    if "label_attr" in self._config:
      ret.add(self._config["label_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "output_sign_attr" in self._config:
      ret.add(self._config["output_sign_attr"])
    if "output_slot_attr" in self._config:
      ret.add(self._config["output_slot_attr"])
    if "slot_as_attr_name_prefix" in self._config:
      ret.add(self._config["slot_as_attr_name_prefix"])
    if "output_item_colossus_pid_attr" in self._config:
      ret.add(self._config["output_item_colossus_pid_attr"])
    return ret

class FusedGsuWithIndexV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fused_gsu_with_index_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr", "user_lon_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret


class OfflineSimExtractorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attr"):
      attrs.add(self._config.get("input_common_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_common_attr"]])
    return ret    

class OfflineDumpSampleInterfaceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_sample_interface"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("sample_from_attr"):
      attrs.add(self._config.get("sample_from_attr"))
    return attrs     

class AdLiveSegmentGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_segment_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveSegmentGsuU2uEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_segment_gsu_u2u"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    fix_new_product_category_option = self._config.get("fix_new_product_category_option" , False)
    if not fix_new_product_category_option:
      if "enable_new_product_category" in self._config:
        ret.add(self._config["enable_new_product_category"])
      else:
        ret.add("enable_new_product_category")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "target_aids_attr" in self._config:
      ret.add(self._config["target_aids_attr"])
    else:
      ret.add("aid")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveV1GoodsClickSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_v1_goods_click_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_goods_id_attr", "item_id_list"))
    ret.add(self._config.get("colossus_timestamp_attr", "click_timestamp_list"))
    ret.add(self._config.get("colossus_category_attr", "category_a_list"))
    ret.add(self._config.get("colossus_host_attr", "seller_id_list"))
    ret.add(self._config.get("colossus_shop_attr", "real_seller_id_list"))
    ret.add(self._config.get("colossus_real_price_attr", "real_price_list"))
    ret.add(self._config.get("click_flow_type_attr", "click_flow_type_list"))
    ret.add(self._config.get("click_from_attr", "click_from_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_sign_attr", "ad_live_v1_goods_click_seq_sign"))
    ret.add(self._config.get("output_slot_attr", "ad_live_v1_goods_click_seq_slot"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class AdLiveV2OptGoodsClickSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_v2_goods_click_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_goods_id_attr", "item_id_list"))
    ret.add(self._config.get("colossus_timestamp_attr", "click_timestamp_list"))
    ret.add(self._config.get("colossus_category_attr", "category_a_list"))
    ret.add(self._config.get("colossus_host_attr", "seller_id_list"))
    ret.add(self._config.get("colossus_shop_attr", "real_seller_id_list"))
    ret.add(self._config.get("colossus_real_price_attr", "real_price_list"))
    ret.add(self._config.get("click_flow_type_attr", "click_flow_type_list"))
    ret.add(self._config.get("click_from_attr", "click_from_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("target_aids_attr", "aid"))
    ret.add(self._config.get("explain_item_x7_cat1_attr", "explain_item_x7_cat1"))
    ret.add(self._config.get("author_hist_x7_cat1_list_attr", "author_hist_x7_cat1_list"))
    ret.add(self._config.get("yellow_car_x7_cat1_list_attr", "yellow_car_x7_cat1_list"))
    ret.add(self._config.get("explain_item_x7_cat2_attr", "explain_item_x7_cat2"))
    ret.add(self._config.get("author_hist_x7_cat2_list_attr", "author_hist_x7_cat2_list"))
    ret.add(self._config.get("yellow_car_x7_cat2_list_attr", "yellow_car_x7_cat2_list"))
    ret.add(self._config.get("explain_item_x7_cat3_attr", "explain_item_x7_cat3"))
    ret.add(self._config.get("yellow_car_x7_cat3_list_attr", "yellow_car_x7_cat3_list"))
    ret.add(self._config.get("author_hist_x7_cat3_list_attr", "author_hist_x7_cat3_list"))
    ret.add(self._config.get("target_live_c3_id_list_attr", "goods_c3_id_list"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr", "ad_live_v2_goods_click_seq_slot"))
    ret.add(self._config.get("output_sign_attr", "ad_live_v2_goods_click_seq_sign"))
    return ret

class CommonCommertialLiveGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdLiveGsuAid2AidV2BatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_aid2aid_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
  
class CommonGsuWithRqVaeV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_gsu_with_rq_vae_enricher_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["photo_ids"])
    ret.add(self._config["semantic_ids"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_semantic_id"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_filter_first"])
    ret.add(self._config["output_filter_second"])
    ret.add(self._config["output_filter_third"])
    
    if 'match_cnt_combine' in self._config and isinstance(self._config['match_cnt_combine'], bool) and self._config['match_cnt_combine']:
      ret.add(self._config["output_match_cnt"])
    else:
      ret.add(self._config["output_match_cnt_first"])
      ret.add(self._config["output_match_cnt_second"])
      ret.add(self._config["output_match_cnt_third"])

    return ret

class CommonAdLiveGsuSessionEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "ad_live_gsu_session"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

class CommonAdLiveGsuSessionV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "ad_live_gsu_session_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

class CommonAdLiveUserNegEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "ad_live_user_neg"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

class AdSimItemIndustry2IndustryBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_sim_item_gsu_industry2industry_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdSimItemGsuU2UBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_u2u_batch_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdSimItemGsuU2UBatchDiversityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_u2u_batch_diversity_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdSimItemGsuU2UBatchDiversityWithUserEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_u2u_batch_diversity_with_user_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["similar_user_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdClickItemGsuU2UBatchDiversityWithUserEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adclickitem_gsu_u2u_batch_diversity_with_user_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["similar_user_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
  
class AdSimItemGsuRetrieverWithColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_retriever_with_colossus_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
  
class AdSimItemGsuRetrieverWithColossusV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_retriever_with_colossus_enricher_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("output_photo_list")
    ret.add("output_author_list")
    ret.add("output_account_list")
    ret.add("output_timestamp_list")
    ret.add("output_production_list")
    ret.add("output_package_list")
    ret.add("output_action_list")
    ret.add("output_scene_list")
    ret.add("output_uri_list")
    ret.add("output_industry_list")
    ret.add("output_quality_list")
    ret.add("output_mmu_list")

    return ret

class AdSimItemGsuRetrieverWithIndustryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_retriever_with_industry_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["industry_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithPidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdSimItemGsuU2UBatchIndustryWithUserEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adsimitem_gsu_u2u_batch_industry_with_user_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["industry_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class DumpContextObserver(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "offline_dump_context"        
    return ret

class CommonAdLiveGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "commertial_gsu_with_cluster_tag_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV4Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV2ConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v2_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV2NewConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v2_new_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdGsuWithClusterTagV3ConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v3_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if self._config["save_pid_map"]:
      ret.add(self._config["photo_id_to_colossus_idx_map"])
      ret.add(self._config["photo_id_to_side_info_map"])
    return ret
  
class CommonAdGsuWithPidListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_gsu_with_pid_list_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    if "use_photo_id_to_idx_map" in self._config and self._config["use_photo_id_to_idx_map"]:
      ret.add(self._config["photo_id_to_colossus_idx_map"])
    if "use_sideinfo_cache" in self._config and self._config["use_sideinfo_cache"]:
      ret.add(self._config["photo_id_to_side_info_map"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_pid_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
  
class CommonAdGsuWithRqVaeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "commertial_gsu_with_rq_vae"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["semantic_ids"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_semantic_id"])
    return ret
  
class CommonAdGsuWithClusterDiverseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_diverse_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonAdLiveGsuAid2AidV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_aid2aid_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonP2lColossusGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "p2l_colossus_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterV4Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdP2lGsuWithAidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_p2l_gsu_with_aid_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_cache_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveAidClusterV2ConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_cache_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_cache_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuWithAidClusterCacheV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_aid_cluster_cache_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class LiveGsuWithAidClusterCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_aid_cluster_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonAdLiveGsuWithClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_cluster_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["cluster_id_type"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class AdunionLiveGsuWithClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_gsu_with_cluster_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class AdunionLiveV2GsuWithClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_v2_gsu_with_cluster_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdunionLiveGsuAid2AidV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_gsu_aid2aid_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdunionLiveGsuWithAidClusterCacheV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_gsu_with_aid_cluster_cache_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonCommertialGsuWithAidClusterDspEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_cluster_commertial_DSP"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class OfflineSampleInterfaceTransRawFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sample_interface_trans_raw_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_attr"):
      attrs.add(self._config.get("input_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    return ret

class MessageFilterRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "message_filter_remap"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_attr"):
      attrs.add(self._config.get("input_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    return ret

class FetchRedisLiveProfileByHashKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_live_hash_attr_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["redis_hash_field_from"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_hash_key_from"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_value_to"])
    return attrs

class CommonLiveGsuWithClusterAddAidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_add_aid_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonAdPhotoGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_photo_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class LzcAdGroupedRecoSimSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_grouped_reco_sim_lzc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

class LzcAdGroupedRecoSimSeqV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_grouped_reco_sim_lzc_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    ret.add(self._config["parse_from_pb"])
    return ret

class AdLiveGsuWithClusterV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_with_cluster_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["cluster_id_type"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class AdunionLiveGsuAid2AidV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_gsu_aid2aid_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveGsuAid2AidV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu_aid2aid_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class AdunionLiveV2GsuWithClusterV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_v2_gsu_with_cluster_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
class TowerFetchTopNDotProductAttrGsimEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_pxtr_for_gsim"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = []
    if self._config.get("is_target_embedding_common", True):
      check_attrs.append("target_embedding_attr")
    for key in check_attrs:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    check_attrs = ["item_embedding_list_key_attr"]
    if not self._config.get("is_target_embedding_common", True):
      check_attrs.append("target_embedding_attr")
    for key in check_attrs:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "sorted_item_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
class CommonGoodsSlotMergeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slot_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sign_first_attr"])
    ret.add(self._config["slot_first_attr"])
    ret.add(self._config["sign_sec_attr"])
    ret.add(self._config["slot_sec_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
class CommonGoodsEmbMeanEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "emb_mean"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["emb1_attr"])
    ret.add(self._config["emb2_attr"])
    ret.add(self._config["emb3_attr"])
    ret.add(self._config["emb4_attr"])
    ret.add(self._config["emb5_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret



class AdAppendIntListItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_append_int_list_item_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
class AdGoodsGsuWithCateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_item_cate2_attr"])
    ret.add(self._config["target_item_cate3_attr"])
    ret.add(self._config["target_item_spu_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_id_attr"])
    return ret
class AdAttrIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_attr_index"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_attr"])
    return ret
class AdGoodsGsuWithX7CateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_gsu_with_x7_cate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["n_minute_ago"])
    ret.add(self._config["goods_num_attr"])
    ret.add(self._config["fill_flag"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_item_cate2_attr"])
    ret.add(self._config["target_item_spu_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
class AdGoodsWithActionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_gsu_spu_id_with_action"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_item_cate2_attr"])
    ret.add(self._config["target_item_cate3_attr"])
    ret.add(self._config["target_item_spu_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_id_attr"])
    return ret
class CommonAttrCopyToItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_attr_copy_to_item"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_common_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret
class AdPhoto2LiveFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_photo2live_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["n_minute_ago"])
    ret.add(self._config["use_padding"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
class AdGoodsUserIndexSlotEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_user_index_slot"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["goods_id_list_attr"])
    ret.add(self._config["user_id_list_attr"])
    ret.add(self._config["label_list_attr"])
    ret.add(self._config["sorted_index_list_attr"])
    ret.add(self._config["output_id_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_id_list_attr"])
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdDragonFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_dragon_feature"

  @strict_types
  def _check_config(self) -> None:
    feature_path = self._config.get("feature_path")
    check_arg(feature_path, "feature_path 是必配项")
    check_arg(PROJECT_SYNC_SERVER_ROOT in feature_path, "feature_path 需要配置为全路径")
    check_arg(not self._config.get("model_feature"), "不支持配置 model_feature, 请使用 feature_path")


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("BslogBsItem")
    ret.add("FeatureResult")
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("serialized_features")
    return ret

class AdMergeFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_merge_feature"

  @strict_types
  def _check_config(self) -> None:
    feature_path = self._config.get("feature_path")
    check_arg(feature_path, "feature_path 是必配项")
    check_arg(PROJECT_SYNC_SERVER_ROOT in feature_path, "feature_path 需要配置为全路径")
    check_arg(not self._config.get("model_feature"), "不支持配置 model_feature, 请使用 feature_path")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    bs_log_attr = self._config.get("bs_log_attr", "BslogBsItem")
    ret.add(bs_log_attr)

    infer_feature_list_attr = self._config.get("infer_feature_list_attr", "FeatureResult")
    ret.add(infer_feature_list_attr)

    check_duplicated_item = self._config.get("check_duplicated_item", True)
    if check_duplicated_item:
      duplicated_item_attr_name = self._config.get("duplicated_item_attr", "DragonDuplicatedItem")
      ret.add(duplicated_item_attr_name)

    feature_attr_key_list = {"user_slot_attrs", "user_sign_attrs", "user_dense_attrs"}
    for key in feature_attr_key_list:
      if key not in self._config.keys():
        continue
      for attr in self._config[key]:
        ret.add(attr)
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    feature_attr_key_list = {"item_slot_attrs", "item_sign_attrs", "item_dense_attrs"}
    for key in feature_attr_key_list:
      if key not in self._config.keys():
        continue
      for attr in self._config[key]:
        ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    output_merged_features_attr = self._config.get("serialized_features_attr", "serialized_features")
    ret.add(output_merged_features_attr)
    return ret

class AdExtractorFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_extractor"

  @strict_types
  def _check_config(self) -> None:
    feature_path = self._config.get("feature_path")
    check_arg(feature_path, "feature_path 是必配项")
    check_arg(PROJECT_SYNC_SERVER_ROOT in feature_path, "feature_path 需要配置为全路径")
    # 根据 feature_path 得到 bs_feature_config_map 位置
    bs_feature_config_map = self._config.get("bs_feature_config_map")
    if not bs_feature_config_map:
      model_dir, _ = osp.split(feature_path)
      bs_feature_config_map = osp.join(model_dir, "bs_feature_config_map")
      self._config["bs_feature_config_map"] = bs_feature_config_map
    check_arg(bs_feature_config_map, "bs_feature_config_map 是必配项")
    check_arg(PROJECT_SYNC_SERVER_ROOT in bs_feature_config_map, "bs_feature_config_map 需要配置为全路径")
    check_arg(not self._config.get("model_feature"), "不支持配置 model_feature, 请使用 feature_path")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    input_bslog_attr = self._config.get("input_bslog_attr", "BslogBsItem")
    ret.add(input_bslog_attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    output_features_attr = self._config.get("output_features_attr", "FeatureResult")
    ret.add(output_features_attr)
    return ret

class AdFeatureProcessEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_get_feature_processor"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_bslog_attr", "BslogBsItem"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class AdItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_get_item_info"

  def _check_config(self) -> None:
    use_component_item = self._config.get("use_component_item")
    component_item = self._config.get("component_item")
    if use_component_item is True:
      check_arg(component_item, "use_component_item 为 True 时, component_item 是必配项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_id_info_attr", "item_id_info"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("bs_item_attr", "bs_item"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_miss_attr", "item_miss"))
    return ret

class AdBslogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bslog"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("item_already_ready", False) is True:
      ret.add(self._config.get("bs_item_attr", "bs_item"))
    if self._config.get("generate_bslog", True) is False:
      ret.add("BslogBsItem")
    ret.add("llsid")
    ret.add("flatten_data_map_name")
    ret.add("flatten_data_map_value")
    ret.add("cmd")
    ret.add("tab_type")
    ret.add("bs_debug_raw_data")
    ret.add("attr_real_time_action_name")
    ret.add("attr_real_time_action_value")
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("generate_bslog", True) is True:
      ret.add(self._config.get("bs_log_attr", "BslogBsItem"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("item_already_ready", False) is False:
      ret.add(self._config.get("item_miss_attr", "item_miss"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add("callback_event")
    return ret

class AdBsAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_bslog"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("limit_num_attr", "limit_num"))
    if "attr_keys" in self._config:
      attr_keys = self._config["attr_keys"]
      for _, val in enumerate(attr_keys):
        if val["common"]:
          ret.add(val["attr_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "attr_keys" in self._config:
      attr_keys = self._config["attr_keys"]
      for _, val in enumerate(attr_keys):
        if not val["common"]:
          ret.add(val["attr_name"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("just_set_limit", False) == False:
      input_bslog_attr = self._config.get("input_bslog_attr", "BslogBsItem")
      ret.add(input_bslog_attr)
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

class AdLiveU2uColossusInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_u2u_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["click_as_positive"])
    ret.add(self._config["uid_list"])
    ret.add(self._config["filter_uid_score"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
       if key in self._config:
          ret.add(self._config[key])
    return ret

class CommonLiveV4GsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_cluster_ad_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveColossusLiveidUserFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_liveid_user_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "uLatestHateTimeLagAttr", "uLatestHateLiveIdAttr", "uLatestHateAuthorIdAttr",
      "uLatestHateClusterIdAttr", "uHourHateCntAttr", "uDayHateCntAttr", "uWeekHateCntAttr",
      "uHourHateRateAttr", "uDayHateRateAttr", "uWeekHateRateAttr", "uLatestShortViewTimeLagAttr",
      "uLatestShortViewLiveIdAttr", "uLatestShortViewAuthorIdAttr", "uLatestShortViewClusterIdAttr",
      "uHourShortViewCntAttr", "uDayShortViewCntAttr", "uWeekShortViewCntAttr", "uHourShortViewRateAttr",
      "uDayShortViewRateAttr", "uWeekShortViewRateAttr", "uHourShowCntAttr", "uHourShowNoCLickCntAttr",
      "uHourShowNoClickRateAttr", "uDayShowCntAttr", "uDayShowNoClickCntAttr", "uDayShowNoClickRateAttr",
      "uWeekShowCntAttr", "uWeekShowNoClickCntAttr", "uWeekShowNoClickRateAttr"
    }

class CommonAdLiveColossusUserRfmEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_live_colossus_user_rfm_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "uLatestHateTimeLagAttr", "uLatestHateLiveIdAttr", "uLatestHateAuthorIdAttr",
      "uLatestHateClusterIdAttr", "uHourHateCntAttr", "uDayHateCntAttr", "uWeekHateCntAttr", "uMonthHateCntAttr",
      "uHourHateRateAttr", "uDayHateRateAttr", "uWeekHateRateAttr", "uMonthHateRateAttr", "uLatestShortViewTimeLagAttr",
      "uLatestShortViewLiveIdAttr", "uLatestShortViewAuthorIdAttr", "uLatestShortViewClusterIdAttr",
      "uHourShortViewCntAttr", "uDayShortViewCntAttr", "uWeekShortViewCntAttr", "uMonthShortViewCntAttr", "uHourShortViewRateAttr",
      "uDayShortViewRateAttr", "uWeekShortViewRateAttr", "uMonthShortViewRateAttr", "uHourShowCntAttr", "uHourShowNoCLickCntAttr",
      "uHourShowNoClickRateAttr", "uDayShowCntAttr", "uDayShowNoClickCntAttr", "uDayShowNoClickRateAttr",
      "uWeekShowCntAttr", "uWeekShowNoClickCntAttr", "uWeekShowNoClickRateAttr",
      "uMonthShowCntAttr", "uMonthShowNoClickCntAttr", "uMonthShowNoClickRateAttr"
    }

class CommonLiveV4GsuWithClusterPartEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_cluster_ad_part_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class ColossusSessionFeaturePartEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_session_part_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

class CommonP2lColossusGsuWithPidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_p2l_colossus_gsu_with_pid_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonP2lColossusGsuWithPidClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_p2l_colossus_gsu_with_pid_cluster_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_request_infer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class AdInferFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_infer_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    input_serialized_features_attr = self._config.get("input_serialized_features_attr", "serialized_features")
    ret.add(input_serialized_features_attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    output_features_attr = self._config.get("output_features_attr", "ad_infer_feature_list")
    ret.add(output_features_attr)
    ret.add("ad_infer_request_context")
    ret.add("ad_infer_validate_request")
    return ret

class AdUniPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_infer_model"

class AdUniPredictFusedEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_uni_predict_fused"

  @strict_types
  def _check_config(self) -> None:
    feature_path = self._config.get("feature_path")
    model_meta_path = self._config.get("model_meta_path")
    emp_service = self._config.get("emp_service")
    hdfs_root = self._config.get("hdfs_root")
    ps_root = self._config.get("ps_root")
    model_path = self._config.get("model_path")
    # 没有配置用默认值
    if not model_path:
      model_path = osp.join(ps_root, "model_version")
      self._config["model_path"] = model_path

    check_arg(feature_path, "feature_path 是必配项")
    check_arg(model_meta_path, "model_meta_path 是必配项")
    check_arg(emp_service, "emp_service 是必配项")
    check_arg(hdfs_root, "hdfs_root 是必配项")
    check_arg(ps_root, "ps_root 是必配项")
    check_arg(model_path, "model_path 是必配项")

    check_arg(PROJECT_SYNC_SERVER_ROOT in feature_path, "feature_path 需要配置为全路径")
    check_arg(PROJECT_SYNC_SERVER_ROOT in model_meta_path, "model_meta_path 需要配置为全路径")
    check_arg(PROJECT_SYNC_SERVER_ROOT in ps_root, "ps_root 配置不正确")
    check_arg(PROJECT_SYNC_SERVER_ROOT in model_path, "model_path 配置不正确")
    model_dir = osp.commonpath([feature_path, model_meta_path, ps_root, model_path])
    check_arg(PROJECT_SYNC_SERVER_ROOT in model_dir and len(model_dir) > len(PROJECT_SYNC_SERVER_ROOT), "请检查配置")

    check_arg(not self._config.get("model_feature"), "不支持配置 model_feature, 请使用 feature_path")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    use_fused_infer_and_feature = self._config.get("use_fused_infer_and_feature", False)
    if not use_fused_infer_and_feature:
      ret.add("ad_infer_request_context")
      ret.add("ad_infer_validate_request")
    dragon_input_feature_list = self._config.get("dragon_input_feature_list", "ad_infer_feature_list")
    ret.add(dragon_input_feature_list)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("ad_infer_status")
    for output in self._config["outputs"]:
      ret.add(output["attr_name"])
      ret.add(output["attr_name"] + "_layer_len")
    return ret

class AdInferResultEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_infer_result"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("ad_infer_status")
    for output in self._config["outputs"]:
      ret.add(output["attr_name"])
      ret.add(output["attr_name"] + "_layer_len")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add("item_miss")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("predict_value")
    ret.add("result_stat")
    return ret

class AdInferResultV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_infer_result_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("ad_infer_status")
    outputs = self._config.get("outputs", [])
    for output in outputs:
      ret.add(output["attr_name"])
      ret.add(output["attr_name"] + "_layer_len")

    vector_outputs = self._config.get("vector_outputs", [])
    for output in vector_outputs:
      ret.add(output["attr_name"])
      ret.add(output["attr_name"] + "_layer_len")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add("item_miss")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()

    outputs = self._config.get("outputs", [])
    if len(outputs) > 0:
      output_prob_attr = self._config.get("output_prob_attr", "predict_value")
      ret.add(output_prob_attr)

    vector_outputs = self._config.get("vector_outputs", [])
    if len(vector_outputs) > 0:
      output_vector_attr = self._config.get("output_vector_attr", "vector_predict_value");
      ret.add(output_vector_attr)

    output_result_stat_attr = self._config.get("output_result_stat_attr", "result_stat")
    ret.add(output_result_stat_attr)

    return ret

class AdColossusV2GoodsClickSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_colossusv2_goods_click_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_goods_id_attr", "item_id_list"))
    ret.add(self._config.get("colossus_timestamp_attr", "click_timestamp_list"))
    ret.add(self._config.get("colossus_category_attr", "category_a_list"))
    ret.add(self._config.get("colossus_host_attr", "seller_id_list"))
    ret.add(self._config.get("colossus_shop_attr", "real_seller_id_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr", "ad_colocussv2_goods_click_seq_slot"))
    ret.add(self._config.get("output_sign_attr", "ad_colocussv2_goods_click_seq_sign"))
    ret.add(self._config.get("output_seq_timestamp_attr", "ad_colocussv2_goods_click_seq_timestamp"))
    ret.add(self._config.get("output_seq_length_attr", "ad_colocussv2_goods_click_seq_length"))
    return ret

class AdLiveHashSlotEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_hash_slot"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("slot"))
    ret.add(self._config.get("size"))
    ret.add(self._config.get("prefix"))
    ret.add(self._config.get("use_murmur3hash64"))
    ret.add(self._config.get("use_djb2_hash64"))
    ret.add(self._config.get("is_list_attr"))
    ret.add(self._config.get("is_common_attr"))
    ret.add(self._config.get("parameter_sign_bits"))
    if self._config.get("is_common_attr"):
      ret.add(self._config.get("common_hash_key_list_attr"))
      ret.add(self._config.get("common_hash_key_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common_attr"):
      ret.add(self._config.get("item_hash_key_list_attr"))
      ret.add(self._config.get("item_hash_key_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr"):
      ret.add(self._config.get("common_hash_output_attr"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common_attr"):
      ret.add(self._config.get("item_hash_output_attr"))
    return ret

class AdLiveV2AuthorLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_v2_author_label"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_author_id_attr"))
    ret.add(self._config.get("colossus_timestamp_attr"))
    ret.add(self._config.get("colossus_play_time_attr"))
    ret.add(self._config.get("min_seconds_ago"))
    ret.add(self._config.get("max_seconds_ago"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr"))
    return ret

class AdLlmUiMatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_llm_ui_match"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_llm_sid_l1_attr"))
    ret.add(self._config.get("user_llm_sid_l2_attr"))
    ret.add(self._config.get("user_llm_sid_l3_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("good_llm_sid_l1_attr"))
    ret.add(self._config.get("good_llm_sid_l2_attr"))
    ret.add(self._config.get("good_llm_sid_l3_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr"))
    return ret

class AdLiveV2ClkCateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_v2_clk_cate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_category_a_attr"))
    ret.add(self._config.get("colossus_click_timestamp_attr"))
    ret.add(self._config.get("min_seconds_ago"))
    ret.add(self._config.get("max_seconds_ago"))
    ret.add(self._config.get("target_attr_yellow_car_attr"))
    ret.add(self._config.get("target_attr_live_yellow_car_attr"))
    ret.add(self._config.get("target_attr_explain_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr"))
    return ret

class AdEcomGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ecom_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["pid_cluster_id_attr"])
    ret.add(self._config["semantic_id_attr"])
    ret.add(self._config["target_pid_cluster_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdEcomGsuV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ecom_gsu_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["pid_cluster_id_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["semantic_id_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdEcomGsuV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ecom_gsu_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["pid_cluster_id_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["semantic_id_attr"])
    ret.add(self._config["target_pid_cluster_id_attr"])
    ret.add(self._config["target_pid_category_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdEcomMatchCntEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ecom_match_cnt"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["target_pid_category_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdEcomAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ecom_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["semantic_id_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_aid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdGoodsClickEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_click"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["colossus_goods_id_attr"])
    ret.add(self._config["colossus_timestamp_attr"])
    ret.add(self._config["colossus_category_attr"])
    ret.add(self._config["colossus_detail_page_view_time_attr"])
    ret.add(self._config["colossus_real_price_attr"])
    ret.add(self._config["colossus_seller_id_attr"])
    ret.add(self._config["colossus_real_seller_id_attr"])
    ret.add(self._config["colossus_click_flow_type_attr"])
    ret.add(self._config["colossus_click_from_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class EshopVideoGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eshop_video_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["duration_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["spu_id_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if self._config["save_pid_map"]:
      ret.add(self._config["photo_id_to_colossus_idx_map"])
    if self._config["save_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret

class EshopVideoGsuWithPidListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eshop_video_gsu_with_pid_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["duration_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["spu_id_attr"])
    ret.add(self._config["photo_id_to_colossus_idx_map"])
    if self._config["use_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_pid_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGoodGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_good_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["commodity_id_attr"])
    ret.add(self._config["pagecode_id_attr"])
    ret.add(self._config["uniform_spu_id_attr"])
    ret.add(self._config["exposure_ratio_attr"])
    ret.add(self._config["exposure_time_attr"])
    ret.add(self._config["detail_content_stay_time_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["leaf_category_attr"])
    ret.add(self._config["seller_id_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if self._config["save_cid_map"]:
      ret.add(self._config["commodity_id_to_colossus_idx_map"])
    if self._config["save_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret

class LiveItemGsuWithAidListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_softsearch_with_aid_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["live_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["lag_day_attr"])
    ret.add(self._config["lag_hour_attr"])
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["hetu_tag_attr"])
    ret.add(self._config["index_attr"])
    ret.add(self._config["reindex_attr"])
    ret.add(self._config["photo_id_to_colossus_idx_map"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_aid_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret
  
class LiveItemGsuWithIdxListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_softsearch_with_idx_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["live_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["lag_day_attr"])
    ret.add(self._config["lag_hour_attr"])
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["hetu_tag_attr"])
    ret.add(self._config["index_attr"])
    ret.add(self._config["reindex_attr"])
    ret.add(self._config["input_bias_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_idx_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class MerchantGoodGsuWithIdxListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_good_gsu_with_idx_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["commodity_id_attr"])
    ret.add(self._config["pagecode_id_attr"])
    ret.add(self._config["uniform_spu_id_attr"])
    ret.add(self._config["exposure_ratio_attr"])
    ret.add(self._config["exposure_time_attr"])
    ret.add(self._config["detail_content_stay_time_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["leaf_category_attr"])
    ret.add(self._config["seller_id_attr"])
    ret.add(self._config["input_bias_attr"])
    if self._config["use_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_idx_list_attr"])
    ret.add(self._config["soft_score_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class GoodsClickGsuWithIdxListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "goods_click_gsu_with_idx_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["commodity_id_attr"])
    ret.add(self._config["seller_id_attr"])
    ret.add(self._config["flow_type_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["price_attr"])
    ret.add(self._config["item_count_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["topk_indices_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    output_suffix = self._config["output_suffix"]
    ret.add(self._config["category_attr"] + output_suffix)
    ret.add(self._config["commodity_id_attr"] + output_suffix)
    ret.add(self._config["seller_id_attr"] + output_suffix)
    ret.add(self._config["price_attr"] + output_suffix)
    ret.add(self._config["item_count_attr"] + output_suffix)
    ret.add(self._config["sideinfo_time_gap_attr"] + output_suffix)
    ret.add(self._config["sideinfo_carry_type_attr"] + output_suffix)
    return ret


class IdCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "id_count_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["item_id_list_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_item_count_list_attr"])
    return ret

class EshopVideoGsuWithIdxListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eshop_video_gsu_with_idx_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["duration_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["spu_id_attr"])
    ret.add(self._config["input_bias_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_idx_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class EshopVideoAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eshop_video_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["play_time_attr"])
    ret.add(self._config["duration_attr"])
    ret.add(self._config["channel_attr"])
    ret.add(self._config["label_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["spu_id_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_aid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdRemoteEmbCheckSaveTimeValidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_remote_emb_check_save_time_valid"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @strict_types
  def from_common(self) -> bool:
    r = self._config.get("from_common_attr", "")
    return len(r) > 0

  @strict_types
  def get_output_attrs(self) -> set:
    ret = set()
    for key in ["output_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["from_common_attr"]:
      if key in self._config: ret.add(self._config[key])
    ret.add(self._config.get("request_time_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["from_item_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attrs() if self.from_common() else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attrs() if not self.from_common() else set()

class AdSlotSignPlainEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_slot_sign_plain"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @strict_types
  def from_common(self) -> bool:
    id_attrs = self._config.get("id_attrs", [])
    for id_attr in id_attrs:
      if "common" in id_attr and not id_attr["common"]:
        return False
    return True

  @strict_types
  def get_output_attrs(self) -> set:
    ret = set()
    for key in ["output_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    id_attrs = self._config.get("id_attrs", [])
    for id_attr in id_attrs:
      if "common" in id_attr and id_attr["common"]:
        if "attr_name" in id_attr:
          ret.add(id_attr["attr_name"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    id_attrs = self._config.get("id_attrs", [])
    for id_attr in id_attrs:
      if "common" in id_attr and not id_attr["common"]:
        if "attr_name" in id_attr:
          ret.add(id_attr["attr_name"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attrs() if self.from_common() else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attrs() if not self.from_common() else set()

class AdListAttrPadEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_list_attr_pad"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @strict_types
  def from_common(self) -> bool:
    return self._config.get("common_attr", True)

  @strict_types
  def get_output_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr"))
    return ret
  
  @strict_types
  def get_input_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_attr"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.get_input_attrs() if self.from_common() else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.get_input_attrs() if not self.from_common() else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attrs() if self.from_common() else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attrs() if not self.from_common() else set()

class AdColossusTimeFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_colossus_time_filter"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    colossus_time_attr = self._config.get("colossus_time_attr", "")
    request_time_attr = self._config.get("request_time_attr", "")
    input_attrs = self._config.get("input_attrs", [])
    for attr in input_attrs + [colossus_time_attr, request_time_attr]:
      if attr != "":
        ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    output_attrs = self._config.get("output_attrs", [])
    for attr in output_attrs:
      ret.add(attr)
    return ret

class CommonAdGsuWithClusterTagV2RfmConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v2_rfm_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])

    ret.add("uc_play_cnt_30min_attr")
    ret.add("uc_play_cnt_2h_attr")

    ret.add("uc_effective1_30min_attr")
    ret.add("uc_effective1_2h_attr")

    ret.add("uc_effective2_30min_attr")
    ret.add("uc_effective2_2h_attr")

    ret.add("uc_playtime_30min_attr")
    ret.add("uc_playtime_2h_attr")
    ret.add("uc_playtime_7h_attr")
    ret.add("uc_playtime_24h_attr")
    ret.add("uc_playtime_3d_attr")
    ret.add("uc_playtime_7d_attr")
    ret.add("uc_playtime_28d_attr")
    ret.add("uc_playtime_nd_attr")
    return ret

class CommonAdGsuWithClusterTagV3RfmConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v3_rfm_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])

    ret.add("uc_play_cnt_30min_attr")
    ret.add("uc_play_cnt_2h_attr")

    ret.add("uc_effective1_30min_attr")
    ret.add("uc_effective1_2h_attr")

    ret.add("uc_effective2_30min_attr")
    ret.add("uc_effective2_2h_attr")

    ret.add("uc_playtime_30min_attr")
    ret.add("uc_playtime_2h_attr")
    ret.add("uc_playtime_7h_attr")
    ret.add("uc_playtime_24h_attr")
    ret.add("uc_playtime_3d_attr")
    ret.add("uc_playtime_7d_attr")
    ret.add("uc_playtime_28d_attr")
    ret.add("uc_playtime_nd_attr")
    return ret

class CommonAdGsuWithClusterTagV4RfmConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v4_rfm_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])

    ret.add("uc_play_cnt_30min_attr")
    ret.add("uc_play_cnt_2h_attr")

    ret.add("uc_effective1_30min_attr")
    ret.add("uc_effective1_2h_attr")

    ret.add("uc_effective2_30min_attr")
    ret.add("uc_effective2_2h_attr")

    ret.add("uc_playtime_30min_attr")
    ret.add("uc_playtime_2h_attr")
    ret.add("uc_playtime_7h_attr")
    ret.add("uc_playtime_24h_attr")
    ret.add("uc_playtime_3d_attr")
    ret.add("uc_playtime_7d_attr")
    ret.add("uc_playtime_28d_attr")
    ret.add("uc_playtime_nd_attr")
    return ret


class AdCommonColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    batch_query = self._config.get("is_batch_query", False)
    if batch_query:
      input_attr = self._config.get("input_attr")
      if not input_attr:
        assert False, "`input_attr` is required in batch_query mode"
      ret.add(self._config["input_attr"])

    input_colossus_resp_attr = self._config.get("input_colossus_resp_attr")
    if input_colossus_resp_attr:
      ret.add(self._config["input_colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class AdCommonRemoteEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_get_remote_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    query_source_type = self._config.get("query_source_type", "item_key")
    if query_source_type == "item_attr":
      ret.add(self._config["query_source_item_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    save_to_common_attr = self._config.get("save_to_common_attr", False)
    if not save_to_common_attr:
      ret.add(self._config["output_attr_name"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    save_to_common_attr = self._config.get("save_to_common_attr", False)
    if save_to_common_attr:
      ret.add(self._config["output_embedding_list_attr"])
      output_item_list_attr = self._config.get("output_item_list_attr")
      if output_item_list_attr and len(output_item_list_attr) > 0:
        ret.add(self._config["output_item_list_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonAdGsuWithClusterTagV5RfmConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial_v5_rfm_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])

    ret.add("uc_play_cnt_30min_attr")
    ret.add("uc_play_cnt_2h_attr")

    ret.add("uc_effective1_30min_attr")
    ret.add("uc_effective1_2h_attr")

    ret.add("uc_effective2_30min_attr")
    ret.add("uc_effective2_2h_attr")

    ret.add("uc_playtime_30min_attr")
    ret.add("uc_playtime_2h_attr")
    ret.add("uc_playtime_7h_attr")
    ret.add("uc_playtime_24h_attr")
    ret.add("uc_playtime_3d_attr")
    ret.add("uc_playtime_7d_attr")
    ret.add("uc_playtime_28d_attr")
    ret.add("uc_playtime_nd_attr")
    return ret

class CommonAdGsuWithHourClusterConfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "commertial_gsu_with_hour_cluster_conf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MultimodalSeqSoftSearchExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multimodal_seq_soft_search_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    input_attrs = [
      "input_seq_timestamp",
      "input_seq_category", "input_seq_sideinfo1", "input_seq_sideinfo2",
      "input_seq_sideinfo3", "input_seq_sideinfo4", "input_seq_sideinfo5",
      "input_seq_sideinfo6"
    ]
    for attr in input_attrs:
      ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    input_attrs = [
      "cartitem_len", "input_seq_cosine_sim", "input_seq_cosine_mask"
    ]
    for attr in input_attrs:
      ret.add(self._config[attr])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    output_attrs = [
      "output_seq_lag_day", "output_seq_time_gap", "output_seq_cate1", 
      "output_seq_cate2", "output_seq_cate3", "output_seq_sideinfo1", 
      "output_seq_sideinfo2", "output_seq_sideinfo3", "output_seq_sideinfo4", 
      "output_seq_sideinfo5", "output_seq_sideinfo6", "output_seq_cosine_score", 
      "output_seq_cartindex", "output_seq_cosine_score_ori"
    ]
    for attr in output_attrs:
      ret.add(self._config[attr])
    return ret

class MultimodalSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multimodal_seq_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 item_attrs 集合
    ret = set()
    ret.add(self._config["mm_seq_soft_search_lag_day"])
    ret.add(self._config["mm_seq_soft_search_timestamp"])
    ret.add(self._config["mm_seq_soft_search_cate1"])
    ret.add(self._config["mm_seq_soft_search_cate2"])
    ret.add(self._config["mm_seq_soft_search_cate3"])
    ret.add(self._config["mm_seq_soft_search_sideinfo1"])
    ret.add(self._config["mm_seq_soft_search_sideinfo2"])
    ret.add(self._config["mm_seq_soft_search_sideinfo3"])
    ret.add(self._config["mm_seq_soft_search_sideinfo4"])
    ret.add(self._config["mm_seq_soft_search_sideinfo5"])
    ret.add(self._config["mm_seq_soft_search_sideinfo6"])
    ret.add(self._config["mm_seq_soft_search_cosine_score"])
    ret.add(self._config["mm_seq_soft_search_cartindex"])
    ret.add(self._config["mm_seq_soft_search_cosine_score_ori"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class GoodsClickSoftSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "goods_click_soft_search_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["good_id_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["real_seller_id_attr"])
    ret.add(self._config["seller_id_attr"])
    ret.add(self._config["real_price_attr"])
    ret.add(self._config["click_flow_type_attr"])
    ret.add(self._config["click_from_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if self._config["save_good_id_map"]:
      ret.add(self._config["good_id_to_colossus_idx_map"])
    if self._config["save_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret

class GoodsClickSoftSearchWithIdListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "goods_click_soft_search_with_id_list_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["good_id_attr"])
    ret.add(self._config["category_attr"])
    ret.add(self._config["real_seller_id_attr"])
    ret.add(self._config["seller_id_attr"])
    ret.add(self._config["real_price_attr"])
    ret.add(self._config["click_flow_type_attr"])
    ret.add(self._config["click_from_attr"])
    ret.add(self._config["good_id_to_colossus_idx_map"])
    if self._config["use_sideinfo_cache"]:
      ret.add(self._config["sideinfo_cache_map"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_good_id_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class AdLiveUserGoodsClickTimesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_user_goods_click_times"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_goods_id_attr"])
    ret.add(self._config["colossus_timestamp_attr"])
    ret.add(self._config["detail_page_view_time_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_user_mean_time_by_hours"])
    ret.add(self._config["output_user_max_time_by_hours"])
    ret.add(self._config["output_user_min_time_by_hours"])
    ret.add(self._config["output_user_cnt_time_by_hours"])
    ret.add(self._config["output_user_mean_time_by_weekdays"])
    ret.add(self._config["output_user_max_time_by_weekdays"])
    ret.add(self._config["output_user_min_time_by_weekdays"])
    ret.add(self._config["output_user_cnt_time_by_weekdays"])
    ret.add(self._config["output_user_mean_time_by_months"])
    ret.add(self._config["output_user_max_time_by_months"])
    ret.add(self._config["output_user_min_time_by_months"])
    ret.add(self._config["output_user_cnt_time_by_months"])
    ret.add(self._config["output_user_mean_time_by_monthdays"])
    ret.add(self._config["output_user_max_time_by_monthdays"])
    ret.add(self._config["output_user_min_time_by_monthdays"])
    ret.add(self._config["output_user_cnt_time_by_monthdays"])
    ret.add(self._config["output_user_cnt_time_by_bins"])
    return ret


class AdRewriteTopkIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rewrite_topk_index_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    ret = set()
    ret.add(self._config["topk_indices"])
    ret.add(self._config["topk_values"])
    ret.add(self._config["origin_item_id_list"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_topk_indices"])
    ret.add(self._config["output_topk_values"])
    if self._config["output_int_value"]:
      ret.add(self._config["output_topk_values_int"])
    return ret

class AdLiveUserGoodsClickV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "ad_live_user_goods_click_v1"
  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    ret.add(self._config["colossus_goods_id_attr"])
    ret.add(self._config["colossus_timestamp_attr"])
    ret.add(self._config["colossus_category_attr"])
    ret.add(self._config["colossus_host_attr"])
    ret.add(self._config["colossus_shop_attr"])
    ret.add(self._config["colossus_real_price_attr"])
    ret.add(self._config["detail_page_view_time_attr"])
    ret.add(self._config["colossus_order_goods_id_attr"])
    ret.add(self._config["colossus_submit_timestamp_attr"])
    ret.add(self._config["colossus_pay_timestamp_attr"]) 
    ret.add(self._config["colossus_label_attr"])
    ret.add(self._config["colossus_uniform_spu_id_attr"])
    ret.add(self._config["click_flow_type_attr"])
    ret.add(self._config["click_from_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["output_sign_attr"])
    return ret

class AdLiveUserCycleClickV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "ad_live_user_cycle_click_v1"
  
  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    ret.add(self._config["colossus_goods_id_attr"])
    ret.add(self._config["colossus_timestamp_attr"])
    ret.add(self._config["colossus_category_attr"])
    ret.add(self._config["colossus_host_attr"])
    ret.add(self._config["colossus_shop_attr"])
    ret.add(self._config["colossus_real_price_attr"])
    ret.add(self._config["detail_page_view_time_attr"])
    ret.add(self._config["colossus_label_attr"])
    ret.add(self._config["colossus_uniform_spu_id_attr"])
    ret.add(self._config["click_flow_type_attr"])
    ret.add(self._config["click_from_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["output_sign_attr"])
    return ret