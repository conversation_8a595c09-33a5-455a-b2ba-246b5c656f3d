#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import <PERSON><PERSON>rranger
from ...common_leaf_util import strict_types

class FollowLeafFilter<PERSON>rranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_filter"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("item_attr_map").values():
            attrs.add(attr)
        for filter in self._config.get("filters"):
            for item in filter.items():
                if item[0] == "item_attr":
                    for item_attr in item[1]:
                        attrs.add(item_attr)
        return attrs
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("low_limit"))
        for filter in self._config.get("filters"):
            for item in filter.items():
                if item[0] == "name" or item[0] == "item_attr" or item[0] == "skip_self_judge":
                    continue
                elif item[0] == "enable":
                    attrs.update(self.extract_dynamic_params(item[1]))
                elif item[0] == "common_attr":
                    for common_attr in item[1]:
                        attrs.add(common_attr)
                else:
                    attrs.add(item[1])
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_complete")
        return attrs

class FollowLeafEnsembleSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_ensemble_sort"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("weights_str")))
        attrs.update(self.extract_dynamic_params(self._config.get("rank_smooth_arg")))
        attrs.update(self.extract_dynamic_params(self._config.get("temperature")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_ranking_multi_ensemble_score")))
        attrs.update(self.extract_dynamic_params(self._config.get("es_alpha")))
        attrs.update(self.extract_dynamic_params(self._config.get("max_score")))
        for es_config_list in self._config.get("es_config_list"):
            for item in es_config_list.items():
                if item[0] == "func_name" or item[0] == "enable":
                    continue
                else:
                    attrs.update(self.extract_dynamic_params(item[1]))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for es_config_list in self._config.get("es_config_list"):
            for item in es_config_list.items():
                if item[0] == "trans_name":
                    attrs.add(item[1])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("out_score_name"))
        return attrs

class FollowLeafAfterFilterSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_after_filter_sort"

    @strict_types
    def is_async(self) -> bool:
        return False


    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("is_complete")
        return attrs
class FollowLeafRetrievalModelBeforeInferFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_model_before_infer_filter_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        if self._config.get("is_hit_cache"):
            attrs.add("author_id")
        else:
            attrs.add("query_index")
        attrs.add("score_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_filter_inverted_top_refreshed_photo_author")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_inverted_top_refreshed_photo_author_in_all")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_inverted_top_refreshed_photo_show_times_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_inverted_top_refreshed_photo_show_time_gap_hours")))
        attrs.update(self.extract_dynamic_params(self._config.get("photo_max_hour_in_fill_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("filter_top_fresh_cursor_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_duplicated_photo_in_high_follow")))
        attrs.update(self.extract_dynamic_params(self._config.get("filter_duplicated_photo_thred_in_high_follow")))
        attrs.update(self.extract_dynamic_params(self._config.get("complete_photo_in_fill_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("is_extend_recall_in_follow")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_filter_global_browseSet_photo_in_recall")))
        attrs.update(self.extract_dynamic_params(self._config.get("photo_max_hour_in_fill_retrieval_opt")))

        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_filter_dup_realshow_photo_in_recall")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_dup_realshow_photo_in_recall_timestamp_gap_hours")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_dup_realshow_photo_in_recall_timestamp_gap_days")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_dup_realshow_photo_in_recall_show_cnt_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_dup_realshow_photo_in_recall_author_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_high_follow_user_item_truncate")))
        attrs.update(self.extract_dynamic_params(self._config.get("high_follow_user_first_fresh_follow_size_threshold")))
        attrs.update(self.extract_dynamic_params(self._config.get("high_follow_user_first_fresh_truncate_num")))

        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_viewed_photo_in_friend")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_feasury_global_action_strs_outside")))

        attrs.update(self.extract_dynamic_params(self._config.get("refresh_type_in_follow")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafRetrievalUaScoreArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_ua_score_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("truncate_photo_size_limit_in_recall_final")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_global_browse_photo_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_trunc_aid_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("trunc_aid_size_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_play_realshow_score_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_keep_newfollow_ua_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("newfollow_author_timegap")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafV5RetrievalUaScoreArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_v5_retrieval_ua_score_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("truncate_photo_size_limit_in_recall_final")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_global_browse_photo_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_trunc_aid_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("trunc_aid_size_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_play_realshow_score_in_ua_score_arranger")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_keep_newfollow_ua_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("newfollow_author_timegap")))
        attrs.add("FollowContextAttrKey")
        return attrs
    
class FollowLeafRetrievalEffectUaFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_effect_ua_filter"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("user_id")
        attrs.add("exp_group_name")
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_recall_effect_ua_filter")))
        attrs.update(self.extract_dynamic_params(self._config.get("ua_filter_follow_time")))
        attrs.update(self.extract_dynamic_params(self._config.get("ua_filter_follow_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("mod_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("judge_num_min")))
        attrs.update(self.extract_dynamic_params(self._config.get("judge_num_max")))
        attrs.update(self.extract_dynamic_params(self._config.get("ua_filter_min_photo_num_threadhold")))
        attrs.add("FollowContextAttrKey")
        return attrs


class FollowLeafV5RetrievalModelTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_v5_retrieval_model_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("v5_recall_final_score")
        attrs.add("query_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("max_request_remote_forward_index_photo_cnt")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_recall_ua_score_rank_truncate")))
        attrs.update(self.extract_dynamic_params(self._config.get("trunc_aid_size_in_model_truncature_arranger")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafRetrievalModelTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_model_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("recall_final_score")
        attrs.add("v5_recall_final_score")
        attrs.add("query_index")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("max_request_remote_forward_index_photo_cnt")))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_high_follow_user_item_truncate")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_recall_ua_score_rank_truncate")))
        attrs.update(self.extract_dynamic_params(self._config.get("trunc_aid_size_in_model_truncature_arranger")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafRetrievalMergeTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_merge_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("recall_final_score")
        attrs.add("v5_recall_final_score")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.update(self.extract_dynamic_params(self._config.get("max_request_remote_forward_index_photo_cnt")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafV5RetrievalMergeTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_v5_retrieval_merge_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("v5_recall_final_score")
        return attrs
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.update(self.extract_dynamic_params(self._config.get("max_request_remote_forward_index_photo_cnt")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafRetrievalTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_truncature"

    @strict_types
    def is_async(self) -> bool:
        return False


    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("unshowed_new_photo_ratio_max_thred")))
        attrs.update(self.extract_dynamic_params(self._config.get("showed_photo_ratio_max_thred")))
        attrs.update(self.extract_dynamic_params(self._config.get("max_request_remote_forward_index_photo_cnt")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_actioned_photo_coeff_in_mc")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_actioned_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_showed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_recent_showed_photo_coeff_in_remote_v2")))
        attrs.update(self.extract_dynamic_params(self._config.get("per_author_max_dup_photo_num_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_follow_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_global_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_follow_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("global_browsed_photo_ratio_max_thred")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_browsed_photo_ratio_max_thred")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_global_browse_photo_filter")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_browse_photo_filter")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_global_browse_photo_filter_final")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_browse_photo_filter_final")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_min_browse_photo_thred_in_retrieval_trunc")))
        attrs.update(self.extract_dynamic_params(self._config.get("min_browse_photo_thred_in_retrieval_trunc")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_filter_inverted_top_refreshed_photo_author")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_inverted_top_refreshed_photo_author_in_all")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_filter_inverted_top_refreshed_photo_show_times_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("refresh_type_in_follow")))
        attrs.add("FollowContextAttrKey")
        attrs.add("diagnosis")
        return attrs
       
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        return {"follow_author_list", "follow_author_each_photo_size","follow_author_photo_list"}

class FollowLeafInvertedFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_inverted_filter"

    @strict_types
    def is_async(self) -> bool:
        return False


    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("score_attr_name"))
        return attrs  
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("enable_follow_leaf_inverted_filter")))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_term_filter_threshold_day_v4")))

        return attrs  


class FollowLeafSessionPhotoFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_session_photo_filter"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        if self._config.get("is_hit_cache"):
            attrs.add("author_id")
        else:
            attrs.add("query_index")
        attrs.add("score_index")
        return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_all_global_view")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_negative_pid_and_aid")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_viewed_photo_in_friend")))
        attrs.update(self.extract_dynamic_params(self._config.get("all_global_view_filter_days_begin")))
        attrs.update(self.extract_dynamic_params(self._config.get("all_global_view_filter_days_end")))
        attrs.add("FollowContextAttrKey")

        return attrs  

class FollowLeafU2U2IPhotoFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_u2u2i_photo_filter"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("score_index")
        return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("enable_u2u2i_retrieval")))
        attrs.add(self._config.get("slide_u2u2i_photo_list"))

        return attrs  

class FollowLeafSessionLiveFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_session_live_filter"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("live_session_browse_attr"))

        return attrs  

class FollowLeafUnfollowPhotoMergeArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_unfollow_photo_merge"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("source")
        attrs.add("author_id")
        return attrs  
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("user_id")
        attrs.add(self._config.get("follow_list_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("unfollow_retrieval_min_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("unfollow_retrieval_max_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("unfollow_photo_retrieval_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("unfollow_retrieval_max_aid_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("unfollow_retrieval_min_aid_num")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_unfollow_max_aid_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_multi_road_merge")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_unfollow_neg_author")))
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowLeafOfflineUAPhotoMergeArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_offline_ua_photo_merge"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        return attrs  
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("offline_ua_retrieval_max_photo_num")))
        return attrs
class FollowLeafCacheRemotePhotoMergeArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_cache_remote_photo_merge"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("score_index")
        return attrs  
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("remote_retrieval_reason")))
        attrs.update(self.extract_dynamic_params(self._config.get("cache_retrieval_reason")))
        attrs.update(self.extract_dynamic_params(self._config.get("cache_retrieval_days_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("remote_retrieval_days_limit")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_retrieval_merge_cache_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_retrieval_cache")))
        attrs.update(self.extract_dynamic_params(self._config.get("min_request_photo_cnt_in_slide_follow_leaf")))
        attrs.add("FollowContextAttrKey")
        return attrs 


class FollowMixEnsembleSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_es_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_mix_ensemble_sort_merchant_avoid_business_weight_discount")
        attrs.add("follow_mix_ensemble_sort_gift_avoid_business_weight_discount")
        attrs.add("follow_color_ab_disable_store_wide_photo")
        attrs.add("realtime_fractile_ad")
        attrs.add("enable_follow_mix_ad_gap_backup")
        attrs.add("enable_adaptive_ad_gap_detail")
        attrs.add("merchant_user_type")
        attrs.add("user_type_adaptive_ad_gap_detail_str")
        attrs.add(self._config.get("is_perf_attr"))
        attrs.add(self._config.get("exp_tag_attr"))
        attrs.add(self._config.get("close_identify_ad_flag_attr"))
        attrs.add(self._config.get("ue_avoid_business_weight_discount_attr"))
        attrs.add(self._config.get("perf_discount_attr"))
        attrs.add(self._config.get("enable_equal_index_is_ad_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("result_size")))
        score_infos = self._config.get("score_infos")
        for score_info in score_infos:
            attrs.add(score_info.get("weight_attr"))
            attrs.add(score_info.get("a_attr"))
            attrs.add(score_info.get("b_attr"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("ad_score")
        attrs.add("natural_index")
        attrs.add("biz_type_list")
        score_infos = self._config.get("score_infos")
        for score_info in score_infos:
            attrs.add(score_info.get("score_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("mix_score")
        attrs.add("biz_type")
        attrs.add("ad_flag")
        attrs.add('next_feed_mix_score')
        return attrs
    
class FollowLeafSlideMcTruncatureArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_slide_mc_truncature_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("enable_time_photo_num_bound"))
        attrs.add(self._config.get("slide_mc_max_result_num"))
        attrs.add(self._config.get("time_mc_photo_num_bound_night_s"))
        attrs.add(self._config.get("time_mc_photo_num_bound_night_e"))
        attrs.add(self._config.get("time_mc_photo_num_bound_af_s"))
        attrs.add(self._config.get("time_mc_photo_num_bound_af_e"))
        attrs.add(self._config.get("time_mc_photo_num_bound_low"))
        attrs.add(self._config.get("time_mc_photo_num_bound_mid"))
        attrs.add(self._config.get("time_mc_photo_num_bound_high"))
        attrs.add(self._config.get("mc_diverse_author_window_size"))
        attrs.add(self._config.get("mc_diverse_author_decay_occurrent_times"))
        attrs.add(self._config.get("mc_diverse_author_decay_rate"))
        
        attrs.add(self._config.get("enable_keep_friend_works"))
        attrs.add(self._config.get("enable_limit_general_friend_work_num"))
        attrs.add(self._config.get("enable_limit_intimate_friend_work_num"))
        attrs.add(self._config.get("enable_limit_high_quality_friend_work_num"))
        attrs.add(self._config.get("general_friend_work_num"))
        attrs.add(self._config.get("intimate_friend_work_num"))
        attrs.add(self._config.get("high_quality_ua_friend_work_num"))
        attrs.add(self._config.get("extra_friend_work_num_limit"))
        attrs.add(self._config.get("max_num_for_appending_friend_works_at_mc"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("author_id")
        attrs.add("is_friend")
        attrs.add("high_quality_author")
        attrs.add("is_intimate_relation")
        return attrs

class FollowSlideLiveEnsembleSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_live_ensemble_sort_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_boost_v5_2es_live_highvalue_ua_v2")
        attrs.add("enable_fill_highvalue_live_ua_v2")
        attrs.add("highvalue_live_author_v2_ids")
        attrs.add("highvalue_author_v2_ids_list")
        attrs.add("v5_2es_live_highvalue_ua_v2_boost_value")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("sort_score"))
        attrs.add("author_id")
        return attrs

class FollowLeafSlideDiversityRankArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_slide_diversity_rank_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("show_pid_list")
        attrs.add("show_pid_emb_list")
        attrs.add("varient_window_by_diversity_score")
        attrs.add("varient_len_by_diversity_score")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_embeddings")
        return attrs