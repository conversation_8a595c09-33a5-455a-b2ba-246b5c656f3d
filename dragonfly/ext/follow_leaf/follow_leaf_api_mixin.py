#!/usr/bin/env python3
# coding=utf-8
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .follow_leaf_arranger import *
from .follow_leaf_retriever import *
from .follow_leaf_enricher import *
from .follow_leaf_observer import *
from .follow_leaf_mixer import *

class FollowLeafApiMixin(CommonLeafBaseMixin):
    def follow_leaf_retrieve_by_extend(self, **kwargs):
        """
        FollowLeafExtendRetriever
        ------
        关注页扩展召回

        参数配置
        ------
        `user_info_path`:[string] 必须参数，扩展pb的地址

        `retrieval_reason`:[int] 必须参数，召回原因

        `enable_last_feed_retrieval`:[bool] feed_list开关

        `enable_old_feed_list_retrieval`:[bool] extra_reco_list开关

        调用示例
        ------
        ```
        .follow_leaf_retrieve_by_extend(
            user_info_path = "user_info",
            enable_last_feed_retrieval = False,
            enable_old_feed_list_retrieval = False,
            retrieval_reason = 1
        )
        ```
        """
        self._add_processor(FollowLeafExtendRetriever(kwargs))

        return self


    def follow_leaf_filter(self, **kwargs):
        """
        FollowLeafFilterArranger
        ------
        关注页过滤

        参数配置
        ------
        `item_attr_map`:[string] 必须参数
        
        `filters`:[string] 必须参数
        
        `low_limit`:[int] 大于0生效，低于此值会用过滤后的item进行补齐

        调用示例
        ------
        ```
        .follow_leaf_filter(
            item_attr_map = "{xx:xx}",
            filters = '{name:xx,enable:true}'
        )
        ```
        """
        self._add_processor(FollowLeafFilterArranger(kwargs))

        return self

    def follow_context_init(self, **kwargs):
        """
         FollowContextInitRetriever
        ------
        关注页context初始化

        参数配置
        ------
        `user_info_path`:[string] 必须参数，扩展pb的地址

        调用示例
        ------
        ```
        .follow_context_init(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowContextInitRetriever(kwargs))
        return self

    def follow_context_reset(self, **kwargs):
        """
        FollowContextResetRetriever
        ------
        关注页context清理

        参数配置
        ------

        调用示例
        ------
        ```
        .follow_context_init()
        ```
        """
        self._add_processor(FollowContextResetRetriever(kwargs))
        return self
    
    def follow_leaf_after_enricher(self, **kwargs):
        """
        FollowLeafAfterRetrievalEnricher
        ------
        关注页参数填充

        参数配置
        ------
        `user_info_path`:[string] 必须参数，扩展pb的地址

        调用示例
        ------
        ```
        .follow_leaf_after_enricher(
            user_info_path = "user_info",
        )
        ```
        """
        self._add_processor(FollowLeafAfterRetrievalEnricher(kwargs))
        return self

    def follow_leaf_predict_enricher(self, **kwargs):
        """
        FollowLeafPredictEnricher
        ------
        关注页访问predict

        参数配置
        ------
        `item_need_predict_attr_name`:[string] 必须参数，判断是否需要访问predict的item属性
        
        `item_need_predict_attr_value`:[int] 必须参数，判断是否需要访问predict的item属性的值
        
        `item_attr_name_list`:[string] 必须参数，feature参数
        
        `pxtr_name_list`:[string] 必须参数，response获取的pxtr参数
        
        `kess_service`:[string] 必须参数
        
        `request_type`:[string] 必须参数
        
        `timeout_ms`:[int] 必须参数
        
        `request_num_limit`:[int] 必须参数

        `output_prefix`:[string] 非必须参数，默认为空
        
        调用示例
        ------
        ```
        .follow_leaf_after_enricher(
            item_need_predict_attr_name = "item_type",
            item_need_predict_attr_value = 1,
            item_attr_name_list = "[]",
            pxtr_name_list = "[]",
            kess_service = "xxx",
            request_type = "xxx",
            timeout_ms = 100,
            request_num_limit = 400,
            output_prefix = ""
        )
        ```
        """
        self._add_processor(FollowLeafPredictEnricher(kwargs))
        return self

    def follow_leaf_retrieval_model_final_score_enricher(self, **kwargs):
        """
        FollowLeafRetrievalModelFinalScoreEnricher
        ------
        关注页召回 es
        ------
        调用示例
        ------
        ```
        .follow_leaf_retrieval_model_final_score_enricher(
            user_info_path = "user_info_pb",
        )
        ```
        """
        self._add_processor(FollowLeafRetrievalModelFinalScoreEnricher(kwargs))
        return self
    def follow_leaf_v5_retrieval_model_final_score_enricher(self, **kwargs):
        """
        FollowLeafV5RetrievalModelFinalScoreEnricher
        ------
        关注页召回 es
        ------
        调用示例
        ------
        ```
        .follow_leaf_v5_retrieval_model_final_score_enricher(
            user_info_path = "user_info_pb",
        )
        ```
        """
        self._add_processor(FollowLeafV5RetrievalModelFinalScoreEnricher(kwargs))
        return self
    def follow_leaf_v4_boost_inner_retrieval_model_final_score_enricher(self, **kwargs):
        """
        FollowLeafV5RetrievalModelFinalScoreEnricher
        ------
        关注页召回 es
        ------
        调用示例
        ------
        ```
        .follow_leaf_v4_boost_inner_retrieval_model_final_score_enricher(
            user_info_path = "user_info_pb",
        )
        ```
        """
        self._add_processor(FollowLeafV4BoostInnerRetrievalModelFinalScoreEnricher(kwargs))
        return self
    def follow_leaf_ensemble_sort(self, **kwargs):
        """
        FollowLeafEnsembleSortArranger
        ------
        关注页一道es

        参数配置
        ------
        `weights_str`:[string] 必须参数
        
        `es_config_list`:[string] 必须参数
        
        `item_type`:[int] 必须参数，1为photo,2为live

        调用示例
        ------
        ```
        .follow_leaf_filter(
            weights_str = "{xx_weight}",
            es_config_list = '{func_name:xx,index:1,weight:0.1,alpha:0.0,biase:0.0}',
            item_type = 1
        )
        ```
        """
        self._add_processor(FollowLeafEnsembleSortArranger(kwargs))

        return self
   
    def follow_leaf_after_filter_sort(self, **kwargs):
        """
        FollowLeafAfterFilterSortArranger
        ------
        过滤后的排序

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_after_filter_sort()
        ```
        """
        self._add_processor(FollowLeafAfterFilterSortArranger(kwargs))

        return self
    
    def follow_leaf_retrieval_truncature(self, **kwargs):
        """
        FollowLeafRetrievalTruncatureArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafRetrievalTruncatureArranger(kwargs))

        return self

    def follow_leaf_retrieval_model_before_infer_filter_truncature(self, **kwargs):
        """
        FollowLeafRetrievalModelBeforeInferFilterArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_model_before_infer_filter_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafRetrievalModelBeforeInferFilterArranger(kwargs))

        return self
    def follow_leaf_retrieval_model_truncature(self, **kwargs):
        """
        FollowLeafRetrievalModelTruncatureArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_model_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafRetrievalModelTruncatureArranger(kwargs))
        
        return self
    def follow_leaf_v5_retrieval_model_truncature(self, **kwargs):
        """
        FollowLeafV5RetrievalModelTruncatureArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_v5_retrieval_model_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafV5RetrievalModelTruncatureArranger(kwargs))
        return self
    def follow_leaf_v5_retrieval_merge_truncature(self, **kwargs):
        """
        FollowLeafV5RetrievalMergeTruncatureArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_v5_retrieval_merge_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafV5RetrievalMergeTruncatureArranger(kwargs))

        return self
    def follow_leaf_retrieval_merge_truncature(self, **kwargs):
        """
        FollowLeafRetrievalMergeTruncatureArranger
        ------
        召回后的截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_merge_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafRetrievalMergeTruncatureArranger(kwargs))

        return self
    
    def follow_leaf_retrieval_ua_score_truncature(self, **kwargs):
        """
        FollowLeafRetrievalUaScoreArranger
        ------
        召回后,按照author 后验分进行截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_ua_score_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafRetrievalUaScoreArranger(kwargs))

        return self
    
    def follow_leaf_v5_retrieval_ua_score_truncature(self, **kwargs):
        """
        FollowLeafV5RetrievalUaScoreArranger
        ------
        召回后,按照author 后验分进行截断

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_v5_retrieval_ua_score_truncature(user_info_path = "user_info")
        ```
        """
        self._add_processor(FollowLeafV5RetrievalUaScoreArranger(kwargs))

        return self
    
    def follow_leaf_retrieval_effect_ua_filter(self, **kwargs):
        """
        FollowLeafRetrievalEffectUaFilterArranger
        ------
        视频有效消费UA过滤测算

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieval_effect_ua_filter(
            user_info_path = "user_info_pb",
            follow_list_attr = follow_list_part_attr,
            enable_recall_ua_filter = "{{enable_recall_ua_filter}}",
            ua_filter_follow_time = "{{ua_filter_follow_time}}",
            ua_filter_follow_num = "{{ua_filter_follow_num}}",
            mod_num = "{{mod_num}}",
            judge_num = "{{judge_num}}",
            ua_filter_exp_group_name = "{{ua_filter_exp_group_name}}"
        )
        ```
        """
        self._add_processor(FollowLeafRetrievalEffectUaFilterArranger(kwargs))

        return self

    def follow_leaf_retrieve_by_local_cache(self, **kwargs):
        """
        FollowLeafLocalCacheRetriever
        ------
        本地cache 召回

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_retrieve_by_local_cache(
            follow_list_attr = "follow_list",
            miss_cache_follow_list_attr = "miss_cache_follow_list",
            enable_retrieval_cache = True,
            cache_query_index_to_attr = "cache_query_index",
            score_index_to_attr = "score_index"
        ) 
        ```
        """
        self._add_processor(FollowLeafLocalCacheRetriever(kwargs))

        return self    
    def follow_leaf_inverted_item_attr_enricher(self, **kwargs):
        """
        FollowLeafInvertedItemAttrEnricher
        ------
        本地cache 召回

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_inverted_item_attr_enricher(
        ) 
        ```
        """
        self._add_processor(FollowLeafInvertedItemAttrEnricher(kwargs))

        return self
    def follow_leaf_local_cache_enricher(self, **kwargs):
        """
        FollowLeafLocalCacheEnricher
        ------
        本地cache 召回

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_local_cache_enricher(
            follow_list_attr = "follow_list",
            miss_cache_follow_list_attr = "miss_cache_follow_list",
            enable_retrieval_cache = True,
            cache_query_index_to_attr = "cache_query_index",
            score_attr_name = "score_index"
        ) 
        ```
        """
        self._add_processor(FollowLeafLocalCacheEnricher(kwargs))

        return self
    
    def follow_leaf_inverted_filter(self, **kwargs):
        """
        FollowLeafInvertedFilterArranger
        ------
        用倒排返回的score过滤

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_inverted_filter(
            score_attr_name = "xxx",
            enable_follow_leaf_inverted_filter = True,
            follow_term_filter_threshold_day_v4 = 75,
        ) 
        ```
        """
        self._add_processor(FollowLeafInvertedFilterArranger(kwargs))

        return self  

    def follow_leaf_time_distribution_perflog(self, **kwargs):
        """
        FollowLeafTimeDistributionPerflogObserver
        ------
        打点视频结果时间分布占比监控

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_leaf_inverted_filter(
            timestamp_attr_name = "xxx",
            item_type = "item_type",
            stage_name = "remote_inverted",
            perf_sample = "perf_sample", #不写为全采样
            is_before_batch = True, #默认false
            time_distribution_setting = [1, 3, 7, 15, 75]
        ) 
        ```
        """
        self._add_processor(FollowLeafTimeDistributionPerflogObserver(kwargs))

        return self

    def follow_mix_biz_type_perflog(self, **kwargs):
        """
        FollowMixBizTypePerflogObserver
        ------
        打点mix biz type, 两种方式:
        1 biz_type_list 一个 item 属于多个 biz
        2 biz_type 一个 item 属于唯一一个 biz

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_mix_biz_type_perflog(
            biz_type_list_name = "biz_type_list",
            biz_type_name = "biz_type",
            stage_name = "xxx",
            perf_sample = "perf_sample", #不写为全采样
        ) 
        ```
        """
        self._add_processor(FollowMixBizTypePerflogObserver(kwargs))

        return self

    def follow_mix_final_perflog(self, **kwargs):
        """
        FollowMixFinalPerflogObserver
        ------
        最终打点

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_mix_final_perflog(
            perf_sample = "perf_sample", #不写为全采样
        ) 
        ```
        """
        self._add_processor(FollowMixFinalPerflogObserver(kwargs))

        return self

    def follow_leaf_retrieve_by_wire_guest(self, **kwargs):
        """
        FollowLeafWireGuestRetriever
        ------
        召回自己关注的人作为连麦嘉宾连麦的直播

        参数配置
        ------

        `retrieval_reason`:[int] 必选参数，召回原因

        `follow_list`:[int list] 必选参数，[动态参数] 关注列表

        `wire_guest_list_attr`:[string] 可选参数，召回的直播间内自己所关注的人的列表 int list

        调用示例
        ------
        ```
        .follow_leaf_retrieve_by_extend(
            reason = 300,
            follow_list = "{{follow_list_attr}}",
            wire_guest_list_attr = “wire_guest_list”
        )
        ```
        """
        self._add_processor(FollowLeafWireGuestRetriever(kwargs))

        return self    

    def follow_leaf_item_type_perflog(self, **kwargs):
        """
        FollowLeafItemTypePerflogObserver
        ------
        打点结果集视频直播数量监控

        参数配置
        ------
        `item_type`:[string] 必须参数
        
        `stage_name`:[string] 必须参数，打点聚合标识
        
        `perf_sample`:[string] 可选参数，监控采样标识，不写为全采样

        调用示例
        ------
        ```
        .follow_leaf_item_type_perflog(
            item_type = "item_type",
            stage_name = "remote_inverted",
            perf_sample = "perf_sample"
        ) 
        ```
        """
        self._add_processor(FollowLeafItemTypePerflogObserver(kwargs))

        return self
    
    def follow_leaf_item_reason_perflog(self, **kwargs):
        """
        FollowLeafItemReasonPerflogObserver
        ------
        打点结果集视频reason数量监控

        参数配置
        ------
        `item_type`:[string] 必须参数
        
        `stage_name`:[string] 必须参数，打点聚合标识
        
        `perf_sample`:[string] 可选参数，监控采样标识，不写为全采样
        
        `exp_group_name`:[string] 可选参数，实验组标识, 默认default

        调用示例
        ------
        ```
        .follow_leaf_item_reason_perflog(
            item_type = "item_type",
            stage_name = "remote_inverted",
            perf_sample = "perf_sample"
        ) 
        ```
        """
        self._add_processor(FollowLeafItemReasonPerflogObserver(kwargs))

        return self

    def follow_retrieve_by_remote_index(self, **kwargs):
        """
        FollowQueryRemoteRetriever
        ------
        从异地部署的通用索引中通过 Query 语句进行 item 召回（`.retrieve_by_local_index()` 的远程版本）

        [Query 检索语法介绍文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAC4SvGLozLHuj0wy-mSZ42Q)

        参数配置
        ------
        `kess_service`: [string] [动态参数] 远程索引服务的 kess 服务名

        `shard_num`: [int] 如果远程索引服务端是分片服务，通过该项指定分片数，默认为 1（未分片），如果设为 0 则自动从 kess 平台识别 shard 数目

        `timeout_ms`: [int] [动态参数] 请求远程索引服务的超时时间，默认值为 300

        `search_num`: [int] [动态参数] 

        `reason`: [int] 召回原因
        
        `term_prefix`: [string] "author_id:"
        
        `batch_limit`: [int] 一次请求最大的item数，多了会自动分片
        
        `query_total_limit` [int] query最大的item数,多了会截断

        `common_query`: [string] 公共查询条件，会分别跟 `querys` 中配置的各个 query 语句进行 AND 操作

        `save_score_to_attr`: [string] 如果不为空，且查询到的 score 不为空，则将各个召回 item 对应的 score 存入该 item_attr 中，可缺省

        `save_query_index_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 query index 存入该 item_attr 中，可缺省 （备注：query index 指的是各个 query 全部展开后从 0 开始的 index）

        调用示例
        ------
        ``` python
        .follow_retrieve_by_remote_index(
            kess_service = "grpc_XXX",
            timeout_ms = 300,
            reason = 1000,
            )
        ```
        """
        self._add_processor(FollowQueryRemoteRetriever(kwargs))
        return self


    def split_follow_list(self, **kwargs):
        """
        SplitFollowListEnricher
        ------
        切分follow list

        参数配置
        ------
        `follow_list_attr`:[string] 必须参数
        
        `each_shard_max_num_attr`:[string]
        
        `max_shard_num`:[int]
        
        `output_shard_num_attr`:[int]

        调用示例
        ------
        ```
        flow.split_follow_list(
            follow_list_attr = "follow_list",
            each_shard_max_num_attr = "",
            max_shard_num = self.max_shard_num,
            output_shard_num_attr = "follow_list_shard_num"
        )
        ```
        """
        self._add_processor(SplitFollowListEnricher(kwargs))

        return self

    def follow_leaf_author_feature_enricher(self, **kwargs):
        """
        follow_leaf_author_feature_enricher
        ------
        author info 拍平到 item info里

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_author_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowLeafAuthorFeatureEnricher(kwargs))

        return self

    def follow_leaf_session_live_filter(self, **kwargs):
        """
        follow_leaf_session_live_filter
        ------
        session live 过滤

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_session_live_filter(
            live_session_browse_attr="xxx"
        )
        ```
        """
        self._add_processor(FollowLeafSessionLiveFilterArranger(kwargs))

        return self

    def follow_leaf_session_photo_filter(self, **kwargs):
        """
        follow_leaf_session_photo_filter
        ------
        session photo 过滤

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_session_photo_filter(
        )
        ```
        """
        self._add_processor(FollowLeafSessionPhotoFilterArranger(kwargs))

        return self
    
    def follow_leaf_u2u2i_photo_filter(self, **kwargs):
        """
        follow_leaf_u2u2i_photo_filter
        ------
        筛选u2u2i路photo list

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_u2u2i_photo_filter(
        )
        ```
        """
        self._add_processor(FollowLeafU2U2IPhotoFilterArranger(kwargs))

        return self

    def follow_leaf_common_attr_enricher(self, **kwargs):
        """
        FollowLeafCommonAttrEnricher
        ------
        处理 common attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_common_attr_enricher(
        )
        ```
        """
        self._add_processor(FollowLeafCommonAttrEnricher(kwargs))

        return self

    def follow_leaf_unfollow_photo_merge(self, **kwargs):
        """
        follow_leaf_unfollow_photo_merge
        ------
        未关视频合并过滤

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_unfollow_photo_merge(
            unfollow_retrieval_min_num = "{{unfollow_retrieval_min_num}}",
            unfollow_retrieval_max_num = "{{unfollow_retrieval_max_num}}",
            unfollow_photo_retrieval_num = "{{unfollow_photo_retrieval_limit_num}}",
        )
        ```
        """
        self._add_processor(FollowLeafUnfollowPhotoMergeArranger(kwargs))

        return self
    def follow_leaf_offline_ua_photo_merge(self, **kwargs):
        """
        follow_leaf_offline_ua_photo_merge
        ------
        离线ua视频合并截断

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_offline_ua_photo_merge(
            offline_ua_retrieval_max_photo_num = "{{offline_ua_retrieval_max_photo_num}}"
        )
        ```
        """
        self._add_processor(FollowLeafOfflineUAPhotoMergeArranger(kwargs))

        return self
    def follow_leaf_cache_remote_photo_merge(self, **kwargs):
        """
        follow_leaf_cache_remote_photo_merge
        ------
        cache 和远端视频合并过滤

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_cache_remote_photo_merge(
            enable_retrieval_merge_cache_remote = "{{enable_retrieval_merge_cache_remote}}",
            enable_retrieval_cache = "{{enable_retrieval_cache}}",
        )
        ```
        """
        self._add_processor(FollowLeafCacheRemotePhotoMergeArranger(kwargs))

        return self

    def follow_leaf_unfollow_list_live_enricher(self, **kwargs):
        """
        follow_leaf_unfollow_list_live_enricher
        ------
        获取未关直播作者

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_unfollow_list_live_enricher(
        )
        ```
        """
        self._add_processor(FollowLeafUnfollowListLiveEnricher(kwargs))

        return self
    

    def follow_whole_store_enricher(self, **kwargs):
        """
        follow_whole_store_enricher
        ------
        全店参数获取

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_whole_store_enricher(
        )
        ```
        """
        self._add_processor(FollowWholeStoreEnricher(kwargs))

        return self


    def follow_rank_feature_enricher(self, **kwargs):
        """
        follow_rank_feature_enricher
        ------
        rank 特征

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_rank_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRankFeatureEnricher(kwargs))

        return self
    
    def follow_recall_feature_enricher(self, **kwargs):
        """
        follow_recall_feature_enricher
        ------
        recall 特征

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_recall_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRecallFeatureEnricher(kwargs))

        return self

    def follow_recall_slide_feature_enricher(self, **kwargs):
        """
        follow_recall_slide_feature_enricher
        ------
        recall 特征

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_recall_slide_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRecallSlideFeatureEnricher(kwargs))

        return self

    def follow_boost_enricher(self, **kwargs):
        """
        follow_boost_enricher
        ------
        boost

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_boost_enricher(
        )
        ```
        """
        self._add_processor(FollowCalcBoostEnricher(kwargs))

        return self

    def follow_feature_perf_observer(self, **kwargs):
        """
        follow_feature_perf_observer
        ------
        perf feature

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_feature_perf_observer(
        )
        ```
        """
        self._add_processor(FollowFeaturePerfObserver(kwargs))

        return self
    
    def follow_memory_data_enrich(self, **kwargs):
        """
        FollowMemoryDataEnricher
        ------
        从内存中获取指定类型的数据指针保存到 common attr 中

        参数配置
        ------
        `data_key`: [string] [动态参数] 数据的 key

        `data_type`: [string] 数据类型

        `save_data_ptr_to_attr`: [string] 保存数据指针的 common attr
        """
        self._add_processor(FollowMemoryDataEnricher(kwargs))
        return self

    def follow_locallife_live_store_enricher(self, **kwargs):
        """
        FollowLocalLifeLiveScoreEnricher
        ------
        本地生活直播计算最终得分

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_locallife_live_store_enricher(
            xtr_list = []
        )
        ```
        """
        self._add_processor(FollowLocalLifeLiveScoreEnricher(kwargs))

        return self
    
    def follow_kuiba_user_enricher(self, **kwargs):
        """
        FollowKuibaUserEnricher
        ------
        user common attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_kuiba_user_enricher(
            kuiba_user_attr_name = "xx"
        )
        ```
        """
        self._add_processor(FollowKuibaUserEnricher(kwargs))

        return self
    
    def follow_mix_es_arranger(self, **kwargs):
        """
        FollowMixEnsembleSortArranger
        ------
        混排 es 

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_mix_es_arranger(
            natural_weight = "xx"
        )
        ```
        """
        self._add_processor(FollowMixEnsembleSortArranger(kwargs))

        return self

    def follow_leaf_ad_athena_send_enricher(self, **kwargs):
        """
        FollowLeafAdAthenaSendEnricher
        ------
        广告召回发送

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_ad_athena_send_enricher(
        )
        ```
        """
        self._add_processor(FollowLeafAdAthenaSendEnricher(kwargs))

        return self

    def follow_retrieve_by_ad_athena(self, **kwargs):
        """
        FollowLeafAdAthenaRetriever
        ------
        广告召回接收

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_retrieve_by_ad_athena(
        )
        ```
        """
        self._add_processor(FollowLeafAdAthenaRetriever(kwargs))

        return self

    def follow_ad_dsp_enricher(self, **kwargs):
        """
        FollowAdDspEnricher
        ------
        广告 dsp info

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_ad_dsp_enricher(
        )
        ```
        """
        self._add_processor(FollowAdDspEnricher(kwargs))

        return self


    def follow_mix_gen_seq_list_retrieve(self, **kwargs):
        """
        FollowMixGenSeqListRetriever
        ------
        序列生成

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_mix_gen_seq_list_retrieve(
            item_table = self.seq_table_name,
            photo_table = "photo_table",
            live_tabl = "live_table",
            follow_mix_rank_seq_length = "{{follow_mix_rank_seq_length}}"
        )
        ```
        """
        self._add_processor(FollowMixGenSeqListRetriever(kwargs))

        return self

    def follow_mix_cal_seq_enricher(self, **kwargs):
        """
        FollowMixCalSeqScoreEnricher
        ------
        序列分计算

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_mix_cal_seq_enricher(
            item_table = self.seq_table_name,
            calculators = self.cal_seq_caculators
        )
        ```
        """
        self._add_processor(FollowMixCalSeqScoreEnricher(kwargs))

        return self

    def follow_mix_select_final_seq_retrieve(self, **kwargs):
        """
        FollowMixSelectFinalSeqRetriever
        ------
        选择最优序列替换主表结果

        参数配置
        ------
 
        调用示例
        ------
        ```
        .follow_mix_select_final_seq_retrieve(
            seq_results_table = self.seq_table_name,
            reset_existing_item_attrs = False
        ) 
        ```
        """
        self._add_processor(FollowMixSelectFinalSeqRetriever(kwargs))

        return self

    def follow_biz_type_enricher(self, **kwargs):
        """
        FollowBizTypeEnricher
        ------
        biz type

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_biz_type_enricher(
        )
        ```
        """
        self._add_processor(FollowBizTypeEnricher(kwargs))

        return self

    def follow_common_score_calculator(self, **kwargs):
        """
        FollowCommonScoreCalculator
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_common_score_calculator(
        )
        ```
        """
        self._add_processor(FollowCommonScoreCalculator(kwargs))

        return self

    def follow_gift_live_score_calculator(self, **kwargs):
        """
        FollowGiftLiveScoreCalculatorEnricher
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_gift_live_score_calculator(
        )
        ```
        """
        self._add_processor(FollowGiftLiveScoreCalculatorEnricher(kwargs))

        return self

    def follow_merchant_live_score_calculator(self, **kwargs):
        """
        FollowMerchantLiveScoreCalculator
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_live_score_calculator(
        )
        ```
        """
        self._add_processor(FollowMerchantLiveScoreCalculatorEnricher(kwargs))

        return self

    def follow_merchant_live_score_calculator_v2(self, **kwargs):
        """
        FollowMerchantLiveScoreCalculator
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_live_score_calculator_v2(
        )
        ```
        """
        self._add_processor(FollowMerchantLiveScoreCalculatorV2Enricher(kwargs))

        return self
    
    def follow_slide_merchant_live_score_calculator(self, **kwargs):
        """
        FollowSlideMerchantLiveScoreCalculatorEnricher
        ------
        SlideMerchantLiveScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_merchant_live_score_calculator(
        )
        ```
        """
        self._add_processor(FollowSlideMerchantLiveScoreCalculatorEnricher(kwargs))

        return self

    def follow_slide_merchant_cart_score_calculator(self, **kwargs):
        """
        FollowSlideMerchantCartScoreCalculatorEnricher
        ------
        SlideMerchantCartScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_merchant_cart_score_calculator(
        )
        ```
        """
        self._add_processor(FollowSlideMerchantCartScoreCalculatorEnricher(kwargs))

        return self
    
    def follow_slide_merchant_livehead_score_calculator(self, **kwargs):
        """
        FollowSlideMerchantLiveheadScoreCalculatorEnricher
        ------
        SlideMerchantLiveheadScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_merchant_livehead_score_calculator(
        )
        ```
        """
        self._add_processor(FollowSlideMerchantLiveheadScoreCalculatorEnricher(kwargs))

        return self

    def follow_slide_gift_live_score_calculator(self, **kwargs):
        """
        FollowSlideGiftLiveScoreCalculatorEnricher
        ------
        SlideGiftLiveScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_gift_live_score_calculator(
        )
        ```
        """
        self._add_processor(FollowSlideGiftLiveScoreCalculatorEnricher(kwargs))

        return self
    
    def follow_store_wide_score_calculator_enricher(self, **kwargs):
        """
        FollowStoreWideScoreCalculatorEnricher
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_store_wide_score_calculator_enricher(
        )
        ```
        """
        self._add_processor(FollowStoreWideScoreCalculatorEnricher(kwargs))

        return self

    def follow_slide_store_wide_score_calculator_enricher(self, **kwargs):
        """
        FollowSlideStoreWideScoreCalculatorEnricher
        ------
        SlideStoreWideScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_store_wide_score_calculator_enricher(
        )
        ```
        """
        self._add_processor(FollowSlideStoreWideScoreCalculatorEnricher(kwargs))

        return self

    def follow_ad_score_calculator_enricher(self, **kwargs):
        """
        FollowAdScoreCalculatorEnricher
        ------
        common_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_ad_score_calculator_enricher(
        )
        ```
        """
        self._add_processor(FollowAdScoreCalculatorEnricher(kwargs))

        return self

    def follow_slide_natural_score_calculator(self, **kwargs):
        """
        FollowSlideNaturalScoreCalculatorEnricher
        ------
        计算 natural_score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_natural_score_calculator(
        )
        ```
        """
        self._add_processor(FollowSlideNaturalScoreCalculatorEnricher(kwargs))

        return self

    def follow_fill_ad_response_enricher(self, **kwargs):
        """
        FollowFillAdResponseEnricher
        ------
        广告字段

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_fill_ad_response_enricher(
        )
        ```
        """
        self._add_processor(FollowFillAdResponseEnricher(kwargs))

        return self

    def follow_kuiba_user_to_str_enricher(self, **kwargs):
        """
        FollowKuibaUserToStrEnricher
        ------
        kuiba user to str

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_kuiba_user_to_str_enricher(
        )
        ```
        """
        self._add_processor(FollowKuibaUserToStrEnricher(kwargs))

        return self

    def follow_mix_log_enricher(self, **kwargs):
        """
        FollowMixLogEnricher
        ------
        mix log enricher

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_mix_log_enricher(
        )
        ```
        """
        self._add_processor(FollowMixLogEnricher(kwargs))

        return self
    
    def follow_feature_debug_enricher(self, **kwargs):
        """
        FollowFeatureDebugEnricher
        ------
        debug

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_feature_debug_enricher(
        )
        ```
        """
        self._add_processor(FollowFeatureDebugEnricher(kwargs))

        return self

    def follow_listwise_model_seq_feature_enricher(self, **kwargs):
        """
        FollowMixListwiseModelSeqFeatureEnricher
        ------
        debug

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_listwise_model_seq_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowMixListwiseModelSeqFeatureEnricher(kwargs))

        return self

    def follow_merchant_str_to_pxtr_enricher(self, **kwargs):
        """
        FollowMerchantStrToPxtrEnricher
        ------
        parse cache

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_str_to_pxtr_enricher(
        )
        ```
        """
        self._add_processor(FollowMerchantStrToPxtrEnricher(kwargs))

        return self


    def follow_merchant_pxtr_to_str_enricher(self, **kwargs):
        """
        FollowMerchantPxtrToStrEnricher
        ------
        cache to string

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_pxtr_to_str_enricher(
        )
        ```
        """
        self._add_processor(FollowMerchantPxtrToStrEnricher(kwargs))

        return self

    def fill_whole_store_roi_photo_enricher(self, **kwargs):
        """
        FollowFillWholeStoreRoiPhotoEnricher
        ------
        全店信息回传 photo

        参数配置
        ------

        调用示例
        ------
        ```
        flow.fill_whole_store_roi_photo_enricher(
        )
        ```
        """
        self._add_processor(FollowFillWholeStoreRoiPhotoEnricher(kwargs))

        return self

    def fill_whole_store_roi_live_enricher(self, **kwargs):
        """
        FollowFillWholeStoreRoiLiveEnricher
        ------
        全店信息回传 live

        参数配置
        ------

        调用示例
        ------
        ```
        flow.fill_whole_store_roi_live_enricher(
        )
        ```
        """
        self._add_processor(FollowFillWholeStoreRoiLiveEnricher(kwargs))

        return self

    def follow_pasre_user_info_enricher(self, **kwargs):
        """
        FollowParseUserInfoEnricher
        ------
        将 user_info 解析到 common attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_pasre_user_info_enricher(
        )
        ```
        """
        self._add_processor(FollowParseUserInfoEnricher(kwargs))

        return self

    def follow_retrieval_feature_enricher(self, **kwargs):
        """
        FollowRetrievalFeatureEnricher
        ------
        生产调用召回所需 attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_retrieval_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRetrievalFeatureEnricher(kwargs))

        return self

    def follow_item_to_common_feature_enricher(self, **kwargs):
        """
        FollowItemToCommonFeatureEnricher
        ------
        将 item 的特征转换到 common attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_item_to_common_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowItemToCommonFeatureEnricher(kwargs))

        return self

    def follow_mix_redis_cache_retrieve(self, **kwargs):
        """
        FollowLeafRedisCacheRetriever
        ------
        follow cache 召回

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_mix_redis_cache_retrieve(
        )
        ```
        """
        self._add_processor(FollowLeafRedisCacheRetriever(kwargs))

        return self

    def follow_leaf_redis_cache_enricher(self, **kwargs):
        """
        FollowLeafRedisCacheEnricher
        ------
        生成 follow cache

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_redis_cache_enricher(
        )
        ```
        """
        self._add_processor(FollowLeafRedisCacheEnricher(kwargs))

        return self


    def follow_parse_dynamic_parameter_enricher(self, **kwargs):
        """
        FollowParseDynamicParameterEnricher
        ------
        解析 dynamic_parameter

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_parse_dynamic_parameter_enricher(
        )
        ```
        """
        self._add_processor(FollowParseDynamicParameterEnricher(kwargs))

        return self


    def follow_mc_slide_feature_enricher(self, **kwargs):
        """
        FollowMcSlideFeatureEnricher
        ------
        内流 粗排 特征

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_mc_slide_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowMcSlideFeatureEnricher(kwargs))

        return self
    

    def follow_percent_punish_item_enricher(self, **kwargs):
        """
        FollowPercentPunishItemEnricher
        ------
        解析 percent_punish_list
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_percent_punish_item_enricher(
        )
        ```
        """
        self._add_processor(FollowPercentPunishItemEnricher(kwargs))

        return self

    def follow_parse_photo_colossus_enricher(self, **kwargs):
        """
        FollowParsePhotoColossusEnricher
        ------
        解析 photo_colossus
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_parse_photo_colossus_enricher(
        )
        ```
        """
        self._add_processor(FollowParsePhotoColossusEnricher(kwargs))

        return self

    def follow_parse_photo_colossus_v2_enricher(self, **kwargs):
        """
        FollowParsePhotoColossusV2Enricher
        ------
        解析 photo_colossus
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_parse_photo_colossus_v2_enricher(
        )
        ```
        """
        self._add_processor(FollowParsePhotoColossusV2Enricher(kwargs))

        return self

    def follow_parse_live_colossus_enricher(self, **kwargs):
        """
        FollowParseLiveColossusEnricher
        ------
        解析 live_colossus
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_parse_live_colossus_enricher(
        )
        ```
        """
        self._add_processor(FollowParseLiveColossusEnricher(kwargs))

        return self

    def follow_leaf_slide_mc_truncature_arranger(self, **kwargs):
        """
        FollowLeafSlideMcTruncatureArranger
        ------
        内流粗排截断
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_slide_mc_truncature_arranger(
        )
        ```
        """
        self._add_processor(FollowLeafSlideMcTruncatureArranger(kwargs))

        return self


    def follow_pack_live_guest_enricher(self, **kwargs):
        """
        FollowPackLiveGuestEnricher
        ------
        pack 连麦
 
        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_pack_live_guest_enricher(
        )
        ```
        """
        self._add_processor(FollowPackLiveGuestEnricher(kwargs))

        return self

    def follow_v5_mc_highvalue_ua_boost_enricher(self, **kwargs):
        """
        FollowV5McHighvalueUABoostEnricher
        ------
        内流粗排 high value ua boost

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_v5_mc_highvalue_ua_boost_enricher(
        )
        ```
        """
        self._add_processor(FollowV5McHighvalueUABoostEnricher(kwargs))

        return self
    
    def follow_support_live_aid_enricher(self, **kwargs):
        """
        FollowSupportLiveAidEnricher
        ------
        follow_support_live_aid_enricher

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_support_live_aid_enricher(
        )
        ```
        """
        self._add_processor(FollowSupportLiveAidEnricher(kwargs))

        return self

    def follow_rank_slide_feature_enricher(self, **kwargs):
        """
        FollowRankSlideFeatureEnricher
        ------
        内流 精排 特征

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_rank_slide_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRankSlideFeatureEnricher(kwargs))

        return self

    def follow_merchant_slide_str_to_pxtr_enricher(self, **kwargs):
        """
        FollowMerchantSlideStrToPxtrEnricher
        ------
        内流 merchant live pxtr parse cache

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_slide_str_to_pxtr_enricher(
        )
        ```
        """
        self._add_processor(FollowMerchantSlideStrToPxtrEnricher(kwargs))

        return self

    def follow_merchant_slide_pxtr_to_str_enricher(self, **kwargs):
        """
        FollowMerchantSlidePxtrToStrEnricher
        ------
        内流 merchant live pxtr cache to string

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_merchant_slide_pxtr_to_str_enricher(
        )
        ```
        """
        self._add_processor(FollowMerchantSlidePxtrToStrEnricher(kwargs))

        return self

    def follow_pasre_user_info_slide_enricher(self, **kwargs):
        """
        FollowParseUserInfoSlideEnricher
        ------
        内流 parse user info

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_pasre_user_info_slide_enricher(
        )
        ```
        """
        self._add_processor(FollowParseUserInfoSlideEnricher(kwargs))

        return self

    def follow_entry_feed_feature_enricher(self, **kwargs):
        """
        FollowEntryFeedFeatureEnricher
        ------
        EntryFeed id feature

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_entry_feed_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowEntryFeedFeatureEnricher(kwargs))

        return self

    def follow_feed_back_feature_enricher(self, **kwargs):
        """
        FollowFeedbackFeatureEnricher
        ------
        feed_back id feature

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_feed_back_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowFeedbackFeatureEnricher(kwargs))

        return self
    
    def follow_rank_final_perflog(self, **kwargs):
        """
        FollowRankFinalPerflogObserver
        ------
        rank 结束后的 perf

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_rank_final_perflog(
        )
        ```
        """
        self._add_processor(FollowRankFinalPerflogObserver(kwargs))
        return self
        
        
    def follow_calc_embedding_score_enricher(self, **kwargs):
        """
        FollowCalcEmbeddingScoreEnricher
        ------
        CalcEmbeddingScore

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_calc_embedding_score_enricher(
        )
        ```
        """
        self._add_processor(FollowCalcEmbeddingScoreEnricher(kwargs))
        return self

    def follow_slide_live_ensemble_sort_arranger(self, **kwargs):
        """
        FollowSlideLiveEnsembleSortArranger
        ------
        内流 live es 排序

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_slide_live_ensemble_sort_arranger(
        )
        ```
        """
        self._add_processor(FollowSlideLiveEnsembleSortArranger(kwargs))
        return self

    def follow_duration_diversity_enricher(self, **kwargs):
        """
        FollowDurationDiversityEnricher
        ------
        Calc DurationDiversity Score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_duration_diversity_enricher(
        )
        ```
        """
        self._add_processor(FollowDurationDiversityEnricher(kwargs))
        return self

    def follow_leaf_slide_diversity_rank_arranger(self, **kwargs):
        """
        FollowLeafSlideDiversityRankArranger
        ------
        arranger durationDiversity Score

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_leaf_slide_diversity_rank_arranger(
        )
        ```
        """
        self._add_processor(FollowLeafSlideDiversityRankArranger(kwargs))
        return self

    def follow_union_table(self, **kwargs):
        """
        FollowTableUnionMixer
        ------
        带去重的 union_table

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_union_table(
        )
        ```
        """
        self._add_processor(FollowTableUnionMixer(kwargs))
        return self

    def follow_bid_info_fill(self, **kwargs):
        """
        FollowBidInfoFilleMixer
        ------
        填充短引投放信息

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_bid_info_fill(
        )
        ```
        """
        self._add_processor(FollowBidInfoFilleMixer(kwargs))
        return self

    def follow_social_cache_result_to_pxtr_enricher(self, **kwargs):
        """
        FollowSocialCacheResultToPxtrEnricher
        ------
        解析 social cache results

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_social_cache_result_to_pxtr_enricher(
        )
        ```
        """
        self._add_processor(FollowSocialCacheResultToPxtrEnricher(kwargs))

        return self

    def follow_social_result_to_str_enricher(self, **kwargs):
        """
        FollowSocialResultToStrEnricher
        ------
        social result to str

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_social_result_to_str_enricher(
        )
        ```
        """
        self._add_processor(FollowSocialResultToStrEnricher(kwargs))

        return self

    def follow_social_ensemble_sort_enricher(self, **kwargs):
        """
        FollowSocialEnsembleSortEnricher
        ------
        social results es

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_social_ensemble_sort_enricher(
        )
        ```
        """
        self._add_processor(FollowSocialEnsembleSortEnricher(kwargs))

        return self

    def follow_item_extra_attr_enricher(self, **kwargs):
        """
        FollowItemExtraAttrEnricher
        ------
        将item attr 转成 ItemExtraAttr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_item_extra_attr_enricher(
        )
        ```
        """
        self._add_processor(FollowItemExtraAttrEnricher(kwargs))

        return self

    def follow_retrieval_slide_feature_enricher(self, **kwargs):
        """
        FollowRetrievalSlideFeatureEnricher
        ------
        内流 rank 生产调用召回所需 attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_retrieval_slide_feature_enricher(
        )
        ```
        """
        self._add_processor(FollowRetrievalSlideFeatureEnricher(kwargs))

        return self

    def follow_retrieval_exp_tag_enricher(self, **kwargs):
        """
        FollowRetrievalExptagEnricher
        ------
        内流召回后生成 exp tag

        参数配置
        ------

        调用示例
        ------
        ```
        flow.follow_retrieval_exp_tag_enricher(
        )
        ```
        """
        self._add_processor(FollowRetrievalExptagEnricher(kwargs))

        return self
