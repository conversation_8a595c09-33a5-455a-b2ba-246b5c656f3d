#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .nr_retriever import *
from .nr_enricher import *
from .nr_arranger import *
from .nr_observer import *

class NrApiMixin(CommonLeafBaseMixin):
  """
  Nr Processor API 接口的 Mixin 实现
  """
  def calc_weighted_cumprod_v2(self, **kwargs):
    """
    NrRecoWeightedCumprodV2Enricher
    ------
    对多个 item attr 进行累乘计算。
    参数配置
    ------
    `formula_version`: [int] 选配项，单项分计算公式, 默认为 0
      - 0: `Si = TT(pow(ATTR_VALUE(channels[i].name), channels[i].weight))`
      - 1: `1 - TT(1 - pow(ATTR_VALUE(channels[i].name), channels[i].weight))`
      
    `channels`: [list] 需要用于计算累乘的队列配置，每个队列包含以下两个配置项：
      - `name`: [string] 队列名，将从同名 double 或 int 类型 item_attr 获取值进行计算
      - `weight`: [double] [动态参数] 该队列的参数
      
    `allow_norm_score`: [bool] 是否归一化输出结果。默认值 True

    `output_item_attr`: [string] 将最后计算出累乘分数存入指定的 double item_attr
    
    调用示例
    ------
    ``` python
    .calc_weighted_cumprod(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      output_item_attr = "final_score",
    )
    ```
    """
    self._add_processor(NrRecoWeightedCumprodV2Enricher(kwargs))
    return self
  
  def nr_gen_seq_num_over_attr(self, **kwargs):
    """
    NrSeqNumOverAttrEnricher
    ------
    基于当前的排序结果，根据 attr 分桶，并统计 item 在桶内的序（从 0 开始）

    参数配置
    ------
    `attr_name`: [string] 用于分桶的 item attr（支持 int/string）

    `default_attr_val`: [int] 选配项，分桶 item_attr 取值为空时，设置的默认值。默认取 0

    `output_seq_attr`: [string] 输出的序信息

    调用示例
    ------
    ``` python
    .nr_gen_seq_num_over_attr(
      attr_name="hetu2",
      default_attr_val=-1,
      output_seq_attr="seq_over_hetu2",
    )
    ```
    """
    self._add_processor(NrSeqNumOverAttrEnricher(kwargs))
    return self
  
  def nr_calc_feedback_score(self, **kwargs):
    """
    NrFeedbackScoreEnricher
    ------
    根据历史浏览视频 items embedding 矩阵和候选集 items embedding 矩阵做乘积,
    生成候选集与历史浏览视频的相似度矩阵,再按照 rowwise sum 计算候选集与历史浏览视频
    的相似分之和,和越大表示候选 item 与历史浏览视频越相似.

    参数配置
    ------
    `item_embedding_attr`: [string] 存储 item embedding 的 item attr

    `browsed_items_from_attr`: [string] 历史观看 items 的 common_attr 名称

    `browsed_items_weight_attr`: [string] 选配项，存储历史观看 items 权重的 item_attr

    `export_item_attr`: [string] 最终输出的 item 反馈分

    `allow_normalize`: [bool] 默认值为 True, 若为 True 则会对 embedding 进行归一化.

    调用示例
    ------
    ``` python
    .nr_calc_feedback_score(
      item_embedding_attr="mmu_embedding",
      browsed_items_from_attr="userprofile_vps",
      browsed_items_weight_attr="time_decay_weight",
      export_item_attr="feedback_score",
    )
    ```
    """
    self._add_processor(NrFeedbackScoreEnricher(kwargs))
    return self


  def nr_colossus_interest_v2(self, **kwargs):
    """
    NrColossusInterestV2Enricher

    参数配置
    ------
    `colossus_item_attr`: [list] 需要输入的 colossus response, 格式为 SimItemV2* list
    `candidate_uids_attr`: [list] 输出的 author id list
    `output_interest_attr`: [list] 输出用户已观类目的视频
    `output_explore_attr`: [list] 输出用户未观（但可能喜欢）类目的视频

    调用示例
    ------
    ``` python
    .nr_colossus_interest_v2(
      colossus_item_attr = "dnn_cluster_colossus_simv2_response",
      candidate_uids_attr = "ctr_candidate_user_ids",
      candidate_uid_top_realshow=2500,
      output_interest_attr='interest_pids_new',
      output_explore_attr='explore_pids_new',
    )
    ```
    """
    self._add_processor(NrColossusInterestV2Enricher(kwargs))
    return self
  
  def nr_calc_fractile(self, **kwargs):
    """
    NrCalcFractileEnricher
    ------
    统计 list common_attr 的分位数，并存入对应的变量中

    参数配置
    ------
    `common_attrs`: [list] 需要统计的 common_attr 名称列表，支持 int_list 和 double_list 类型

    `fractile_num`: [int] 需要统计的分位数个数，分位数会等间隔选择。如：fractile_num=3，会计算分位数 [0.25, 0.5, 0.75]，并存入 output_fractile_list

    `output_fractile_prefix`: [string] 输出的分位数 list attr 的前缀。

    调用示例
    ------
    ``` python
    # 统计 pctr_list 分位数，并存入 fractile_of_pctr_list
    .nr_calc_fractile(
      common_attrs=["pctr_list"],
      fractile_num=3, 
      output_fractile_prefix="fractile_of_"
    )
    ```
    """

    self._add_processor(NrCalcFractileEnricher(kwargs))
    return self

  def nr_debias_score(self, **kwargs):
    """
    NrDebiasScoreEnricher
    ------
    对当前的序进行纠偏

    参数配置
    ------
    `dimensions`: [list] 需要用于纠偏的维度配置，每个维度包含以下几个配置项：
      - `item_attr`: [string] 维度名，将从同名 int 类型 item_attr 获取值进行分组纠偏
      - `confidence`: [double] [动态参数] 描述该维度的置信度（值域[0, 1]）

    `debiased_score`: [string] 输出的纠偏分

    调用示例
    ------
    ``` python
    .nr_debias_score(
      dimensions=[
        dict(item_attr="duration_bucket", confidence=0.9),
        dict(item_attr="hetu_level_one", confidence=0.6),
      ],
      debiased_score="debiased_score"
    )
    ```
    """

    self._add_processor(NrDebiasScoreEnricher(kwargs))
    return self
  
  def nr_global_dynamic_fractile_score(self, **kwargs):
    """
    NrGlobalDynamicFractileScoreEnricher
    ------
    ranking personal user xtr enrich

    参数配置
    ------
    `kess_service`: [string][动态参数] 必配项，非空。 调用的 kess service
    `timout_ms`: [int][动态参数] 必配项，非空。 调用服务超时时间
    `request_type`: [int][动态参数] 必配项，非空。 request type

    调用示例
    ------
    ``` python
      .nr_global_dynamic_fractile_score(
          kess_service = '{{nr_global_dynamic_fractile_service_name}}',
          request_type= '{{nr_global_dynamic_fractile_request_type}}',
          timeout_ms = '{{nr_global_dynamic_fractile_timeout_ms}}',
          input_common_attr=['user_info_str'],
          input_item_attrs=['ctr', 'ltr', 'ftr', 'cmtr', 'ptr', 'lvtr', 'svr'],
          output_item_attrs=['fr_ncee_score']
      )
    ```
    """
    self._add_processor(NrGlobalDynamicFractileScoreEnricher(kwargs))
    return self
  
  def vector_operation(self, **kwargs):
    """
    NrVectorOperationEnricher
    ------
    vector 按位运算： output_vector = (vector_a * scalar_a) operator (vector_b * scalar_b)

    参数配置
    ------
    `vector_a`: [string] common list attr 的变量名

    `scalar_a`: [double|int] [动态参数] 选配项，vector_a 的系数（common attr），默认值为 1.0 
    
    `vector_b`: [string] 选配项，common list attr 的变量名。若不配置，则使用向量 [1, 1, ..., 1] 参与计算，长度与 vector_a 相同
    
    `scalar_b`: [double|int] [动态参数] 选配项，vector_a 的系数（common attr），默认值为 0.0

    `operator`: [string] vector 运算符，支持四则运算（"+", "-", "*", "/"）, 位运算（"&", "|", "^", "<<", ">>"），指数运算（"pow"）

    `output_vector`: [string] 输出 vector 的变量（common attr）

    调用示例
    ------
    ``` python
    .vector_operation(
      vector_a="vector_a",
      scalar_a=2.0,
      vector_b="vector_b",
      scalar_b=1.0,
      operator="+",
      output_vector="output_vector"
    )
    ```
    """
    self._add_processor(NrVectorOperationEnricher(kwargs))
    return self
  def max_cost_distribute(self, **kwargs):
    """
    NrHetuCostDistributeEnricher
    ------
    统计总成本一定，不同种类下的 01 背包分配

    参数配置
    ------
    `photo_cost_list`: [list] [动态参数] 每个 photo 对应的 cost 大小。

    `photo_score_list`: [list] [动态参数] 每个 photo 对应的 score 大小

    `max_cost_quota`: [int] 背包问题的最大 cost 阈值。

    `output_common_attr`: [list] 输出 photo list 是否会被背包选中。

    调用示例
    ------
    ``` python
    # 统计总成本一定，不同种类下的 01 背包分配
    .max_cost_distribute(
      photo_cost_list="{{photo_cost_list}}",
      photo_score_list="{{photo_score_list}}",
      max_cost_quota="nr_t_mc_hetu_distribute_max_cost",
      output_common_attr="mc_hetu_distribute_hit_list"
    )
    ```
    """

    self._add_processor(NrHetuCostDistributeEnricher(kwargs))
    return self

  def retrieve_by_common_attr_with_reason(self, **kwargs):
    """
    NrCommonAttrWithReasonRetriever
    ------
    1、从某个 int 或 int_list 类型的 CommonAttr 中获取 item_id 
    2、从另一个 int 或 int_list 类型的 CommonAttr 中获取 reason 
    3、按item_id和reason一一对应进行召回并填入结果集

    参数配置
    ------
    `attr`: [string] 待召回的 CommonAttr 名称

    `reason_attr`: [string] 待配置的reason CommonAttr 名称

    调用示例
    ------
    ``` python
    .retrieve_by_common_attr_with_reason(attr="like_list", reason_attr="exp_tag_list")
    ```
    """
    self._add_processor(NrCommonAttrWithReasonRetriever(kwargs))
    return self

  def graph_collab_retrieve(self, **kwargs):
    """
    NrGraphCollabRetriever
    参数
    ------
    `user_info_str_attr`: [string] 序列化的 user info attr 名称

    `kess_service`: [string] [动态参数] 召回服务 kess 名

    `timeout_ms`: [int] [动态参数] 请求召回服务超时时间

    `retrieve_num`: [int] [动态参数] 召回数量

    `reason`: [int] [动态参数] 召回 reason

    `redis_key_prefix`: [string] [动态参数] redis key 前缀

    `trigger_res_num`: [int] [动态参数] trigger 结果数

    `trigger_num`: [int] [动态参数] trigger 数， 默认值 50

    `max_click_size`: [int] [动态参数] max_click_size 默认值 30

    `max_like_size`: [int] [动态参数] max_like_size 默认值 20

    `max_follow_size`: [int] [动态参数] max_follow_size 默认值 10

    `max_forward_size`: [int] [动态参数] max_forward_size 默认值 0

    `max_longview_size`: [int] [动态参数] max_longview_size 默认值 0

    `max_profile_enter_size`: [int] [动态参数] max_profile_enter_size 默认值 0

    `trigger_type`: [int] [动态参数] trigger_type 默认值 0

    `oaid`: [string] [动态参数] oaid 默认值为空字符串

    示例
    ------
    ``` python
    .graph_collab_retrieve(
            user_info_str_attr = 'user_info_str',
            kess_service = '{{gamora_graph_collab_retr_service_ri}}',
            retrieve_num = 1000,
            redis_key_prefix = '{{gamora_graph_collab_redis_key_prefix_ri}}',
            trigger_res_num = '{{gamora_graph_collab_trigger_res_num_ri}}',
            timeout_ms = 150,
            reason = 1234,
            max_click_size = 30,
            max_like_size = 20,
            max_follow_size = 10,
            max_forward_size = 0,
            max_longview_size = 0,
            max_profile_enter_size = 0,
            oaid = '{{oaid}}'
        )
    ```
    """
    self._add_processor(NrGraphCollabRetriever(kwargs))
    return self
  
  def longterm_itemcf_retrieve(self, **kwargs):
    """
    NrLongtermItemcfRetriever
    参数
    ------
    `user_info_attr`: [string] user info attr 名称

    `kess_service`: [string] [动态参数] 召回服务 kess 名

    `timeout_ms`: [int] [动态参数] 请求召回服务超时时间, 默认值 50

    `expected_trigger_count`: [int] [动态参数] 触发数量

    `reason`: [int] [动态参数] 召回 reason

    `prefix`: [string] [动态参数] 召回用的 redis prefix

    `long_term_triggers`: [int_list] [动态参数] 召回用的 long term triggers

    示例
    ------
    ``` python
    .longterm_itemcf_retrieve(
            user_info_attr = 'user_info',
            kess_service = '{{kess_service_ab_param}}',
            expected_trigger_count = 1000,
            timeout_ms = 50,
            reason = 1234,
            prefix = 'profile_p2p_',
            long_term_triggers = '{{long_term_triggers}}'
        )
    ```
    """
    self._add_processor(NrLongtermItemcfRetriever(kwargs))
    return self

  def calc_nr_weighted_sum(self, **kwargs):
    """
    NrRecoWeightedSumEnricher
    ------
    对多个 item attr 进行加权计算。

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (线性加权)
      - 0: `Si = channels[i].weight * ATTR_VALUE(channels[i].name` 线性加权公式
      - 1: `Si = channels[i].weight * log(ATTR_VALUE(channels[i].name)` 对数加权公式
      - 100: `Si = channels[i].weight * pow(ATTR_VALUE(channels[i].name, channels[i].pow)` 指数加权公式

    `channels`: [list] 需要用于加权计算的队列配置，每个队列包含以下两个个配置项：
      - `name`: [string] 队列名，将从同名 double 或 int 类型 item_attr 获取值进行加权计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `pow`: [double] [动态参数] 该队列的指数项

    `output_item_attr`: [string] 将最后计算出加权和分数存入指定的 double item_attr

    调用示例
    ------
    ``` python
    .calc_nr_weighted_sum(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      output_item_attr = "final_score",
    )
    ```
    """
    self._add_processor(NrRecoWeightedSumEnricher(kwargs))
    return self

  def list_to_string(self, **kwargs):
    """
    NrRecoListToStringEnricher
    ------
    将 list common attr 转化为 string

    参数配置
    ------
    `attr_seperator`: [string] attr 之间的分隔符，默认为 ','

    `list_seperator`: [string] 列表成员之间的分隔符，默认为 ' ' (空格)

    `attrs`: [list] 带转化的 attr 列表，仅支持 list common attr

    `output_attr`: [string] 输出的 common attr

    调用示例
    ------
    ``` python
    .list_to_string(
      attrs=["ctr_list", "ltr_list"],
      output_attr="concat_str"
    )
    ```
    """
    self._add_processor(NrRecoListToStringEnricher(kwargs))
    return self
  
  def replace_mock_user_info(self, **kwargs):
    """
    NrRecoReplaceMockUserInfoEnricher
    ------
    将真机模拟请求中user info的uid和did修改为真实的

    参数配置
    ------
    `input_user_info_pb`: [string] 需要修改的 user_info protobuf 字段名

    `replace_user_id_attr`: [string] 需要替换成 user_id 的 common attr

    `replace_device_id_attr`: [string] 需要替换成 device_id 的 common attr

    调用示例
    ------
    ``` python
    .replace_mock_user_info(
      input_user_info_pb="user_info",
      replace_user_id_attr="replace_user_id",
      replace_device_id_attr="replace_device_id"
    )
    ```
    """
    self._add_processor(NrRecoReplaceMockUserInfoEnricher(kwargs))
    return self

  def nr_lite_online_configurable_filter_photo_arranger(self, **kwargs):
    """
    NrLiteOnlineConfigurableFilterPhotoArranger

    参数配置
    ------
    `input_item_attrs`: [list] 需要输入的 item attr

    `input_common_attrs`: [list] 需要输入的 common attr

    调用示例
    ------
    ``` python
    .nr_lite_online_configurable_filter_photo_arranger(
        "input_item_attrs": [
            "ad_social_type",
            "audit_cold_review_level",
            "audit_hot_high_tag_level",
            "hetu_tag_list",
            "is_kwai_game_photo",
            "photo_id",
            "topk_audit_level"
        ],
        "input_common_attrs": [
            "user_info",
            "nr_online_configurable_post_filter_user_type",
            "nr_online_configurable_post_filter_user_group_strategy_suffix"
        ]
    )
    ```
    """
    self._add_processor(NrLiteOnlineConfigurableFilterPhotoArranger(kwargs))
    return self

  def nr_parse_colossus_item_enricher(self, **kwargs):
    """
    NrParseColossusItemEnricher

    参数配置
    ------
    `colossus_item_attr`: [list] 需要输入的 colossus response, 格式为 SimItemV2* list

    `output_pid_list_attr`: [list] 输出的 photo id list
    `output_aid_list_attr`: [list] 输出的 author id list
    `output_ts_list_attr`: [list] 输出的 timestamp list
    `output_play_time_list_attr`: [list] 输出的 play_time list
    `output_duration_list_attr`: [list] 输出的 duration list

    调用示例
    ------
    ``` python
    .nr_parse_colossus_item_enricher(
        colossus_item_attr = "colossus_item_attr",
        output_pid_list_attr = "output_pid_list_attr",
        output_aid_list_attr = "output_aid_list_attr",
        output_ts_list_attr = "output_ts_list_attr",
        output_play_time_list_attr = "output_play_time_list_attr",
        output_duration_list_attr = "output_duration_list_attr"
    )
    ```
    """
    self._add_processor(NrParseColossusItemEnricher(kwargs))
    return self
  
  def nr_retr_filter_arranger(self, **kwargs):
    """
    NrRetrievalFilterArranger

    参数配置
    ------
    `user_info_ptr_attr`: [str] user_info_ptr attr名称

    `item_attr_map`: [dict] 输入的 item attr

    `filters` : [dict] 执行的过滤

    调用示例
    ------
    ``` python
    flow.nr_retr_filter_arranger(
            user_info_ptr_attr = "user_info",
            item_attr_map = {'photo_id_attr' : 'photo_info_photo_id',
                             },
            filters = [{'name': 'PhotoInfoFilter', 'enable' : True}]
        )
    ```
    """
    self._add_processor(NrRetrievalFilterArranger(kwargs))
    return self

  def split_string_list(self, **kwargs):
    """
    SlideLeafStringListSplitEnricher
    ------
    将 string_list attr 的每一个元素，切割后，合并放入到 string list attr，仅支持同时处理 input_common_attr

    参数配置
    ------
    `input_common_attr`: [string] 选配项，输入的 string common attr，需与 output_common_attr 配合使用

    `output_common_attr`: [string] 选配项，输出的 string list common attr，需与 input_common_attr 配合使用

    `delimiters`: [string] 分割字符集合。当 delimiters 为空字符串时会按 UTF8 单字符的粒度分割并且忽略其他分割相关配置。

    `trim_spaces`: [bool] 删除切割出来的 string list 周围的空白字符，默认为 False。

    `skip_empty_tokens`: [bool] 跳过空字符串，默认为 False。

    `parse_to_int`: [bool] 是否将分割出的每个字符串转义为 int 值按 int_list 类型存储，默认为 False。

    `parse_to_double`: [bool] 是否将分割出的每个字符串转义为 double 值按 double_list 类型存储，默认为 False。

    `max_num_per_str`: [int] 选配项，动态参数，list_attr 中每个 str 分割后超过 max_num_per_str 后做截断，默认不截断。

    调用示例
    ------
    ``` python
    .split_string_list(
      input_common_attr = "input_string",
      output_common_attr = "output_string_list",
      delimiters=",",
    )
    ```
    """
    self._add_processor(SlideLeafStringListSplitEnricher(kwargs))
    return self

  def retrieve_by_pandora(self, **kwargs):
    """
    RecoPandoraRetriever
    ------
    Pandora定制化召回服务，主要用于 ComiRec 多峰触发召回系列服务

    注意：extra_args 当前默认仅配置 auxiliary；默认使用 trim user_info；当前 hate_filter 未启用。以上如有场景需要，需要 dsl 传递开关并调整 processor cpp 代码

    参数配置
    ------
    `app_name`: [string] [动态参数] 应用名

    `user_info_attr`: [string] UserInfo使用的attr_name，注意只支持Pb格式

    `extra_args`: [string] [动态参数] 召回动态参数配置

    `retrieve_num`: [int] [动态参数] 召回个数

    `kess_service`: [string] [动态参数] 召回服务名

    `service_group`: [string] [动态参数] 类似PRODUCTION

    `timeout_ms`: [int] [动态参数] 服务超时

    `reason`: [int] 召回配置exp_tag

    调用示例
    ------
    ``` python
    .retrieve_by_pandora(
      app_name = "nebula",
      user_info_attr = "user_info_ptr",
      extra_args = "1,2,3...",
      retrieve_num = 100,
      kess_service = "Grpc_xx",
      service_group = "PRODUCTION",
      timeout_ms = 100,
      reason = 1949
    )
    ```
    """
    self._add_processor(RecoPandoraRetriever(kwargs))
    return self

  def slide_leaf_duration_segment_cut_filter(self, **kwargs):
    """
    SlideLeafDurationSegmentCutArranger
    ------
    单列 slide_leaf duration 分桶截断过滤 Arranger。

    参数配置
    ------
    `name`: [string] 过滤流的名字。

    `enable_duration_bucket_cut`: [bool][动态参数] 是否打开分桶截断 支持动态参数。

    `duration_bucket_cut_radio_str`: [string][动态参数] 分桶截断 radio str 字段 支持动态参数。

    `duration_bucket_seg_str`: [string][动态参数] 分桶截断 bucket str 字段 支持动态参数。

    `skip_duration_fiter_exptag_set_str`: [string][动态参数] 分桶截断 exptag 白名单 str 支持动态参数。

    `disable_filter_cutoff_attr_list`:[dict] 豁免过滤的参数 common attr 配置。

    `import_item_attr`: [dict] 依赖的 item attr。

    调用示例
    ------
    ``` python
    .slide_leaf_duration_segment_cut_filter(
        name = "slide_leaf_duration_segment_cut_filter",
        traceback = True,
        user_info_ptr_attr = "user_info_ptr",
        enable_duration_bucket_cut = "{{enable_duration_bucket_cut}}",
        duration_bucket_cut_radio_str = "{{duration_bucket_cut_radio_str}}",
        duration_bucket_seg_str = "{{duration_bucket_seg_str}}",
        skip_duration_fiter_exptag_set_str = "{{skip_duration_fiter_exptag_set_str}}",
        disable_filter_cutoff_attr_list = {
          "enable_adsocial_exempt" : "enable_adsocial_exempt"
        },
        import_item_attr = {
          "upload_time_attr" : "upload_time"
        }
      )
    ```

    """
    self._add_processor(SlideLeafDurationSegmentCutArranger(kwargs))
    return self

  def nr_mock_user_info_post_processing_enricher(self, **kwargs):
    """
    NrMockUserInfoPostProcessingEnricher
    ------
    1. 重写 UserInfo 的部分字段，如 uid、did、app_info_list、like/follow/foward/comment/click、gender 等字段
    2. 构造未登录用户的 action list
    3. 根据 hate_list、report_list 调整 action_list

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段。 [不会修改输入]
    `user_type_ab_param_attr`:[string] 选填项, 指向 ks::reco::nr::UserTypeABParam*, 默认值: `user_type_ab_param`
    `mocked_user_info_str`: [string] 经过修改之后的，序列化的 ks::reco::UserInfo 字段。
    `is_open_common_mock_merge`:[bool] 必填项, 1 部分功能的开关，mock leaf 打开，retr leaf 关闭, 默认: true
    `is_open_local_mock_merge`:[bool] 必填项, 2,3 部分功能的开关，retr leaf 打开，mock leaf 关闭, 默认: true
    `is_open_kuiba_attr_modify`:[bool] 选填项, is_open_local_mock_merge = true 时,是否打开 kuiba 逻辑, 默认: false
    `kuiba_user_attr_str_attr`:[bool] 选填项, is_open_kuiba_attr_modify = true 时, kuiba_user_attr_str 所在 attr 可能会修改它, 默认: `kuiba_user_attr_str`


    调用示例
    ------
    ``` python
    .nr_mock_user_info_post_processing_enricher(
        name = "nr_mock_user_info_post_processing_enricher",
        user_info_attr = "user_info",
        mocked_user_info_str = "mocked_user_info_str"
      )
    ```

    """
    self._add_processor(NrMockUserInfoPostProcessingEnricher(kwargs))
    return self
  
  def nr_mock_low_vv_user_action_info_enricher(self, **kwargs):
    """
    NrMockLowVVUserActionInfoEnricher
    ------
    基于用户的 click_list like_list 等行为进行 i2i 泛化, 然后 mock 进对应的 action list 中

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段。 [不会修改输入]
    `mocked_user_info_str`: [string] 经过修改之后的，序列化的 ks::reco::UserInfo 字段。
    `src_item_list`:[int_list] [动态参数] 泛化的原始 photo id list
    `sim_pid_list`:[int_list] [动态参数] 泛化得到的 photo id list
    `author_id_list`:[int_list] [动态参数] 泛化后的 photo 对应的 author id list
    `duration_ms_list`:[int_list] [动态参数] 泛化后的 photo 对应的 duration_ms list
    `hetu_one_list`:[int_list] [动态参数] 泛化后的 photo 对应的 hetu one list
    `hetu_two_list`:[int_list] [动态参数] 泛化后的 photo 对应的 hetu two list
    `min_action_size`: [int] [动态参数] 填充后的 action list 的最大长度
    `upv1_long_view_pid_list`: [int] [动态参数] video playing stat 获取到的用户长播 photo id list
    `upv1_long_view_ts_list`: [int] [动态参数] video playing stat 获取到的用户长播 server timestamp list


    调用示例
    ------
    ``` python
    .nr_mock_low_vv_user_action_info_enricher(
          user_info_attr = "user_info",
          mocked_user_info_str = "user_info_str",
          src_item_list = "{{src_item_list}}",
          sim_pid_list = "{{sim_pid_list}}",
          author_id_list = "author_id_list",
          duration_ms_list = "{{duration_ms_list}}",
          hetu_one_list = "{{hetu_one_list}}",
          hetu_two_list = "{{hetu_two_list}}",
          min_action_size = "{{min_action_size}}",
          upv1_long_view_pid_list = "{{upv1_long_view_pid_list}}",
          upv1_long_view_ts_list = "{{upv1_long_view_ts_list}}"
      )
    ```

    """
    self._add_processor(NrMockLowVVUserActionInfoEnricher(kwargs))
    return self
  
  def nr_empirical_xtr_enricher(self, **kwargs):
    """
    NrEmpiricalXtrEnricher
    ------
    1. 计算 empirical xtr

    参数配置
    ------
    指定输出的 xtr 名称，详见示例。

    调用示例
    ------
    ``` python
    .nr_empirical_xtr_enricher(
      name = "nr_empirical_xtr_enricher",
      emp_ctr = "emp_ctr",
      emp_ftr = "emp_ftr",
      emp_wtr = "emp_wtr",
      emp_ltr = "emp_ltr",
      emp_ptr = "emp_ptr",
      emp_htr = "emp_htr",
      emp_cmtr = "emp_cmtr",
      emp_svtr = "emp_svtr",
      emp_tag_ctr = "emp_tag_ctr"
    )
    ```

    """
    self._add_processor(NrEmpiricalXtrEnricher(kwargs))
    return self

  def nr_init_common_attr_from_user_info_enricher(self, **kwargs):
    """
    NrInitCommonAttrFromUserInfoEnricher
    ------
    计算 CommonRecoRequest 用到的 common attr

    参数配置
    ------
    `user_info_attr`: [string] 选填项, ks::reco::UserInfo 字段, 默认值 `user_info`
    `user_type_ab_param_attr`:[string] 选填项, 指向 ks::reco::nr::UserTypeABParam*, 默认值: `user_type_ab_param`

    调用示例
    ------
    ``` python
    .nr_init_common_attr_from_user_info_enricher(
      name = "nr_init_common_attr_from_user_info_enricher",
      user_info_attr = "user_info"
    )
    ```

    """
    self._add_processor(NrInitCommonAttrFromUserInfoEnricher(kwargs))
    return self
  
  def nr_user_type_ab_param_enricher(self, **kwargs):
    """
    NrUserTypeABParamEnricher
    ------
    读取 ab 参数 组装成 UserTypeABParam 放到 common attr ptr 里面

    参数配置
    ------
    `user_type_ab_param_out_attr`: [string] 选填项, 产出 判断 user_type 需要用到的 AB 参数结构体, 指向 ks::reco::nr::UserTypeABParam*, 默认值: `user_type_ab_param`

    调用示例
    ------
    ``` python
    .nr_init_common_attr_from_user_info_enricher(
      name = "nr_init_common_attr_from_user_info_enricher",
      user_type_ab_param_out_attr = "user_type_ab_param"
    )
    ```

    """
    self._add_processor(NrUserTypeABParamEnricher(kwargs))
    return self 

  def nr_init_clear_set_userinfo_enricher(self, **kwargs):
    """
    NrInitClearSetUserInfoEnricher
    ------
    clear and set userinfo 部分字段对应 之前的逻辑

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段。
    `user_type_ab_param_attr`:[string] 选填项, 指向 ks::reco::nr::UserTypeABParam*, 默认值: `user_type_ab_param`

    调用示例
    ------
    ``` python
    .nr_init_clear_set_userinfo_enricher(
      name = "nr_init_clear_set_userinfo_enricher",
      user_info_attr = "user_info"
    )
    ```

    """
    self._add_processor(NrInitClearSetUserInfoEnricher(kwargs))
    return self
    
  def nr_abtest_observer(self, **kwargs):
    """
    NrABTestObserver
    ------
    发送AB实时指标

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段，默认值 user_info
    `fr_result_count`: [int] fr common leaf 返回结果数, 默认值 fr_result_count
    `mc_result_count`: [int] mc common leaf 返回结果数, 默认值 mc_result_count
    `fr_end_ts`: [int] 调用 fr common leaf 完成时间戳, 默认值 fr_end_ts
    `mc_end_ts`: [int] 调用 mc common leaf 完成时间戳, 默认值 mc_end_ts
    `retr_end_ts`: [int] retr 流程完成时间戳, 默认值 retr_end_ts

    调用示例
    ------
    ``` python
    .nr_abtest_observer(
      name = "nr_abtest_observer"
    )
    ```

    """
    self._add_processor(NrABTestObserver(kwargs))
    return self
  
  def nr_sample_observer(self, **kwargs):
    """
    NrSampleObserver
    ------
    替代 tnu leaf 的 Distill Sample 和 FullLink Sample

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段，默认值 user_info
    `is_mc_result`: [int] 是否为 mc leaf 返回的 item，默认值 is_mc_result
    `is_sample_mc_result`: [int] 是否为 mc leaf 返回并进行了采样的 item ，默认值 is_sample_mc_result
    `is_fr_result`: [int] 是否为 fr leaf 返回的 item，默认值 is_fr_result

    调用示例
    ------
    ``` python
    .nr_sample_observer(
      name = "nr_sample_observer"
    )
    ```

    """
    self._add_processor(NrSampleObserver(kwargs))
    return self

  def nr_user_info_merge_browsed_ids_enricher(self, **kwargs):
    """
    NrUserInfoMergeBrowsedIdsEnricher
    ------
    userInfo merge request browsed ids字段,对应之前的逻辑

    参数配置
    ------
    `user_info_attr`: [string] ks::reco::UserInfo 字段。

    调用示例
    ------
    ``` python
    .nr_user_info_merge_browsed_ids_enricher(
      name = "nr_user_info_merge_browsed_ids_enricher",
      user_info_attr = "user_info"
    )
    ```

    """
    self._add_processor(NrUserInfoMergeBrowsedIdsEnricher(kwargs))
    return self
  
  def nr_request_align_related_uid_list_enricher(self, **kwargs):
    """
    NrRequestAlignRelatedUidListEnricher
    ------
    新回请求关系链服务, 获取对齐的相关作者

    参数配置
    ------
    `aid_list_attr`: [int_list] [动态参数] 需要获取相关作者的 uid 列表

    `align_related_uid_attr`: [int_list] 输出的对齐的相关的 uid 列表, 不足的用 0 补齐

    `kess_service`: [string] [动态参数] 请求服务的 kess_name

    `timeout_ms`: [int] [动态参数] 服务超时

    `request_num_per_uid`: [int] [动态参数] 每个 uid 请求相似 uid 的上限


    调用示例
    ------
    ``` python
    .nr_request_align_related_uid_list_enricher(
      aid_list_attr = "aid_list",
      align_related_uid_attr = "related_uid_list",
      kess_service = "grpc_ds_relationApiServer",
      timeout_ms = 100,
      request_num_per_uid = 30,
    )
    ```
    """
    self._add_processor(NrRequestAlignRelatedUidListEnricher(kwargs))
    return self
  
  def nr_sliding_window_mmr_diversity(self, **kwargs):
    """
    NrSlidingWindowMmrDiversityArranger
    ------
    带滑动窗口的 mmr 多样性策略

    参数配置
    ------
    'limit': [int] [动态参数] mmr 计算的 item 数

    'mmr_lambda': [double] [动态参数] 相关性和多样性权重, 取值范围 [0, 1)

    'item_embedding_attr': [string] [动态参数] mmr 使用的 embedding attr

    'ranking_score_attr': [string] [动态参数] mmr 计算使用的 ranking score attr

    'output_item_mmr_score_attr': [string] [动态参数] 计算 mmr 时生成的 item attr mmr_score

    'allow_empty_embedding": [bool] [动态参数] 允许 item embedding 为空, 默认为 False

    'sliding_window_size': [int] [动态参数] 滑动窗口数, 默认值为 mmr 计算的 item 数

    调用示例
    ------
    ``` python
    .nr_sliding_window_mmr_diversity(
      limit=8,
      mmr_lambda=0.5,
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score",
      output_item_mmr_score_attr="mmr_score",
      sliding_window_size=5
    )
    ```
    """
    self._add_processor(NrSlidingWindowMmrDiversityArranger(kwargs))
    return self
  
  def nr_ssd_variant(self, **kwargs):
    """
    NrSsdVariantArranger
    ------
    ssd 算法打散，支持跨屏打散.

    参数配置
    ------
    `limit`: [int] 动态参数,需要返回的计算个数

    `theta`: [double] 动态参数, (0, 1) 之间的浮点数

    `allow_empty_embedding`: [bool] 动态参数, 是否允许 item embedding 为空

    `stable_ssd`: [bool] 动态参数,是否用稳定 ssd, 默认为 true

    `optimized_ssd`: [bool] 动态参数,是否用性能优化版 ssd, 默认为 false

    `cross_screen_variant`: [bool] 动态参数,是否开启跨屏打散, 默认为 false

    `cross_screen_items_from_attr`: [string] 跨屏打散时需要输入的 跨屏 item 集合, common attr

    `cross_screen_item_embedding_attr`: [string] 跨屏 item 集合 对应的 item embedding attr

    `item_embedding_attr`: [string] 候选集 item embedding

    `ranking_score_attr`: [string] 使用的排序分

    调用示例
    ------
    ``` python
    .nr_ssd_variant(
      limit=8,
      theta=0.4,
      allow_empty_embedding=False,
      stable_ssd=True,
      cross_screen_variant=True,
      cross_screen_items_from_attr="prev_items",
      cross_screen_item_embedding_attr="tower_embedding",
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score",
      enable_simscore_scale = True,
      sim_scale_params_str = "1,1,0.8,10"
    )
    ```
    """
    self._add_processor(NrSsdVariantArranger(kwargs))
    return self

  def nr_condition_set_common_attr_value(self, **kwargs):
    """
    UgConditionCommonAttrValueEnricher
    -----
    参数配置
    ------
    有条件的CommonAttr值获取，目前支持 按request_type、时间(星期几,当天小时)进行获取，一般用于不同流量和不同时间段的降级因子获取
    **注意**：
    * 策略是个list：命中规则是<font color='red'>依次遍历命中即退出的短路策略,务必找架构review，防止配置出错</font>
    * kconf 格式配置出错的话，<font color='red'>不做容错处理，全部设置代码中的默认值</font>，配置完请检查是否有error perf 报错！！

    `kconf_path`: [string] 必填，kconf 路径
    `save_common_attrs`: [list] 必填，支持对 attr 以 name/as 的配置格式重命名保存, 当 kconf 获取不到数据 或 启动有异常时用 default_value 值, set value 类型以 default_value 为准 

    调用示例
    ------
    ``` python
    .nr_condition_set_common_attr_value(
      kconf_path = "recoBiz.kuaishouNrSlide.retrCommonLeafDegradeConf",
      save_common_attrs = [
        {"name": "mc_degree_ratio", "as": "mc_degree_ratio_in_dragon", "default_value": 2.0},
        {"name": "fr_num", "default_value": 300},
      ]
    )
    ```
    ``` json
    {
      "data": 
        [
          {
            "request_type": ["nr_new_device","2","3"],  # 选填, 指定request_type生效，不指定 表示所有request_type生效， 配置[]表示所有数据不生效
            "week_day_set": [4,5,6],                       # 选填, 指定 周几生效, 不指定 表示 一周 都生效, 有效范围 [1-7], ， 配置[]表示所有数据不生效
            "time_span": ["00:20-09:10","22:10-23:00"], # 选填, 指定 几点生效，不指定 均表示 24小时生效, ， 配置[]表示所有数据不生效
            "value": {                                  # 必填
                  "mc_degree_ratio": 0.8,               # 取值目前仅支持 float 和 int
                  "fr_num": 100
            }
          },
          {
            "value": {                                  # 最后 check，即兜底配置
                  "mc_degree_ratio": 1.0,
                  "fr_num": 200,
                  "mc_num":600
            }
          }
        ]
    }
    ```
    """
    self._add_processor(UgConditionCommonAttrValueEnricher(kwargs))
    return self
  
  def nr_get_common_attr_from_redis_by_lrange(self, **kwargs):
    '''
    NrCommonAttrFromRedisByLrangeEnricher
    -----
    从redis中读取list型数据, 返回结果为string list格式

    参数配置
    -----
    `cluster_name`: [string] 输入redis集群名
    `redis_key`: [string] 动态参数, 输入redis key
    `output_common_attr`: [string] 动态参数, 输出结果attr name

    调用示例
    -----
    ```python
    .nr_get_common_attr_from_redis_by_lrange(
      cluster_name="redisxxxx",
      redis_key="redis_key_xx",
      output_common_attr="redis_value_xxx"
    )
    ```
    '''
    self._add_processor(NrCommonAttrFromRedisByLrangeEnricher(kwargs))
    return self
  def write_common_attr_to_redis_by_lpush(self, **kwargs):
    '''
    NrCommonAttrFromRedisByLrangeEnricher
    -----

    将String的 common attr写入到redis的对应key的list中

    参数配置
    -----
    `kcc_cluster_`: [string] 输入redis集群名
    `key`: [string] 动态参数, 输入redis key
    `value`: [string] 动态参数, 往redis的key中追加的value
    `timeout_ms`:[int] 写入redis的超时时间，单位ms
    `expire_second`: [int] value的过期时间，单位是秒

    调用示例
    -----
    ```python
    .write_common_attr_to_redis_by_lpush(
      kcc_cluster="redisxxxx",
      key="redis_key_xx",
      value="xxx",
      timeout_ms = 10,
      expire_second = 300,
    )
    ```
    '''
    self._add_processor(NrCommonAttrToRedisByLpushObserver(kwargs))
    return self
  def nr_copy_common_attr(self, **kwargs):
    '''
    '''
    self._add_processor(NrCopyCommonAttrEnricher(kwargs))
    return self
  
# 不要再往下面这个类里加processor了
class NrRetrievalPostApiMixin(CommonLeafBaseMixin):
  """
  NrRetrievalPost Processor API 接口的 Mixin 实现
  """

  def colossus_interest(self, **kwargs):
    """
    CommonRecoColossusInterestEnricher
    """
    self._add_processor(CommonRecoColossusInterestEnricher(kwargs))
    return self

  def colossus_interest_opt(self, **kwargs):
    """
    CommonRecoColossusInterestOptEnricher
    """
    self._add_processor(CommonRecoColossusInterestOptEnricher(kwargs))
    return self

  def kgnn_sample_pair(self, **kwargs):
    """
    CommonKgnnSamplePairEnricher
    """
    self._add_processor(CommonKgnnSamplePairEnricher(kwargs))
    return self
  
  def longterm_interest_trigger_generate(self, **kwargs):
    """
    CommonRecoLongtermInterestTriggerGenerateEnricher
    """
    self._add_processor(CommonRecoLongtermInterestTriggerGenerateEnricher(kwargs))
    return self
  
  def tnu_redis_sampling(self, **kwargs):
    """
    TnuRedisSamplingRetriever
    """
    self._add_processor(TnuRedisSamplingRetriever(kwargs))
    return self

  def tnu_pool_sampling(self, **kwargs):
    """
    TnuSamplingPoolRetriever
    """
    self._add_processor(TnuSamplingPoolRetriever(kwargs))
    return self

  def tnu_hetu_uid(self, **kwargs):
    """
    TnuHetuRetriever
    """
    self._add_processor(TnuHetuRetriever(kwargs))
    return self
  
  def nr_gsu_enricher(self, **kwargs):
    """
    NrGsuEnricher 
    """
    self._add_processor(NrGsuEnricher(kwargs))
    return self
  
  def nr_gsu_retriever(self, **kwargs):
    """
    NrGsuRetriever
    """
    self._add_processor(NrGsuRetriever(kwargs))
    return self
    
  def tnu_hetu_uid_direct(self, **kwargs):
    """
    TnuHetuRetrieverDirect
    """
    self._add_processor(TnuHetuRetrieverDirect(kwargs))
    return self
    
  def candidate_user_cross_feature_enrich(self, **kwargs):
    """
    CommonRecoCandidateColossusFeatureGenerateEnricher
    """
    self._add_processor(CommonRecoCandidateUserCrossFeatureEnricher(kwargs))
    
    return self

  def filter_reflux(self, **kwargs):
    """
    CommonRecoRefluxEnricher
    """
    self._add_processor(CommonRecoRefluxEnricher(kwargs))

    return self

  def linear_es(self, **kwargs):
    """
    CommonLinearEsEnricher
    """
    self._add_processor(CommonLinearEsEnricher(kwargs))

    return self
  
  def predict_dot_product_xtr_cross(self, embedding_type, **kwargs):
    """
    TowerPredictFloatDotProductXtrAttrEnricherCross
    ------
    计算向量内积、再算 sigmoid

    参数
    ------
    `predict_target_num`: [int] model predict target number

    `common_embedding_dim`: [int] 单个 target common embedding 的维度

    `common_embedding_attr`: [string] common embedding 在 context 中字段名 (common attr)

    `load_from_item_context`: [bool] 是否从 context item attr 中获取 item embedding ，默认 false
    
    `item_embedding_attr`: [string] item embedding 在 context 中的字段名

    `miss_embedding_mark`: [string] 如果 item 缺失 embedding ，则用该字段标记一个 int attr = 1，否则标记 0

    `output_type`: [int] 0: 以 `BaseMatrixWrapper` 输出至 common attr （默认）
                          1: 以 `Doublelist` 输出至 item attr
                          2: 以 `std::vector<T>*` 输出至 common attr

    `pxtr_value_attr`: [string] pxtr 结果保存到 context 中的字段名

    `downsampled_target_no`: [int] 需要进行特殊转换的 xtr 的编号

    `downsampled_alpha`: [int] 一个转换参数

    `skip_sigmoid`: [bool] 是否跳过 sigmoid 过程

    示例
    ------
    ``` python
    .predict_dot_product_xtr_cross(predict_target_num=14,
                                     common_embedding_dim=128,
                                     common_embedding_attr="user_embedding",
                                     load_from_item_context=False,
                                     item_embedding_attr="item_embedding",
                                     miss_embedding_mark="miss_embedding",
                                     output_type=0,
                                     pxtr_value_attr="pxtr_value",
                                    )
    ```
    """
    self._add_processor(TowerPredictFloatDotProductXtrAttrEnricherCross(kwargs))
    return self

  def tnu_causal_retriever(self, **kwargs):
    """
    TnuCausalRetriever
    """
    self._add_processor(TnuCausalRetriever(kwargs))
    return self

  def causal_random_enricher(self, **kwargs):
    """
    CausalRandomEnricher
    """
    self._add_processor(CausalRandomEnricher(kwargs))
    return self

  def build_proto_enricher(self, **kwargs):
    """
    BuildProtoEnricher
    """
    self._add_processor(BuildProtoEnricher(kwargs))
    return self
  
  def tnu_causal_author_retriever(self, **kwargs):
    """
    TnuCausalAuthorRetriever
    """
    self._add_processor(TnuCausalAuthorRetriever(kwargs))
    return self

  def merchant_slice_feature_colossus_enricher(self, **kwargs):
    """
    CommonMerchantSliceFeatureColossusEnricher
    ------
    电商直播间切片特征 colossus 解析 

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp_attr

    `aid_list_attr` : [string] aid_list_attr

    `pid_list_attr` : [string] pid_list_attr

    `request_time_attr` : [string] request_time_attr

    `enable_filter_request_time_attr` : [boolean]] enable_filter_request_time_attr

    `filter_offset_ms_attr` : [int] filter_offset_ms_attr

    调用示例
    ------
    ``` python
    .merchant_slice_feature_colossus_enricher(
        colossus_resp_attr="colossus_output",
        aid_list_attr="aId_list",
        pid_list_attr="item_key_list",
        request_time_attr="request_time",
        enable_filter_request_time_attr=True,
        filter_offset_ms_attr=60*3
    )
    ```
    """
    self._add_processor(CommonMerchantSliceFeatureColossusEnricher(kwargs))
    return self
  
  def merchant_slice_feature_colossus_opt_enricher(self, **kwargs):
    """
    CommonMerchantSliceFeatureColossusOptEnricher
    ------
    电商直播间切片特征 colossus 解析 

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp_attr

    `aid_list_attr` : [string] aid_list_attr

    `pid_list_attr` : [string] pid_list_attr

    `request_time_attr` : [string] request_time_attr

    `enable_filter_request_time_attr` : [boolean]] enable_filter_request_time_attr

    `filter_offset_ms_attr` : [int] filter_offset_ms_attr

    调用示例
    ------
    ``` python
    .merchant_slice_feature_colossus_enricher(
        colossus_resp_attr="colossus_output",
        aid_list_attr="aId_list",
        pid_list_attr="item_key_list",
        request_time_attr="request_time",
        category_click_attr="category_click_attr",
        timestamp_click_attr="timestamp_click_attr",
        category_order_attr="category_order_attr",
        timestamp_order_attr="timestamp_order_attr",
        enable_filter_request_time_attr=True,
        filter_offset_ms_attr=60*3,
        slice_feature_limit_size_attr="slice_feature_limit_size_attr"
    )
    ```
    """
    self._add_processor(CommonMerchantSliceFeatureColossusOptEnricher(kwargs))
    return self
  
  def merchant_slice_feature_colossus_opt_v2_enricher(self, **kwargs):
    """
    CommonMerchantSliceFeatureColossusOptEnricher
    ------
    电商直播间切片特征 colossus 解析 

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp_attr

    `aid_list_attr` : [string] aid_list_attr

    `pid_list_attr` : [string] pid_list_attr

    `request_time_attr` : [string] request_time_attr

    `enable_filter_request_time_attr` : [boolean]] enable_filter_request_time_attr

    `filter_offset_ms_attr` : [int] filter_offset_ms_attr

    调用示例
    ------
    ``` python
    .merchant_slice_feature_colossus_enricher(
        colossus_resp_attr="colossus_output",
        aid_list_attr="aId_list",
        pid_list_attr="item_key_list",
        request_time_attr="request_time",
        category_click_attr="category_click_attr",
        timestamp_click_attr="timestamp_click_attr",
        category_order_attr="category_order_attr",
        timestamp_order_attr="timestamp_order_attr",
        enable_filter_request_time_attr=True,
        filter_offset_ms_attr=60*3,
        slice_feature_limit_size_attr="slice_feature_limit_size_attr"
    )
    ```
    """
    self._add_processor(CommonMerchantSliceFeatureColossusOptV2Enricher(kwargs))
    return self
  
  def merchant_user_photo_feature_enricher(self, **kwargs):
    """
    CommonMerchantUserPhotoFeatureEnricher
    ------
    电商用户短视频消费统计特征 

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp_attr

    `leaf_reques_time_attr` : [string] request_time_attr

    调用示例
    ------
    ``` python
    .merchant_slice_feature_colossus_enricher(
        colossus_resp_attr="colossus_output",
        leaf_reques_time_attr="leaf_request_time",
    )
    ```
    """
    self._add_processor(CommonMerchantUserPhotoFeatureEnricher(kwargs))
    return self

  def causal_log_enricher(self, **kwargs):
    """
    CausalLogEnricher
    """
    self._add_processor(CausalLogEnricher(kwargs))
    return self

  def nr_push_enricher(self, **kwargs):
    """
    NrPushEnricher 
    """
    self._add_processor(NrPushEnricher(kwargs))
    return self


  def nr_mc_common_leaf_enricher(self, **kwargs):
    """
    NrMcCommonLeafEnricher
    -----
    参数配置
    ------
    `kess_service`: [string] [动态参数] 必填，请求的 kess name, 有默认值
    `max_request_num`: [int] [动态参数] 选填项，请求 mc leaf 设置的 request_num，默认为 1000
    `user_info_attr_name`: [string] 必填项，UserInfo 的 common attr name
    `is_mc_result_set_attr`: [string] 选填项，默认值 `is_mc_result`, mc leaf 返回结果会 set 这个 item attr 值为 1
    `mc_result_order_attr`: [string] 选填项，默认值 ``, mc leaf 返回结果, 会将 返回的顺序 记录到这个 itemAttr, 为空则不记录
    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，支持对 attr 重命名发送
    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type
 

    调用示例
    ------
    ``` python
    .nr_mc_common_leaf_enricher(
      kess_service = "grpc_KrpCommonLeafTest",
      recv_item_attrs = ["ctr"],
      send_common_attrs = ["user_id"],
      user_info_attr_name = "user_info"
    )
    # 如果有 attr 需要改名
    .nr_mc_common_leaf_enricher(
      kess_service = "grpc_KrpCommonLeafTest",
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      user_info_attr_name = "user_info"
    )
    ```
    """
    self._add_processor(NrMcCommonLeafEnricher(kwargs))
    return self 

  def nr_fr_common_leaf_enricher(self, **kwargs):
    """
    NrFrCommonLeafEnricher
    -----
    参数配置
    ------
    `kess_service`: [string] [动态参数] 必填，请求的 kess name, 有默认值
    `max_request_num`: [int] [动态参数] 选填项，请求 mc leaf 设置的 request_num，默认为 1000, 超出会截断
    `user_info_attr_name`: [string] 必填项，UserInfo 的 common attr name
    `is_mc_result_set_attr`: [string] 选填项，默认值 `is_mc_result`, mc leaf 返回结果会 set 这个 item attr 值为 1
    `is_fr_result_set_attr`: [string] 选填项，默认值 `is_fr_result`, fr leaf 返回结果会 set 这个 item attr 值为 1
    `fr_result_order_atrr`: [string] 选填项，默认值 `fr_result_order`, fr leaf 返回结果, 会将 返回的顺序 记录到这个 itemAttr
    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，支持对 attr 重命名发送
    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type
    `fr_result_count_attr`: [string] 选填项，默认值 `fr_result_count`, fr leaf 返回结果数会填到这里
    `timeout_ms`: [int] [动态参数] 选填项，请求的超时时间，单位毫秒

    调用示例
    ------
    ``` python
    .nr_fr_common_leaf_enricher(
      kess_service = "grpc_KrpCommonLeafTest",
      recv_item_attrs = ["ctr"],
      user_info_attr_name = "user_info"
    )
    # 如果有 attr 需要改名
    .nr_fr_common_leaf_enricher(
      kess_service = "grpc_KrpCommonLeafTest",
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      user_info_attr_name = "user_info"
    )
    ```
    """
    self._add_processor(NrFrCommonLeafEnricher(kwargs))
    return self

