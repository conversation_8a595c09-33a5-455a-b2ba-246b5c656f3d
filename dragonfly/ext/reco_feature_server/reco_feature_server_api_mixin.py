#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .reco_feature_server_enricher import *
from .reco_feature_server_observer import *


class RecoFeatureServerMixin(CommonLeafBaseMixin):
  def extract_kuiba_discrete_simplified(self, **kwargs):
    """
    KuibaParameterDiscreteSimplifiedEnricher
    ------
    抽取离散化特征放到 attr 里（如果是 common 则放到 common attr, 否则放到 item attr）, 针对 kuiba discrete 算子简化实现提高抽取效率

    参数配置
    ------
    `slots_output`: [string] 输出 slots 的位置

    `parameters_output`: [string] 输出 parameters 的位置

    `config`: [dict] discrete 抽取配置，详见示例。kuiba discrete 算子，当前仅处理 attrs[0].attr[0] 数据，兼容原配置格式，保留数组配置形式
      - `slotid_999`: 自定义名称，用于去重判断的标识

    `is_common_attr`: [bool] 是否仅从 common attr 中抽取，默认为 false

    调用示例
    ------
    ``` python
    .extract_kuiba_discrete_simplified(
      slots_output="slots",
      parameters_output="parameters",
      is_common_attr=False,
      config={
        "slotid_999": {
          "attrs": [
            {
              "attr": ["pctr"],
              "attr_type": ["float64"],
              "key_type": 999,
              "converter_args": "0.25,0,4,10000,0",
            }
          ]
        }
      }
    )
    ```
    """
    self._add_processor(KuibaParameterDiscreteSimplifiedEnricher(kwargs))
    return self

  def perflog_slot_sign(self, **kwargs):
    """
    RecoFeatureSlotPerflogObserver
    ------
    将抽取到attr的特征上报到perlflog

    参数配置
    ------
    `common_slots`: [list] 选配项，指定要上报的 common_slots 列表，不填则不上报

    `common_signs`: [list] 选配项，指定要上报的 common_signs 列表，不填则不上报

    common_slots和common_signs 必须同时配置或不配置，且每一项都必须一一对应

    `item_slots`: [list] 选配项，指定要上报的 item_slots 列表，不填则不上报

    `item_signs`: [list] 选配项，指定要上报的 common_signs 列表，不填则不上报

    item_slots和item_signs 必须同时配置或不配置，且每一项都必须一一对应

    `prob`: [double] [动态参数] 选配项，对每个item进行抽样的概率，取值范围为(0.0, 1.0]，默认为0.1

    调用示例
    ------
    ``` python
    .perflog_slot_sign(
      common_slots = ["common_slots_1", "common_slots_2"],
      common_signs = ["common_param_1", "common_param_2"],
      item_slots = ["item_slots_1", "item_slots_2"],
      item_signs = ["item_param_1", "item_param_2"],
      prob = 0.3
    )
    ```
    """
    self._add_processor(RecoFeatureSlotPerflogObserver(kwargs))
    return self