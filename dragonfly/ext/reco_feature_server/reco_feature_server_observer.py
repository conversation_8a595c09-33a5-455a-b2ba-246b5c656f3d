#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver


class RecoFeatureSlotPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog_slot_sign"

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("item_slots",[])) == len(self._config.get("item_signs",[])), "item_slots 与 item_signs 长度不一致")
    check_arg(len(self._config.get("common_slots",[])) == len(self._config.get("common_signs",[])), "common_slots 与 common_signs 长度不一致")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_slots",[])) and bool(self._config.get("item_signs",[]))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("item_slots", []))
    attrs.update(self._config.get("item_signs", []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("common_slots", []))
    attrs.update(self._config.get("common_signs", []))
    if "prob" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["prob"]))
    return attrs