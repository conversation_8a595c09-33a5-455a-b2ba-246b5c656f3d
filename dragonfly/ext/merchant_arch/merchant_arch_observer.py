#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class MerchantArchDistributedCacheXtrScoreWriterObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_arch_distributed_cache_xtr_score_writer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "get_table_name_from_kconf" in self._config:
      assert "photo_store_kconf_key" in self._config, "缺少 photo_store_kconf_key 配置"
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["expire_seconds_attr"])
    attrs = set(self._config.get("item_attr_names", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kconf_path"), "缺少kconf_path")
    check_arg(self._config.get("table_name"), "缺少table_name")
