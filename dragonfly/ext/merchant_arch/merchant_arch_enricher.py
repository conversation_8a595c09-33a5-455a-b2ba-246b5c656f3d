#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for merchant
author: <EMAIL>
date: 2021-01-14 13:58:00
"""

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel, extract_attr_names
from ...common_leaf_processor import LeafEnricher

class DynamicComputationAllocationFeatureExtractionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dynamic_computation_allocation_feature_extraction"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("biz_name"), "biz_name 不能为空")
    check_arg(self._config.get("dcaf_features"), "dcaf_features 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("pack_user_features", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["biz_name"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("pack_context_features", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config.get("dcaf_features")])


class DynamicComputationAllocationSampleCreationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dynamic_computation_allocation_sample_creation"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("biz_name"), "biz_name 不能为空")
    check_arg(self._config.get("dcaf_features"), "dcaf_features 不能为空")
    check_arg(self._config.get("output_json"), "output_json 不能为空")
    check_arg(self._config.get("rank_score"), "rank_score 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config.get("biz_name", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("sample_maker", 3)))
    attrs.update(self.extract_dynamic_params(self._config.get("limit_num", 5)))
    attrs.update(self.extract_dynamic_params(self._config.get("aggregator", "sum")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_rerank", 0)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("mc_truncate_seq"))
    attrs.update(self._config.get("mc_quota_level"))
    attrs.update(self._config.get("mc_score"))
    attrs.update(self._config.get("rank_score"))
    attrs.update(self._config.get("egpm"))
    attrs.update(self._config.get("mtb_rate"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_json"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("dcaf_features"))
    return attrs


class GenMerchantAbSuffixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_merchant_ab_suffix"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("import_common_attrs"), "import_common_attrs 不能为空")
    check_arg(self._config.get("export_common_attrs"), "export_common_attrs 不能为空")
    check_arg(self._config.get("kconf_key"), "kconf_key 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("import_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("kconf_key", "")))
    app_source_type = self._config.get("app_source_type", "app_source_type")
    attrs.add(app_source_type)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("export_common_attrs", []))
    prioritized_suffix = self._config.get("prioritized_suffix", "prioritized_suffix")
    sample_tag = self._config.get("sample_tag", "sample_tag")
    attrs.add(prioritized_suffix)
    attrs.add(sample_tag)
    return attrs
  
class MerchantArchDistributedCacheXtrScoreReaderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_arch_distributed_cache_xtr_score_reader"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attr_name_types" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attr_name_types", []) }
    if "is_cache_hit" in self._config:
      attrs.add(self._config.get("is_cache_hit"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attr_name_types"), "缺少 attr_name_types")
    check_arg(self._config.get("photo_store_kconf_key"), "缺少 photo_store_kconf_key")
    check_arg(self._config.get("table_kconf_path"), "缺少 table_kconf_path")
    check_arg(self._config.get("table_name"), "缺少 table_name")

