#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""

import ast
import base64
import hashlib
import inspect
import itertools
import os

from ...common_leaf_util import strict_types, check_arg, ArgumentError, \
    gen_attr_name_with_item_attr_channel, extract_attr_names, check_lua_script
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>, try_add_table_name


class DistributedIndexBaseEnricher(LeafEnricher):
  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = { self._config.get("output_attr_prefix", "") + v.get("as", v["name"]) if isinstance(v, dict) else v \
              for v in self._config.get("attrs", []) }
    save_item_info_to_attr = self._config.get("save_item_info_to_attr")
    if save_item_info_to_attr is not None:
      attrs.add(save_item_info_to_attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("photo_store_rpc_req_cache_rate", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("data_set_tags_bit", "")))
    attrs.add(self._config.get("photo_store_request_data_set_tags_attr", ""))
    return attrs

  @property
  @strict_types
  def photo_store_kconf_key(self) -> str:
    return self._config.get("photo_store_kconf_key", "")

class CommonRecoDistributedIndexMerchantItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_merchant_item_attr_by_distributed_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg("photo_store_kconf_key" in self._config, f"{self.get_type_alias()} 缺少 photo_store_kconf_key 配置")

class CommonRecoDistributedIndexMerchantLivingItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_merchant_living_item_attr_by_distributed_index"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    val = set()
    if self._config.get("enable_kconf_key"):
      val.add(self._config.get("enable_init_common_index", "enable_init_common_index_default"))
    return val

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg("photo_store_kconf_key" in self._config, f"{self.get_type_alias()} 缺少 photo_store_kconf_key 配置")

class CommonRecoGenericGrpcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_generic_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config["request_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["response_attr"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return False


class CommonRecoSphinxParameterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_sphinx_params"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["model_name"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for c in self._config.get("params", []):
      if type(c) is str:
        attrs.add(c)
      else:
        attrs.add(c["as"])
    return attrs

class CommonRecoShuffleListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "shuffle_list_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("common_attr", ""), str), "common_attr 需为 str 类型")
    check_arg(isinstance(self._config.get("item_attr", ""), str), "item_attr 需为 str 类型")
    check_arg("item_attr" in self._config or "common_attr" in self._config, "common_attr 和 item_attr 至少有一项")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_attr"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    return attrs
  
class CommonRecoAuthorExpParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_author_exp_params"

  @strict_types
  def _check_config(self) -> None:
    TYPE_NAME = ['int', 'double', 'string', 'bool']
    ORIGINAL_TYPE_NAME = ['str', 'float', 'bool', 'int']
    TYPE_NAME_MAP = {"str": "string", "float": "double", "bool": "bool", "int": "int"}
    check_arg(isinstance(self._config.get("author_tail", ""), str), "author_tail 需为 str 类型")
    for param in self._config["author_exp_params"]:
      check_arg(isinstance(param.get("param_name", ""), str), "param_name 需为 str 类型")
      check_arg(isinstance(param.get("attr_name", ""), str), "attr_name 需为 str 类型")
      check_arg(param.get("attr_type", "") in TYPE_NAME, "非法的 attr_type, 合法值: ['int', 'double', 'string', 'bool']")
      check_arg(type(param["default_value"]).__name__ in ORIGINAL_TYPE_NAME, "非法的 default_value type, 合法值: ['str', 'float', 'bool', 'int']")
      type_name = TYPE_NAME_MAP[type(param["default_value"]).__name__]
      check_arg(type_name == param.get("attr_type", ""), f"不一致的 abtest param default_value type: {type_name}")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("author_tail"):
      attrs.add(self._config["author_tail"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for param in self._config["author_exp_params"]:
      if param.get("attr_name"):
        attrs.add(param["attr_name"])
    return attrs
    
class CommonRecoAttrStatisticEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "attr_statistic"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr_config in self._config.get("statistic_config", []):
      attrs.add(attr_config.get("attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_as_json_to"))
    for attr_config in self._config.get("statistic_config", []):
      attrs.add(attr_config.get("save_values_to"))
      attrs.add(attr_config.get("save_results_to"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("statistic_config"), list), "statistic_config 需为 list 类型")
    for attr_config in self._config.get("statistic_config", []):
      check_arg(isinstance(attr_config.get("attr_name"), str) and attr_config.get("attr_name"), "attr_name 需为非空字符串")

class CommonRecoLocalIndexItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_local_index"

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return self._config.get("additional_item_source", {}).get("reco_results", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    item_source = self._config.get("additional_item_source", {})
    return set(item_source.get("common_attr", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    item_source = self._config.get("additional_item_source", {})
    attrs.update(item_source.get("item_attr", []))
    attrs.add(self._config.get("item_key_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(self._config.get("attrs", []))
    attrs.add(self._config.get("item_miss_tag"))
    return attrs

class CommonRecoLocalAttrIndexItemAttrEnricher(CommonRecoLocalIndexItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_local_attr_index"

class CommonRecoRemoteIndexItemAttrEnricher(CommonRecoLocalIndexItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_remote_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    return attrs

class CommonRecoDistributedIndexItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_distributed_index"

  # TODO(fangjianbing): 为保持老服务兼容, 暂时先去掉 photo_store_kconf_key 的检查
  # @strict_types
  # def _check_config(self) -> None:
  #   check_arg("photo_store_kconf_key" in self._config, f"{self.get_type_alias()} 缺少 photo_store_kconf_key 配置")

class CommonRecoDistributedIndexNewPhotoInfoItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_distributed_new_photo_info_index"

  @strict_types
  def _check_config(self) -> None:
    check_arg("photo_store_kconf_key" in self._config, f"{self.get_type_alias()} 缺少 photo_store_kconf_key 配置")

class CommonRecoDistributedIndexAttrKVItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_distributed_common_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    item_source = self._config.get("additional_item_source", {})
    attrs.update(item_source.get("common_attr", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    val = set()
    if self._config.get("enable_kconf_key"):
      val.add(self._config.get("enable_init_common_index", "enable_init_common_index_default"))
    return val

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attrs" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attrs", []) }
    if "attr_name_types" in self._config:
      attrs = { key for key in self._config.get("attr_name_types", {}).keys() }
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

  def is_valid_type_str(self, type_name : str) -> bool:
    return type_name in ['int', 'float', 'string', 'int_list', 'float_list', 'string_list']
  
  @property
  @strict_types
  def is_more_config(self) -> bool:
    """ 是否支持除了 name 和 type 外的更多配置，比如 class_name 或 其它 """
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        if not sorted(["name", "type"]) == sorted(list(v.keys())):
          return True
    return False

  @property
  @strict_types
  def flat_index_item_attrs_with_type(self) -> dict:
    attrs_with_type = {}
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        attrs_with_type[v["name"]] = v.get("type", "auto")
      else:
        attrs_with_type[v] = "auto"
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_type[name] = t
    return attrs_with_type

  @property
  @strict_types
  def flat_index_item_attrs_with_more_config(self) -> list:
    attrs_with_more_config = []
    for v in self._config.get("attrs", []):
      t = {}
      if isinstance(v, dict):
        if "class_name" in v.keys():
          v["class_name"] = v["class_name"].replace("::", ".")
        t.update(v)
      else:
        t.update(dict(name=v))
      t.setdefault("type", "auto")
      attrs_with_more_config.append(t)
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_more_config.append({"name": name, "type": t})
    return attrs_with_more_config

  @strict_types
  def _check_config(self) -> None:
    if "save_item_info_to_attr" in self._config:
      raise ArgumentError(f"{self.get_type_alias()} does not support 'save_item_info_to_attr'")
    count = 0
    if "attrs" in self._config:
      count += 1
      attr_set = set()
      for v in self._config.get("attrs", []):
        attr_name = v if isinstance(v, str) else v.get("as", v["name"])
        if isinstance(v, dict) and "as" in v and v['as'] in attr_set:
          raise ArgumentError(f"{self.get_type_alias()} 包含重复的重命名 attr: {v['as']}")
        attr_set.add(attr_name)
        if isinstance(v, dict) and "type" in v.keys() and not self.is_valid_type_str(v["type"]):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v['type']}")
    if "attr_name_types" in self._config:
      count += 1
      for v in self._config.get("attr_name_types", {}).values():
        if not self.is_valid_type_str(v):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v}")
    if count != 1:
      raise ArgumentError(f"{self.get_type_alias()} 'attrs' 或者 'attr_name_types' 中必须配置其中一个")

class CommonRecoDistributedIndexFlatKvItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_distributed_flat_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    item_source = self._config.get("additional_item_source", {})
    attrs.update(item_source.get("common_attr", []))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attrs"):
      attrs = {name for name in self._config["output_common_attrs"]} # 目前只有一个，标志该 photo_store是否启动 enable_flat_photo_store
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attrs" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
                for v in self._config.get("attrs", []) }
    if "attr_name_types" in self._config:
      attrs = { key for key in self._config.get("attr_name_types", {}).keys() }
    if "packed_key" in self._config:
      attrs.clear()
      if "use_flat_kv" not in self._config:
        attrs.add(self._config.get("packed_key", ""))
    return attrs

  @property
  @strict_types
  def is_more_config(self) -> bool:
    """ 是否支持除了 name 和 type 外的更多配置，比如 class_name 或 其它 """
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        if not sorted(["name", "type"]) == sorted(list(v.keys())):
          return True
    return False

  @property
  @strict_types
  def flat_index_item_attrs_with_type(self) -> dict:
    attrs_with_type = {}
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        attrs_with_type[v["name"]] = v.get("type", "auto")
      else:
        attrs_with_type[v] = "auto"
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_type[name] = t

    packed_key = self._config.get("packed_key", "")
    if packed_key != "":
      attrs_with_type.clear()
      attrs_with_type[packed_key] = "string"
    return attrs_with_type

  @property
  @strict_types
  def flat_index_item_attrs_with_more_config(self) -> list:
    attrs_with_more_config = []
    for v in self._config.get("attrs", []):
      t = {}
      if isinstance(v, dict):
        if "class_name" in v.keys():
          v["class_name"] = v["class_name"].replace("::", ".")
        t.update(v)
      else:
        t.update(dict(name=v))
      t.setdefault("type", "auto")
      attrs_with_more_config.append(t)
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_more_config.append({"name": name, "type": t})

    packed_key = self._config.get("packed_key", "")
    if packed_key != "":
      attrs_with_more_config.clear()
      attrs_with_more_config.append({"name": packed_key, "type": "string"})
    return attrs_with_more_config

  @property
  @strict_types
  def photo_store_kconf_key(self) -> str:
    return self._config.get("photo_store_kconf_key", "")


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

  def is_valid_type_str(self, type_name : str) -> bool:
    return type_name in {'int', 'float', 'string', 'int_list', 'float_list', 'string_list'}

  @strict_types
  def _check_config(self) -> None:
    count = 0
    if "packed_key" in self._config or "use_flat_kv" in self._config:
      count += 1
    if "save_item_info_to_attr" in self._config:
      raise ArgumentError(f"{self.get_type_alias()} does not support 'save_item_info_to_attr'")
    if "attrs" in self._config:
      count += 1
      attr_set = set()
      for v in self._config.get("attrs", []):
        attr_name = v if isinstance(v, str) else v.get("as", v["name"])
        if isinstance(v, dict) and "as" in v and v['as'] in attr_set:
          raise ArgumentError(f"{self.get_type_alias()} 包含重复的重命名 attr: {v['as']}")
        attr_set.add(attr_name)
        if isinstance(v, dict) and "type" in v.keys() and not self.is_valid_type_str(v["type"]):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v['type']}")
    if "attr_name_types" in self._config:
      count += 1
      for v in self._config.get("attr_name_types", {}).values():
        if not self.is_valid_type_str(v):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v}")
    if count != 1:
      raise ArgumentError(f"{self.get_type_alias()} 'attrs' 或者 'attr_name_types' 或者 'packed_key' 中必须且仅配置其中一个")

class CommonRecoCommonAttrPackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_common_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "limit_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["limit_num"]))
    if isinstance(self._config["input_common_attrs"], str):
      attrs.update(self.extract_dynamic_params(self._config["input_common_attrs"]))
    else:
      attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config["output_common_attr"] }

class CommonRecoAggregateListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "aggregate_list_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return not bool(self._config.get("for_common", False))

  @strict_types
  def _check_config(self) -> None:
    support_agg = {"count", "min", "max", "sum", "avg"}
    for cfg in self._config["mappings"]:
      check_arg(cfg.get("from_attr", False), f"aggregate_list_attr mappings 配置 {cfg} 错误，没有指定 from_attr")
      check_arg(cfg.get("to_attr", False), f"aggregate_list_attr mappings 配置 {cfg} 错误，没有指定 to_attr")
      check_arg(cfg.get("aggregator","") in support_agg, f'aggregate_list_attr mappings 配置 {cfg} 错误，不支持的 aggerator')

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if (not self.depend_on_items()):
      return { v["from_attr"] for v in self._config["mappings"] }
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if (not self.depend_on_items()):
      return { v["to_attr"] for v in self._config["mappings"] }
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if (self.depend_on_items()):
      return { v["from_attr"] for v in self._config["mappings"] }
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if (self.depend_on_items()):
      return { v["to_attr"] for v in self._config["mappings"] }
    return set()

class CommonRecoPackBytesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_attr_to_bytes"

  @strict_types
  def _check_config(self) -> None:
    if "schema" not in self._config or not self._config["schema"]:
      raise ArgumentError(f"{self.get_type_alias()} 必须配置 schema")

    for part in self._config["schema"]:
      if "attr_name" not in part or not part["attr_name"]:
          raise ArgumentError(f"{self.get_type_alias()} 的 schema 必须配置 attr_name: {part}")

      if part.get("dtype") not in {"int8", "int16", "int32", "int64", "fp16", "fp32", "fp64", "bytes", "scale_int8"}:
          raise ArgumentError(f"{self.get_type_alias()} 的 schema 中的 dtype 不支持: {part}")

    if "output_attr_name" not in self._config or not self._config["output_attr_name"]:
      raise ArgumentError(f"{self.get_type_alias()} 必须配置 output_attr_name")

  @property
  @strict_types
  def _is_common(self) -> bool:
    return self._config.get("is_common", False)

  @property
  @strict_types
  def _input_attrs(self) -> set:
    return { part["attr_name"] for part in self._config["schema"] }

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return self._is_common

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._is_common:
      return self._input_attrs
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._is_common:
      return { self._config["output_attr_name"] }
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._is_common:
      return self._input_attrs
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._is_common:
      return { self._config["output_attr_name"] }
    return set()

class CommonRecoUnpackBytesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unpack_bytes_to_attr"

  @strict_types
  def _check_config(self) -> None:
    if "schema" not in self._config or not self._config["schema"]:
      raise ArgumentError(f"{self.get_type_alias()} 必须配置 schema")

    for part in self._config["schema"]:
      if "attr_name" not in part or not part["attr_name"]:
          raise ArgumentError(f"{self.get_type_alias()} 的 schema 必须配置 attr_name: {part}")

      if part.get("dtype") not in {"int8", "int16", "int32", "int64", "fp16", "fp32", "fp64", "bytes", "scale_int8"}:
          raise ArgumentError(f"{self.get_type_alias()} 的 schema 中的 dtype 不支持: {part}")

    if "source_attr_name" not in self._config or not self._config["source_attr_name"]:
      raise ArgumentError(f"{self.get_type_alias()} 必须配置 source_attr_name")

  @property
  @strict_types
  def _is_common(self) -> bool:
    return self._config.get("is_common", False)
  
  @property
  @strict_types
  def _is_array(self) -> bool:
    return self._config.get("is_array", False)

  @property
  @strict_types
  def _input_attrs(self) -> set:
    return {self._config["source_attr_name"] }

  @property
  @strict_types
  def _output_attrs(self) -> set:
    return { part["attr_name"] for part in self._config["schema"] }

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return self._is_common

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._is_common:
      return self._input_attrs
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._is_common:
      return self._output_attrs
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._is_common:
      return self._input_attrs
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._is_common:
      return self._output_attrs
    return set()

class CommonRecoItemAttrPackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_item_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg('item_source' in self._config, f"{self.get_type_alias()} 缺少 item_source 配置")
    count = 0
    item_source = self._config["item_source"]
    if item_source.get("reco_results", False):
      count += 1
    common_attr = item_source.get("common_attr", [])
    check_arg(isinstance(common_attr, list), f"{self.get_type_alias()} 的 item_source.common_attr 配置需为 string list 类型")
    if common_attr:
      count += 1
    if "latest_browse_set_item" in item_source:
      count += 1
    if count != 1:
      raise ArgumentError(f"{self.get_type_alias()} 必须且只能配置一个 item_source: {item_source}")

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return self._config["item_source"].get("reco_results", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "total_limit" in self._config["item_source"]:
      attrs.update(self.extract_dynamic_params(self._config["item_source"]["total_limit"]))
    attrs.update(self._config["item_source"].get("common_attr", []))
    for cfg in self._config["mappings"]:
      if "item_attr_limit" in cfg:
        attrs.update(self.extract_dynamic_params(cfg["item_attr_limit"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { v["to_common_attr"] for v in self._config["mappings"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    mappings = self._config["mappings"]
    for mapping in mappings:
      if "from_item_attr" in mapping:
        attrs.add(mapping["from_item_attr"])
      if "pack_if" in mapping:
        attrs.add(mapping["pack_if"])
    return attrs

class CommonRecoDumpContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_context"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config and not isinstance(self._config["item_attrs"], str):
      attrs.update(self._config.get("item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("dump_to_attr", "")
    attrs.add(attr)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if isinstance(self._config.get("item_attrs"), str):
      check_arg(self._config.get("item_attrs", "") == "*", 'string 格式的 item_attrs 只能设置为 "*"')

  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    return self._config.get("item_attrs") == "*"

class CommonRecoItemAttrDefaultValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_attr_value"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "item_attrs" not in self._config:
      return ret
    for default_value_config in self._config["item_attrs"]:
      ret.add(default_value_config["name"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "common_attrs" not in self._config:
      return ret
    for default_value_config in self._config["common_attrs"]:
      ret.add(default_value_config["name"])
    return ret

  @strict_types
  def _check_config(self) -> None:
    check_arg("item_attrs" in self._config or "common_attrs" in self._config,
              "item_attrs 与 common_attrs 至少配置一个")
    if "item_attrs" in self._config:
      check_arg(isinstance(
          self._config["item_attrs"], list), "item_attrs 不为 list 类型")
      check_arg(len(
          self._config["item_attrs"]) > 0, "item_attrs 不可为空")
    if "common_attrs" in self._config:
      check_arg(isinstance(
          self._config["common_attrs"], list), "common_attrs 不为 list 类型")
      check_arg(len(
          self._config["common_attrs"]) > 0, "common_attrs 不可为空")

class CommonRecoAttrDefaultValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_attr_default_value"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "item_attrs" not in self._config:
      return ret
    for default_value_config in self._config["item_attrs"]:
      ret.add(default_value_config["name"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "item_attrs" not in self._config:
      return ret
    for default_value_config in self._config["item_attrs"]:
      if isinstance(default_value_config["value"], list):
        for attr in default_value_config["value"]:
          ret.update(self.extract_dynamic_params(attr))
      else:
        ret.update(self.extract_dynamic_params(default_value_config["value"]))
    return ret

  @strict_types
  def _check_config(self) -> None:
    check_arg("item_attrs" in self._config, "item_attrs 必须配置")
    check_arg("common_attrs" not in self._config, "common_attrs 目前不支持")
    check_arg(isinstance(
        self._config["item_attrs"], list), "item_attrs 不为 list 类型")
    check_arg(len(
        self._config["item_attrs"]) > 0, "item_attrs 不可为空")

class CommonRecoCommonPredictItemAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    if config.get("include_sample_list_user_info", False):
      config["use_sample_list_attr_flag"] = True
      config["sample_list_common_attr_key"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
      if "use_sample_list_attr_flatten" not in config:
        config["use_sample_list_attr_flatten"] = True
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_predict"

  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    return True

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    return self._config.get("use_sample_list_attr_flag", True) and bool(self._config.get("sample_list_common_attr_key"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self._config.get("extra_common_attrs", []))
    attrs.update(self._config.get("attr_name_transform_map", {}).keys())
    attrs.add(self._config.get("item_attrs_in_name_list"))
    if self._config.get("sample_list_common_attr_key", ""):
      attrs.add(self._config.get("sample_list_common_attr_key", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs = extract_attr_names(self._config.get("item_attrs", []), "name")
    if "item_key_attr" in self._config:
      attrs.add(self._config["item_key_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config.get("output_prefix", "") + v for v in self._config["loss_function_name"] }

class CommonRecoPredictFetcherItemAttrEnricher(LeafEnricher):
  __PXTR_CANDIDATES = ["pctr", "pltr", "pwtr", "plvtr", "psvtr", "pftr", "pshowtr", "plvtr2", \
      "ptr", "pwatch_time", "pepstr", "pcmtr", "pecstr", "plivingtr", "pcestr", "plttr", "pwttr",
      "pdtr", "pelivingtr", "pcotr", "pfostr", "pwtd", "pclk_cmt", "pswptr", "pswptr_after", "pcltr", "phtr"]

  @strict_types
  def _check_config(self) -> None:
    for xtr in self._config.get("pxtr", []):
      check_arg(xtr in self.__PXTR_CANDIDATES, f"非法的 pxtr 名: {xtr}, 仅支持: {self.__PXTR_CANDIDATES}")

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_predict_fetcher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { self._config["user_info_attr"] }
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = self._config.get("pxtr", self.__PXTR_CANDIDATES)
    return { self._config.get("output_prefix", "") + v for v in attrs }

class CommonRecoPredictFetcherV2ItemAttrEnricher(CommonRecoPredictFetcherItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_predict_fetcher_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["kess_service", "tower_request_type"]:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["user_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class CommonRecoDistributedIndexKuibaPredictItemAttrEnricher(DistributedIndexBaseEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_distributed_kuiba_predict_item_index"

  @strict_types
  def _check_config(self) -> None:
    check_arg("photo_store_kconf_key" in self._config, f"{self.get_type_alias()} 缺少 photo_store_kconf_key 配置")


class CommonRecoRandomItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_random_item_attr"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["attr_name"] }

class CommonRecoAbtestCommonAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    TYPE_NAME_MAP = {
      "int": "int",
      "float": "double",
      "str": "string",
      "bool": "bool",
    }
    prioritized_suffix = config.get("prioritized_suffix")
    param_prefix = config.get("param_prefix")
    for param in config["ab_params"]:
      param_name = param.get("param_name")
      check_arg(isinstance(param_name, (str, dict)), "ab_params 里的 param_name 值必须为 string 或 dict 类型")
      check_arg(param_name, "ab_params 里的 param_name 不可为空")
      if prioritized_suffix or isinstance(param_name, dict) or param_prefix:
        check_arg(param.get("attr_name", ""), f"配置了 prioritized_suffix 或 param_prefix 或 param_name 为 dict 类型的情况下必须指定 attr_name")
      if "param_type" not in param:
        type_name = type(param["default_value"]).__name__
        check_arg(type_name in TYPE_NAME_MAP, f"非法的 abtest param default_value: {param['default_value']}")
        param["param_type"] = TYPE_NAME_MAP[type_name]
    check_arg(isinstance(config.get("deduplicate", False), bool), "deduplicate 必须为 bool 类型")
    check_arg(isinstance(config.get("parallel_get", 1), int), "parallel_get 必须为 int 类型")
    check_arg(config.get("parallel_get", 1) >= 1, "parallel_get 不可小于1")
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_abtest_params"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  def __get_attr_names(self) -> set:
    attrs = set()
    for ab in self._config["ab_params"]:
      attrs.add(ab.get("attr_name") or ab["param_name"])
      if not self._config.get("for_item_level", False):
        for key in ["save_exp_id_to", "save_group_id_to"]:
          value = ab.get(key)
          if value is not None:
            attrs.add(value)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["biz_name", "prioritized_suffix", "user_id", "device_id", "session_id", "param_prefix", \
                 "product", "platform", "app_version", "explore_locale", "country", "province", \
                 "city", "photo_page", "browse_type", "network_type", "phone_model", "language", "isp"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    # 下面两个静态 Common Attrs 一般不应该使用的，就不出现 API 文档里面了
    attrs.add("_ABTEST_USER_TAG_NAMES_")
    attrs.add("_ABTEST_USER_TAG_VALUES_")
    for ab in self._config["ab_params"]:
      attrs.update(self.extract_dynamic_params(ab.get("report_ab_hit")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set() if self._config.get("for_item_level", False) else self.__get_attr_names()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("for_item_level", False):
      attrs.add(self._config.get("item_level_user_id_attr", ""))
      attrs.add(self._config.get("item_level_device_id_attr", ""))
      attrs.add(self._config.get("item_level_session_id_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.__get_attr_names() if self._config.get("for_item_level", False) else set()

class CommonRecoAbtestExpInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_abtest_exp_info"

  @strict_types
  def _check_config(self) -> None:
    for exp in self._config["exp_info"]:
      count = 0
      if "world" in exp:
        count=count+1
        check_arg(isinstance(exp["world"], str), "exp_info 里的 world 值必须为字符串")
        check_arg(len(exp["world"]) > 0, "exp_info 里的 world 不可为空")
      if "ab_key" in exp:
        count=count+1
        check_arg("biz_name" in self._config, "ab_key 和 biz_name 必须同时配置")
        check_arg(len(self._config["biz_name"]) > 0, "biz_name 不可为空")
        check_arg(isinstance(self._config["biz_name"], str), "biz_name 值必须为字符串")
        check_arg(isinstance(exp["ab_key"], str), "exp_info 里的 ab_key 值必须为字符串")
        check_arg(len(exp["ab_key"]) > 0, "exp_info 里的 ab_key 不可为空")
      check_arg(count==1, "exp_info 必须拥有 world 或 ab_key 其中之一")
      check_arg(isinstance(exp.get("save_exp_id_to", ""), str), "exp_info 里的 save_exp_id_to 值必须为字符串")
      check_arg(isinstance(exp.get("save_group_id_to", ""), str), "exp_info 里的 save_group_id_to 值必须为字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for exp in self._config["exp_info"]:
      if "world" in exp :
        attrs.update(self.extract_dynamic_params(exp["world"]))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for exp in self._config["exp_info"]:
      attrs.add(exp.get("save_exp_id_to", ""))
      attrs.add(exp.get("save_group_id_to", ""))
    return attrs

class CommonRecoAutoAdjustEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "auto_adjust"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["set_point", "kp", "ki", "kd", "group_name"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    attrs.add(self._config.get("fractions_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["adjust_output"]])

class CommonRecoItemShowCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_show_count"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["show_item_attr"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["common_attr"]])

class CommonRecoCommonAttrDispatchEnricher(LeafEnricher):
  @strict_types
  def _check_config(self) -> None:
    from_common_attr = self._config.get("from_common_attr")
    to_item_attr = self._config.get("to_item_attr")
    dispatch_config = self._config.get("dispatch_config")
    check_arg((bool(from_common_attr) & bool(to_item_attr)) ^ bool(dispatch_config),
            "(from_common_attr,to_item_attr) 和 dispatch_config 两种配置方式有且只能有其中一种")
    if from_common_attr and to_item_attr:
      check_arg(isinstance(from_common_attr, str) & isinstance(to_item_attr, str),
              "`from_common_attr` 和 `to_item_attr` 配置需为 string 类型")
    if dispatch_config:
      check_arg(isinstance(dispatch_config, list), "`dispatch_config` 配置需为 list 类型")
      for cfg in self._config.get("dispatch_config"):
        check_arg(isinstance(cfg, dict), "`dispatch_config` 中的每一项需为 dict类型")
        from_common_attr = cfg.get("from_common_attr")
        check_arg(bool(from_common_attr), "不能缺少 `from_common_attr`")
        to_item_attr = cfg.get("to_item_attr")
        check_arg(bool(to_item_attr), "不能缺少 `to_item_attr`")
        check_arg(isinstance(from_common_attr, str) & isinstance(to_item_attr, str),
                "`dispatch_config` 中的配置项需为 string 类型")

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dispatch_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "from_common_attr" in self._config:
      attrs.add(self._config["from_common_attr"])
    if "dispatch_config" in self._config:
      for cfg in self._config.get("dispatch_config"):
        attrs.add(cfg.get("from_common_attr"));
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "to_item_attr" in self._config:
      attrs.add(self._config["to_item_attr"])
    if "dispatch_config" in self._config:
      for cfg in self._config.get("dispatch_config"):
        attrs.add(cfg.get("to_item_attr"));
    return attrs

class CommonRecoItemAttrDispatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dispatch_item_attrs"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("attrs", []))

  @strict_types
  def _get_range_mode_output_attrs(self) -> set:
    start = self._config["from_item_range"].get("start", 0)
    end = self._config["from_item_range"]["end"]
    output_prefix = self._config.get("output_prefix", "")
    return { output_prefix + str(index) + "_" + attr for attr in self._config["attrs"] for index in range(start, end) }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "prev_n" in self._config:
      count = self._config["prev_n"] + (1 if self._config.get("include_self", True) else 0)
      output_prefix = self._config.get("output_prefix", "")
      return { output_prefix + str(index) + "_" + attr for attr in self._config["attrs"] for index in range(count) }
    else:
      if self._config.get("dispatch_to_common_attr", False):
        return set()
      else:
        return self._get_range_mode_output_attrs()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "prev_n" not in self._config and self._config.get("dispatch_to_common_attr", False):
      return self._get_range_mode_output_attrs()
    return set()

class CommonRecoStringSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "split_string"

  @strict_types
  def _check_config(self) -> None:
    input_common_attr = self._config.get("input_common_attr")
    output_common_attr = self._config.get("output_common_attr")
    input_item_attr = self._config.get("input_item_attr")
    output_item_attr = self._config.get("output_item_attr")

    check_arg(bool(input_common_attr) ^ bool(input_item_attr), "common_attr 和 item_attr 两组参数，必须有且只有有其中一组")
    if input_common_attr:
      check_arg(output_common_attr, "`output_common_attr` 参数缺失")
      check_arg(isinstance(input_common_attr, str), "`input_common_attr` 配置需为 string 类型")
      check_arg(isinstance(output_common_attr, str), "`output_common_attr` 配置需为 string 类型")

    if input_item_attr:
      check_arg(output_item_attr, "`output_item_attr` 参数缺失")
      check_arg(isinstance(input_item_attr, str), "`input_item_attr` 配置需为 string 类型")
      check_arg(isinstance(output_item_attr, str), "`output_item_attr` 配置需为 string 类型")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("input_item_attr"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attr"):
      attrs.add(self._config["input_common_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attr"):
      attrs.add(self._config["output_common_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_item_attr"):
      attrs.add(self._config["input_item_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_item_attr"):
      attrs.add(self._config["output_item_attr"])
    return attrs

class CommonRecoKconfCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_kconf_params"

  @strict_types
  def _check_config(self) -> None:
    available_types = {"int64", "int", "double", "string", "json", "bool", "list_int64", \
        "set_int64", "int64_list", "int64_set", "int_list", "int_set", "list_double", \
        "set_double", "double_list", "double_set", "list_string", "set_string", "string_list", \
        "string_set", "list_bool", "set_bool", "bool_list", "bool_set"}
    for conf in self._config["kconf_configs"]:
      check_arg("param_type" not in conf, "不存在 param_type 配置项，参数类型请通过 value_type 配置")
      value_type = conf.get("value_type")
      if value_type:
        check_arg(value_type in available_types, f"错误的 kconf value_type: {value_type}，请参考文档配置")
      else:
        default_value = conf.get("default_value", None)
        if default_value is not None and "json_path" not in conf:
          check_arg(isinstance(default_value, (int, float, str, bool)), f"default_value={default_value} 在非单值的情况下必须显式指定 value_type")
      if conf.get("export_item_attr"):
        check_arg(not conf.get("export_common_attr"), "export_common_attr 和 export_item_attr 不能同时配置")
        json_path = conf.get("json_path", "")
        check_arg(json_path.startswith("{{") and json_path.endswith("}}") and len(json_path) > 4, '配置 export_item_attr 时，json_path 必须用 "{{xxx}}" 格式指定一个 item attr 名称')
        check_arg(not json_path.startswith("{{return "), f"配置 export_item_attr 时，json_path 并非真正的动态参数，不支持 return 语句: {json_path}")

  @strict_types
  def depend_on_items(self) -> bool:
    return any("export_item_attr" in v for v in self._config["kconf_configs"])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      attrs.update(self.extract_dynamic_params(v["kconf_key"]))
      if "export_common_attr" in v and "json_path" in v:
        attrs.update(self.extract_dynamic_params(v["json_path"], check_format=False))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      if "export_item_attr" in v and "json_path" in v:
        attrs.update(self.extract_dynamic_params(v["json_path"], check_format=False))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { v["export_common_attr"] for v in self._config["kconf_configs"] if "export_common_attr" in v }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["export_item_attr"] for v in self._config["kconf_configs"] if "export_item_attr" in v }

class CommonRecoKconfLookupEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lookup_kconf"

  @strict_types
  def _check_config(self) -> None:
    available_types = {"set_int64", "set_string", "map_string_bool", "map_string_int64", "map_string_double", "map_string_string", "tail_number"}
    for conf in self._config["kconf_configs"]:
      value_type = conf.get("value_type")
      check_arg(value_type in available_types, f"错误的 kconf value_type: {value_type}，请参考文档配置")
      check_arg(conf.get("kconf_key"), "缺少 kconf_key 配置项")
      check_arg(conf.get("lookup_attr"), "缺少 lookup_attr 配置项")
      check_arg(conf.get("output_attr"), "缺少 output_attr 配置项")

  @strict_types
  def depend_on_items(self) -> bool:
    return any(not v.get("is_common_attr", True) for v in self._config["kconf_configs"])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      attrs.update(self.extract_dynamic_params(v["kconf_key"]))
      if v.get("is_common_attr", True):
        attrs.add(v["lookup_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      if not v.get("is_common_attr", True):
        attrs.add(v["lookup_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { v["output_attr"] for v in self._config["kconf_configs"] if v.get("is_common_attr", True) }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["output_attr"] for v in self._config["kconf_configs"] if not v.get("is_common_attr", True) }

class CommonRecoTfServingPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tf_serving_predict"

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("extra_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("send_item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config.get("output_prefix", "") + v for v in self._config["loss_function_name"] }

class CommonRecoSampleListCommonAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    if "save_attr_names_to_attr" not in config:
      config["save_attr_names_to_attr"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_by_sample_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("include_attrs", []))
    attr = self._config.get("save_attr_names_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

class CommonRecoTransformItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "transform_item_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for mapping in self._config["mappings"]:
      for rule in mapping["rules"]:
        for val in rule.get("check_values", []):
          attrs.update(self.extract_dynamic_params(val, check_format=False))
        if "check_range" in rule:
          for key in ["lower_bound", "upper_bound"]:
            if key in rule["check_range"]:
              attrs.update(self.extract_dynamic_params(rule["check_range"][key]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { v["check_attr_name"] for v in self._config["mappings"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["output_attr_name"] for v in self._config["mappings"] }

class CommonRecoOccurrenceCountItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "count_item_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for counter in self._config["counters"]:
      for val in counter["check_values"]:
        attrs.update(self.extract_dynamic_params(val))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { v["check_attr_name"] for v in self._config["counters"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["output_attr_name"] for v in self._config["counters"] }

class CommonRecoUserMetaInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_user_meta_info"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("save_result_size_to_attr"))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    fields = ["save_user_id_to_attr", "save_device_id_to_attr", "save_request_id_to_attr",
              "save_request_type_to_attr", "save_browse_set_size_to_attr", "save_result_size_to_attr",
              "save_request_time_to_attr", "save_request_num_to_attr", "save_current_time_ms_to_attr",
              "save_host_name_to_attr", "save_host_ip_to_attr", "save_elapsed_time_to_attr",
              "save_shard_no_to_attr", "save_shard_num_to_attr", "save_flow_cpu_cost_to_attr",
              "save_need_traceback_to_attr"]
    return set([self._config[v] for v in fields if v in self._config])

class CommonRecoItemMetaInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_item_meta_info"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    fields = ["save_item_key_to_attr", "save_item_id_to_attr", "save_item_type_to_attr", \
        "save_reason_to_attr", "save_score_to_attr", "save_in_browse_set_to_attr", \
        "save_item_seq_to_attr"]
    return set([self._config[v] for v in fields if v in self._config])

class CommonRecoLuaAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_lua"

  @strict_types
  def _check_config(self) -> None:
    function_for_common = self._config.get("function_for_common")
    function_for_item = self._config.get("function_for_item")
    import_common_attr = self._config.get("import_common_attr", [])
    import_item_attr = self._config.get("import_item_attr", [])
    if function_for_common and not isinstance(function_for_common, str):
      raise ArgumentError(f"function_for_common 配置项需为 string 类型")
    if function_for_item and not isinstance(function_for_item, str):
      raise ArgumentError(f"function_for_item 配置项需为 string 类型")
    if self._config.get("export_common_attr") and not function_for_common:
      raise ArgumentError("配置了 export_common_attr 但未配置 function_for_common")
    if self._config.get("export_item_attr") and not function_for_item:
      raise ArgumentError("配置了 export_item_attr 但未配置 function_for_item")
    if import_item_attr and not function_for_item:
      raise ArgumentError("配置了 import_item_attr 但未配置 function_for_item")
    if function_for_item and not self._config.get("export_item_attr"):
      raise ArgumentError("配置了 function_for_item 但未配置 export_item_attr")
    MAX_ATTR_NAME_LENGTH = 128
    for attr in itertools.chain(import_common_attr, import_item_attr):
      if isinstance(attr, str) and len(attr) > MAX_ATTR_NAME_LENGTH:
        raise ArgumentError("import_attr 包含长度超过 128 的字段 " + attr + "，可能有内存泄漏的风险")
    function_list = []
    if self._config.pop("check_lua", False):
      if function_for_common:
        function_list.append(function_for_common)
      if function_for_item:
        function_list.append(function_for_item)
    check_lua_script(self._config["lua_script"], function_list)


  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("function_for_item"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("import_common_attr", []))
    attrs.update(self.extract_dynamic_params(self._config.get("function_for_common", ""), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("function_for_item", ""), check_format=False))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("import_item_attr", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("export_common_attr", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("export_item_attr", []))

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    if self._config.get("for_branch_control", False):
      return ["$code_info"]
    return ["function_for_common", "function_for_item", "export_common_attr", "export_item_attr"]

class CommonRecoPythonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_python"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("import_item_attr"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("import_common_attr", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("import_item_attr", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("export_common_attr", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("export_item_attr", []))

class CommonRecoDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "delegate_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    if "item_from_tables" in self._config:
      check_arg("use_packed_item_attr" in self._config and self._config["use_packed_item_attr"] == True,
                f"{self.get_type_alias()} 配置了 item_from_tables 的情况下必须同时配置 use_packed_item_attr 为 True")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("hash_id")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("sample_list_ptr_attr"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    if "item_from_tables" in self._config:
      table_attrs = set()
      for tables in self._config["item_from_tables"]:
        table_attrs.update(try_add_table_name(tables, attrs))
      return table_attrs
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    if "item_from_tables" in self._config:
      table_attrs = set()
      for tables in self._config["item_from_tables"]:
        table_attrs.update(try_add_table_name(tables, attrs))
      return table_attrs
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

  # FIXME(fangjianbing): 这个 bug fix 对直播的耗时增长影响很大，暂时先注释掉
  # @strict_types
  # def depend_on_all_item_attrs(self) -> bool:
  #   return bool(self._config.get("send_item_attrs_in_name_list", ""))

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    return ["kess_service", "return_item_attrs"]

class CommonRecoProtobufSerializeAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "serialize_protobuf_message"

  @strict_types
  def depend_on_items(self) -> bool:
    return "from_item_attr" in self._config

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "from_common_attr" in self._config:
      attrs.add(self._config["from_common_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "from_item_attr" in self._config:
      attrs.add(self._config["from_item_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "serialize_to_common_attr" in self._config:
      return set([self._config["serialize_to_common_attr"]])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "serialize_to_item_attr" in self._config:
      return set([self._config["serialize_to_item_attr"]])
    return set()

class CommonRecoProtobufInjectBytesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inject_protobuf_bytes"

  @strict_types
  def depend_on_items(self) -> bool:
    return "from_item_attr" in self._config

  @strict_types
  def _check_config(self) -> None:
    for append_msg in self._config["append_msgs"]:
      if append_msg.get("field_type"):
        # - `field_type`: [string] pb 的 field type, 默认为 message; 可选值为 string, message, float, double, int, int64
        check_arg(append_msg["field_type"] in ["string", "message", "float", "double", "int", "int64"],
                  "field_type 只能为 string, message, float, double, int, int64")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { i["common_attr"] for i in self._config["append_msgs"] if "common_attr" in i }
    if "from_common_attr" in self._config:
      attrs.add(self._config["from_common_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = { i["item_attr"] for i in self._config["append_msgs"] if "item_attr" in i }
    if "from_item_attr" in self._config:
      attrs.add(self._config["from_item_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "output_common_attr" in self._config:
      return set([self._config["output_common_attr"]])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "output_item_attr" in self._config:
      return set([self._config["output_item_attr"]])
    return set()

class CommonRecoReverseListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reverse_list_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("common_attr", ""), str), "common_attr 需为 str 类型")
    check_arg(isinstance(self._config.get("item_attr", ""), str), "item_attr 需为 str 类型")
    check_arg(isinstance(self._config.get("common_attrs", []), list), "common_attrs需为list类型")
    check_arg(isinstance(self._config.get("item_attrs", []), list), "item_attrs需为list类型")
    check_arg("item_attr" in self._config or "common_attr" in self._config
              or "common_attrs" in self._config or "item_attrs" in self._config, "common_attr、item_attr、common_attrs、item_attrs 至少有一项")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_attr")) or bool(self._config.get("item_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    if self._config.get("common_attrs"):
      attrs.update(self._config["common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    if self._config.get("common_attrs"):
      attrs.update(self._config["common_attrs"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    if self._config.get("item_attrs"):
      attrs.update(self._config["item_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    if self._config.get("item_attrs"):
      attrs.update(self._config["item_attrs"])
    return attrs

class CommonRecoProtobufReleaseMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "release_protobuf_message"

  @strict_types
  def depend_on_items(self) -> bool:
    return "from_item_attr" in self._config

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "from_common_attr" in self._config:
      return set([self._config["from_common_attr"]])
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "from_item_attr" in self._config:
      return set([self._config["from_item_attr"]])
    return set()

class CommonRecoProtobufAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_protobuf"

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("save_all_fields", False):
      check_arg(not self._config.get("attrs", []), "save_all_fields=True 时禁止设置 attrs")
    else:
      check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif "name" in attr:
        attrs.add(attr["name"])
      elif "sample_attr_name" in attr:
        attrs.add(attr["sample_attr_name"])
      elif "sample_attr_name_value" in attr:
        attrs.add(str(attr["sample_attr_name_value"]))
      else:
        attrs.add(attr["path"])
    serialize_to_attr = self._config.get("serialize_to_attr")
    if serialize_to_attr:
      attrs.add(serialize_to_attr)
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    output_attrs = self._get_output_attrs() if self._is_common_attr else set()
    if self._config.get("save_all_fields", False):
      save_attr_names_to_attr = self._config.get("save_attr_names_to_attr")
      if save_attr_names_to_attr:
        output_attrs.add(save_attr_names_to_attr)
    return output_attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self._is_common_attr else self._get_output_attrs()

class CommonRecoJsonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_json"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    return set([self._config["import_attr"]])

  @strict_types
  def _get_output_attrs(self) -> set:
    return {x["name"] for x in self._config["attrs"]}

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return  self._get_output_attrs() if not self._is_common_attr else set()


class CommonRecoCountRecoResultEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "count_reco_result"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["save_result_size_to_common_attr"]])

class CommonRecoWeightedSumEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_weighted_sum"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      for key in ["weight"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class CommonRecoEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_ensemble_score"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2, 3), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1 或 2 或 3")
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["regulator", "smooth", "cliff_ratio", "cliff_height"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      save_score_to = channel.get("save_score_to")
      if save_score_to:
        attrs.add(save_score_to)
    attrs.add(self._config["output_attr"])
    return attrs

class CommonRecoEnsembleSeqNumEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_ensemble_seq_num"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("continuous_tied_seq")))
    attrs.update(self.extract_dynamic_params(self._config.get("epsilon")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config["ensemble_attrs"])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { attr + self._config.get("output_attr_postfix", "") \
        for attr in self._config["ensemble_attrs"] }

class CommonRecoCommonClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_cluster"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    dynamic_configs = ["kess_service", "bucket"]
    attrs = set()
    for config in dynamic_configs:
      attrs.update(self.extract_dynamic_params(self._config[config]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("add_item_embeddings_from_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

class CommonRecoItemListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "map_item_list_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { gen_attr_name_with_item_attr_channel(v, self._config["items_in_item_attr"]) \
             for v in self._config["item_attr_mapping"].keys() }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config["item_attr_mapping"].values())

class CommonRecoRedisCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_from_redis"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("is_async", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key", "")))
    for string_config in self._config.get("redis_params", []):
      attrs.update(self.extract_dynamic_params(string_config.get("redis_key", "")))
      attrs.update(self.extract_dynamic_params(string_config.get("key_prefix", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("redis_params", []):
      attrs.add(string_config["output_attr_name"])
    attrs.add(self._config.get("save_err_code_to"))
    return attrs


  @strict_types
  def _check_config(self) -> None:
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and 0 <= self._config["cache_bits"] < 30,
                "cache_bits 需为大于等于 0 且小于 30 的整数")

class CommonRecoRedisItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("is_async", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("key_prefix")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_key_from"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_value_to"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and 0 <= self._config["cache_bits"] < 30,
                "cache_bits 需为大于等于 0 且小于 30 的整数")

class CommonRecoTensorFlowAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_tensorflow"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for input_arg in self._config.get("inputs", {}).values():
      if "attr_source" in input_arg and input_arg["attr_source"] != "user":
        continue
      attrs.add(input_arg["attr_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for input_arg in self._config.get("inputs", {}).values():
      if "attr_source" not in input_arg or input_arg["attr_source"] == "item":
        attrs.add(input_arg["attr_name"])
    return attrs

  @property
  @strict_types
  def output_common_attr(self) -> bool:
    return self._config.get("output_common_attr", False)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self.output_common_attr:
      return set(self._config["outputs"].values())
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self.output_common_attr:
      return set(self._config["outputs"].values())
    else:
      return set()

  @classmethod
  def from_function(cls, func, output_as, output_common_attr=False, compress_common_input=False, **kwargs):
    import tensorflow as tf
    assert tf.__version__ == '1.12.3', "tensorflow==1.12.3 required"

    signature = inspect.signature(func)

    graph = tf.Graph()
    placeholders = []
    inputs = {}
    with graph.as_default():
      for arg, parameter in signature.parameters.items():
        arg_parts = arg.split("__")

        input_arg = inputs[arg] = dict()

        dim = -1
        if len(arg_parts) > 1:
          try:
            dim = input_arg["dim"] = int(arg_parts[-1])
            arg_parts = arg_parts[:-1]
          except ValueError:
            pass

        if len(arg_parts) > 1 and arg_parts[0] in ("user", "item"):
          input_arg["attr_source"] = arg_parts[0]
          arg_parts = arg_parts[1:]

        dtype = tf.float32
        if len(arg_parts) > 1:
          try:
            data_type = arg_parts[0]
            dtype = tf.dtypes.as_dtype(data_type)
            input_arg["data_type"] = data_type
            arg_parts = arg_parts[1:]
          except TypeError:
            pass

        if len(arg_parts) == 1:
          input_arg["attr_name"] = arg_parts[0]
        else:
          raise ArgumentError("func 的参数名非法: " + arg)

        if compress_common_input and "attr_source" in input_arg and input_arg["attr_source"] == "user":
          if dim > 0:
            placeholders.append(tf.placeholder(dtype=dtype, shape=(dim), name=arg))
          else:
            placeholders.append(tf.placeholder(dtype=dtype, shape=(), name=arg))
        else:
          if dim > 0:
            placeholders.append(tf.placeholder(dtype=dtype, shape=(None, dim), name=arg))
          else:
            placeholders.append(tf.placeholder(dtype=dtype, shape=(None), name=arg))

        default = parameter.default
        if default is not inspect.Parameter.empty:
          try:
            input_arg["default_value"] = float(default)
          except ValueError:
            raise ArgumentError("func 的默认值必须为数字: " + default)

      output_tensors = func(*placeholders)
      if tf.contrib.framework.is_tensor(output_tensors):
        output_tensors = (output_tensors, )
      if type(output_as) is str:
        output_as = (output_as, )

      if len(output_tensors) != len(output_as):
        raise ArgumentError(f"func 的返回值数量和 output_as 的长度不符: {len(output_tensors)} vs {len(output_as)}")

      for t in output_tensors:
        if output_common_attr:
          if t.get_shape().ndims > 1:
            raise ArgumentError("func 的返回值 shape 非法 [output_common_attr=True]: " + str(t.get_shape()))
        else:
          if t.get_shape().ndims == 0 or t.get_shape()[0] != None:
            raise ArgumentError("func 的返回值 shape 非法: " + str(t.get_shape()))

      outputs = {
        tensor.name : attr_name for tensor, attr_name in zip(output_tensors, output_as)
      }

    graph_def = graph.as_graph_def()
    return cls(config=dict(
      graph="base64://" + base64.b64encode(graph_def.SerializeToString()).decode("ascii"),
      inputs=inputs,
      outputs=outputs,
      output_common_attr=output_common_attr,
      compress_common_input=compress_common_input,
      **kwargs,
    ))

class CommonRecoProtobufParseAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_protobuf_from_string"

  @strict_types
  def _get_input_attrs(self) -> set:
    return { self._config["input_attr"] }

  @strict_types
  def _get_output_attrs(self) -> set:
    return { self._config["output_attr"] }

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._config.get("is_common_attr", True)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return set()
    else:
      return self._get_input_attrs()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return self._get_input_attrs()
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return set()
    else:
      return self._get_output_attrs()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return self._get_output_attrs()
    else:
      return set()

class CommonRecoProtobufCopyAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_from_protobuf"

  @strict_types
  def _check_config(self) -> None:
    check_arg("is_common_attr" in self._config,
              f"{self.get_type_alias()} 必须配置 is_common_attr 字段")
    check_arg("class_name" in self._config,
              f"{self.get_type_alias()} 必须配置 class_name 字段")
    check_arg(all("field_type" in i for i in self._config["inputs"]),
              f"{self.get_type_alias()} 的 inputs 每一个必须配置 field_type 字段类型")
    check_arg(all("build_list" in i for i in self._config["inputs"]),
              f"{self.get_type_alias()} 的 inputs 每一个必须配置 build_list 字段列表")
    for item in self._config["inputs"]:
      for build_item in item["build_list"]:
        check_arg("from_attr" in build_item,
                f"{self.get_type_alias()} 必须配置了 from_attr 字段")
        check_arg("to_path" in build_item,
                f"{self.get_type_alias()} 必须配置了 to_path 字段")
        if item["field_type"] == "pb_field":
          check_arg("from_path" in build_item,
                f"{self.get_type_alias()} 必须配置了 from_path 字段")
          check_arg("class_name" in item,
                f"{self.get_type_alias()} 必须配置了 class_name 字段")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self.input_item_attrs) or bool(self.output_item_attrs)

  @strict_types
  def _get_input_attrs(self) -> set:
    res = set()
    for item in self._config["inputs"]:
      for build_item in item["build_list"]:
        res.add(build_item["from_attr"])
    return res

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return set()
    return self._get_input_attrs()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return self._get_input_attrs()
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return set()
    return { self._config["output_attr"] }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return { self._config["output_attr"] }
    return set()

class CommonRecoProtobufBuildAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_protobuf"

  @strict_types
  def _check_config(self) -> None:
    if "is_common_attr" in self._config:
      check_arg("output_attr" in self._config, f"{self.get_type_alias()} 配置了 is_common_attr 的情况下必须同时配置 output_attr")
      check_arg(all("attr_name" in i for i in self._config["inputs"]), f"{self.get_type_alias()} 配置了 is_common_attr 的情况下必须同时给每个 inputs 配置 attr_name")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self.input_item_attrs) or bool(self.output_item_attrs)

  @strict_types
  def _get_input_attrs(self) -> set:
    return { i["attr_name"] for i in self._config["inputs"] if "attr_name" in i }

  @strict_types
  def _get_output_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return set()
      else:
        return self._get_input_attrs()
    else:
      return { i["item_attr"] for i in self._config["inputs"] if "item_attr" in i }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return self._get_input_attrs()
      else:
        return set()
    else:
      return { i["common_attr"] for i in self._config["inputs"] if "common_attr" in i }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return set()
      else:
        return self._get_output_attrs()
    else:
      if "output_item_attr" in self._config:
        return { self._config["output_item_attr"] }
      else:
        return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "is_common_attr" in self._config:
      if self._config.get("is_common_attr", True):
        return self._get_output_attrs()
      else:
        return set()
    else:
      if "output_common_attr" in self._config:
        return { self._config["output_common_attr"] }
      else:
        return set()

class CommonRecoConcatAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "concat_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg(("output_common_attr" in self._config) ^ ("output_item_attr" in self._config),"output_common_attr 和 output_item_attr 需且仅需配置一个")
    if "output_common_attr" in self._config:
      check_arg("input_common_attrs" in self._config and type(self._config["input_common_attrs"]) is list
                and len(self._config["input_common_attrs"]) > 0,
                f"output_common_attr 设置后 input_common_attrs 必须存在")

    if "output_item_attr" in self._config:
      check_arg("input_item_attrs" in self._config and type(self._config["input_item_attrs"]) is list
                and len(self._config["input_item_attrs"]) > 0,
                f"output_item_attr 设置后 input_item_attrs 必须存在")
    if "output_common_attr_name" in self._config and "output_common_attr" not in self._config:
      check_arg(0, "output_common_attr 设置后 output_common_attr_name 才有效")
    if "output_item_attr_name" in self._config and "output_item_attr" not in self._config:
      check_arg(0, "output_item_attr 设置后 output_item_attr_name 才有效")

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return bool(self._config.get("input_item_attrs"))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    val = set()
    if self._config.get("output_common_attr"):
      val.add(self._config.get("output_common_attr"))
    if self._config.get("output_common_attr_name"):
      val.add(self._config.get("output_common_attr_name"))
    return val

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    val = set()
    if self._config.get("output_item_attr"):
      val.add(self._config.get("output_item_attr"))
    if self._config.get("output_item_attr_name"):
      val.add(self._config.get("output_item_attr_name"))
    return val

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("input_item_attrs"):
      return set(self._config.get("input_item_attrs"))
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("input_common_attrs"):
      return set(self._config.get("input_common_attrs"))
    else:
      return set()

  @property
  @strict_types
  def output_item_attr_name(self) -> str:
    return self._config["output_item_attr_name"]

  @property
  @strict_types
  def output_common_attr_name(self) -> str:
    return self._config["output_common_attr_name"]

  @property
  @strict_types
  def output_attr_name_save_as_int(self) -> bool:
    return self._config["output_attr_name_save_as_int"]

class CommonRecoSelectSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_sign"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", False)

  @property
  @strict_types
  def _get_input_attrs(self) -> set:
    val = set()
    val.add(self._config.get("input_slot_attr"))
    val.add(self._config.get("input_sign_attr"))
    return val

  @property
  @strict_types
  def _get_output_attrs(self) -> set:
    val = set()
    for k in self._config.get("output_sign_attrs"):
      val.add(k)
    return val

  @strict_types
  def _check_config(self) -> None:
    check_arg("select_slots" in self._config and type(self._config["select_slots"]) is list
              and len(self._config["select_slots"]) > 0,
              f"select_slots 必须存在")
    check_arg("output_sign_attrs" in self._config and type(self._config["output_sign_attrs"]) is list
              and len(self._config["output_sign_attrs"]) > 0,
              f"output_sign_attrs 必须存在")
    check_arg(len(self._config["output_sign_attrs"]) == len(self._config["select_slots"]), "select_slots 与 output_sign_attrs 长度需一致")
    check_arg(("input_slot_attr" in self._config) and ("input_sign_attr" in self._config),"input_slot_attr 和 input_sign_attr 需不能为空")

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self._get_output_attrs if not self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs if not self._is_common_attr else set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs if self._is_common_attr else set()

class CommonRecoBuildSampleAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_sample_attr"

  @strict_types
  def _get_input_attrs(self) -> set:
    return { m["from_attr"] for m in self._config["mappings"] if "from_attr" in m }

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for m in self._config["mappings"]:
      if "to_attr" in m:
        attrs.add(m["to_attr"])
      else:
        attrs.add(m["from_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return self._get_input_attrs()
    else:
      return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return set()
    else:
      return self._get_input_attrs()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return set()
    else:
      return self._get_output_attrs()

class CommonRecoTfLocalPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_tf_local_predict"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set([self._config.get("tf_kuiba_update_model_key", "PULL_MODEL_VARIABLE")])
    for attr in self._config.get("inputs", {}).values():
      ret.add(attr["attr_name"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      return set(self._config["outputs"])
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      return set()
    else:
      return set(self._config["outputs"])

class CommonRecoUserInfoCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_user_info"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "new_user" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("new_user")))
    if "user_id" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    if "device_id" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config["save_to_common_attr"] }

class CommonRecoEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_local_embedding"

  @strict_types
  def _get_input_attrs(self) -> set:
    return set(self._config["parameters_inputs"])

  @strict_types
  def _get_output_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return set()
    else:
      return self._get_input_attrs()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return self._get_input_attrs()
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return set()
    else:
      return self._get_output_attrs()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()


class CommonRecoRemoteEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_remote_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      ret = {self._config["output_embedding_list_attr"]}
      if "output_item_list_attr" in self._config:
        ret.add(self._config["output_item_list_attr"])
      return ret
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      return set()
    else:
      return {self._config["output_attr_name"]}

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    converter_conf = self._config["id_converter"]
    attrs.update(self.extract_dynamic_params(converter_conf.get("type_name")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("query_source_type", "") in ["item_attr"]:
      attrs.add(self._config["query_source_item_attr"])
    return attrs

class CommonRecoRemoteEmbeddingAttrLiteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_remote_embedding_lite"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["user_id", "device_id", "common_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_key", "item_id", "item_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    if self._config.get("query_source_type", "item_key") in ["common_attr"]:
      attrs.add(self._config["input_attr_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_attr"]:
      return {self._config["input_attr_name"]}
    else:
      return set()

class CommonRecoRemoteEmbeddingAttrRawLiteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_remote_embedding_lite_v2"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["user_id", "device_id", "common_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_key", "item_id", "item_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    if self._config.get("query_source_type", "item_key") in ["common_attr"]:
      attrs.add(self._config["input_attr_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_attr"]:
      return {self._config["input_attr_name"]}
    else:
      return set()

class CommonRecoLocalAnnEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_local_ann_embedding"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      attrs = {self._config["item_list_output_attr"], self._config["embedding_list_output_attr"]}
      if "data_type_list_output_attr" in self._config:
        attrs.add(self._config["data_type_list_output_attr"])
      return attrs
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("save_to_common_attr", False):
      return set()
    else:
      attrs = {self._config["embedding_item_attr"]}
      if "data_type_item_attr" in self._config:
        attrs.add(self._config["data_type_item_attr"])
      return attrs

class CommonRecoExecutionStatusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "return_"

  @strict_types
  def is_reorder_barrier(self) -> bool:
    return True

  @strict_types
  def need_reorder(self, _: bool = False) -> bool:
    return False

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config["status_code"] in (0, 1, 2, 4), f"{self.get_type_alias()} 的 code 值必须为 0, 1, 2 或 4")

class CommonRecoBase64Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "base64"

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._config.get("is_common_attr", True)


  @strict_types
  def _check_config(self) -> None:
    mode = self._config.get("mode")
    check_arg(mode and mode in {"encode", "decode"}, '`mode` 必须选自 {"encode", "decode"}')

    check_arg(self._config.get("input_attr"), "`input_attr` 是必选项")
    check_arg(self._config.get("output_attr"), "`output_attr` 是必选项")

  def is_common(self):
    return self._config.get("is_common_attr", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

class CommonRecoEncryptedIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "encrypted_id"

  @strict_types
  def _check_config(self) -> None:
    mode = self._config.get("mode")
    valid_mode_set = {"encrypt_photo_id", "decrypt_photo_id", "encrypt_live_id", "decrypt_live_id", "decrypt_id"}
    check_arg(mode and mode in valid_mode_set, f'`mode` 必须选自 {valid_mode_set}')

    check_arg(self._config.get("input_attr"), "`input_attr` 是必选项")
    check_arg(self._config.get("output_attr"), "`output_attr` 是必选项")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._is_common_attr:
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._is_common_attr:
      attrs.add(self._config["output_attr"])
    return attrs

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("reset_errno")))
    if self._is_common_attr:
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._is_common_attr:
      attrs.add(self._config["output_attr"])
    return attrs


class CommonRecoZstdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "zstd"

  @strict_types
  def _check_config(self) -> None:
    mode = self._config.get("mode")
    check_arg(mode and mode in {"compress", "decompress"}, '`mode` 必须选自 {"compress", "decompress"}')
    if self._config.get("input_common_attr"):
      check_arg(self._config.get("output_common_attr"), "`output_common_attr` 参数缺失")
      check_arg(not self._config.get("input_item_attr"), "不应该配置 `input_item_attr`")
      check_arg(not self._config.get("output_item_attr"), "不应该配置 `output_item_attr`")
    elif self._config.get("input_item_attr"):
      check_arg(self._config.get("output_item_attr"), "`output_item_attr` 参数缺失")
      check_arg(not self._config.get("output_common_attr"), "不应该配置 `output_common_attr`")
    else:
      check_arg(False, "common_attr 和 item_attr 两组参数，必须选其中一组进行配置")

  @strict_types
  def depend_on_items(self) -> bool:
    if self._config.get("input_item_attr"):
      return True
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("compression_level", 3)))
    if self._config.get("input_common_attr"):
      attrs.add(self._config["input_common_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attr"):
      attrs.add(self._config["output_common_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_item_attr"):
      attrs.add(self._config["input_item_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_item_attr"):
      attrs.add(self._config["output_item_attr"])
    return attrs

class CommonRecoStringFormatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "string_format"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("format_string"), "`format_string` must be set")
    check_arg(self._config.get("input_attrs"), "`input_attrs` must be set")
    check_arg(self._config.get("output_attr"), "`output_attr` must be set")

  def is_common(self):
    return self._config.get("is_common_attr", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.update(self._config["input_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.update(self._config["input_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

class CommonRecoStrFormatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "str_format"

  @strict_types
  def depend_on_items(self) -> bool:
    return not self.is_common()

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("format_string"), "`format_string` must be set")
    check_arg(self._config.get("input_attrs"), "`input_attrs` must be set")
    check_arg(self._config.get("output_attr"), "`output_attr` must be set")

  def is_common(self):
    return self._config.get("is_common_attr", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.update(self._config["input_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.update(self._config["input_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

class CommonRecoMappingAggregateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "aggregate_mapping_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["key_list_attr"])
    for config in self._config.get("aggregate_config", []):
      attrs.add(config["value_list_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("match_key_in_item_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    item_channel = ""
    if self._config.get("save_for_match_key", False):
      item_channel = self._config["match_key_in_item_attr"]
    attrs.add(gen_attr_name_with_item_attr_channel(self._config.get("save_count_to", ""), item_channel))
    for config in self._config.get("aggregate_config", []):
      attrs.add(gen_attr_name_with_item_attr_channel(config["save_result_to"], item_channel))
    return attrs


class CommonRecoPipelineEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    from ...common_leaf_dsl import LeafFlow
    check_arg(isinstance(self.__sub_flow, LeafFlow), f"sub_flow 类型必须为 LeafFlow。但是取到的类型为: {type(self.__sub_flow)} Tips: 请检查下有没有继承 LeafFLow、链式调用的函数有没有返回 self")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = "merge_common_attrs" not in self._config
    self._auto_detect_merge_item_attrs = "merge_item_attrs" not in self._config

  @strict_types
  def get_type_alias(self) -> str:
    return "enrich_by_sub_flow"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("task_queue_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = extract_attr_names(self._config.get("merge_common_attrs", []), "as")
    if "save_results_to" in self._config:
      ret.add(self._config["save_results_to"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("merge_item_attrs", []), "as")

  @strict_types
  def _check_config(self) -> None:
    for processor in self.__sub_flow._processors:
      check_arg(self.item_table not in processor.output_item_tables,
                f"sub_flow {self.__sub_flow.name} 中不可包含 retriever 类型 processor: {processor.name or processor.get_type_alias()}")

class CommonRecoNormAndDiscreteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_norm_and_discrete"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("is_common_attr"), bool), "`is_common_attr` 需为 bool 类型")
    check_arg(isinstance(self._config.get("input_attr"), str), "`input_attr` 需为 string 类型")
    check_arg(isinstance(self._config.get("output_norm_attr", ""), str), "`output_norm_attr` 需为 string 类型")
    check_arg(isinstance(self._config.get("output_discrete_attr", ""), str), "`output_discrete_attr` 需为 string 类型")
    check_arg(isinstance(self._config.get("quantile_list"), (list, str)), "`quantile_list` 需为 list 类型")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.add(self._config["input_attr"])
    # 动态参数处理
    attrs.update(self.extract_dynamic_params(self._config["quantile_list"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.add(self._config.get("output_norm_attr"))
      attrs.add(self._config.get("output_discrete_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.add(self._config.get("output_norm_attr"))
      attrs.add(self._config.get("output_discrete_attr"))
    return attrs

class CommonRecoGatherAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_list_values"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    return set([self._config["index_attr"], *[v["from"] for v in self._config["list_values"]]])

  @strict_types
  def _get_output_attrs(self) -> set:
    return set(v["to"] for v in self._config["list_values"])

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return  self._get_output_attrs() if not self._is_common_attr else set()

class CommonRecoRedisItemAttrBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_from_redis_batch"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("is_async", False)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("attrs_config", []):
      attrs.add(config["value_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("attrs_config", []):
      attrs.add(config["key_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key", "")))
    for config in self._config.get("attrs_config", []):
      if "key_prefix" in config:
        attrs.update(self.extract_dynamic_params(config["key_prefix"]))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and 0 <= self._config["cache_bits"] < 30,
                "cache_bits 需为大于等于 0 且小于 30 的整数")


class CommonRecoCopyAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_attr"

  @strict_types
  def _check_config(self) -> None:
    for attr in self._config.get("attrs", []):
      check_arg(("from_common" in attr) ^ ("from_item" in attr), "attr 里的 from 需要且只能配置一个")
      check_arg(("to_common" in attr) ^ ("to_item" in attr), "attr 里的 to 需要且只能配置一个")

      check_arg(isinstance(attr.get("from_common", ""), str), "attr 里的 from_common 值必须为字符串")
      check_arg(isinstance(attr.get("from_item", ""), str), "attr 里的 from_item 值必须为字符串")
      check_arg(isinstance(attr.get("to_common", ""), str), "attr 里的 to_common 值必须为字符串")
      check_arg(isinstance(attr.get("to_item", ""), str), "attr 里的 to_item 值必须为字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return any("from_item" in attr or "to_item" in attr for attr in self._config.get("attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("from_common"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("to_common"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("from_item"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("to_item"))
    return attrs

class CommonRecoLightFunctionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_light_function"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(any(self._config.get("import_item_attr", [])) or any(self._config.get("export_item_attr", [])))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_item_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "as")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    return attrs

class CommonRecoNormalizationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normalize_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

class CommonRecoMemcachedCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_from_memcached"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.extract_dynamic_params(self._config.get("query_key", ""))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config.get("output_attr_name") }

class CommonRecoItemMultiAttrPackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_multi_item_attrs"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for e in self._config.get("from_attrs", list()):
      ret.add(e)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["to_attr"]:
      ret.add(self._config["to_attr"])
    return ret

class CommonRecoJsonStringAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_json"

  @strict_types
  def _check_config(self) -> None:
    check_arg("json_attr" in self._config, "必须配置 json_attr")
    check_arg("json_configs" in self._config, "必须配置 json_configs")
    check_arg(isinstance(self._config["json_attr"], str), "json_attr 必须为 string")
    check_arg(isinstance(self._config["json_configs"], list), "json_configs 必须为 list")

    for config in self._config["json_configs"]:
      check_arg("json_path" in config, "一个 config 里必须配置 json_path")
      check_arg(("export_common_attr" in config) ^ ("export_item_attr" in config), "一个 config 里的 export_common_attr 和 export_item_attr 需要且只能配置一个")

  @strict_types
  def depend_on_items(self) -> bool:
    return any("export_item_attr" in v for v in self._config["json_configs"]) or self._config.get("json_from_item_attr", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if not self._config.get("json_from_item_attr", False):
      attrs.add(self._config["json_attr"])
    for config in self._config["json_configs"]:
      if "export_common_attr" in config:
        attrs.update(self.extract_dynamic_params(config["json_path"], check_format=False))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { v["export_common_attr"] for v in self._config["json_configs"] if "export_common_attr" in v }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("json_from_item_attr", False):
      attrs.add(self._config["json_attr"])
    for config in self._config["json_configs"]:
      if "export_item_attr" in config:
        attrs.update(self.extract_dynamic_params(config["json_path"], check_format=False))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["export_item_attr"] for v in self._config["json_configs"] if "export_item_attr" in v }


class CommonProtobufListParseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_protobuf_list_from_common_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(map(lambda x: x.get("name") or x.get("path"), self._config.get("attrs", [])))
    return attrs

class CommonRecoKconfTailNumberEnricher(LeafEnricher):
  @strict_types
  def _check_config(self) -> None:
    kconf_key = self._config.get("kconf_key");
    test_value = self._config.get("test_value");
    output_to = self._config.get("output_to");
    check_arg(bool(kconf_key), f"{self.get_type_alias()} 的 kconf_key 配置不可为空")
    check_arg(isinstance(kconf_key, str), f"{self.get_type_alias()} 的 kconf_key 配置必须为 string 类型")
    check_arg(bool(test_value), f"{self.get_type_alias()} 的 test_value 配置不可为空")
    check_arg(isinstance(test_value, str), f"{self.get_type_alias()} 的 test_value 配置必须为 string 类型")
    check_arg(isinstance(output_to, str), f"{self.get_type_alias()} 的 output_to 配置必须为 string 类型")

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "check_tail_number"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("test_value")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_to", "tail_number_output"))
    return attrs

class CommonRecoAttrTypeCastEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cast_attr_type"

  @strict_types
  def _check_config(self) -> None:
    for cfg in self._config["attr_type_cast_configs"]:
      from_common_attr = cfg.get("from_common_attr")
      to_common_attr = cfg.get("to_common_attr")
      from_item_attr = cfg.get("from_item_attr")
      to_item_attr = cfg.get("to_item_attr")
      to_type = cfg.get("to_type")
      check_arg((bool(from_common_attr) & bool(to_common_attr)) ^ (bool(from_item_attr) & bool(to_item_attr)),
                "from 和 to 必须一起配置, item 与 common 不能交叉配置")
      check_arg((to_type in ["int", "integer", "float", "double", "str", "string", "float16", "float32"]),
                "to_type 必须是 int/integer/float/double/str/string/float16/float32 中的一种")
      if from_item_attr and to_item_attr:
        check_arg((os.getenv("ENABLE_SAME_ITEM_ATTR_NAME", 'false') == 'true') or (from_item_attr != to_item_attr), 
                  "from_item_attr 和 to_item_attr 不能同名")

  @property
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["attr_type_cast_configs"]:
      if "from_common_attr" in cfg:
        attrs.add(cfg["from_common_attr"])
    return attrs

  @property
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["attr_type_cast_configs"]:
      if "to_common_attr" in cfg:
        attrs.add(cfg["to_common_attr"])
    return attrs

  @property
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["attr_type_cast_configs"]:
      if "from_item_attr" in cfg:
        attrs.add(cfg["from_item_attr"])
    return attrs

  @property
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["attr_type_cast_configs"]:
      if "to_item_attr" in cfg:
        attrs.add(cfg["to_item_attr"])
    return attrs

class CommonRecoPackItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_item_attr_to_item_attr"

  @strict_types
  def _check_config(self) -> None:
    if "aggregator" in self._config:
      valid_aggregator = ["concat", "copy"]
      if self._config["aggregator"] not in valid_aggregator:
        raise ArgumentError(f"aggregator 只支持 {valid_aggregator}")

    if "from_item_attrs" not in self._config:
      raise ArgumentError("必须配置 from_item_attrs")
    if not isinstance(self._config["from_item_attrs"], list):
      raise ArgumentError("from_item_attrs 必须为 string list")

    if "to_item_attr" not in self._config:
      raise ArgumentError("必须配置 to_item_attr")
    if not isinstance(self._config["to_item_attr"], str):
      raise ArgumentError("to_item_attr 必须为 string")

    if "default_val" not in self._config:
      raise ArgumentError("必须配置 default_val")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    from_item_attrs = self._config["from_item_attrs"]
    for item_attr in from_item_attrs:
      attrs.add(item_attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("limit_num")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["to_item_attr"])
    return attrs

class CommonRecoWasmEnricher(LeafEnricher):
  def compile_c_to_wasm(self, config: dict):
    # compile c to wasm
    template_c = """
#include <stdint.h>

typedef __SIZE_TYPE__ size_t;

#define NULL ((void *)0)

typedef int64_t i64;
typedef int32_t i32;
typedef double f64;
typedef float f32;
typedef struct {
  int size;
  char data[0];
} string_t;

#pragma pack(push, 1)
typedef struct {
  int size;
  i64 data[0];
} i64_list_t;
typedef struct {
  int size;
  f64 data[0];
} f64_list_t;
#pragma pack(pop)

extern void __heap_base;
size_t heap_used = 0;

#define PAGE_SIZE 65536
#define PAGE_SIZE_LOG_2 16
#define DEFAULT_ALIGN 4

static inline size_t align(size_t val, size_t alignment) {
  return (val + alignment - 1) & ~(alignment - 1);
}

// a simple allocator
void *alloc(size_t size) {
  size_t memory_size = __builtin_wasm_memory_size(0) * PAGE_SIZE;
  size_t used = (size_t)(&__heap_base) + heap_used;
  size_t need_size = align(size, DEFAULT_ALIGN);
  if (need_size > SIZE_MAX - used) {
    // overflow
    return NULL;
  }
  if (need_size + used > memory_size) {
    // allocate pages
    size_t pages =
        align(need_size + used - memory_size, PAGE_SIZE) >> PAGE_SIZE_LOG_2;
    size_t ret = __builtin_wasm_memory_grow(0, pages);
    if (ret == -1) {
      // not enough memory
      return NULL;
    }
  }
  // save used heap size
  heap_used += need_size;
  return (void *)used;
}

void init() {
  // makes the heap reusable
  heap_used = 0;
}

string_t *alloc_string(int len) {
  string_t *r = (string_t *)alloc(sizeof(int) + sizeof(char) * len);
  r->size = len;
  return r;
}

i64_list_t *alloc_i64_list(int len) {
  i64_list_t *r = (i64_list_t *)alloc(sizeof(int) + sizeof(i64) * len);
  r->size = len;
  return r;
}

f64_list_t *alloc_f64_list(int len) {
  f64_list_t *r = (f64_list_t *)alloc(sizeof(int) + sizeof(f64) * len);
  r->size = len;
  return r;
}

string_t *copy_string(char *data, int size) {
  string_t *r = alloc_string(size);
  for (int i = 0; i < size; i++) {
    r->data[i] = data[i];
  }
  return r;
}

i64_list_t *copy_i64_list(i64 *data, int size) {
  i64_list_t *r = alloc_i64_list(size);
  for (int i = 0; i < size; i++) {
    r->data[i] = data[i];
  }
  return r;
}

f64_list_t *copy_f64_list(f64 *data, int size) {
  f64_list_t *r = alloc_f64_list(size);
  for (int i = 0; i < size; i++) {
    r->data[i] = data[i];
  }
  return r;
}
    """

    def handle_attrs(attrs) -> str:
      attr_name_set = dict()
      first_item_attr = True
      gen_attrs = []
      for attr in attrs:
        name = attr['name']
        value_type = attr['value_type']
        wasm_var = attr['wasm_var'] if "wasm_var" in attr else name
        if name in attr_name_set:
          if attr_name_set[name] == value_type:
            continue
          else:
            raise Exception("duplicate attr name with different type")
        else:
          attr_name_set[name] = value_type

        if "type" not in attr or attr["type"] == "common_attr":
          if value_type == "i64":
            gen_attrs.append(f"i64 {wasm_var};")
          elif value_type == "f64":
            gen_attrs.append(f"f64 {wasm_var};")
          elif value_type == "string":
            gen_attrs.append(f"string_t *{wasm_var};")
          elif value_type == "i64_list":
            gen_attrs.append(f"i64_list_t *{wasm_var};")
          elif value_type == "f64_list":
            gen_attrs.append(f"f64_list_t *{wasm_var};")
        elif attr["type"] == "item_attr":
          if first_item_attr:
            first_item_attr = False
            gen_attrs.append("int item_num;")

          if value_type == "i64":
            gen_attrs.append(f"i64 *{wasm_var};")
          elif value_type == "f64":
            gen_attrs.append(f"f64 *{wasm_var};")
          elif value_type == "string":
            gen_attrs.append(f"string_t **{wasm_var};")
          elif value_type == "i64_list":
            gen_attrs.append(f"i64_list_t **{wasm_var};")
          elif value_type == "f64_list":
            gen_attrs.append(f"f64_list_t **{wasm_var};")
        else:
          raise Exception(f"invalid attr type '{attr['type']}'")
      return "\n".join(gen_attrs)

    input_and_output_attrs = []
    if "input_attrs" in config:
      input_and_output_attrs.extend(config["input_attrs"])
    if "output_attrs" in config:
      input_and_output_attrs.extend(config["output_attrs"])

    code = ""
    if "template_c" in config:
      code = config["template_c"]
    else:
      code = template_c
    code += handle_attrs(input_and_output_attrs) + "\n"
    code += config["c"]

    if "debug_show_code" in config and config["debug_show_code"]:
      print("START SHOW CODE----------------------------")
      print(code)
      print("END SHOW CODE----------------------------")

    import time
    import subprocess
    import os
    suffix = str(int(time.time()))
    path = f"/tmp/enrich_by_wasm.{suffix}.c"
    with open(path, "w") as f:
      f.write(code)
    
    clang_args = [
      '--target=wasm32-unknown-unknown',
      '--optimize=3',
      '-nostdlib',
      '-mbulk-memory',
      '-mreference-types',
      '-mmultivalue',
      '-mmutable-globals',
      '-mnontrapping-fptoint',
      '-msign-ext',
      '-Wl,--export-all',
      '-Wl,--no-entry',
      '-Wl,--allow-undefined',
    ]
    if "clang_args" in config:
      clang_args = config["clang_args"]
    
    compile_to_wat = "compile_to_wat" in config and config["compile_to_wat"]
    wasm_output_path = ""
    if "wasm_file" in config:
      wasm_output_path = config["wasm_file"]
    elif compile_to_wat:
      wasm_output_path = f"/tmp/enrich_by_wasm.{suffix}.wasm"
    else:
      raise ArgumentError("当 compile_to_wat 为 False 时 wasm_file 没有配置")

    try:
      process = subprocess.Popen(
        [
          'clang',
          path,
          '--output',
          wasm_output_path
        ] + clang_args,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE)
      stdout, stderr = process.communicate()
      if process.returncode != 0:
        raise Exception(f"compile c code to wasm failed!\n{stderr.decode()}")
    except Exception as e:
      raise Exception("clang compile failed!") from e
    os.remove(path)
    
    if "debug_show_wat" in config and config["debug_show_wat"]:
      try:
        process = subprocess.Popen(
          ['wasm-tools', 'parse', '-t', wasm_output_path], 
          stdout=subprocess.PIPE,
          stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        print("START SHOW WAT----------------------------")
        print(stdout.decode())
        print(stderr.decode())
        print("END SHOW WAT----------------------------")
      except Exception as e:
        raise Exception("debug show wat failed!") from e

    if compile_to_wat:
      try:
        process = subprocess.Popen(
          ['wasm-tools', 'parse', '-t', wasm_output_path], 
          stdout=subprocess.PIPE,
          stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
          raise Exception(f"wasm-tools parse wasm to wat failed!\n{stderr.decode()}")
        # set to wat
        config["wat"] = stdout.decode()
        os.remove(wasm_output_path)
      except Exception as e:
        raise Exception("compile to wat failed!") from e

  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    if "c" in config:
      self.compile_c_to_wasm(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_wasm"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    calls = self._config["calls"]
    for call in calls:
      if "params" in call:
        for attr in call["params"]:
          attrs.add(attr)
    if "input_attrs" in self._config:
      for in_attr in self._config["input_attrs"]:
        if "type" not in in_attr or in_attr["type"] == "common_attr":
          attrs.add(in_attr["name"])
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "input_attrs" in self._config:
      for in_attr in self._config["input_attrs"]:
        if "type" in in_attr and in_attr["type"] == "item_attr":
          attrs.add(in_attr["name"])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    calls = self._config["calls"]
    for call in calls:
      if "results" in call:
        for attr in call["results"]:
          attrs.add(attr)
    if "output_attrs" in self._config:
      for out_attr in self._config["output_attrs"]:
        if "type" not in out_attr or out_attr["type"] == "common_attr":
          attrs.add(out_attr["name"])
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "output_attrs" in self._config:
      for out_attr in self._config["output_attrs"]:
        if "type" in out_attr and out_attr["type"] == "item_attr":
          attrs.add(out_attr["name"])
    return attrs
  
  @strict_types
  def check_in_out_attr(self, attr_conf) -> None:
    if "name" not in attr_conf:
      raise ArgumentError("'input_attrs', 'output_attrs' 的每个元素必须配置 attr 名称")
    if "type" in attr_conf:
      if attr_conf["type"] != "common_attr" and attr_conf["type"] != "item_attr":
        raise ArgumentError("'input_attrs', 'output_attrs' 的每个元素的 'type' 必须是 'common_attr' 或 'item_attr'")
    if "value_type" not in attr_conf:
      raise ArgumentError("'input_attrs', 'output_attrs' 的每个元素必须配置 value_type")
    if attr_conf["value_type"] != "i64" and attr_conf["value_type"] != "f64" and \
        attr_conf["value_type"] != "string" and attr_conf["value_type"] != "i64_list" and \
        attr_conf["value_type"] != "f64_list":
      raise ArgumentError("'input_attrs', 'output_attrs' 的每个元素的 'value_type' 必须是 'i64', 'f64', 'string', 'i64_list', 'f64_list' 之一")

  @strict_types
  def _check_config(self) -> None:
    compile_to_wat = "compile_to_wat" in self._config and self._config["compile_to_wat"]
    if compile_to_wat:
      if "c" not in self._config:
        raise ArgumentError("compile_to_wat 为 True 时，必须配置 c")
      if "wasm_file" in self._config:
        raise ArgumentError("compile_to_wat 为 True 时，不能同时配置 wasm_file")
    if "wat" not in self._config and "wasm_file" not in self._config:
      raise ArgumentError("必须配置 wat 或 wasm_file 或 compile_to_wat 为 True")
    if "wat" in self._config and "wasm_file" in self._config:
      raise ArgumentError("不能同时配置 wat 和 wasm_file")
    if "calls" not in self._config:
      raise ArgumentError("必须配置 calls")
    if len(self._config["calls"]) <= 0:
      raise ArgumentError("calls 至少要包含一个内容且是 array 类型")
    for call in self._config["calls"]:
      if "name" not in call or len(call["name"]) <= 0:
        raise ArgumentError("每个 call 必须包含 name")
      if "params" in call:
        for param in call["params"]:
          if not isinstance(param, str):
            raise ArgumentError("每个 param 必须是 string 类型")
      if "results" in call:
        for result in call["results"]:
          if not isinstance(result, str):
            raise ArgumentError("每个 result 必须是 string 类型")
    if "input_attrs" in self._config:
      for in_attr in self._config["input_attrs"]:
        self.check_in_out_attr(in_attr)
    if "output_attrs" in self._config:
      for out_attr in self._config["output_attrs"]:
        self.check_in_out_attr(out_attr)
    clear_attr_if_null = "clear_attr_if_null" in self._config and self._config["clear_attr_if_null"]
    set_empty_attr_if_null = "set_empty_attr_if_null" in self._config and self._config["set_empty_attr_if_null"]
    if clear_attr_if_null and set_empty_attr_if_null:
      raise ArgumentError("clear_attr_if_null 与 set_empty_attr_if_null 不能同时为 True")

class CommonRecoFormulaOneEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_by_formula1"

  @strict_types
  def _check_config(self) -> None:
    check_arg(bool("kconf_key" in self._config)^bool("base_json" in self._config), "kconf_key 和 base_json 需且仅需配置其中一项")
    if "kconf_key" in self._config:
      check_arg("abtest_biz_name" in self._config, "必须配置 formula1 的 abtest_biz_name")
    if "base_json" in self._config:
      base_json = self._config["base_json"]
      check_arg(isinstance(base_json, dict), "base_json 需为 dict")
      check_arg("scenario" in base_json, "base_json 中必须配置 scenario，但不会用于取 ab 数据")
      check_arg("import_common_attr" not in base_json, "base_json 中不支持 import_common_attr，请用 processor 的配置")
      check_arg("import_item_attr" not in base_json, "base_json 中不支持 import_item_attr，请用 processor 的配置")
    check_arg(len(self._config["export_formula_value"]) > 0, "formula1 的 export_formula_value 不可为空")

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("session_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("prioritized_suffix")))
    attrs.update(self.extract_dynamic_params(self._config.get("perf_tag")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_pruning_opt")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("import_item_attr", []), "name")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    config = self._config.get("export_formula_value", [])
    for c in config:
      if isinstance(c, dict):
        if c.get("to_common", False) == True:
          if ("as" in c):
            ret.add(c["as"])
          else:
            ret.add(c["name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    config = self._config.get("export_formula_value", [])
    for c in config:
      if isinstance(c, str):
        ret.add(c)
      elif isinstance(c, dict):
        if c.get("to_common", False) == False:
          if ("as" in c):
            ret.add(c["as"])
          else:
            ret.add(c["name"])
      else:
        raise ArgumentError(f"该 list 中存在不支持的配置类型 {type(c)}: {config}")
    return ret

class CommonRecoBuildCustomKVUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_custom_kv_user_info"

  @strict_types
  def _check_config(self) -> None:
    check_arg("collection_name" in self._config, "必须配置 collection_name")
    check_arg("kv_user_info_attr" in self._config, "必须配置 kv_user_info_attr")
    check_arg("save_result_to_attr" in self._config, "必须配置 save_result_to_attr")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("kv_user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("custom_user_info_keys")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_result_to_attr"))
    return attrs

class CommonRecoParseFromCustomKVUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_from_custom_kv_user_info"

  @strict_types
  def _check_config(self) -> None:
    check_arg("collection_name" in self._config, "必须配置 collection_name")
    check_arg("custom_user_info_from" in self._config, "必须配置 custom_user_info_from")
    check_arg("fields_to_read" in self._config, "必须配置 fields_to_read")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("custom_user_info_from"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if isinstance(self._config.get("fields_to_read"), list):
      attrs.update(self._config.get("fields_to_read"))
    return attrs


class CommonRecoReadonlyAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mark_attr_readonly"

  @strict_types
  def depend_on_items(self) -> bool:
    return len(self.input_item_attrs) > 0

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))


class CommonRecoBuildResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_common_reco_response"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return len(self.input_item_attrs) > 0
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("item_num")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("to_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()


class CommonRecoSupplementIdMappingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "supplement_abtest_mapping_id"


  @strict_types
  def _check_config(self) -> None:
    check_arg("cluster" in self._config, "必须配置 cluster")


class CommonRecoRodisCounterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_rodis_counter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "group_by" in self._config:
      attrs.add(self._config.get("group_by", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for c in self._config["action_list"]:
      ret.add(c["save_count_to"]) 
    return ret

  @strict_types
  def _check_config(self) -> None:
    check_arg("rodis_kess_name" in self._config, "必须配置 rodis_kess_name")
    check_arg("rodis_domain" in self._config, "必须配置 rodis_domain")
    check_arg("action_list" in self._config, "必须配置 action_list")
    if "group_by" in self._config:
      check_arg(isinstance(self._config.get("group_by"), str), "group_by 必须为 str 类型")
    for a in self._config["action_list"]:
      check_arg("save_count_to" in a, "action_list 内每个元素必须配置 save_count_to")
      check_arg("action" in a, "action_list 内每个元素必须配置 action")
      check_arg("channel_list" in a, "action_list 内每个元素必须配置 channel_list")
  

class CommonRecoPyAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    from ...matx.py_enricher_util import PyUdfCompileUtil
    function_set = config.get("function_set")
    py_function = config.get("py_function")
    function_code = config.get("function_code")
    if py_function is None and function_code is None:
      raise ArgumentError(f"必须配置 py_function 或 function_code")
    if py_function and function_code:
      raise ArgumentError(f"py_function 和 function_code 只能配置一项")
    if function_set:
      if getattr(function_set, py_function.__name__, None) == py_function and inspect.isfunction(py_function):
        config["function_set"] = function_set.__name__
        config["py_function"] = py_function.__name__
        compile_object = function_set
        compile_object_name = compile_object.__name__
        config["compile_object_name"] = compile_object_name
        PyUdfCompileUtil.check_and_add_compile_class(compile_object_name, compile_object)
      else:
        raise ArgumentError(f"py_function 配置项需为 function_set 中的方法")
    elif py_function:
      if inspect.isfunction(py_function):
        config["function_set"] = ""
        config["py_function"] = py_function.__name__
        compile_object = py_function
        compile_object_name = compile_object.__name__
        config["compile_object_name"] = compile_object_name
        PyUdfCompileUtil.check_and_add_compile_class(compile_object_name, compile_object)
      else:
        raise ArgumentError(f"py_function 配置项需为方法")

    # dsl 中不显示写出的，隐式添加依赖
    source_import_common_attr = list()
    source_export_common_attr = list()
    source_import_item_attr = list()
    source_export_item_attr = list()

    import_common_function_list = ["GetInt", "GetDouble", "GetString", "GetIntList", "GetDoubleList", "GetStringList", "GetPtr", "HasValue"]
    export_common_function_list = ["SetInt", "SetDouble", "SetString", "SetIntList", "SetDoubleList", "SetStringList", "SetPtr", "AppendIntList", "AppendDoubleList", "AppendStringList"]
    import_item_function_list = ["ItemAttrGetter"]
    export_item_function_list = ["ItemAttrSetter"]

    function_source_code = inspect.getsource(py_function) if py_function else function_code
    tree = ast.parse(function_source_code.strip()).body[0]
    if not isinstance(tree, ast.FunctionDef):
      raise ValueError("py_function 或 code 只能包含一个函数")
    for node in ast.walk(tree):
      if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute) \
            and isinstance(node.func.value, ast.Name) and node.func.value.id == "ctx" \
            and isinstance(node.func.attr, str) \
            and len(node.args) >= 1 and isinstance(node.args[0], ast.Bytes):
        attr_name = node.args[0].s.decode("utf-8")
        if node.func.attr in import_common_function_list:
          if attr_name not in source_import_common_attr:            
            source_import_common_attr.append(attr_name)
        elif node.func.attr in export_common_function_list:
          if attr_name not in source_export_common_attr:
            source_export_common_attr.append(attr_name)
        elif node.func.attr in import_item_function_list:
          if attr_name not in source_import_item_attr:
            source_import_item_attr.append(attr_name)
        elif node.func.attr in export_item_function_list:
          if attr_name not in source_export_item_attr:
            source_export_item_attr.append(attr_name)
    config["import_common_attr"] = self.py_add_attr_names(config.get("import_common_attr", []), source_import_common_attr, "name", "as")
    config["import_item_attr"] = self.py_add_attr_names(config.get("import_item_attr", []), source_import_item_attr, "name", "as")
    config["export_common_attr"] = self.py_add_attr_names(config.get("export_common_attr", []), source_export_common_attr, "as", "name")
    config["export_item_attr"] = self.py_add_attr_names(config.get("export_item_attr", []), source_export_item_attr, "as", "name")
    
    # 字符串代码额外设置 function
    if function_code:
      hash = hashlib.blake2b(function_code.encode(), digest_size=16).hexdigest()
      function_name = tree.name + '_' + hash
      config["py_function"] = function_name
      config["function_set"] = 'AnonymousFunctionSet'
      config["compile_object_name"] = 'AnonymousFunctionSet'
      PyUdfCompileUtil.py_compile_functions[function_name] = {'code': function_code}
    
      # 如果有额外需要添加的 import 语句，适用于 function_code 方式
      imports = config.get("imports")
      if imports:
        if not isinstance(imports, list):
          raise ArgumentError(f"imports 配置项需为 list")
        for imp in imports:
          PyUdfCompileUtil.py_import.add(imp.strip())

    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_py"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(any(self._config.get("import_item_attr", [])) or any(self._config.get("export_item_attr", [])))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_item_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "as")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    return attrs

  def py_add_attr_names(self, config: list, source_attr_names: list, need_add_key: str, need_remove_key: str) -> list:
    def judge(attr_name: str, source_attr_names: list):
      if attr_name not in source_attr_names:
         raise ArgumentError(f"{attr_name} 被显示写在 import/export 中但没有在 py function 中使用")

    ret = config
    for c in config:
      if isinstance(c, str):
        judge(c, source_attr_names)
      elif isinstance(c, dict):
        for d in c:
          if d == need_add_key:
            pass
          elif d == need_remove_key:
            # 删去推导出的 attr
            judge(c[d], source_attr_names)
            source_attr_names.remove(c[d])
          else:
            raise ArgumentError("enrich_attr_by_py 的 import/export 中的 dict 必须为 name as 格式")
      else:
        raise ArgumentError(f"该 list 中存在不支持的配置类型 {type(c)}: {config}")
    # 添加推断出但未 name as 的 attr
    ret.extend(source_attr_names)
    return ret


class CommonRecoRodisNoProxyAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_rodis_value_no_proxy"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["value_attr"]}

class CommonRecoRodisAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_rodis_value"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["value_attr"]}

class CommonRecoStreamingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "streaming_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_common_attrs", []), "as")
    attrs.add(self._config.get("save_streaming_loop_index_to"))
    return attrs
 
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = []
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = []
    return attrs

class CommonRecoStreamingStatusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "streaming_status"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("status", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_streaming_status_to"))
    return attrs
 

class CommonRecoItemAttrOperationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_attr_operation"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("item_attr_a"), f"{self.get_type_alias()} 缺少 item_attr_a 配置")
    ret.add(self._config["item_attr_a"])
    if "item_attr_b" not in self._config and "common_attr_b" not in self._config:
      raise ValueError("item_attr_b 和 common_attr_b 至少需要配置一项")
    if "item_attr_b" in self._config:
      ret.add(self._config["item_attr_b"])
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "common_attr_b" in self._config:
      ret.update(self.extract_dynamic_params(self._config.get("common_attr_b")))
    check_arg(self._config.get("operator"), f"{self.get_type_alias()} 缺少 operator 配置")
    if self._config.get("operator") not in ["+", "-", "*", "/", "&", "|", "^", "pow", "<<", ">>"]:
      raise ValueError('operator 仅支持四则运算（"+", "-", "*", "/"）, 位运算（"&", "|", "^", "<<", ">>"），指数运算（"pow"）')
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_attr"), f"{self.get_type_alias()} 缺少 output_attr 配置")
    ret.add(self._config["output_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class CommonRecoItemAttrMissingRateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_attr_missing_rate"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    checks = self._config.get("check", [])
    check_arg(isinstance(checks, list), f"{self.get_type_alias()} 缺少 check 配置")
    ret = set()
    for check in checks:
      check_arg(check.get("item_attr"), f"{self.get_type_alias()} 缺少 item_attr 配置")
      check_arg(isinstance(check["item_attr"], str), f"{self.get_type_alias()} 的 item_attr 里需要为 string 类型")
      ret.add(check["item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    checks = self._config.get("check", [])
    ret = set()
    for check in checks:
      check_arg(check.get("save_rate_to"), f"{self.get_type_alias()} 缺少 save_rate_to 配置")
      check_arg(isinstance(check["save_rate_to"], str), f"{self.get_type_alias()} 的 save_rate_to 里需要为 string 类型")
      ret.add(check["save_rate_to"])
    return ret

class CommonRecoClothoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_clotho_value"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    value_attr = self._config.get("value_attr",[])
    valid_list = []
    for attr in value_attr:
      if isinstance(attr, dict):
        valid_list.append(attr.get("to"))
      elif isinstance(attr, str):
        valid_list.append(attr)
    return set(valid_list)

class CommonRecoFindValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "find_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("value", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("result", ""))
    attrs.add(self._config.get("output_index", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("input" in self._config, f"{self.get_type_alias()} 缺少 input 配置")
    check_arg("value" in self._config, f"{self.get_type_alias()} 缺少 value 配置")
    check_arg("result" in self._config, f"{self.get_type_alias()} 缺少 result 配置")

class CommonRecoGeoInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_geoinfo"

  @strict_types
  def _check_config(self) -> None:
    check_arg("lat_attr" in self._config, f"{self.get_type_alias()} 缺少 lat_attr 配置")
    check_arg("lon_attr" in self._config, f"{self.get_type_alias()} 缺少 lon_attr 配置")

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    fields = ["lat_attr", "lon_attr"]
    for v in fields:
      if v in self._config:
        attrs.add(self._config[v])
    return attrs

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    fields = ["save_bdcode_to_attr", "save_adcode_to_attr", "save_nation_to_attr", "save_province_to_attr",
              "save_city_to_attr", "save_county_to_attr", "save_bdname_to_attr"]
    for v in fields:
      if v in self._config:
        attrs.add(self._config[v])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return  self._get_output_attrs() if not self._is_common_attr else set()

class CommonRecoRodisUniqueListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rodis_unique_list_enricher"
  
  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kess_name"), "`kess_name` 是必选项")
    check_arg(self._config.get("domain"), "`domain` 是必选项")
    check_arg(self._config.get("payload_id"), "`payload_id` 是必选项")
    check_arg(self._config.get("key_attr"), "`key_attr` 是必选项")
    check_arg(self._config.get("unique_key_attr"), "`unique_key_attr` 是必选项")
    check_arg(self._config.get("sort_key_attr"), "`sort_key_attr` 是必选项")
    check_arg(self._config.get("data_attr"), "`data_attr` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_attr"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("unique_key_attr"))
    attrs.add(self._config.get("sort_key_attr"))
    attrs.add(self._config.get("data_attr"))
    return attrs

class CommonMemoryDataEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "memory_data_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("data_key")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_data_ptr_to_attr"))
    return attrs

class CommonRecoHttpCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_common_attr_by_http"

  @strict_types
  def _check_config(self) -> None:
    check_arg("url" in self._config, "必须配置 url")
    check_arg("path" in self._config, "必须配置 path")
    check_arg(self._config.get("method", "") in ['GET', 'POST'], "method 必需属于 ['GET', 'POST']")
    if "headers" in self._config:
      for config in self._config["headers"]:
        check_arg("name" in config, "headers 中必须配置 name")
        check_arg("value" in config, "headers 中必须配置 value")
    check_arg("output_common_attr" in self._config, "必须配置 output_common_attr")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "headers" in self._config:
      for config in self._config["headers"]:
        self.extract_dynamic_params(config["value"], check_format=False)
    if "input_common_attrs" in self._config:
      for attr in self._config["input_common_attrs"]:
        self.extract_dynamic_params(attr, check_format=False)
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_common_attr"])
    return attrs

class CommonRecoRodisTlAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_rodis_timelist"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["value_attr"]}

class CommonRecoDotProductEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dot_product"

  @strict_types
  def _check_config(self) -> None:
    check_arg("x_attr" in self._config, f"{self.get_type_alias()} 缺少 x_attr 配置")
    check_arg(isinstance(self._config.get("x_attr"), str), "x_attr 需为 str 类型")
    check_arg("y_attr" in self._config, f"{self.get_type_alias()} 缺少 y_attr 配置")
    check_arg(isinstance(self._config.get("y_attr"), str), "y_attr 需为 str 类型")
    check_arg("save_result_to" in self._config, f"{self.get_type_alias()} 缺少 save_result_to 配置")
    check_arg(isinstance(self._config.get("save_result_to"), str), "save_result_to 需为 str 类型")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("x_attr", ""))
    if self._config.get("y_is_common_attr", False):
      attrs.add(self._config.get("y_attr", ""))  
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("y_is_common_attr", False):
      attrs.add(self._config.get("save_result_to", ""))  
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("y_is_common_attr", False):
      attrs.add(self._config.get("y_attr", ""))  
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("y_is_common_attr", False):
      attrs.add(self._config.get("save_result_to", ""))  
    return attrs

class CommonRecoElasticSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_elastic_search"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg("clusters" in self._config, "clusters 必须配置")
    check_arg("index" in self._config, "index 必须配置")
    for config in self._config.get("headers", []):
      check_arg("name" in config, "headers 中必须配置 name")
      check_arg("value" in config, "headers 中必须配置 value")
    check_arg("query" in self._config, "query 必须配置")
    check_arg("save_score_to" in self._config 
              or "save_response_to" in self._config 
              or "item_attrs" in self._config, 
              "save_score_to, save_response_to 和 item_attrs 不能同时为空")
    for config in self._config.get("item_attrs", []):
      check_arg("name" in config, "item_attrs 中必须配置 name")
      check_arg("type" in config, "item_attrs 中必须配置 type")
      check_arg(config["type"] in ["int", "float", "string", "float_list"], "item_attrs 中 type 只支持 int, float, string, float_list")
      check_arg("value" in config, "item_attrs 中必须配置 value")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("clusters", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("index", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("query", "")))
    for config in self._config.get("headers", []):
      attrs.update(self.extract_dynamic_params(config.get("value", "")))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_response_to", ""))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("item_attrs", []), "name"))
    attrs.add(self._config.get("save_score_to", ""))
    return attrs
