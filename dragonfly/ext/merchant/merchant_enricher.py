#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for merchant
author: <EMAIL>
date: 2021-01-14 13:58:00
"""

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class MerchantPhotoGsuGoodCateV2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_good_cate_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_col"))
      ret.add(self._config.get("author_id_list_col"))
      ret.add(self._config.get("label_list_col"))
      ret.add(self._config.get("time_stamp_col"))
      ret.add(self._config.get("category_col"))
      ret.add(self._config.get("play_time_list_col"))
      ret.add(self._config.get("duration_list_col"))
      ret.add(self._config.get("channel_list_col"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cate1_attr"))
      ret.add(self._config.get("target_cate2_attr"))
      ret.add(self._config.get("target_cate3_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_list_attr"))
      ret.add(self._config.get("cate2_list_attr"))
      ret.add(self._config.get("cate3_list_attr"))
      return ret
class MerchantMmScoreDiscreteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_mm_score_discrete_enricher"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["cart_item_id_list"])
    ret.add(self._config["cart_top_item_score_attr"])
    ret.add(self._config["output_feature_prefix_attr"])
    
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output_feature_prefix = self._config["output_feature_prefix_attr"]
    attrs = set([
      output_feature_prefix + "discrete_feature_1",
      output_feature_prefix + "discrete_feature_2",
      output_feature_prefix + "discrete_feature_3",
      output_feature_prefix + "discrete_feature_4",
      output_feature_prefix + "discrete_feature_5"
    ])
    return attrs

class AttrLabelConvEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "attr_label_conv"

  @strict_types
  def _check_config(self) -> None:
    for attr in self._config.get("attrs", []):
      check_arg(isinstance(attr.get("from_item", ""), str), "attr 里的 from_item 值必须为字符串")
      check_arg(isinstance(attr.get("to_item", ""), str), "attr 里的 to_item 值必须为字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return any("from_item" in attr or "to_item" in attr for attr in self._config.get("attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("from_item"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr.get("to_item"))
    return attrs
class AttrMioConvEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "attr_mio_conv"
   
    @strict_types
    def depend_on_items(self) -> bool:
      if self._config.get("shadow_mode", False):
        return False
      return self._config.get("item_num_limit", 10) > 0 and bool(self._config.get("item_attrs", []))
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      if self._config.get("shadow_mode", False):
        return set()
      attrs = set(self._config.get("common_attrs", []))
      if self._config.get("to", "") == "file":
        attrs.update(self.extract_dynamic_params(self._config.get("to_file_name")))
      for key in ["respect_sample_logging", "trace_user_ids", "trace_device_ids"]:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      if self._config.get("shadow_mode", False):
        return set()
      return set(self._config.get("item_attrs", []))
class MerchantInferEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_infer_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    if "shard_num" in self._config:
      check_arg("shard_num" in self._config and self._config["shard_num"] == 1, f"目前电商侧无分布式推理服务，所以merchant_infer_enrich算子的参数[shard_num]仅支持设置为1, 若有分布式推理服务需求，请联系panyuchen、lining17或其他电商架构同学支持")

    if "item_from_tables" in self._config:
      check_arg("use_packed_item_attr" in self._config and self._config["use_packed_item_attr"] == True,
                f"{self.get_type_alias()} 配置了 item_from_tables 的情况下必须同时配置 use_packed_item_attr 为 True")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("hash_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("internal_partition_size")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("sample_list_ptr_attr"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    attrs.add(self._config.get("dynamic_send_common_attrs"))
    attrs.add(self._config.get("dynamic_send_item_attrs"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

  # FIXME(fangjianbing): 这个 bug fix 对直播的耗时增长影响很大，暂时先注释掉
  # @strict_types
  # def depend_on_all_item_attrs(self) -> bool:
  #   return bool(self._config.get("send_item_attrs_in_name_list", ""))

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    return ["kess_service", "return_item_attrs"]

class IncUpdaterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inc_updater"

  @strict_types
  def _check_config(self) -> None:
    trigger_source = self._config.get("trigger_source")
    check_arg(trigger_source and trigger_source in {"streaming_receiver", "version_based_receiver"}, '`trigger_source` 必须选自 {"streaming_receiver", "version_based_receiver"}')

    key = self._config.get("key")
    check_arg(key and key != "", '`key` 必须配置，取值与`uni-predict`相同')

    if trigger_source == "streaming_receiver":
      snapshot_interval_second = self._config.get("snapshot_interval_second")
      assert type(snapshot_interval_second) is int, "streaming_receiver 中 snapshot_interval_second 必须为 int"
      check_arg(snapshot_interval_second >= 3, "snapshot_interval_second 必须大于等于3")
      check_arg(self._config.get("expire_item_time_second") is not None, "增量cache必须填写 expire_item_time_second [item过期时间]")
      
class FullRecallMapItemIdToGatherIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "full_recall_map_item_id_to_gather_index"
  
  @strict_types
  def _check_config(self) -> None:
    pass

  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_list_common_attr_name"))
    return attrs
  
  @property
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("gather_index_tensor_common_attr_name"))
    return attrs

class MerchantColossusClickSliceFeatureV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_click_slice_feature_v2"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["item_id_list_attr", "click_timestamp_list_attr", "category_list_attr", 
                "leaf_category_list_attr", "seller_id_list_attr", "click_colossus_latest_timestamp"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["target_aid", "target_cate1", "target_cate2", "target_cate3", "target_leaf"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["merchant_colossus_click_authorid_last1",
            "merchant_colossus_click_authorid_last5",
            "merchant_colossus_click_authorid_last10",
            "merchant_colossus_click_authorid_last20",
            "merchant_colossus_click_authorid_last100",
            "merchant_colossus_click_authorid_last200",
            "merchant_colossus_click_authorid_1h",
            "merchant_colossus_click_authorid_3h",
            "merchant_colossus_click_authorid_6h",
            "merchant_colossus_click_authorid_12h",
            "merchant_colossus_click_authorid_1d",
            "merchant_colossus_click_authorid_2d",
            "merchant_colossus_click_authorid_3d",
            "merchant_colossus_click_authorid_7d",
            "merchant_colossus_click_authorid_14d",
            "merchant_colossus_click_authorid_28d",
            "merchant_colossus_click_authorid_30d",
            "merchant_colossus_click_authorid_60d",
            "merchant_colossus_click_authorid_90d",
            "merchant_colossus_click_authorid_10min",
            "merchant_colossus_click_authorid_30min",
            "merchant_colossus_click_cate1_last1",
            "merchant_colossus_click_cate1_last5",
            "merchant_colossus_click_cate1_last10",
            "merchant_colossus_click_cate1_last20",
            "merchant_colossus_click_cate1_last100",
            "merchant_colossus_click_cate1_last200",
            "merchant_colossus_click_cate1_1h",
            "merchant_colossus_click_cate1_3h",
            "merchant_colossus_click_cate1_6h",
            "merchant_colossus_click_cate1_12h",
            "merchant_colossus_click_cate1_1d",
            "merchant_colossus_click_cate1_2d",
            "merchant_colossus_click_cate1_3d",
            "merchant_colossus_click_cate1_7d",
            "merchant_colossus_click_cate1_14d",
            "merchant_colossus_click_cate1_28d",
            "merchant_colossus_click_cate1_30d",
            "merchant_colossus_click_cate1_60d",
            "merchant_colossus_click_cate1_90d",
            "merchant_colossus_click_cate1_10min",
            "merchant_colossus_click_cate1_30min",
            "merchant_colossus_click_cate2_last1",
            "merchant_colossus_click_cate2_last5",
            "merchant_colossus_click_cate2_last10",
            "merchant_colossus_click_cate2_last20",
            "merchant_colossus_click_cate2_last100",
            "merchant_colossus_click_cate2_last200",
            "merchant_colossus_click_cate2_1h",
            "merchant_colossus_click_cate2_3h",
            "merchant_colossus_click_cate2_6h",
            "merchant_colossus_click_cate2_12h",
            "merchant_colossus_click_cate2_1d",
            "merchant_colossus_click_cate2_2d",
            "merchant_colossus_click_cate2_3d",
            "merchant_colossus_click_cate2_7d",
            "merchant_colossus_click_cate2_14d",
            "merchant_colossus_click_cate2_28d",
            "merchant_colossus_click_cate2_30d",
            "merchant_colossus_click_cate2_60d",
            "merchant_colossus_click_cate2_90d",
            "merchant_colossus_click_cate2_10min",
            "merchant_colossus_click_cate2_30min",
            "merchant_colossus_click_cate3_last1",
            "merchant_colossus_click_cate3_last5",
            "merchant_colossus_click_cate3_last10",
            "merchant_colossus_click_cate3_last20",
            "merchant_colossus_click_cate3_last100",
            "merchant_colossus_click_cate3_last200",
            "merchant_colossus_click_cate3_1h",
            "merchant_colossus_click_cate3_3h",
            "merchant_colossus_click_cate3_6h",
            "merchant_colossus_click_cate3_12h",
            "merchant_colossus_click_cate3_1d",
            "merchant_colossus_click_cate3_2d",
            "merchant_colossus_click_cate3_3d",
            "merchant_colossus_click_cate3_7d",
            "merchant_colossus_click_cate3_14d",
            "merchant_colossus_click_cate3_28d",
            "merchant_colossus_click_cate3_30d",
            "merchant_colossus_click_cate3_60d",
            "merchant_colossus_click_cate3_90d",
            "merchant_colossus_click_cate3_10min",
            "merchant_colossus_click_cate3_30min",
            "merchant_colossus_click_leaf_last1",
            "merchant_colossus_click_leaf_last5",
            "merchant_colossus_click_leaf_last10",
            "merchant_colossus_click_leaf_last20",
            "merchant_colossus_click_leaf_last100",
            "merchant_colossus_click_leaf_last200",
            "merchant_colossus_click_leaf_1h",
            "merchant_colossus_click_leaf_3h",
            "merchant_colossus_click_leaf_6h",
            "merchant_colossus_click_leaf_12h",
            "merchant_colossus_click_leaf_1d",
            "merchant_colossus_click_leaf_2d",
            "merchant_colossus_click_leaf_3d",
            "merchant_colossus_click_leaf_7d",
            "merchant_colossus_click_leaf_14d",
            "merchant_colossus_click_leaf_28d",
            "merchant_colossus_click_leaf_30d",
            "merchant_colossus_click_leaf_60d",
            "merchant_colossus_click_leaf_90d",
            "merchant_colossus_click_leaf_10min",
            "merchant_colossus_click_leaf_30min", ])
    return attrs

class MerchantGoodsGsuDpTopnSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_goods_gsu_dp_topn_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in []:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_key_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in [
      "output_item_colossus_distance_attr", "output_item_colossus_id_attr"
    ]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantFeatureDiffOnlineOfflineEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_feature_diff_online_offline"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    for input_attr in ["online_batched_sample", "offline_batched_sample", "get_check_slot_from_kconf"]:
      if self._config.get(input_attr):
        ret.add(self._config.get(input_attr))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("fr_kess")
    ret.add("is_send")
    return ret

class MerchantRecoModelSlotConvertEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_reco_model_slot_convert_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_slots_from", "common_signs_from"]:
      values = self._config.get(key, [])
      if values:
        ret.update(values)
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_slots_from", "item_signs_from"]:
      values = self._config.get(key, [])
      if values:
        ret.update(values)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("common_slots_output")
    ret.add("common_signs_output")

    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("item_slots_output")
    ret.add("item_signs_output")
    
    return ret

class MerchantConvertLiveToItemEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    if "reason" not in self._config:
      self._config["reason_"] = 1
    if "item_type" not in self._config:
      self._config["item_type_"] = 99

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_convert_live_to_item"
  
class MerchantExtractAttrFromCommonRecoResponseEnricher(LeafEnricher):  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["response_attr"])
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_extract_attr_from_common_reco_response"

class MerchantRecoUserMetaInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_user_meta_info"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("save_result_size_to_attr"))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    fields = ["save_user_id_to_attr", "save_device_id_to_attr", "save_request_id_to_attr",
              "save_request_type_to_attr", "save_browse_set_size_to_attr", "save_result_size_to_attr",
              "save_request_time_to_attr", "save_request_num_to_attr", "save_current_time_ms_to_attr",
              "save_host_name_to_attr", "save_host_ip_to_attr", "save_elapsed_time_to_attr",
              "save_shard_no_to_attr", "save_shard_num_to_attr", "save_flow_cpu_cost_to_attr",
              "save_need_traceback_to_attr", "save_service_name_to_attr"]
    return set([self._config[v] for v in fields if v in self._config])

class MerchantItemXtrReadDistributedCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "read_xtr_score_attr_by_distributed_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "item_score_attrs" in self._config:
      attrs = { self._config.get("output_attr_prefix", "") + v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("item_score_attrs", []) }
    if "cache_hit_flag" in self._config:
      attrs.add(self._config.get("cache_hit_flag"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("cache_key_attr", "")
    return { attr } if attr else set()

  def is_valid_type_str(self, type_name : str) -> bool:
    return type_name in ['int', 'float', 'string', 'int_list', 'float_list', 'string_list']
  
  @property
  @strict_types
  def is_more_config(self) -> bool:
    """ 是否支持除了 name 和 type 外的更多配置，比如 class_name 或 其它 """
    for v in self._config.get("item_score_attrs", []):
      if isinstance(v, dict):
        if not sorted(["name", "type"]) == sorted(list(v.keys())):
          return True
    return False

  @property
  @strict_types
  def flat_index_item_attrs_with_type(self) -> dict:
    attrs_with_type = {}
    for v in self._config.get("cache_key_attr", []):
      if isinstance(v, dict):
        attrs_with_type[v["name"]] = v.get("type", "auto")
      else:
        attrs_with_type[v] = "auto"
    return attrs_with_type

  @property
  @strict_types
  def flat_index_item_attrs_with_more_config(self) -> list:
    attrs_with_more_config = []
    for v in self._config.get("cache_key_attr", []):
      t = {}
      if isinstance(v, dict):
        if "class_name" in v.keys():
          v["class_name"] = v["class_name"].replace("::", ".")
        t.update(v)
      else:
        t.update(dict(name=v))
      t.setdefault("type", "auto")
      attrs_with_more_config.append(t)
    return attrs_with_more_config

  @strict_types
  def _check_config(self) -> None:
    if "item_score_attrs" in self._config:
      for v in self._config.get("item_score_attrs", []):
        if isinstance(v, dict) and "type" in v.keys() and not self.is_valid_type_str(v["type"]):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v['type']}")

class MerchantParseCandidateFromCacheContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_parse_candidate_from_cache_context_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("common_attrs", []))
    attrs.add(self._config["parse_from_attr"])
    attrs.add(self._config["parse_prefix_attr"])
    attrs.add(self._config["parse_aid_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config and not isinstance(self._config["item_attrs"], str):
      attrs.update(self._config.get("item_attrs", []))
    return attrs

class MerchantDumpAttrToCacheContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dump_attr_to_cache_context_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("common_attrs", []))
    attrs.add(self._config["parse_from_attr"])
    attrs.add(self._config["cache_expire_time_attr"])
    attrs.add(self._config["dump_prefix_attr"])
    attrs.add(self._config["parse_aid_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config and not isinstance(self._config["item_attrs"], str):
      attrs.update(self._config.get("item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("dump_to_attr", "")
    attrs.add(attr)
    return attrs

class MerchantParseAttrFromCacheContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_parse_attr_from_cache_context_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["parse_from_attr"])
    ret.add(self._config["cache_valid_time_attr"])
    ret.add(self._config["parse_prefix_attr"])
    ret.add(self._config["parse_aid_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_common_attrs", []))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_item_attrs", []))
    return ret

class MerchantUserClickItemActionCartGsuOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_user_click_item_action_cart_gsu_opt_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["user_click_content_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["cart_cate1_id_attr"])
    ret.add(self._config["cart_cate2_id_attr"])
    ret.add(self._config["cart_cate3_id_attr"])
    ret.add(self._config["live_aid_attr"])
    ret.add(self._config["cart_spu_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("multi_cate_gsu_aid_list"))
    attrs.add(self._config.get("multi_cate_gsu_spu_list"))
    attrs.add(self._config.get("multi_cate_gsu_cate1_list"))
    attrs.add(self._config.get("multi_cate_gsu_cate2_list"))
    attrs.add(self._config.get("multi_cate_gsu_cate3_list"))
    attrs.add(self._config.get("multi_cate_gsu_decay_list"))
    attrs.add(self._config.get("multi_cate_gsu_label_list"))
    attrs.add(self._config.get("multi_cate_gsu_click_from_list"))
    attrs.add(self._config.get("multi_cate_gsu_click_flow_type_list"))
    attrs.add(self._config.get("spu_match_cnt"))
    attrs.add(self._config.get("cate3_match_cnt"))
    attrs.add(self._config.get("cate2_match_cnt"))
    attrs.add(self._config.get("cate1_match_cnt"))

    return attrs

class MerchantUgLiveItemGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ug_live_item_gsu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_seller_id_attr"])
    
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([
      "live_gsu_author_id_match_author_id_list",
      "live_gsu_author_id_match_play_time_list",
      "live_gsu_author_id_match_auto_play_time_list",
      "live_gsu_author_id_match_hetu_tag_channel_list",
      "live_gsu_author_id_match_label_list",
      "live_gsu_author_id_match_reward_list",
      "live_gsu_author_id_match_item_id_list",
      "live_gsu_author_id_match_audience_count_list"
      "live_gsu_author_id_match_order_price_list",
      "live_gsu_author_id_match_time_decay_list",
      "live_gsu_author_id_match_time_decay_second_list",
      "live_gsu_seller_mcnt"
      "live_gsu_multi_mcnt",
      "live_gsu_output_signs",
      "live_gsu_output_slots",
      "live_gsu_output_values",
      "live_mcnt_output_signs",
      "live_mcnt_output_slots",
      "live_mcnt_output_values",
    ])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "live_select_colossus_item_aid",
      "live_select_colossus_play_time",
      "live_select_colossus_auto_play_time",
      "live_select_colossus_label",
      "live_select_colossus_reward",
      "live_select_colossus_item_id",
      "live_select_colossus_audience_count",
      "live_select_colossus_order_price",
      "live_select_colossus_time_decay",
      "live_select_colossus_time_decay_second",
      "live_recent_output_signs",
      "live_recent_output_slots",
      "live_recent_output_values"
    ])
    return attrs

class MerchantUgClickItemGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ug_click_item_gsu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["click_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_leafcate_id_attr"])
    ret.add(self._config["item_cate1_id_attr"])
    ret.add(self._config["item_cate2_id_attr"])
    ret.add(self._config["item_cate3_id_attr"])
    ret.add(self._config["item_realtime_price_attr"])
    
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([
      "click_gsu_cate3_match_iid_list",
      "click_gsu_cate3_match_aid_list",
      "click_gsu_cate3_match_decay_list",
      "click_gsu_cate3_match_stay_time_list",
      "click_gsu_cate2_match_iid_list",
      "click_gsu_cate2_match_aid_list",
      "click_gsu_cate2_match_decay_list",
      "click_gsu_cate2_match_stay_time_list"
      "click_gsu_cate1_match_iid_list",
      "click_gsu_cate1_match_aid_list",
      "click_gsu_cate1_match_decay_list",
      "click_gsu_cate1_match_stay_time_list"
      "click_gsu_cate3_mcnt",
      "click_gsu_cate2_mcnt",
      "click_gsu_cate1_mcnt",
      "click_gsu_real_seller_mcnt",
      "click_gsu_seller_mcnt",
      "click_gsu_multi_mcnt",
      "click_gsu_output_signs",
      "click_gsu_output_slots",
      "click_gsu_output_values",
      "click_mcnt_output_signs",
      "click_mcnt_output_slots",
      "click_mcnt_output_values",
      "click_mcnt_output_signs",
      "click_mcnt_output_slots",
      "click_mcnt_output_values",
    ])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "click_select_colossus_item_aid",
      "click_select_colossus_item_iid",
      "click_select_colossus_item_decay",
      "click_select_colossus_item_cate1",
      "click_select_colossus_item_cate2",
      "click_select_colossus_item_cate3",
      "click_recent_output_signs",
      "click_recent_output_slots",
      "click_recent_output_values"
    ])
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MerchantUgClickItemGsuOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ug_click_item_gsu_opt_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["click_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_leafcate_id_attr"])
    ret.add(self._config["item_cate1_id_attr"])
    ret.add(self._config["item_cate2_id_attr"])
    ret.add(self._config["item_cate3_id_attr"])
    ret.add(self._config["item_realtime_price_attr"])
    
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([
      "click_gsu_cate3_match_iid_list",
      "click_gsu_cate3_match_aid_list",
      "click_gsu_cate3_match_decay_list",
      "click_gsu_cate3_match_stay_time_list",
      "click_gsu_cate2_match_iid_list",
      "click_gsu_cate2_match_aid_list",
      "click_gsu_cate2_match_decay_list",
      "click_gsu_cate2_match_stay_time_list"
      "click_gsu_cate1_match_iid_list",
      "click_gsu_cate1_match_aid_list",
      "click_gsu_cate1_match_decay_list",
      "click_gsu_cate1_match_stay_time_list"
      "click_gsu_cate3_mcnt",
      "click_gsu_cate2_mcnt",
      "click_gsu_cate1_mcnt",
      "click_gsu_real_seller_mcnt",
      "click_gsu_seller_mcnt",
      "click_gsu_multi_mcnt",
      "click_gsu_output_signs",
      "click_gsu_output_slots",
      "click_gsu_output_values",
      "click_mcnt_output_signs",
      "click_mcnt_output_slots",
      "click_mcnt_output_values"
    ])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "click_select_colossus_item_aid",
      "click_select_colossus_item_iid",
      "click_select_colossus_item_decay",
      "click_select_colossus_item_cate1",
      "click_select_colossus_item_cate2",
      "click_select_colossus_item_cate3",
      "click_recent_output_signs",
      "click_recent_output_slots",
      "click_recent_output_values",
      "click_colossus_item_flow_type",
      "click_colossus_item_category",
      "click_colossus_item_cate1",
      "click_colossus_item_cate2",
      "click_colossus_item_cate3",
      "click_colossus_item_detail_page_view_time",
      "click_colossus_item_id",
      "click_colossus_item_leaf_category",
      "click_colossus_item_real_price",
      "click_colossus_item_seller_id",
      "click_colossus_item_real_seller_id",
      "click_colossus_item_time_decay",
      "click_colossus_item_time_decay_second"
    ])
    return attrs

class MerchantUgOrderItemGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ug_order_item_gsu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["order_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_leafcate_id_attr"])
    ret.add(self._config["item_cate1_id_attr"])
    ret.add(self._config["item_cate2_id_attr"])
    ret.add(self._config["item_cate3_id_attr"])
    ret.add(self._config["item_realtime_price_attr"])
    
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([
      "order_gsu_cate3_match_iid_list",
      "order_gsu_cate3_match_aid_list",
      "order_gsu_cate3_match_decay_list",
      "order_gsu_cate2_match_iid_list",
      "order_gsu_cate2_match_aid_list",
      "order_gsu_cate2_match_decay_list",
      "order_gsu_cate1_match_iid_list",
      "order_gsu_cate1_match_aid_list",
      "order_gsu_cate1_match_decay_list",
      "order_gsu_leafcate_match_iid_list",
      "order_gsu_leafcate_match_aid_list",
      "order_gsu_leafcate_match_decay_list",
      "order_gsu_price_match_iid_list",
      "order_gsu_price_match_aid_list",
      "order_gsu_price_match_decay_list",
      "order_gsu_cate3_mcnt",
      "order_gsu_cate2_mcnt",
      "order_gsu_cate1_mcnt",
      "order_gsu_real_seller_mcnt",
      "order_gsu_seller_mcnt",
      "order_gsu_multi_mcnt",
      "order_gsu_output_signs",
      "order_gsu_output_slots",
      "order_gsu_output_values",
      "order_mcnt_output_signs",
      "order_mcnt_output_slots",
      "order_mcnt_output_values"
    ])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "order_select_colossus_item_aid",
      "order_select_colossus_item_iid",
      "order_select_colossus_item_decay",
      "order_select_colossus_item_cate1",
      "order_select_colossus_item_cate2",
      "order_select_colossus_item_cate3",
      "order_select_colossus_item_pay_price"
      ])
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MerchantUgOrderItemGsuOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ug_order_item_gsu_opt_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["order_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_leafcate_id_attr"])
    ret.add(self._config["item_cate1_id_attr"])
    ret.add(self._config["item_cate2_id_attr"])
    ret.add(self._config["item_cate3_id_attr"])
    ret.add(self._config["item_realtime_price_attr"])
    
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([
      "order_gsu_cate3_match_iid_list",
      "order_gsu_cate3_match_aid_list",
      "order_gsu_cate3_match_decay_list",
      "order_gsu_cate2_match_iid_list",
      "order_gsu_cate2_match_aid_list",
      "order_gsu_cate2_match_decay_list",
      "order_gsu_cate1_match_iid_list",
      "order_gsu_cate1_match_aid_list",
      "order_gsu_cate1_match_decay_list",
      "order_gsu_leafcate_match_iid_list",
      "order_gsu_leafcate_match_aid_list",
      "order_gsu_leafcate_match_decay_list",
      "order_gsu_price_match_iid_list",
      "order_gsu_price_match_aid_list",
      "order_gsu_price_match_decay_list"
      ])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "order_select_colossus_item_aid",
      "order_select_colossus_item_iid",
      "order_select_colossus_item_decay",
      "order_select_colossus_item_cate1",
      "order_select_colossus_item_cate2",
      "order_select_colossus_item_cate3",
      "order_select_colossus_item_pay_price",
      "order_colossus_item_flow_type",
      "order_colossus_item_category",
      "order_colossus_item_cate3",
      "order_colossus_item_cate2",
      "order_colossus_item_cate1",
      "order_colossus_item_pay_order_time",
      "order_colossus_item_id",
      "order_colossus_item_leaf_category",
      "order_colossus_item_pay_order_amt",
      "order_colossus_item_real_seller_id",
      "order_colossus_item_time_decay"
      ])
    return attrs

class MerchantGetItemAttrFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_merchant_item_attr_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("prefix_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("suffix_name")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("save_item_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],
                "cluster_name 需为非空字符串")

class ShopCustomerChangeItemToCommonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "change_item_to_common"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attr_list = ["intent", "query", "question"]
    for v in attr_list:
      attr = self._config.get(v, "")
      if attr:
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("attr_name", ""))
    return attrs

class MerchantElasticSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enricher_by_es_elastic_search"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("intent_id", ""))
    attrs.add(self._config.get("answer_id", ""))
    attrs.add(self._config.get("question_id", ""))
    attrs.add(self._config.get("knowledge_id", ""))
    return attrs

class MerchantUserSampleAttrEnricher(LeafEnricher):

  @strict_types
  def __init__(self, config: dict):
    if "save_attr_names_to_attr" not in config:
      config["save_attr_names_to_attr"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_merchant_user_sample_attr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("include_attrs", []))
    attr = self._config.get("save_attr_names_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs


class MerchantFollowCrossFeatureEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_merchant_follow_cross_attr"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = {
      "pIsHateLiveAuthorItem",
      "uHateAuthor",
      "uIsItemCommented",
      "uIsItemClicked",
      "uIsItemLiked",
      "uAuthorCrossTodayEmpCtrFollowLive",
      "uAuthorCrossTodayEmpCmtrFollowLive",
      "uAuthorCrossTodayAvgTimeFollowLive",
      "uFollowAuthorLive7dWatchTimePercent",
      "uFollowAuthorLive7dWatchTime",
      "pUserAuthorPhotoEmpTPC",
      "uFollowAuthorPhotoAggrWatchTime",
      "pUserAuthorPhotoEmpCtr",
      "uFollowAuthorPhotoAggrWatchTimePercent",
      "uAuthorCrossRecentAvgTimeFollowLive",
      "uAuthorCrossRecentEmpCmtrFollowLive",
      "uAuthorCrossRecentEmpCtrFollowLive",
      "pUserAuthorLiveEmpLtr",
      "pUserAuthorLiveEmpTPC",
      "pUserAuthorLiveEmpCtr",
      "uAuthorEmpCmtrFollowLive",
      "uAuthorEmpLtrFollowLive",
      "uAuthorEmpCtrFollowLive",
      "uAuthorAvgTimeFollowLive",
      "uAuthorAvgTimeProfile",
      "uAuthorEmpCtrProfile",
      "uAuthorIsFriend",
      "uFollowAuthorTimeDay",
      "pIsUnfollowAuthorItem"
    }
    return attrs

class MerchantRecoDcafBeforeTideEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dcaf_before_tide"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_value", "save_name", "group_name"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config["save_name"] }

class MerchantRecoDcafUserValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dcaf_user_value"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["user_value", "save_name", "recommend_name"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["save_name", "recommend_name"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class MerchantRecoDcafAfterTideEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dcaf_after_tide"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_common_attr"))
    for key in ["max_value", "min_value", "default_value", "pid_kp", "pid_ki", "pid_kd", "step", "window_size", "target", "from_common_attr"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

class GsuWithIndexGoodClickEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_good_click"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "cate1_attr", "cate2_attr",
                "cate3_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantLiveGsuCommonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_gsu_common"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_limit_num_attr", "colossus_resp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr","output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantLiveGsuIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_gsu_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit_num_attr", "colossus_limit_num_attr", "colossus_resp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["target_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_index_item_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_index_common_attr", "output_sign_attr","output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantLiveGsuWithClusterIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_gsu_with_cluster_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit_num_attr", "colossus_limit_num_attr", "colossus_resp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["target_cluster_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_index_item_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_index_common_attr", "output_sign_attr","output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantExtractLiveCartEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_extract_live_cart"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sCartItemList_attr"])
    ret.add(self._config["sCartItemCate1IdList_attr"])
    ret.add(self._config["sCartItemCate2IdList_attr"])
    ret.add(self._config["sCartItemCate3IdList_attr"])
    ret.add(self._config["sCartItemCategoryLeafIdList_attr"])
    ret.add(self._config["sCartItemGoodPayOrder7DaysList_attr"])
    ret.add(self._config["sCartItemGoodPayGmv7DaysList_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([])
    l = ["sCartItemCate1IdList_top", "sCartItemCate2IdList_top", "sCartItemCate3IdList_top",
         "sCartItemCategoryLeafIdList_top", "sCartItemGoodPayOrder7DaysList_top", "sCartItemGoodPayGmv7DaysList_top"]
    for i in l:
      attrs.add(i)
    return attrs

class MerchantExtractLiveRealfeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_extract_live_realfea"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["LiveRealshowCountSlide1m_attr"])
    ret.add(self._config["LiveRealshowCountSlide5m_attr"])
    ret.add(self._config["LiveRealshowCountSlide10m_attr"])
    ret.add(self._config["LiveClickCountSlide1m_attr"])
    ret.add(self._config["LiveClickCountSlide5m_attr"])
    ret.add(self._config["LiveClickCountSlide10m_attr"])
    ret.add(self._config["LiveClickCountAll1m_attr"])
    ret.add(self._config["LiveClickCountAll5m_attr"])
    ret.add(self._config["LiveClickCountAll10m_attr"])
    ret.add(self._config["PayCount1m_attr"])
    ret.add(self._config["PayCount5m_attr"])
    ret.add(self._config["PayCount10m_attr"])
    ret.add(self._config["PayAmount1m_attr"])
    ret.add(self._config["PayAmount5m_attr"])
    ret.add(self._config["PayAmount10m_attr"])
    return ret

class MerchantGsuSession(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_session"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("limit_num_attr"))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("session_time_range_attr"))
    ret.add(self._config.get("n_minute_ago"))
    ret.add(self._config.get("filter_show_attr"))
    ret.add(self._config.get("colossus_live_item_timestamp_attr"))
    ret.add(self._config.get("colossus_live_item_label_attr"))
    ret.add(self._config.get("colossus_live_item_play_time_attr"))
    ret.add(self._config.get("colossus_live_item_live_id_attr"))
    ret.add(self._config.get("colossus_live_item_author_id_attr"))
    ret.add(self._config.get("colossus_live_item_hetu_tag_channel_attr"))
    ret.add(self._config.get("colossus_live_item_cluster_id_attr"))
    ret.add(self._config.get("colossus_live_item_order_price_attr"))
    ret.add(self._config.get("colossus_live_item_auto_play_time_attr"))
    ret.add(self._config.get("colossus_live_item_item_id_attr"))

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(str(slot))
    else:
      ret.add(self._config["output_sign_attr"])
      ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsuMultiSessionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_multi_session"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("limit_num_attr"))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("session_time_range_list_attr"))
    ret.add(self._config.get("colossus_live_item_timestamp_attr"))
    ret.add(self._config.get("colossus_live_item_label_attr"))
    ret.add(self._config.get("colossus_live_item_play_time_attr"))
    ret.add(self._config.get("colossus_live_item_live_id_attr"))
    ret.add(self._config.get("colossus_live_item_author_id_attr"))
    ret.add(self._config.get("colossus_live_item_hetu_tag_channel_attr"))
    ret.add(self._config.get("colossus_live_item_cluster_id_attr"))
    ret.add(self._config.get("colossus_live_item_item_id_attr"))
    
    return ret

class MerchantGsuAllMultiSessionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_all_multi_session"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("limit_num_attr"))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("session_time_range_list_attr"))
    ret.add(self._config.get("colossus_live_item_timestamp_attr"))
    ret.add(self._config.get("colossus_live_item_play_time_attr"))
    ret.add(self._config.get("colossus_live_item_live_id_attr"))
    ret.add(self._config.get("colossus_live_item_author_id_attr"))
    ret.add(self._config.get("colossus_live_item_hetu_tag_channel_attr"))
    ret.add(self._config.get("colossus_live_item_cluster_id_attr"))
    ret.add(self._config.get("colossus_live_item_item_id_attr"))
    return ret

class MerchantCommonColossusParseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_common_colossus_parse_enricher"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(map(lambda x: x.get("name") or x.get("path"), self._config.get("attrs", [])))
    return attrs

class MerchantCommonColossusBatchParseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_common_colossus_batch_parse_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(map(lambda x: x.get("name") or x.get("path"), self._config.get("attrs", [])))
    return attrs

class MerchantColossusClickSliceFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_click_slice_feature"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("cart_truncat_num"))
    attrs.add(self._config.get("filter_time"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_aid"))
    attrs.add(self._config.get("target_cate1"))
    attrs.add(self._config.get("target_cate2"))
    attrs.add(self._config.get("target_cate3"))
    attrs.add(self._config.get("target_leaf"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["merchant_colossus_click_authorid_last1",
            "merchant_colossus_click_authorid_last5",
            "merchant_colossus_click_authorid_last10",
            "merchant_colossus_click_authorid_last20",
            "merchant_colossus_click_authorid_last100",
            "merchant_colossus_click_authorid_last200",
            "merchant_colossus_click_authorid_1h",
            "merchant_colossus_click_authorid_3h",
            "merchant_colossus_click_authorid_6h",
            "merchant_colossus_click_authorid_12h",
            "merchant_colossus_click_authorid_1d",
            "merchant_colossus_click_authorid_2d",
            "merchant_colossus_click_authorid_3d",
            "merchant_colossus_click_authorid_7d",
            "merchant_colossus_click_authorid_14d",
            "merchant_colossus_click_authorid_28d",
            "merchant_colossus_click_authorid_30d",
            "merchant_colossus_click_authorid_60d",
            "merchant_colossus_click_authorid_90d",
            "merchant_colossus_click_authorid_10min",
            "merchant_colossus_click_authorid_30min",
            "merchant_colossus_click_cate1_last1",
            "merchant_colossus_click_cate1_last5",
            "merchant_colossus_click_cate1_last10",
            "merchant_colossus_click_cate1_last20",
            "merchant_colossus_click_cate1_last100",
            "merchant_colossus_click_cate1_last200",
            "merchant_colossus_click_cate1_1h",
            "merchant_colossus_click_cate1_3h",
            "merchant_colossus_click_cate1_6h",
            "merchant_colossus_click_cate1_12h",
            "merchant_colossus_click_cate1_1d",
            "merchant_colossus_click_cate1_2d",
            "merchant_colossus_click_cate1_3d",
            "merchant_colossus_click_cate1_7d",
            "merchant_colossus_click_cate1_14d",
            "merchant_colossus_click_cate1_28d",
            "merchant_colossus_click_cate1_30d",
            "merchant_colossus_click_cate1_60d",
            "merchant_colossus_click_cate1_90d",
            "merchant_colossus_click_cate1_10min",
            "merchant_colossus_click_cate1_30min",
            "merchant_colossus_click_cate2_last1",
            "merchant_colossus_click_cate2_last5",
            "merchant_colossus_click_cate2_last10",
            "merchant_colossus_click_cate2_last20",
            "merchant_colossus_click_cate2_last100",
            "merchant_colossus_click_cate2_last200",
            "merchant_colossus_click_cate2_1h",
            "merchant_colossus_click_cate2_3h",
            "merchant_colossus_click_cate2_6h",
            "merchant_colossus_click_cate2_12h",
            "merchant_colossus_click_cate2_1d",
            "merchant_colossus_click_cate2_2d",
            "merchant_colossus_click_cate2_3d",
            "merchant_colossus_click_cate2_7d",
            "merchant_colossus_click_cate2_14d",
            "merchant_colossus_click_cate2_28d",
            "merchant_colossus_click_cate2_30d",
            "merchant_colossus_click_cate2_60d",
            "merchant_colossus_click_cate2_90d",
            "merchant_colossus_click_cate2_10min",
            "merchant_colossus_click_cate2_30min",
            "merchant_colossus_click_cate3_last1",
            "merchant_colossus_click_cate3_last5",
            "merchant_colossus_click_cate3_last10",
            "merchant_colossus_click_cate3_last20",
            "merchant_colossus_click_cate3_last100",
            "merchant_colossus_click_cate3_last200",
            "merchant_colossus_click_cate3_1h",
            "merchant_colossus_click_cate3_3h",
            "merchant_colossus_click_cate3_6h",
            "merchant_colossus_click_cate3_12h",
            "merchant_colossus_click_cate3_1d",
            "merchant_colossus_click_cate3_2d",
            "merchant_colossus_click_cate3_3d",
            "merchant_colossus_click_cate3_7d",
            "merchant_colossus_click_cate3_14d",
            "merchant_colossus_click_cate3_28d",
            "merchant_colossus_click_cate3_30d",
            "merchant_colossus_click_cate3_60d",
            "merchant_colossus_click_cate3_90d",
            "merchant_colossus_click_cate3_10min",
            "merchant_colossus_click_cate3_30min",
            "merchant_colossus_click_leaf_last1",
            "merchant_colossus_click_leaf_last5",
            "merchant_colossus_click_leaf_last10",
            "merchant_colossus_click_leaf_last20",
            "merchant_colossus_click_leaf_last100",
            "merchant_colossus_click_leaf_last200",
            "merchant_colossus_click_leaf_1h",
            "merchant_colossus_click_leaf_3h",
            "merchant_colossus_click_leaf_6h",
            "merchant_colossus_click_leaf_12h",
            "merchant_colossus_click_leaf_1d",
            "merchant_colossus_click_leaf_2d",
            "merchant_colossus_click_leaf_3d",
            "merchant_colossus_click_leaf_7d",
            "merchant_colossus_click_leaf_14d",
            "merchant_colossus_click_leaf_28d",
            "merchant_colossus_click_leaf_30d",
            "merchant_colossus_click_leaf_60d",
            "merchant_colossus_click_leaf_90d",
            "merchant_colossus_click_leaf_10min",
            "merchant_colossus_click_leaf_30min", ])
    return attrs

class MerchantColossusOrderSliceFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_order_slice_feature"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("cart_truncat_num"))
    attrs.add(self._config.get("filter_time"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_aid"))
    attrs.add(self._config.get("target_cate1"))
    attrs.add(self._config.get("target_cate2"))
    attrs.add(self._config.get("target_cate3"))
    attrs.add(self._config.get("target_leaf"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["merchant_colossus_order_authorid_last1",
            "merchant_colossus_order_authorid_last5",
            "merchant_colossus_order_authorid_last10",
            "merchant_colossus_order_authorid_last20",
            "merchant_colossus_order_authorid_last100",
            "merchant_colossus_order_authorid_last200",
            "merchant_colossus_order_authorid_1h",
            "merchant_colossus_order_authorid_3h",
            "merchant_colossus_order_authorid_6h",
            "merchant_colossus_order_authorid_12h",
            "merchant_colossus_order_authorid_1d",
            "merchant_colossus_order_authorid_2d",
            "merchant_colossus_order_authorid_3d",
            "merchant_colossus_order_authorid_7d",
            "merchant_colossus_order_authorid_14d",
            "merchant_colossus_order_authorid_28d",
            "merchant_colossus_order_authorid_30d",
            "merchant_colossus_order_authorid_60d",
            "merchant_colossus_order_authorid_90d",
            "merchant_colossus_order_authorid_10min",
            "merchant_colossus_order_authorid_30min",
            "merchant_colossus_order_cate1_last1",
            "merchant_colossus_order_cate1_last5",
            "merchant_colossus_order_cate1_last10",
            "merchant_colossus_order_cate1_last20",
            "merchant_colossus_order_cate1_last100",
            "merchant_colossus_order_cate1_last200",
            "merchant_colossus_order_cate1_1h",
            "merchant_colossus_order_cate1_3h",
            "merchant_colossus_order_cate1_6h",
            "merchant_colossus_order_cate1_12h",
            "merchant_colossus_order_cate1_1d",
            "merchant_colossus_order_cate1_2d",
            "merchant_colossus_order_cate1_3d",
            "merchant_colossus_order_cate1_7d",
            "merchant_colossus_order_cate1_14d",
            "merchant_colossus_order_cate1_28d",
            "merchant_colossus_order_cate1_30d",
            "merchant_colossus_order_cate1_60d",
            "merchant_colossus_order_cate1_90d",
            "merchant_colossus_order_cate1_10min",
            "merchant_colossus_order_cate1_30min",
            "merchant_colossus_order_cate2_last1",
            "merchant_colossus_order_cate2_last5",
            "merchant_colossus_order_cate2_last10",
            "merchant_colossus_order_cate2_last20",
            "merchant_colossus_order_cate2_last100",
            "merchant_colossus_order_cate2_last200",
            "merchant_colossus_order_cate2_1h",
            "merchant_colossus_order_cate2_3h",
            "merchant_colossus_order_cate2_6h",
            "merchant_colossus_order_cate2_12h",
            "merchant_colossus_order_cate2_1d",
            "merchant_colossus_order_cate2_2d",
            "merchant_colossus_order_cate2_3d",
            "merchant_colossus_order_cate2_7d",
            "merchant_colossus_order_cate2_14d",
            "merchant_colossus_order_cate2_28d",
            "merchant_colossus_order_cate2_30d",
            "merchant_colossus_order_cate2_60d",
            "merchant_colossus_order_cate2_90d",
            "merchant_colossus_order_cate2_10min",
            "merchant_colossus_order_cate2_30min",
            "merchant_colossus_order_cate3_last1",
            "merchant_colossus_order_cate3_last5",
            "merchant_colossus_order_cate3_last10",
            "merchant_colossus_order_cate3_last20",
            "merchant_colossus_order_cate3_last100",
            "merchant_colossus_order_cate3_last200",
            "merchant_colossus_order_cate3_1h",
            "merchant_colossus_order_cate3_3h",
            "merchant_colossus_order_cate3_6h",
            "merchant_colossus_order_cate3_12h",
            "merchant_colossus_order_cate3_1d",
            "merchant_colossus_order_cate3_2d",
            "merchant_colossus_order_cate3_3d",
            "merchant_colossus_order_cate3_7d",
            "merchant_colossus_order_cate3_14d",
            "merchant_colossus_order_cate3_28d",
            "merchant_colossus_order_cate3_30d",
            "merchant_colossus_order_cate3_60d",
            "merchant_colossus_order_cate3_90d",
            "merchant_colossus_order_cate3_10min",
            "merchant_colossus_order_cate3_30min",
            "merchant_colossus_order_leaf_last1",
            "merchant_colossus_order_leaf_last5",
            "merchant_colossus_order_leaf_last10",
            "merchant_colossus_order_leaf_last20",
            "merchant_colossus_order_leaf_last100",
            "merchant_colossus_order_leaf_last200",
            "merchant_colossus_order_leaf_1h",
            "merchant_colossus_order_leaf_3h",
            "merchant_colossus_order_leaf_6h",
            "merchant_colossus_order_leaf_12h",
            "merchant_colossus_order_leaf_1d",
            "merchant_colossus_order_leaf_2d",
            "merchant_colossus_order_leaf_3d",
            "merchant_colossus_order_leaf_7d",
            "merchant_colossus_order_leaf_14d",
            "merchant_colossus_order_leaf_28d",
            "merchant_colossus_order_leaf_30d",
            "merchant_colossus_order_leaf_60d",
            "merchant_colossus_order_leaf_90d",
            "merchant_colossus_order_leaf_10min",
            "merchant_colossus_order_leaf_30min", ])
    return attrs

class MerchantColossusLiveSliceFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_live_slice_feature"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("filter_time"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_aid"))
    attrs.add(self._config.get("target_cluster_id"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["merchant_colossus_live_click_authorid_last1",
                "merchant_colossus_live_click_authorid_last5",
                "merchant_colossus_live_click_authorid_last10",
                "merchant_colossus_live_click_authorid_last20",
                "merchant_colossus_live_click_authorid_last100",
                "merchant_colossus_live_click_authorid_last200",
                "merchant_colossus_live_click_authorid_1h",
                "merchant_colossus_live_click_authorid_3h",
                "merchant_colossus_live_click_authorid_6h",
                "merchant_colossus_live_click_authorid_12h",
                "merchant_colossus_live_click_authorid_1d",
                "merchant_colossus_live_click_authorid_2d",
                "merchant_colossus_live_click_authorid_3d",
                "merchant_colossus_live_click_authorid_7d",
                "merchant_colossus_live_click_authorid_14d",
                "merchant_colossus_live_click_authorid_28d",
                "merchant_colossus_live_click_authorid_30d",
                "merchant_colossus_live_click_authorid_60d",
                "merchant_colossus_live_click_authorid_90d",
                "merchant_colossus_live_click_authorid_10min",
                "merchant_colossus_live_click_authorid_30min",
                "merchant_colossus_live_click_clusterid_last1",
                "merchant_colossus_live_click_clusterid_last5",
                "merchant_colossus_live_click_clusterid_last10",
                "merchant_colossus_live_click_clusterid_last20",
                "merchant_colossus_live_click_clusterid_last100",
                "merchant_colossus_live_click_clusterid_last200",
                "merchant_colossus_live_click_clusterid_1h",
                "merchant_colossus_live_click_clusterid_3h",
                "merchant_colossus_live_click_clusterid_6h",
                "merchant_colossus_live_click_clusterid_12h",
                "merchant_colossus_live_click_clusterid_1d",
                "merchant_colossus_live_click_clusterid_2d",
                "merchant_colossus_live_click_clusterid_3d",
                "merchant_colossus_live_click_clusterid_7d",
                "merchant_colossus_live_click_clusterid_14d",
                "merchant_colossus_live_click_clusterid_28d",
                "merchant_colossus_live_click_clusterid_30d",
                "merchant_colossus_live_click_clusterid_60d",
                "merchant_colossus_live_click_clusterid_90d",
                "merchant_colossus_live_click_clusterid_10min",
                "merchant_colossus_live_click_clusterid_30min",
                "merchant_colossus_live_like_authorid_last1",
                "merchant_colossus_live_like_authorid_last5",
                "merchant_colossus_live_like_authorid_last10",
                "merchant_colossus_live_like_authorid_last20",
                "merchant_colossus_live_like_authorid_last100",
                "merchant_colossus_live_like_authorid_last200",
                "merchant_colossus_live_like_authorid_1h",
                "merchant_colossus_live_like_authorid_3h",
                "merchant_colossus_live_like_authorid_6h",
                "merchant_colossus_live_like_authorid_12h",
                "merchant_colossus_live_like_authorid_1d",
                "merchant_colossus_live_like_authorid_2d",
                "merchant_colossus_live_like_authorid_3d",
                "merchant_colossus_live_like_authorid_7d",
                "merchant_colossus_live_like_authorid_14d",
                "merchant_colossus_live_like_authorid_28d",
                "merchant_colossus_live_like_authorid_30d",
                "merchant_colossus_live_like_authorid_60d",
                "merchant_colossus_live_like_authorid_90d",
                "merchant_colossus_live_like_authorid_10min",
                "merchant_colossus_live_like_authorid_30min",
                "merchant_colossus_live_like_clusterid_last1",
                "merchant_colossus_live_like_clusterid_last5",
                "merchant_colossus_live_like_clusterid_last10",
                "merchant_colossus_live_like_clusterid_last20",
                "merchant_colossus_live_like_clusterid_last100",
                "merchant_colossus_live_like_clusterid_last200",
                "merchant_colossus_live_like_clusterid_1h",
                "merchant_colossus_live_like_clusterid_3h",
                "merchant_colossus_live_like_clusterid_6h",
                "merchant_colossus_live_like_clusterid_12h",
                "merchant_colossus_live_like_clusterid_1d",
                "merchant_colossus_live_like_clusterid_2d",
                "merchant_colossus_live_like_clusterid_3d",
                "merchant_colossus_live_like_clusterid_7d",
                "merchant_colossus_live_like_clusterid_14d",
                "merchant_colossus_live_like_clusterid_28d",
                "merchant_colossus_live_like_clusterid_30d",
                "merchant_colossus_live_like_clusterid_60d",
                "merchant_colossus_live_like_clusterid_90d",
                "merchant_colossus_live_like_clusterid_10min",
                "merchant_colossus_live_like_clusterid_30min",
                "merchant_colossus_live_comment_authorid_last1",
                "merchant_colossus_live_comment_authorid_last5",
                "merchant_colossus_live_comment_authorid_last10",
                "merchant_colossus_live_comment_authorid_last20",
                "merchant_colossus_live_comment_authorid_last100",
                "merchant_colossus_live_comment_authorid_last200",
                "merchant_colossus_live_comment_authorid_1h",
                "merchant_colossus_live_comment_authorid_3h",
                "merchant_colossus_live_comment_authorid_6h",
                "merchant_colossus_live_comment_authorid_12h",
                "merchant_colossus_live_comment_authorid_1d",
                "merchant_colossus_live_comment_authorid_2d",
                "merchant_colossus_live_comment_authorid_3d",
                "merchant_colossus_live_comment_authorid_7d",
                "merchant_colossus_live_comment_authorid_14d",
                "merchant_colossus_live_comment_authorid_28d",
                "merchant_colossus_live_comment_authorid_30d",
                "merchant_colossus_live_comment_authorid_60d",
                "merchant_colossus_live_comment_authorid_90d",
                "merchant_colossus_live_comment_authorid_10min",
                "merchant_colossus_live_comment_authorid_30min",
                "merchant_colossus_live_comment_clusterid_last1",
                "merchant_colossus_live_comment_clusterid_last5",
                "merchant_colossus_live_comment_clusterid_last10",
                "merchant_colossus_live_comment_clusterid_last20",
                "merchant_colossus_live_comment_clusterid_last100",
                "merchant_colossus_live_comment_clusterid_last200",
                "merchant_colossus_live_comment_clusterid_1h",
                "merchant_colossus_live_comment_clusterid_3h",
                "merchant_colossus_live_comment_clusterid_6h",
                "merchant_colossus_live_comment_clusterid_12h",
                "merchant_colossus_live_comment_clusterid_1d",
                "merchant_colossus_live_comment_clusterid_2d",
                "merchant_colossus_live_comment_clusterid_3d",
                "merchant_colossus_live_comment_clusterid_7d",
                "merchant_colossus_live_comment_clusterid_14d",
                "merchant_colossus_live_comment_clusterid_28d",
                "merchant_colossus_live_comment_clusterid_30d",
                "merchant_colossus_live_comment_clusterid_60d",
                "merchant_colossus_live_comment_clusterid_90d",
                "merchant_colossus_live_comment_clusterid_10min",
                "merchant_colossus_live_comment_clusterid_30min",
                "merchant_colossus_live_profile_authorid_last1",
                "merchant_colossus_live_profile_authorid_last5",
                "merchant_colossus_live_profile_authorid_last10",
                "merchant_colossus_live_profile_authorid_last20",
                "merchant_colossus_live_profile_authorid_last100",
                "merchant_colossus_live_profile_authorid_last200",
                "merchant_colossus_live_profile_authorid_1h",
                "merchant_colossus_live_profile_authorid_3h",
                "merchant_colossus_live_profile_authorid_6h",
                "merchant_colossus_live_profile_authorid_12h",
                "merchant_colossus_live_profile_authorid_1d",
                "merchant_colossus_live_profile_authorid_2d",
                "merchant_colossus_live_profile_authorid_3d",
                "merchant_colossus_live_profile_authorid_7d",
                "merchant_colossus_live_profile_authorid_14d",
                "merchant_colossus_live_profile_authorid_28d",
                "merchant_colossus_live_profile_authorid_30d",
                "merchant_colossus_live_profile_authorid_60d",
                "merchant_colossus_live_profile_authorid_90d",
                "merchant_colossus_live_profile_authorid_10min",
                "merchant_colossus_live_profile_authorid_30min",
                "merchant_colossus_live_profile_clusterid_last1",
                "merchant_colossus_live_profile_clusterid_last5",
                "merchant_colossus_live_profile_clusterid_last10",
                "merchant_colossus_live_profile_clusterid_last20",
                "merchant_colossus_live_profile_clusterid_last100",
                "merchant_colossus_live_profile_clusterid_last200",
                "merchant_colossus_live_profile_clusterid_1h",
                "merchant_colossus_live_profile_clusterid_3h",
                "merchant_colossus_live_profile_clusterid_6h",
                "merchant_colossus_live_profile_clusterid_12h",
                "merchant_colossus_live_profile_clusterid_1d",
                "merchant_colossus_live_profile_clusterid_2d",
                "merchant_colossus_live_profile_clusterid_3d",
                "merchant_colossus_live_profile_clusterid_7d",
                "merchant_colossus_live_profile_clusterid_14d",
                "merchant_colossus_live_profile_clusterid_28d",
                "merchant_colossus_live_profile_clusterid_30d",
                "merchant_colossus_live_profile_clusterid_60d",
                "merchant_colossus_live_profile_clusterid_90d",
                "merchant_colossus_live_profile_clusterid_10min",
                "merchant_colossus_live_profile_clusterid_30min",
                "merchant_colossus_live_realshow_authorid_last1",
                "merchant_colossus_live_realshow_authorid_last5",
                "merchant_colossus_live_realshow_authorid_last10",
                "merchant_colossus_live_realshow_authorid_last20",
                "merchant_colossus_live_realshow_authorid_last100",
                "merchant_colossus_live_realshow_authorid_last200",
                "merchant_colossus_live_realshow_authorid_1h",
                "merchant_colossus_live_realshow_authorid_3h",
                "merchant_colossus_live_realshow_authorid_6h",
                "merchant_colossus_live_realshow_authorid_12h",
                "merchant_colossus_live_realshow_authorid_1d",
                "merchant_colossus_live_realshow_authorid_2d",
                "merchant_colossus_live_realshow_authorid_3d",
                "merchant_colossus_live_realshow_authorid_7d",
                "merchant_colossus_live_realshow_authorid_14d",
                "merchant_colossus_live_realshow_authorid_28d",
                "merchant_colossus_live_realshow_authorid_30d",
                "merchant_colossus_live_realshow_authorid_60d",
                "merchant_colossus_live_realshow_authorid_90d",
                "merchant_colossus_live_realshow_authorid_10min",
                "merchant_colossus_live_realshow_authorid_30min",
                "merchant_colossus_live_realshow_clusterid_last1",
                "merchant_colossus_live_realshow_clusterid_last5",
                "merchant_colossus_live_realshow_clusterid_last10",
                "merchant_colossus_live_realshow_clusterid_last20",
                "merchant_colossus_live_realshow_clusterid_last100",
                "merchant_colossus_live_realshow_clusterid_last200",
                "merchant_colossus_live_realshow_clusterid_1h",
                "merchant_colossus_live_realshow_clusterid_3h",
                "merchant_colossus_live_realshow_clusterid_6h",
                "merchant_colossus_live_realshow_clusterid_12h",
                "merchant_colossus_live_realshow_clusterid_1d",
                "merchant_colossus_live_realshow_clusterid_2d",
                "merchant_colossus_live_realshow_clusterid_3d",
                "merchant_colossus_live_realshow_clusterid_7d",
                "merchant_colossus_live_realshow_clusterid_14d",
                "merchant_colossus_live_realshow_clusterid_28d",
                "merchant_colossus_live_realshow_clusterid_30d",
                "merchant_colossus_live_realshow_clusterid_60d",
                "merchant_colossus_live_realshow_clusterid_90d",
                "merchant_colossus_live_realshow_clusterid_10min",
                "merchant_colossus_live_realshow_clusterid_30min",
                "merchant_colossus_live_gift_authorid_last1",
                "merchant_colossus_live_gift_authorid_last5",
                "merchant_colossus_live_gift_authorid_last10",
                "merchant_colossus_live_gift_authorid_last20",
                "merchant_colossus_live_gift_authorid_last100",
                "merchant_colossus_live_gift_authorid_last200",
                "merchant_colossus_live_gift_authorid_1h",
                "merchant_colossus_live_gift_authorid_3h",
                "merchant_colossus_live_gift_authorid_6h",
                "merchant_colossus_live_gift_authorid_12h",
                "merchant_colossus_live_gift_authorid_1d",
                "merchant_colossus_live_gift_authorid_2d",
                "merchant_colossus_live_gift_authorid_3d",
                "merchant_colossus_live_gift_authorid_7d",
                "merchant_colossus_live_gift_authorid_14d",
                "merchant_colossus_live_gift_authorid_28d",
                "merchant_colossus_live_gift_authorid_30d",
                "merchant_colossus_live_gift_authorid_60d",
                "merchant_colossus_live_gift_authorid_90d",
                "merchant_colossus_live_gift_authorid_10min",
                "merchant_colossus_live_gift_authorid_30min",
                "merchant_colossus_live_gift_clusterid_last1",
                "merchant_colossus_live_gift_clusterid_last5",
                "merchant_colossus_live_gift_clusterid_last10",
                "merchant_colossus_live_gift_clusterid_last20",
                "merchant_colossus_live_gift_clusterid_last100",
                "merchant_colossus_live_gift_clusterid_last200",
                "merchant_colossus_live_gift_clusterid_1h",
                "merchant_colossus_live_gift_clusterid_3h",
                "merchant_colossus_live_gift_clusterid_6h",
                "merchant_colossus_live_gift_clusterid_12h",
                "merchant_colossus_live_gift_clusterid_1d",
                "merchant_colossus_live_gift_clusterid_2d",
                "merchant_colossus_live_gift_clusterid_3d",
                "merchant_colossus_live_gift_clusterid_7d",
                "merchant_colossus_live_gift_clusterid_14d",
                "merchant_colossus_live_gift_clusterid_28d",
                "merchant_colossus_live_gift_clusterid_30d",
                "merchant_colossus_live_gift_clusterid_60d",
                "merchant_colossus_live_gift_clusterid_90d",
                "merchant_colossus_live_gift_clusterid_10min",
                "merchant_colossus_live_gift_clusterid_30min",
                "merchant_colossus_live_forward_authorid_last1",
                "merchant_colossus_live_forward_authorid_last5",
                "merchant_colossus_live_forward_authorid_last10",
                "merchant_colossus_live_forward_authorid_last20",
                "merchant_colossus_live_forward_authorid_last100",
                "merchant_colossus_live_forward_authorid_last200",
                "merchant_colossus_live_forward_authorid_1h",
                "merchant_colossus_live_forward_authorid_3h",
                "merchant_colossus_live_forward_authorid_6h",
                "merchant_colossus_live_forward_authorid_12h",
                "merchant_colossus_live_forward_authorid_1d",
                "merchant_colossus_live_forward_authorid_2d",
                "merchant_colossus_live_forward_authorid_3d",
                "merchant_colossus_live_forward_authorid_7d",
                "merchant_colossus_live_forward_authorid_14d",
                "merchant_colossus_live_forward_authorid_28d",
                "merchant_colossus_live_forward_authorid_30d",
                "merchant_colossus_live_forward_authorid_60d",
                "merchant_colossus_live_forward_authorid_90d",
                "merchant_colossus_live_forward_authorid_10min",
                "merchant_colossus_live_forward_authorid_30min",
                "merchant_colossus_live_forward_clusterid_last1",
                "merchant_colossus_live_forward_clusterid_last5",
                "merchant_colossus_live_forward_clusterid_last10",
                "merchant_colossus_live_forward_clusterid_last20",
                "merchant_colossus_live_forward_clusterid_last100",
                "merchant_colossus_live_forward_clusterid_last200",
                "merchant_colossus_live_forward_clusterid_1h",
                "merchant_colossus_live_forward_clusterid_3h",
                "merchant_colossus_live_forward_clusterid_6h",
                "merchant_colossus_live_forward_clusterid_12h",
                "merchant_colossus_live_forward_clusterid_1d",
                "merchant_colossus_live_forward_clusterid_2d",
                "merchant_colossus_live_forward_clusterid_3d",
                "merchant_colossus_live_forward_clusterid_7d",
                "merchant_colossus_live_forward_clusterid_14d",
                "merchant_colossus_live_forward_clusterid_28d",
                "merchant_colossus_live_forward_clusterid_30d",
                "merchant_colossus_live_forward_clusterid_60d",
                "merchant_colossus_live_forward_clusterid_90d",
                "merchant_colossus_live_forward_clusterid_10min",
                "merchant_colossus_live_forward_clusterid_30min",])
    return attrs

class MerchantRepeatImpressionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_merchant_repeat_impression"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("max_user_num_limt"))
    attrs.add(self._config.get("filter_minute_ago"))
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("colossus_live_item_timestamp_attr"))
    attrs.add(self._config.get("colossus_live_item_live_id_attr"))
    attrs.add(self._config.get("colossus_live_item_play_time_attr"))
    attrs.add(self._config.get("colossus_live_item_auto_play_time_attr"))

    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pid_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(['merchant_showcase_is_shown', 'merchant_showcase_is_click',
                 'merchant_showcase_show_cnt', 'merchant_showcase_click_cnt',
                 "merchant_showcase_last_show_time_lag_second",
                 'merchant_showcase_last_show_time_lag_minute',
                 'merchant_showcase_last_show_time_lag_hour'])
    return attrs

class MerchantCommonMcntFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_merchant_common_mcnt_feature"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("common_action_category_list"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("filter_time"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_aid"))
    attrs.add(self._config.get("target_cate1"))
    attrs.add(self._config.get("target_cate2"))
    attrs.add(self._config.get("target_cate3"))
    return attrs

class MerchantCommonMcntFeatureEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_merchant_common_mcnt_feature_v2"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("common_action_category_list"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("filter_time"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_aid"))
    attrs.add(self._config.get("target_cate1"))
    attrs.add(self._config.get("target_cate2"))
    attrs.add(self._config.get("target_cate3"))
    return attrs

class MerchantColossusCartGSUNewOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_cart_gsu_new_opt"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_request_time"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("click_colossus_resp_attr"))
    attrs.add(self._config.get("order_colossus_resp_attr"))
    attrs.add(self._config.get("cart_gsu_feature_prefix"))
    attrs.add(self._config.get("max_user_num_limt"))
    attrs.add(self._config.get("target_attr"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([])
    l = ['order_item_id','order_cate1_id','order_cate2_id',
        'order_cate3_id','order_cate4_id','order_author_id',
        'order_real_author_id','u_buystat_is_fenxiao','order_buy_item_lag',
        'order_pay_time_lag','u_buystat_pay_hourofday',
        'u_buystat_pay_dayofweek', 'u_buystat_pay_dayofmonth','u_buystat_pay_monthofyear']
    for i in l:
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([])
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    return attrs

class MerchantColossusCartGSUNewEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_cart_gsu_new"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_request_time"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("click_colossus_resp_attr"))
    attrs.add(self._config.get("order_colossus_resp_attr"))
    attrs.add(self._config.get("cart_gsu_feature_prefix"))
    attrs.add(self._config.get("max_user_num_limt"))
    attrs.add(self._config.get("target_attr"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([])
    l = ['order_item_id','order_cate1_id','order_cate2_id',
        'order_cate3_id','order_cate4_id','order_author_id',
        'order_real_author_id','u_buystat_is_fenxiao','order_buy_item_lag',
        'order_pay_time_lag','u_buystat_pay_hourofday',
        'u_buystat_pay_dayofweek', 'u_buystat_pay_dayofmonth','u_buystat_pay_monthofyear']
    for i in l:
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([])
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    return attrs

class MerchantColossusCartGSUEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_cart_gsu"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_request_time"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("click_colossus_resp_attr"))
    attrs.add(self._config.get("order_colossus_resp_attr"))
    attrs.add(self._config.get("cart_gsu_feature_prefix"))
    attrs.add(self._config.get("max_user_num_limt"))
    attrs.add(self._config.get("target_attr"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([])
    l = ['order_item_id','order_cate1_id','order_cate2_id',
        'order_cate3_id','order_cate4_id','order_author_id',
        'order_real_author_id','u_buystat_is_fenxiao','order_buy_item_lag',
        'order_pay_time_lag','u_buystat_pay_hourofday',
        'u_buystat_pay_dayofweek', 'u_buystat_pay_dayofmonth','u_buystat_pay_monthofyear']
    for i in l:
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([])
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    return attrs

class MerchantColossusCartGSUEnricherV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_cart_gsu_v2"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_request_time"))
    attrs.add(self._config.get("print_debug_log"))
    attrs.add(self._config.get("click_colossus_resp_attr"))
    attrs.add(self._config.get("order_colossus_resp_attr"))
    attrs.add(self._config.get("cart_gsu_feature_prefix"))
    attrs.add(self._config.get("max_user_num_limt"))
    attrs.add(self._config.get("target_attr"))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([])
    l = ['u_recent_item_id','u_recent_carrier_type','u_recent_cate1_id','u_recent_cate2_id',
        'u_recent_cate3_id','u_recent_cate4_id','u_recent_author_id',
        'u_recent_real_author_id','u_recent_is_fenxiao','u_recent_click_item_lag',
        'u_recent_is_buy','u_recent_hourofday',
        'u_recent_dayofweek', 'u_recent_dayofmonth','u_recent_monthofyear',
        'u_recent_click_count','u_recent_order_count']
    for i in l:
      attrs.add(i)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([])
    for i in self._config.get("side_info_int_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    for i in self._config.get("side_info_str_attr_lists"):
      attrs.add(self._config.get("cart_gsu_feature_prefix")+i)
    return attrs

class MerchantGsuAidListEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_aid_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.update(self.extract_dynamic_params(self._config.get("only_show_target")))
      ret.update(self.extract_dynamic_params(self._config.get("valid_watch_time_threshold")))
      ret.update(self.extract_dynamic_params(self._config.get("result_deduplicate")))
      ret.update(self.extract_dynamic_params(self._config.get("time_interval")))
      ret.add(self._config.get("save_to", "colossus_aid_list"))
      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("save_to", ""))
      return attrs

class MerchantGsuAutoAidListEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_auto_aid_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.update(self.extract_dynamic_params(self._config.get("only_show_target")))
      ret.update(self.extract_dynamic_params(self._config.get("valid_watch_time_threshold")))
      ret.update(self.extract_dynamic_params(self._config.get("result_deduplicate")))
      ret.update(self.extract_dynamic_params(self._config.get("time_interval")))
      ret.add(self._config.get("save_to", "colossus_aid_list"))
      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("save_to", ""))
      return attrs

class MerchantGsuAutoAidListV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_auto_aid_list_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.update(self.extract_dynamic_params(self._config.get("only_show_target")))
      ret.update(self.extract_dynamic_params(self._config.get("valid_watch_time_threshold")))
      ret.update(self.extract_dynamic_params(self._config.get("result_deduplicate")))
      ret.update(self.extract_dynamic_params(self._config.get("time_interval")))
      ret.add(self._config.get("save_to", "colossus_aid_list"))
      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("save_to", ""))
      return attrs


class MerchantPhotoGsuFromAidClusterIdEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "thread_num" not in self._config:
        self._config["thread_num"] = 1
      if "only_merchant_attr" not in self._config:
        self._config["only_merchant_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_from_aid_cluster_id"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cluster_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("lag_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("auto_play_time_list_attr"))
      ret.add(self._config.get("hetu_tag_list_attr"))
      ret.add(self._config.get("cluster_id_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("order_price_list_attr"))
      ret.add(self._config.get("audience_count_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("aid_cluster_id_list_attr"))
      ret.add(self._config.get("entity_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      return ret

class MerchantLiveGsuFromAidCateEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "thread_num" not in self._config:
        self._config["thread_num"] = 1
      if "filter_no_merchant_attr" not in self._config:
        self._config["filter_no_merchant_attr"] = True
      if "filter_old_days_flag_attr" not in self._config:
        self._config["filter_old_days_flag_attr"] = True
      if "filter_old_days_attr" not in self._config:
        self._config["filter_old_days_attr"] = 180
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_live_gsu_from_aid_cate"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cate1_attr"))
      ret.add(self._config.get("target_cate2_attr"))
      ret.add(self._config.get("target_cate3_attr"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("lag_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("auto_play_time_list_attr"))
      ret.add(self._config.get("hetu_tag_list_attr"))
      ret.add(self._config.get("cluster_id_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("order_price_list_attr"))
      ret.add(self._config.get("audience_count_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_attr"))
      ret.add(self._config.get("cate2_attr"))
      ret.add(self._config.get("cate3_attr"))
      return ret

class MerchantPhotoGsuFromGoodInfoV2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "thread_num" not in self._config:
        self._config["thread_num"] = 1
      if "entity_match_attr" not in self._config:
        self._config["entity_match_attr"] = True
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_from_good_info_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cluster_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("tag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_list_attr"))
      ret.add(self._config.get("cate2_list_attr"))
      ret.add(self._config.get("cate3_list_attr"))
      ret.add(self._config.get("entity_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      return ret

class MerchantPhotoGsuFromGoodInfoV2ColumnEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "thread_num" not in self._config:
        self._config["thread_num"] = 1
      if "entity_match_attr" not in self._config:
        self._config["entity_match_attr"] = True
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_from_good_info_v2_column_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("input_photo_id_attr"))
      ret.add(self._config.get("input_author_id_attr"))
      ret.add(self._config.get("input_duration_attr"))
      ret.add(self._config.get("input_play_time_attr"))
      ret.add(self._config.get("input_tag_attr"))
      ret.add(self._config.get("input_channel_attr"))
      ret.add(self._config.get("input_label_attr"))
      ret.add(self._config.get("input_timestamp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cluster_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("tag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_list_attr"))
      ret.add(self._config.get("cate2_list_attr"))
      ret.add(self._config.get("cate3_list_attr"))
      ret.add(self._config.get("entity_list_attr"))
      return ret

class MerchantPhotoGsuFromGoodInfoV3Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "thread_num" not in self._config:
        self._config["thread_num"] = 1
      if "entity_match_attr" not in self._config:
        self._config["entity_match_attr"] = True
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_from_good_info_v3"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cate1_attr"))
      ret.add(self._config.get("target_cate2_attr"))
      ret.add(self._config.get("target_cate3_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("tag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_list_attr"))
      ret.add(self._config.get("cate2_list_attr"))
      ret.add(self._config.get("cate3_list_attr"))
      ret.add(self._config.get("entity_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      return ret
    
class MerchantPhotoGsuGoodCateEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_good_cate"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cate1_attr"))
      ret.add(self._config.get("target_cate2_attr"))
      ret.add(self._config.get("target_cate3_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("cate1_list_attr"))
      ret.add(self._config.get("cate2_list_attr"))
      ret.add(self._config.get("cate3_list_attr"))
      return ret
class MerchantPhotoGsuGoodSemanticEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "n_minute_ago" not in self._config:
        self._config["n_minute_ago"] = 0
      if "level1_match_attr" not in self._config:
        self._config["level1_match_attr"] = True
      if "level2_match_attr" not in self._config:
        self._config["level2_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_gsu_good_semantic"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("timestamp_list_attr"))
      ret.add(self._config.get("spu_list_attr"))
      ret.add(self._config.get("category_list_attr"))
      ret.add(self._config.get("item_id_list_attr"))
      ret.add(self._config.get("semantic_id_list_attr"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_level_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("match_photo_id_list_attr"))
      ret.add(self._config.get("match_author_id_list_attr"))
      ret.add(self._config.get("match_play_time_list_attr"))
      ret.add(self._config.get("match_duration_list_attr"))
      ret.add(self._config.get("match_channel_list_attr"))
      ret.add(self._config.get("match_label_list_attr"))
      ret.add(self._config.get("match_spu_list_attr"))
      ret.add(self._config.get("match_cate1_list_attr"))
      ret.add(self._config.get("match_cate2_list_attr"))
      ret.add(self._config.get("match_cate3_list_attr"))
      ret.add(self._config.get("match_item_id_list_attr"))
      ret.add(self._config.get("match_semantic1_list_attr"))
      ret.add(self._config.get("match_semantic2_list_attr"))
      ret.add(self._config.get("match_semantic3_list_attr"))
      ret.add(self._config.get("match_play_lag_list_attr"))
      ret.add(self._config.get("match_index_list_attr"))
      return ret
class MerchantGsuFromContextEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "cate1_match_attr" not in self._config:
        self._config["cate1_match_attr"] = True
      if "cate2_match_attr" not in self._config:
        self._config["cate2_match_attr"] = True
      if "cate3_match_attr" not in self._config:
        self._config["cate3_match_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_from_context"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("context_aid"))
      ret.add(self._config.get("context_iid"))
      ret.add(self._config.get("context_cate1"))
      ret.add(self._config.get("context_cate2"))
      ret.add(self._config.get("context_cate3"))

      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_cluster_attr"))

      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("context_aid_out"))
      ret.add(self._config.get("context_iid_out"))
      ret.add(self._config.get("context_cate1_out"))
      ret.add(self._config.get("context_cate2_out"))
      ret.add(self._config.get("context_cate3_out"))
      return ret

class MerchantPhotoShortListEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "only_cart_photo_attr" not in self._config:
        self._config["only_cart_photo_attr"] = False
      if "only_valid_view_photo_attr" not in self._config:
        self._config["only_valid_view_photo_attr"] = False
      if "valid_view_threshold" not in self._config:
        self._config["valid_view_threshold"] = 5

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo_short_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("duration_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("play_lag_list_attr"))
      ret.add(self._config.get("tag_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      return ret

class KuibaUserAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_extract_kuiba_user_attr"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_all_item_names"))
    return attrs

class MerchantPhoto2LiveClickListEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "only_merchant_live_attr" not in self._config:
        self._config["only_merchant_live_attr"] = False
      if "only_photo2live_attr" not in self._config:
        self._config["only_photo2live_attr"] = False
      if "limit_num_by_ori_index_attr" not in self._config:
        self._config["limit_num_by_ori_index_attr"] = False
      if "n_minute_ago" not in self._config:
        self._config["n_minute_ago"] = 0

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo2live_click_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("lag_day_list_attr"))
      ret.add(self._config.get("lag_hour_list_attr"))
      ret.add(self._config.get("timestamp_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("hetu_tag_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("before_dup_index_list_attr"))
      return ret

class MerchantPhoto2LiveClickListV2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "only_merchant_live_attr" not in self._config:
        self._config["only_merchant_live_attr"] = False
      if "only_photo2live_attr" not in self._config:
        self._config["only_photo2live_attr"] = False
      if "limit_num_by_ori_index_attr" not in self._config:
        self._config["limit_num_by_ori_index_attr"] = False
      if "n_minute_ago" not in self._config:
        self._config["n_minute_ago"] = 0

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_photo2live_click_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))

      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_id_list_attr"))
      ret.add(self._config.get("author_id_list_attr"))
      ret.add(self._config.get("label_list_attr"))
      ret.add(self._config.get("lag_day_list_attr"))
      ret.add(self._config.get("lag_hour_list_attr"))
      ret.add(self._config.get("timestamp_list_attr"))
      ret.add(self._config.get("play_time_list_attr"))
      ret.add(self._config.get("hetu_tag_list_attr"))
      ret.add(self._config.get("index_list_attr"))
      ret.add(self._config.get("before_dup_index_list_attr"))

      ret.add(self._config.get("item_id_list_attr"))
      ret.add(self._config.get("channel_list_attr"))
      ret.add(self._config.get("auto_play_time_list_attr"))

      return ret
    
class MerchantQueryIntentListEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "filter_hour_attr" not in self._config:
        self._config["filter_hour_attr"] = -1
      if "search_query_stat_list_attr" not in self._config:
        self._config["search_query_stat_list_attr"] = "uQueryShopIntent"
      if "entity_id_attr" not in self._config:
        self._config["entity_id_attr"] = "uQueryEntityId"
      if "brand_id_attr" not in self._config:
        self._config["brand_id_attr"] = "uQueryBrandId"
      if "cate_level1_attr" not in self._config:
        self._config["cate_level1_attr"] = "uQueryCateLevel1"
      if "cate_level2_attr" not in self._config:
        self._config["cate_level2_attr"] = "uQueryCateLevel2"
      if "cate_level3_attr" not in self._config:
        self._config["cate_level3_attr"] = "uQueryCateLevel3"
      if "search_time_lag_attr" not in self._config:
        self._config["search_time_lag_attr"] = "uSearchTimeLag"
      if "search_time_attr" not in self._config:
        self._config["search_time_attr"] = "uSearchTimeList"
      if "search_query_attr" not in self._config:
        self._config["search_query_attr"] = "uSearchQueryList"

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_query_intent_list"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("search_query_stat_list_attr"))

      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("entity_id_attr"))
      ret.add(self._config.get("brand_id_attr"))
      ret.add(self._config.get("cate_level1_attr"))
      ret.add(self._config.get("cate_level2_attr"))
      ret.add(self._config.get("cate_level3_attr"))
      ret.add(self._config.get("search_time_lag_attr"))
      ret.add(self._config.get("search_time_attr"))
      ret.add(self._config.get("search_query_attr"))
      return ret

class MerchantGsuAid2AidPhoto2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_photo_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_match_aid_photo_id_list", "photo_match_aid_photo_id_list"))
      ret.add(self._config.get("photo_match_aid_author_id_list", "photo_match_aid_author_id_list"))
      ret.add(self._config.get("photo_match_aid_duration_list", "photo_match_aid_duration_list"))
      ret.add(self._config.get("photo_match_aid_play_time_list", "photo_match_aid_play_time_list"))
      ret.add(self._config.get("photo_match_aid_tag_list", "photo_match_aid_tag_list"))
      ret.add(self._config.get("photo_match_aid_channel_list", "photo_match_aid_channel_list"))
      ret.add(self._config.get("photo_match_aid_label_list", "photo_match_aid_label_list"))
      ret.add(self._config.get("photo_match_aid_timestamp_list", "photo_match_aid_timestamp_list"))
      return ret

class MerchantGsuAid2AidPhotoCommon2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_photo_common_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aid_list"))
      return ret
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_match_aid_photo_id_list", "photo_match_aid_photo_id_list"))
      ret.add(self._config.get("photo_match_aid_author_id_list", "photo_match_aid_author_id_list"))
      ret.add(self._config.get("photo_match_aid_duration_list", "photo_match_aid_duration_list"))
      ret.add(self._config.get("photo_match_aid_play_time_list", "photo_match_aid_play_time_list"))
      ret.add(self._config.get("photo_match_aid_tag_list", "photo_match_aid_tag_list"))
      ret.add(self._config.get("photo_match_aid_channel_list", "photo_match_aid_channel_list"))
      ret.add(self._config.get("photo_match_aid_label_list", "photo_match_aid_label_list"))
      ret.add(self._config.get("photo_match_aid_timestamp_list", "photo_match_aid_timestamp_list"))
      return ret

class MerchantGsuAid2AidListPhoto2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_play_time" not in self._config:
        self._config["filter_play_time"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aidlist_photo_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr_list", "aId_list"))
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("photo_match_aid_list_photo_id_list", "photo_match_aid_list_photo_id_list"))
      ret.add(self._config.get("photo_match_aid_list_author_id_list", "photo_match_aid_list_author_id_list"))
      ret.add(self._config.get("photo_match_aid_list_duration_list", "photo_match_aid_list_duration_list"))
      ret.add(self._config.get("photo_match_aid_list_play_time_list", "photo_match_aid_list_play_time_list"))
      ret.add(self._config.get("photo_match_aid_list_tag_list", "photo_match_aid_list_tag_list"))
      ret.add(self._config.get("photo_match_aid_list_channel_list", "photo_match_aid_list_channel_list"))
      ret.add(self._config.get("photo_match_aid_list_label_list", "photo_match_aid_list_label_list"))
      ret.add(self._config.get("photo_match_aid_list_timestamp_list", "photo_match_aid_list_timestamp_list"))
      return ret

class MerchantGsuAid2AidListGoodClick2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aidlist_good_click_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr_list", "aId_list"))
      return ret
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_click_match_aid_list_item_id_list", "good_click_match_aid_list_item_id_list"))
      ret.add(self._config.get("good_click_match_aid_list_real_seller_id_list", "good_click_match_aid_list_real_seller_id_list"))
      ret.add(self._config.get("good_click_match_aid_list_seller_id_list", "good_click_match_aid_list_seller_id_list"))
      ret.add(self._config.get("good_click_match_aid_list_timestamp_list", "good_click_match_aid_list_timestamp_list"))
      ret.add(self._config.get("good_click_match_aid_list_category_list", "good_click_match_aid_list_category_list"))
      ret.add(self._config.get("good_click_match_aid_list_flow_type_list", "good_click_match_aid_list_flow_type_list"))
      ret.add(self._config.get("good_click_match_aid_list_from_list", "good_click_match_aid_list_from_list"))
      ret.add(self._config.get("good_click_match_aid_list_price_list", "good_click_match_aid_list_price_list"))
      return ret

class MerchantGsuAid2AidListGoodOrder2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aidlist_good_order_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr_list", "aId_list"))
      return ret
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_order_match_aid_list_item_id_list", "good_order_match_aid_list_item_id_list"))
      ret.add(self._config.get("good_order_match_aid_list_real_seller_id_list", "good_order_match_aid_list_real_seller_id_list"))
      ret.add(self._config.get("good_order_match_aid_list_seller_id_list", "good_order_match_aid_list_seller_id_list"))
      ret.add(self._config.get("good_order_match_aid_list_timestamp_list", "good_order_match_aid_list_timestamp_list"))
      ret.add(self._config.get("good_order_match_aid_list_category_list", "good_order_match_aid_list_category_list"))
      ret.add(self._config.get("good_order_match_aid_list_flow_type_list", "good_order_match_aid_list_flow_type_list"))
      ret.add(self._config.get("good_order_match_aid_list_from_list", "good_order_match_aid_list_from_list"))
      ret.add(self._config.get("good_order_match_aid_list_price_list", "good_order_match_aid_list_price_list"))
      return ret

class MerchantGsuAid2AidLive2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False
      if "if_colossus_v2_mode_attr" not in self._config:
        self._config["if_colossus_v2_mode_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_live_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      # colossus v2
      ret.add(self._config.get("colossus_live_item_timestamp_attr"))
      ret.add(self._config.get("colossus_live_item_author_id_attr"))
      ret.add(self._config.get("colossus_live_item_live_id_attr"))
      ret.add(self._config.get("colossus_live_item_play_time_attr"))
      ret.add(self._config.get("colossus_live_item_auto_play_time_attr"))
      ret.add(self._config.get("colossus_live_item_hetu_tag_channel_attr"))
      ret.add(self._config.get("colossus_live_item_cluster_id_attr"))
      ret.add(self._config.get("colossus_live_item_label_attr"))
      ret.add(self._config.get("colossus_live_item_order_price_attr"))
      ret.add(self._config.get("colossus_live_item_audience_count_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_match_aid_live_id_list", "live_match_aid_live_id_list"))
      ret.add(self._config.get("live_match_aid_aid_list", "live_match_aid_aid_list"))
      ret.add(self._config.get("live_match_aid_timestamp_list", "live_match_aid_timestamp_list"))
      ret.add(self._config.get("live_match_aid_play_time_list", "live_match_aid_play_time_list"))
      ret.add(self._config.get("live_match_aid_auto_play_time_list", "live_match_aid_auto_play_time_list"))
      ret.add(self._config.get("live_match_aid_hetu_tag_channel_list", "live_match_aid_hetu_tag_channel_list"))
      ret.add(self._config.get("live_match_aid_cluster_id_list", "live_match_aid_cluster_id_list"))
      ret.add(self._config.get("live_match_aid_label_list", "live_match_aid_label_list"))
      ret.add(self._config.get("live_match_aid_order_price_list", "live_match_aid_order_price_list"))
      ret.add(self._config.get("live_match_aid_audience_count_list", "live_match_aid_audience_count_list"))
      return ret


class MerchantGsuAid2AidListLive2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_play_time" not in self._config:
        self._config["filter_play_time"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False
      if "if_use_live_v4" not in self._config:
        self._config["if_use_live_v4"] = False
      if "if_recent_list" not in self._config:
        self._config["if_recent_list"] = False
      if "if_recent_click" not in self._config:
        self._config["if_recent_click"] = False
      if "if_action_follow" not in self._config:
        self._config["if_action_follow"] = False
      if "if_click_cart" not in self._config:
        self._config["if_click_cart"] = False
      if "if_common_target_attr" not in self._config:
        self._config["if_common_target_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aidlist_live_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("common_target_attr_list", "common_target_attr_list"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr_list", "aId_list"))
      return ret
  
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_match_aid_list_live_id_list", "live_match_aid_list_live_id_list"))
      ret.add(self._config.get("live_match_aid_list_aid_list", "live_match_aid_list_aid_list"))
      ret.add(self._config.get("live_match_aid_list_timestamp_list", "live_match_aid_list_timestamp_list"))
      ret.add(self._config.get("live_match_aid_list_play_time_list", "live_match_aid_list_play_time_list"))
      ret.add(self._config.get("live_match_aid_list_auto_play_time_list", "live_match_aid_list_auto_play_time_list"))
      ret.add(self._config.get("live_match_aid_list_hetu_tag_channel_list", "live_match_aid_list_hetu_tag_channel_list"))
      ret.add(self._config.get("live_match_aid_list_cluster_id_list", "live_match_aid_list_cluster_id_list"))
      ret.add(self._config.get("live_match_aid_list_label_list", "live_match_aid_list_label_list"))
      ret.add(self._config.get("live_match_aid_list_order_price_list", "live_match_aid_list_order_price_list"))
      ret.add(self._config.get("live_match_aid_list_audience_count_list", "live_match_aid_list_audience_count_list"))
      return ret

class MerchantGsuAid2AidSessionLiveEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "session_num" not in self._config:
        self._config["session_num"] = 50
      if "session_time" not in self._config:
        self._config["session_time"] = 4320
      if "session_break_num" not in self._config:
        self._config["session_break_num"] = 200
      if "filter_play_time" not in self._config:
        self._config["filter_play_time"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = True
      if "if_recent_click" not in self._config:
        self._config["if_recent_click"] = False
      if "if_click_cart" not in self._config:
        self._config["if_click_cart"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_session_live"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret

class MerchantCountAidLiveEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_count_aid_live"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret

class MerchantGsuAid2AidLiveCommonEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "limit_per_aid_num" not in self._config:
        self._config["limit_per_aid_num"] = 10
      if "filter_play_time" not in self._config:
        self._config["filter_play_time"] = 10
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aidlist_live_common"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
  
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId_list"))
      return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_common_aid_list_live_id_list", "live_common_aid_list_live_id_list"))
      ret.add(self._config.get("live_common_aid_list_aid_list", "live_common_aid_list_aid_list"))
      ret.add(self._config.get("live_common_aid_list_timestamp_list", "live_common_aid_list_timestamp_list"))
      ret.add(self._config.get("live_common_aid_list_play_time_list", "live_common_aid_list_play_time_list"))
      ret.add(self._config.get("live_common_aid_list_auto_play_time_list", "live_common_aid_list_auto_play_time_list"))
      ret.add(self._config.get("live_common_aid_list_hetu_tag_channel_list", "live_common_aid_list_hetu_tag_channel_list"))
      ret.add(self._config.get("live_common_aid_list_cluster_id_list", "live_common_aid_list_cluster_id_list"))
      ret.add(self._config.get("live_common_aid_list_label_list", "live_common_aid_list_label_list"))
      ret.add(self._config.get("live_common_aid_list_order_price_list", "live_common_aid_list_order_price_list"))
      ret.add(self._config.get("live_common_aid_list_audience_count_list", "live_common_aid_list_audience_count_list"))
      return ret

class MerchantGsuAid2AidGoodClick2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_good_click_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_click_match_aid_item_id_list", "good_click_match_aid_item_id_list"))
      ret.add(self._config.get("good_click_match_aid_real_seller_id_list", "good_click_match_aid_real_seller_id_list"))
      ret.add(self._config.get("good_click_match_aid_seller_id_list", "good_click_match_aid_seller_id_list"))
      ret.add(self._config.get("good_click_match_aid_timestamp_list", "good_click_match_aid_timestamp_list"))
      ret.add(self._config.get("good_click_match_aid_category_list", "good_click_match_aid_category_list"))
      ret.add(self._config.get("good_click_match_aid_flow_type_list", "good_click_match_aid_flow_type_list"))
      ret.add(self._config.get("good_click_match_aid_from_list", "good_click_match_aid_from_list"))
      ret.add(self._config.get("good_click_match_aid_price_list", "good_click_match_aid_price_list"))
      return ret


class MerchantGoodsRsSideInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_goods_rs_sideinfo_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("good_click_item_id_list_extend", "good_click_item_id_list_extend"))
    ret.add(self._config.get("good_click_timestamp_list_extend", "good_click_timestamp_list_extend"))
    ret.add(self._config.get("good_order_item_id_list_extend", "good_order_item_id_list_extend"))
    ret.add(self._config.get("good_order_create_order_time_list_extend", "good_order_create_order_time_list_extend"))
    ret.add(self._config.get("realtime_cart_iid", "realtime_cart_iid"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_timestamp_list", "colossus_rs_timestamp_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_category_list", "colossus_rs_category_list"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("cate1_attr", "iCate1Id"))
    ret.add(self._config.get("cate2_attr", "iCate2Id"))
    ret.add(self._config.get("cate3_attr", "iCate3Id"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_1_list", "colossus_rs_category_1_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_2_list", "colossus_rs_category_2_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_3_list", "colossus_rs_category_3_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_lagV1_list", "colossus_rs_lagV1_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_count_index_list", "colossus_rs_count_index_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_lagV2_list", "colossus_rs_lagV2_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_ts_count_list", "colossus_rs_ts_count_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_id_count_list", "colossus_rs_id_count_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_is_click_cross_list", "colossus_rs_is_click_cross_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_is_buy_cross_list", "colossus_rs_is_buy_cross_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("colossus_rs_category_1_list", "colossus_rs_category_1_list"))
    ret.add(self._config.get("colossus_rs_category_2_list", "colossus_rs_category_2_list"))
    ret.add(self._config.get("colossus_rs_category_3_list", "colossus_rs_category_3_list"))
    ret.add(self._config.get("colossus_rs_lagV1_list", "colossus_rs_lagV1_list"))
    ret.add(self._config.get("colossus_rs_is_click_list", "colossus_rs_is_click_list"))
    ret.add(self._config.get("colossus_rs_is_cart_list", "colossus_rs_is_cart_list"))
    ret.add(self._config.get("colossus_rs_is_buy_list", "colossus_rs_is_buy_list"))
    ret.add(self._config.get("colossus_rs_count_index_list", "colossus_rs_count_index_list"))
    ret.add(self._config.get("colossus_rs_lagV2_list", "colossus_rs_lagV2_list"))
    ret.add(self._config.get("colossus_rs_ts_count_list", "colossus_rs_ts_count_list"))
    ret.add(self._config.get("colossus_rs_is_click_cross_list", "colossus_rs_is_click_cross_list"))
    ret.add(self._config.get("colossus_rs_is_buy_cross_list", "colossus_rs_is_buy_cross_list"))
    return ret

class MerchantGoodsRsSideInfoForGoodsSlideEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_goods_rs_sideinfo_for_goods_slide_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("good_click_item_id_list_extend", "good_click_item_id_list_extend"))
    ret.add(self._config.get("good_click_timestamp_list_extend", "good_click_timestamp_list_extend"))
    ret.add(self._config.get("good_order_item_id_list_extend", "good_order_item_id_list_extend"))
    ret.add(self._config.get("good_order_create_order_time_list_extend", "good_order_create_order_time_list_extend"))
    ret.add(self._config.get("realtime_cart_iid", "realtime_cart_iid"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_timestamp_list", "colossus_rs_timestamp_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("input_prefix_attr", "origin_") + self._config.get("colossus_rs_category_list", "colossus_rs_category_list"))

    ret.add(self._config.get("common_cate1_attr", "cate1_id"))
    ret.add(self._config.get("common_cate2_attr", "cate2_id"))
    ret.add(self._config.get("common_cate3_attr", "cate3_id"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("cate1_attr", "iCate1Id"))
    ret.add(self._config.get("cate2_attr", "iCate2Id"))
    ret.add(self._config.get("cate3_attr", "iCate3Id"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_exposure_time_list", "colossus_rs_exposure_time_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_1_list", "colossus_rs_category_1_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_2_list", "colossus_rs_category_2_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_category_3_list", "colossus_rs_category_3_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_lagV1_list", "colossus_rs_lagV1_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_count_index_list", "colossus_rs_count_index_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_lagV2_list", "colossus_rs_lagV2_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_ts_count_list", "colossus_rs_ts_count_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_id_count_list", "colossus_rs_id_count_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_is_click_cross_list", "colossus_rs_is_click_cross_list"))
    ret.add(self._config.get("gsu_prefix_attr", "gsu_") + self._config.get("colossus_rs_is_buy_cross_list", "colossus_rs_is_buy_cross_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("colossus_rs_exposure_time_list", "colossus_rs_exposure_time_list"))
    ret.add(self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("colossus_rs_category_1_list", "colossus_rs_category_1_list"))
    ret.add(self._config.get("colossus_rs_category_2_list", "colossus_rs_category_2_list"))
    ret.add(self._config.get("colossus_rs_category_3_list", "colossus_rs_category_3_list"))
    ret.add(self._config.get("colossus_rs_lagV1_list", "colossus_rs_lagV1_list"))
    ret.add(self._config.get("colossus_rs_is_click_list", "colossus_rs_is_click_list"))
    ret.add(self._config.get("colossus_rs_is_cart_list", "colossus_rs_is_cart_list"))
    ret.add(self._config.get("colossus_rs_is_buy_list", "colossus_rs_is_buy_list"))
    ret.add(self._config.get("colossus_rs_count_index_list", "colossus_rs_count_index_list"))
    ret.add(self._config.get("colossus_rs_lagV2_list", "colossus_rs_lagV2_list"))
    ret.add(self._config.get("colossus_rs_ts_count_list", "colossus_rs_ts_count_list"))
    ret.add(self._config.get("colossus_rs_is_click_cross_list", "colossus_rs_is_click_cross_list"))
    ret.add(self._config.get("colossus_rs_is_buy_cross_list", "colossus_rs_is_buy_cross_list"))

    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_item_id_list", "colossus_rs_item_id_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_pagecode_id_list", "colossus_rs_pagecode_id_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_uniform_spu_id_list", "colossus_rs_uniform_spu_id_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_exposure_ratio_list", "colossus_rs_exposure_ratio_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_exposure_time_list", "colossus_rs_exposure_time_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_seller_id_list", "colossus_rs_seller_id_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_category_1_list", "colossus_rs_category_1_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_category_2_list", "colossus_rs_category_2_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_category_3_list", "colossus_rs_category_3_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_lagV1_list", "colossus_rs_lagV1_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_count_index_list", "colossus_rs_count_index_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_lagV2_list", "colossus_rs_lagV2_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_ts_count_list", "colossus_rs_ts_count_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_id_count_list", "colossus_rs_id_count_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_is_click_cross_list", "colossus_rs_is_click_cross_list"))
    ret.add(self._config.get("common_gsu_prefix_attr", "common_gsu_") + self._config.get("colossus_rs_is_buy_cross_list", "colossus_rs_is_buy_cross_list"))
    return ret

class MerchantExtractCartItemMatchAttrsEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "extract_cart_item_match_attrs"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("retr_item_id_list"))
      ret.add(self._config.get("recent_cate3_id_list"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("cart_item_id_list", "sCartItemList"))
      ret.add(self._config.get("cart_item_cate3_id_list", "sCartItemCate3IdList"))
      ret.add(self._config.get("cart_item_is_interpreting_list", "sCartItemIsInterpretingList"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("item_score", "item_score"))
      ret.add(self._config.get("match_index_list", "match_index_list"))
      ret.add(self._config.get("top_item_id_list", "top_item_id_list"))
      ret.add(self._config.get("top_cate3_id_list", "top_cate3_id_list"))
      ret.add(self._config.get("match_at_top_flag", "mc_csqf_match_at_top_channel"))
      ret.add(self._config.get("cate_cover_flag", "mc_csqf_cate_cover_channel"))
      ret.add(self._config.get("interpreting_item_index", "interpreting_item_index"))
      return ret

class MerchantExtractItemCateMatchScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "extract_item_cate_match_score"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("input_item_id_list_attr"))
      ret.add(self._config.get("input_cate1_id_list_attr"))
      ret.add(self._config.get("input_cate2_id_list_attr"))
      ret.add(self._config.get("input_cate3_id_list_attr"))
      for attr in ['item_match_weight', 'cate1_match_weight', 'cate2_match_weight', 'cate3_match_weight']:
        ret.update(self.extract_dynamic_params(self._config.get(attr)))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("cart_item_id_list_attr", "sCartItemList"))
      ret.add(self._config.get("cart_item_cate1_id_list_attr", "sCartItemCate1IdList"))
      ret.add(self._config.get("cart_item_cate2_id_list_attr", "sCartItemCate2IdList"))
      ret.add(self._config.get("cart_item_cate3_id_list_attr", "sCartItemCate3IdList"))
      ret.add(self._config.get("cart_item_is_interpreting_list_attr", "sCartItemIsInterpretingList"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("output_live_match_score_attr", "output_live_match_score"))
      ret.add(self._config.get("output_item_match_score_attr", "output_item_match_score"))
      ret.add(self._config.get("output_cate1_match_score_attr", "output_cate1_match_score"))
      ret.add(self._config.get("output_cate2_match_score_attr", "output_cate2_match_score"))
      ret.add(self._config.get("output_cate3_match_score_attr", "output_cate3_match_score"))
      return ret

class MerchantGsuCate2CateGoodClickEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "cate2cate_good_click_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("cate1_attr", "iCate1Id"))
      ret.add(self._config.get("cate2_attr", "iCate2Id"))
      ret.add(self._config.get("cate3_attr", "iCate3Id"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_click_cate2cate_item_id_list", "good_click_cate2cate_item_id_list"))
      ret.add(self._config.get("good_click_cate2cate_real_seller_id_list", "good_click_cate2cate_real_seller_id_list"))
      ret.add(self._config.get("good_click_cate2cate_seller_id_list", "good_click_cate2cate_seller_id_list"))
      ret.add(self._config.get("good_click_cate2cate_timestamp_list", "good_click_cate2cate_timestamp_list"))
      ret.add(self._config.get("good_click_cate2cate_lag_list", "good_click_cate2cate_lag_list"))
      ret.add(self._config.get("good_click_cate2cate_category_list", "good_click_cate2cate_category_list"))
      ret.add(self._config.get("good_click_cate2cate_cate1_list", "good_click_cate2cate_cate1_list"))
      ret.add(self._config.get("good_click_cate2cate_cate2_list", "good_click_cate2cate_cate2_list"))
      ret.add(self._config.get("good_click_cate2cate_cate3_list", "good_click_cate2cate_cate3_list"))
      ret.add(self._config.get("good_click_cate2cate_click_flow_type_list", "good_click_cate2cate_click_flow_type_list"))
      ret.add(self._config.get("good_click_cate2cate_click_from_list", "good_click_cate2cate_click_from_list"))
      ret.add(self._config.get("good_click_cate2cate_real_price_list", "good_click_cate2cate_real_price_list"))
      ret.add(self._config.get("good_click_cate2cate_click_index_list", "good_click_cate2cate_click_index_list"))
      return ret


class MerchantGsuCate2CateGoodOrderEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num_attr" not in self._config:
        self._config["limit_num_attr"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "cate2cate_good_order_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("cate1_attr", "iCate1Id"))
      ret.add(self._config.get("cate2_attr", "iCate2Id"))
      ret.add(self._config.get("cate3_attr", "iCate3Id"))
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_order_cate2cate_item_id_list", "good_order_cate2cate_item_id_list"))
      ret.add(self._config.get("good_order_cate2cate_real_seller_id_list", "good_order_cate2cate_real_seller_id_list"))
      ret.add(self._config.get("good_order_cate2cate_seller_id_list", "good_order_cate2cate_seller_id_list"))
      ret.add(self._config.get("good_order_cate2cate_timestamp_list", "good_order_cate2cate_timestamp_list"))
      ret.add(self._config.get("good_order_cate2cate_lag_list", "good_order_cate2cate_lag_list"))
      ret.add(self._config.get("good_order_cate2cate_category_list", "good_order_cate2cate_category_list"))
      ret.add(self._config.get("good_order_cate2cate_cate1_list", "good_order_cate2cate_cate1_list"))
      ret.add(self._config.get("good_order_cate2cate_cate2_list", "good_order_cate2cate_cate2_list"))
      ret.add(self._config.get("good_order_cate2cate_cate3_list", "good_order_cate2cate_cate3_list"))
      ret.add(self._config.get("good_order_cate2cate_order_flow_type_list", "good_order_cate2cate_order_flow_type_list"))
      ret.add(self._config.get("good_order_cate2cate_order_from_list", "good_order_cate2cate_order_from_list"))
      ret.add(self._config.get("good_order_cate2cate_real_price_list", "good_order_cate2cate_real_price_list"))
      ret.add(self._config.get("good_order_cate2cate_order_index_list", "good_order_cate2cate_order_index_list"))
      return ret


    
class MerchantGsuCateSearchGoodsClickEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "cate_search_good_click"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "iCate2Id"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_click_match_cate_item_id_list", "good_click_match_cate_item_id_list"))
      ret.add(self._config.get("good_click_match_cate_real_seller_id_list", "good_click_match_cate_real_seller_id_list"))
      ret.add(self._config.get("good_click_match_cate_seller_id_list", "good_click_match_cate_seller_id_list"))
      ret.add(self._config.get("good_click_match_cate_timestamp_list", "good_click_match_cate_timestamp_list"))
      ret.add(self._config.get("good_click_match_cate_category_list", "good_click_match_cate_category_list"))
      ret.add(self._config.get("good_click_match_cate_cate1_list", "good_click_match_cate_cate1_list"))
      ret.add(self._config.get("good_click_match_cate_cate2_list", "good_click_match_cate_cate2_list"))
      ret.add(self._config.get("good_click_match_cate_cate3_list", "good_click_match_cate_cate3_list"))
      ret.add(self._config.get("good_click_match_cate_flow_type_list", "good_click_match_cate_flow_type_list"))
      ret.add(self._config.get("good_click_match_cate_from_list", "good_click_match_cate_from_list"))
      ret.add(self._config.get("good_click_match_cate_price_list", "good_click_match_cate_price_list"))
      return ret
    
class MerchantGsuAid2AidGoodOrder2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_good_order_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_order_match_aid_item_id_list", "good_order_match_aid_item_id_list"))
      ret.add(self._config.get("good_order_match_aid_real_seller_id_list", "good_order_match_aid_real_seller_id_list"))
      ret.add(self._config.get("good_order_match_aid_seller_id_list", "good_order_match_aid_seller_id_list"))
      ret.add(self._config.get("good_order_match_aid_timestamp_list", "good_order_match_aid_timestamp_list"))
      ret.add(self._config.get("good_order_match_aid_category_list", "good_order_match_aid_category_list"))
      ret.add(self._config.get("good_order_match_aid_flow_type_list", "good_order_match_aid_flow_type_list"))
      ret.add(self._config.get("good_order_match_aid_from_list", "good_order_match_aid_from_list"))
      ret.add(self._config.get("good_order_match_aid_price_list", "good_order_match_aid_price_list"))
      return ret

class MerchantGsuAid2AidGoodCateOrder2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_good_cate_order_v2"
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      ret.add(self._config.get("target_attr_cate1", "aMainGoods90dCateL1IDList"))
      ret.add(self._config.get("target_attr_cate2", "aMainGoods90dCateL2IDList"))
      ret.add(self._config.get("target_attr_cate3", "aMainGoods90dCateL3IDList"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_cate_order_match_aid_item_id_list", "good_cate_order_match_aid_item_id_list"))
      ret.add(self._config.get("good_cate_order_match_aid_real_seller_id_list", "good_cate_order_match_aid_real_seller_id_list"))
      ret.add(self._config.get("good_cate_order_match_aid_seller_id_list", "good_cate_order_match_aid_seller_id_list"))
      ret.add(self._config.get("good_cate_order_match_aid_timestamp_list", "good_cate_order_match_aid_timestamp_list"))
      ret.add(self._config.get("good_cate_order_match_aid_category_list", "good_cate_order_match_aid_category_list"))
      ret.add(self._config.get("good_cate_order_match_aid_flow_type_list", "good_cate_order_match_aid_flow_type_list"))
      ret.add(self._config.get("good_cate_order_match_aid_from_list", "good_cate_order_match_aid_from_list"))
      ret.add(self._config.get("good_cate_order_match_aid_price_list", "good_cate_order_match_aid_price_list"))
      return ret
class MerchantGsuAid2AidGoodCateClick2Enricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_good_cate_click_v2"
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      ret.add(self._config.get("target_attr_cate1", "aMainGoods90dCateL1IDList"))
      ret.add(self._config.get("target_attr_cate2", "aMainGoods90dCateL2IDList"))
      ret.add(self._config.get("target_attr_cate3", "aMainGoods90dCateL3IDList"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("good_cate_click_match_aid_item_id_list", "good_cate_click_match_aid_item_id_list"))
      ret.add(self._config.get("good_cate_click_match_aid_real_seller_id_list", "good_cate_click_match_aid_real_seller_id_list"))
      ret.add(self._config.get("good_cate_click_match_aid_seller_id_list", "good_cate_click_match_aid_seller_id_list"))
      ret.add(self._config.get("good_cate_click_match_aid_timestamp_list", "good_cate_click_match_aid_timestamp_list"))
      ret.add(self._config.get("good_cate_click_match_aid_category_list", "good_cate_click_match_aid_category_list"))
      ret.add(self._config.get("good_cate_click_match_aid_flow_type_list", "good_cate_click_match_aid_flow_type_list"))
      ret.add(self._config.get("good_cate_click_match_aid_from_list", "good_cate_click_match_aid_from_list"))
      ret.add(self._config.get("good_cate_click_match_aid_price_list", "good_cate_click_match_aid_price_list"))
      return ret
class MerchantGsuAid2AidLiveCateEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_live_cate"
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("result_key_field", "author_id_list"))
      ret.add(self._config.get("result_cate_field", "aid_features"))
      ret.add(self._config.get("filter_by", "timestamp_list"))
      for i in self._config.get("side_field_config"):
        ret.add(i)
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      ret.add(self._config.get("target_attr_cate1", "aMainGoods90dCateL1IDList"))
      ret.add(self._config.get("target_attr_cate2", "aMainGoods90dCateL2IDList"))
      ret.add(self._config.get("target_attr_cate3", "aMainGoods90dCateL3IDList"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      for i in self._config.get("side_field_config"):
        ret.add(self._config.get("side_field_config")[i])
      return ret

class ShopCustomerDeduplicateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "intent_deduplicate"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attr_list = ["intent_id", "intent", "tenant_id", "scene_id"]
    for v in attr_list:
      attr = self._config.get(v, "")
      if attr:
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_dedpulicate_attr", ""))
    return attrs

class MerchantExtBuy(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ext_buy"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr", ""))
    return attrs

class MerchantCascadingConsistenceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_cascading_consistence"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mc_truncate_num_attr", ""))
    attrs.add(self._config.get("mc_con_extra_item_cnt_attr", ""))
    attrs.add(self._config.get("mc_con_insert_item_cnt_attr", ""))
    attrs.add(self._config.get("mc_con_enable_insert_item_attr", ""))
    attrs.add(self._config.get("mc_con_redis_gpm_score_threshold_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mc_con_redis_gpm_score_attr", ""))
    attrs.add(self._config.get("mc_con_redis_gpm_flag_attr", ""))
    
    return attrs

class MerchantStringListSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_split_string_list"

  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["input_common_attr"] }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { v["export_common_attr"] for v in self._config["output_attr_configs"] if "export_common_attr" in v}

class MerchantGenEnsembleSeedSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gen_ensemble_seed_sequence"

class MerchantMixRankGenSeedSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_mix_rank_gen_seed_sequence"
    
class MerchantMixRankGenSeedSequenceV1Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_mix_rank_gen_seed_sequence_v1"
    
class MerchantMixRankNewMixGeneratorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_mix_rank_new_mix_generator_enricher"
    
class MerchantEmbdSimVariantEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_embd_sim_variant_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("attr_name", ""))
      attrs.add(self._config.get("prevs_attr_name", ""))
      return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        ret = set()
        ret.add(self._config.get("output_attr_name", "embd_variant_pos"))
        return ret

class ListwiseSequeceDedupEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "listwise_sequence_dedup_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      return attrs
    
class MerchantMixRankGenSeedSequenceV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_mix_rank_gen_seed_sequence_v2"

class MerchantMixRankGenPermutationsSeedSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_mix_rank_gen_permutations_seed_sequence"

class MerchantMixRankGenEgpmWithGuassionPermSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_mix_rank_gen_egpm_with_guassion_perm_sequence_enricher"

class MerchantMixRankGenSeedSequenceBeamSearchEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_mix_rank_gen_seed_sequence_beam_search_enricher"

class MerchantMixRankGenSeedSequenceTopkSamplingEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_mix_rank_gen_seed_sequence_topk_sampling_enricher"

class MerchantMixRankGenSeedSequenceWeightRangeEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_mix_rank_gen_seed_sequence_weight_range_enricher"

class MerchantMixRankGenSeedSequenceModelEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_mix_rank_gen_seed_sequence_model_enricher"

class MerchantListwiseGeneratorCandidateAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_listwise_generator_candidate_attr_enricher"
    
class MerchantListwiseGeneratorCandidateAttrV1Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_listwise_generator_candidate_attr_v1_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      return attrs

class MerchantLtvByColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ltv_by_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set(self._config.get("last_p_exp_tag_list", []))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("author_id_attr"))
    ret.add(self._config.get("ltv_length", 259200))

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "pay_amt", "reward_times", "item_ids", "last_p_exp_tags"
    }

class MerchantListwiseSeqAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_listwise_seq_attr_enrich"
    
class MerchantListwiseSeqAttrV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_listwise_seq_attr_v2_enrich"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      return attrs
    
class MerchantListwiseNewSeqAttrV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_listwise_new_seq_attr_v2_enrich"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      attrs.add("fr_item_id_embedding")
      return attrs

class ListwiseUnifyBonusEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "listwise_unify_bonus_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      return attrs
    
class ListwiseUnifyBonusV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "listwise_unify_bonus_v2_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("seq_item_attr_name", ""))
      return attrs
    
class MerchantBusinessFlagEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_business_flag_enricher"
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("output_attr_name", "merchant_business_flag"))
      return attrs
    
class MerchantListwiseSequencePackEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_listwise_sequence_pack_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("sequence_attr_name", "generated_variant_lists"))
      return attrs

class MerchantGenOriginalSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gen_original_sequence"

class MerchantGenPRMSequenceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gen_prm_sequence_enricher"

    @property
    @strict_types
    def output_item_attrs(self)->set:
      attrs = set()
      attrs.add(self._config.get("output_list_score_attr_name", ""))
      attrs.add(self._config.get("output_item_score_attr_name", ""))
      return attrs
    
class MerchantGenPRMSequenceV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gen_prm_sequence_v2_enricher"
    
    @property
    @strict_types
    def output_item_attrs(self)->set:
      attrs = set()
      prm_score_mappings = self._config.get("prm_score_mappings", [])
      for score in prm_score_mappings:
        attrs.add(score["to_attr"])     

      list_score_mappings = self._config.get("list_score_mappings", [])
      for score in list_score_mappings:
        attrs.add(score["to_attr"])
      return attrs

class MerchantMultiSortPostEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_multi_sort_post"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["common_distance_ptr_attr"])
      ret.add(self._config["colossus_pid_attr"])
      return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      for key in ["item_click_author_id", "item_click_time_stamp"]:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      if self._config.get("slot_as_attr_name", False):
        ret.update(map(str, self._config.get("mio_slots_id", [100, 101, 102, 103, 104, 105, 106, 107])))
      else:
        for key in ["output_sign_attr", "output_slot_attr"]:
          ret.add(self._config[key])
      return ret

class MerchantLtvWatchEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_watch_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.add(self._config["author_id_attr"])
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config['watch_attr'])
      return ret
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config['ltv_pay_amt'])
      ret.add(self._config['ltv_pay_cnt'])
      return ret

class MerchantLtvCartEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_cart_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.add(self._config["author_id_attr"])
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      return ret
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      return ret

class MerchantLtvSliceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_slice_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["slice_input_attr"])
      ret.add(self._config["date_attr"])
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      return ret
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      return ret

class MerchantLtvRetargetEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_retarget_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_resp_attr"])
      ret.add(self._config["author_id_attr"])
      ret.add(self._config["ltv_length"])
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      return ret
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      return ret

class MerchantCommonRodisEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "get_label_from_rodis"
  
    @strict_types
    def is_async(self) -> bool:
      return True
  
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      payload_config = self._config["payload_desc"]
      for key in ["len_limit", "after_ts", "before_ts"]:
        if key in payload_config:
          attrs.update(self.extract_dynamic_params(payload_config[key]))
      return attrs
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_common_attr", ""))
        return attrs

class MerchantLtvByRodisEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_by_rodis"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set(self._config.get("last_p_exp_tag_list", []))
      ret.add(self._config.get("rodis_resp_attr"))
      ret.add(self._config.get("author_id_attr"))
      ret.add(self._config.get("ltv_length", 259200))

      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      return {
          "pay_amt", "reward_times", "item_ids", "last_p_exp_tags", 'ltv_pay_amt', 'ltv_pay_cnt'
      }

class MerchantGsuPhotoClusterParsePbEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_photo_cluster_parse_pb"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsuPhotoClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_photo_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsuAidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_aid_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["aid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantColdStartGenBidMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_cold_start_gen_bid_message_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("input_common_attr_info")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config["input_item_attr_info"])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_common_json"])
    return ret

class MerchantGsuRetargetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_retarget_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["aid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["retarget_attr"])
    return ret

class MerchantCateRetargetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_cate_retarget_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["aid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["retarget_attr"])
    return ret

class MerchantLtvByCateEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_by_cate"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set(self._config.get("last_p_exp_tag_list", []))
      ret.add(self._config.get("rodis_resp_attr"))
      ret.add(self._config.get("author_id_attr"))
      ret.add(self._config.get("ltv_length", 259200))

      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      return {
          "pay_amt", "pay_cnt", 'ltv_pay_amt', 'ltv_pay_cnt'
      }


class MerchantUserLtvEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_user_ltv"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set(self._config.get("prefix"))
      ret.add(self._config.get("follow_list_attr"))
      ret.add(self._config.get("cluster_name"))
      ret.add(self._config.get("cache_bits"))
      ret.add(self._config.get("cache_delay_delete_ms"))
      ret.add(self._config.get("cache_expire_second"))
      ret.add(self._config.get("user_bias"))
      ret.add(self._config.get("dim"))
      return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attr_list = ["author_id_attr"]
      for v in attr_list:
        attr = self._config.get(v, "")
        if attr:
          attrs.add(attr)
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      return {
          "user_ltv"
      }

class MerchantLtvByExtEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_by_ext"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("rodis_resp_attr"))
      ret.add(self._config.get("author_id_attr"))
      ret.add(self._config.get("ltv_length", 259200))
      return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      return {
         self._config.get("click_cart"),
         self._config.get("click_item"),
         self._config.get("comment_count"),
         self._config.get("like_count")
      }


class MerchantLiveGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "play_time_attr", "page_type_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in [
      "output_sign_attr", "output_slot_attr", "hard_match_output_slot_attr", "hard_match_output_sign_attr",
      "output_item_colossus_pid_attr", "output_item_distance_attr", "output_item_raw_distance_attr"
    ]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantGoodsGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_goods_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in [
      "timestamp_attr",
      "field1_attr",
      "field2_attr",
      "field3_attr",
      "field4_attr",
      "field5_attr",
      "field6_attr",
      "field7_attr",
      "field8_attr",
      "field9_attr",
      "field10_attr",
      "field11_attr",
      "field12_attr",
      "field13_attr",
      "field14_attr",
      "field15_attr",
      "field16_attr",
      "field17_attr",
      "field18_attr",
      "field19_attr",
      "field20_attr",
      "category_attr",
      "real_price_attr",
      "origin_price_attr",
      ]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_key_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in [
      "output_sign_attr", "output_slot_attr","output_item_colossus_distance_attr",
    ]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MerchantColossusDedupEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_colossus_dedup_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config["colossus_raw_itemid_attr"])
      ret.add(self._config["colossus_itemkey_attr"])
      return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      for key in ["timestamp_attr", "playtime_attr"]:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_itemkey_attr"]))
      return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("output_deduped_colossus_itemkey_attr", ""))
      return attrs

class ItemPredictByKmlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_predict_by_kml_grpc_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_caller_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("model_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("common_attrs_oredr_kconf")))
    attrs.update(self.extract_dynamic_params(self._config.get("item_attrs_oredr_kconf")))

    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_item_attrs"))
    return attrs

class MerchantLiveGsuPostGatherEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_live_gsu_post_gather_enricher"

class MerchantLiveGsuAid2aidWithSlotEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_aid2aid_with_slot"

@strict_types
def extract_attr_names(config: list, field: str) -> set:
  ret = set()
  for c in config:
    if isinstance(c, str):
      ret.add(c)
    elif isinstance(c, dict):
      ret.add(c[field])
    else:
      raise ArgumentError(f"该 list 中存在不支持的配置类型 {type(c)}: {config}")
  return ret


class MerchantDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_delegate_enricher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_item_attrs", []), "as")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")


class MerchantGsuGoodClickSessionEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_good_click_session"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

class MerchantVideoPlaySessionEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_video_play_session"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      if not self._config.get("from_colossus_sim_v2", False):
        ret.add(self._config["colossus_resp_attr"])
      else:
        for key in ["photo_id_from", "author_id_from", "play_time_from",
                    "duration_from", "channel_from", "label_from",
                    "timestamp_from", "spu_id_from", "category_from",
                    "from_colossus_sim_v2"
                    ]:
          ret.add(self._config[key])
          #if key in self._config:
          #  ret.add(self._config[key])
      return ret


class MerchantGsuGoodOrderSessionEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_gsu_good_order_session"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

class MerchantGoodRecentSessionEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False
      if "colossus_type" not in self._config:
        self._config["colossus_type"] = "goodclick"

    @classmethod
    def get_type_alias(cls) -> str:
      return "merchant_good_click_recent_session"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("aId", "aId"))
      ret.add(self._config.get("c1id", "c1id"))
      return ret

class MerchantLtvFeatEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_feat_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("last_p_exp_tag_list"))
      ret.add(self._config.get("rodis_resp_attr"))
      ret.add(self._config.get("author_id_attr"))
      return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      return {
         self._config.get('aid_feat', 'aid_feat'),
         self._config.get('iid_feat', 'iid_feat'),
         self._config.get('gmv_feat', 'gmv_feat'),
         self._config.get('ordr_feat', 'ordr_feat'),
         self._config.get('cat1_feat', 'cat1_feat'),
         self._config.get('cat2_feat', 'cat2_feat'),
         self._config.get('cat3_feat', 'cat3_feat'),
         self._config.get('lag_feat', 'lag_feat'),
         self._config.get('ptag_feat', 'ptag_feat'),
      }

class MerchantLtvFeatExtEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_feat_ext_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("rodis_resp_attr"))
      ret.add(self._config.get("author_id_attr"))
      return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      return {
         self._config.get('aid_feat', 'aid_feat'),
         self._config.get('cart_feat', 'cart_feat'),
         self._config.get('item_feat', 'item_feat'),
         self._config.get('lag_feat', 'lag_feat'),
      }

class MerchantGsuA2aExtEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50
      if "filter_future_attr" not in self._config:
        self._config["filter_future_attr"] = True
      if "if_parse_to_pb_attr" not in self._config:
        self._config["if_parse_to_pb_attr"] = False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "aid2aid_live_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("live_match_aid_live_id_list", "live_match_aid_live_id_list"))
      ret.add(self._config.get("live_match_aid_aid_list", "live_match_aid_aid_list"))
      ret.add(self._config.get("live_match_aid_timestamp_list", "live_match_aid_timestamp_list"))
      ret.add(self._config.get("live_match_aid_play_time_list", "live_match_aid_play_time_list"))
      ret.add(self._config.get("live_match_aid_auto_play_time_list", "live_match_aid_auto_play_time_list"))
      ret.add(self._config.get("live_match_aid_hetu_tag_channel_list", "live_match_aid_hetu_tag_channel_list"))
      ret.add(self._config.get("live_match_aid_cluster_id_list", "live_match_aid_cluster_id_list"))
      ret.add(self._config.get("live_match_aid_label_list", "live_match_aid_label_list"))
      ret.add(self._config.get("live_match_aid_order_price_list", "live_match_aid_order_price_list"))
      ret.add(self._config.get("live_match_aid_audience_count_list", "live_match_aid_audience_count_list"))
      return ret

class MerchantLtvSeq2seqEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_ltv_seq2seq_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("feats_behind_attr", "feats_behind_attr"))
      ret.add(self._config.get("feats_ahead_attr", "feats_ahead_attr"))
      return ret

class MerchantCouponFeatureEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_coupon_feature"

class MerchantCouponGsuEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_coupon_gsu_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("colossus_resp_attr"))
      return ret
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      prefix = self._config.get("prefix", "coupon_gsu_")
      ret.add(prefix + 'id_list')
      ret.add(prefix + 'aid_list')
      ret.add(prefix + 'rcv_time_list')
      ret.add(prefix + 'use_time_list')
      ret.add(prefix + 'status_list')
      ret.add(prefix + 'type_list')
      ret.add(prefix + 'coupon_price_list')
      ret.add(prefix + 'coupon_base_list')
      ret.add(prefix + 'item_price_list')
      ret.add(prefix + 'tmplt_id_list')
      return ret

class GetCommonAttrsFromRequestEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
    
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "get_common_attrs_from_request"

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      ret = set()
      for key in ["save_attr_names_to_attr"]:
        if key in self._config:
          ret.add(self._config[key])
      return ret

class RecoInferEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "infer_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    attrs.add(self._config.get("model_family_name") + "_mm_origin_infer_label_and_scores")
    #attrs.add(self._config.get("predict_ab_parameter") + "_mm_origin_infer_labels")
    return attrs
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs= extract_attr_names(self._config.get("recv_common_attrs", []), "as")
    attrs.add(self._config.get("model_family_name") + "_infer_meta")
    return attrs

class MerchantConsistentRankingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_consistent_ranking_ranker"
  
  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["revenue_params", "revenue_weights", "content_params", "content_weights", "content_coeff", "content_bias", "bonus_weight", "merchant_score_weight", "merchant_score_bias", "merchant_score_exp", "ue_score_weight", "ue_score_bias", "ue_score_exp", "ue_score_lower_bound", "ue_score_upper_bound"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in ["c_bouns", "b_bouns"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in ["export_egpm", "export_total_bonus", "export_merchant_score", "export_ue_score",
                 "export_ue_score_final", "export_ue_score_final"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class MerchantLiveUeScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_ue_score_enricher"
  
  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["content_params", "content_weights", "content_coeff", "content_bias", "ue_score_lower_bound", "ue_score_upper_bound"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in []:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in ["export_ue_score"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class MerchantGoodsSliceFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_goods_slice_feature_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set(self._config.get("input_int_attr", []))
    ret.add(self._config["input_keys_attr"])
    return ret

class MerchantItemListToStringEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_list_to_string"

  @strict_types
  def _check_config(self) -> None:
    input_item_attr = self._config.get("input_item_attr")
    output_item_attr = self._config.get("output_item_attr")
    check_arg(isinstance(input_item_attr, str), "`input_item_attr` 配置需为 string 类型")
    check_arg(isinstance(output_item_attr, str), "`output_item_attr` 配置需为 string 类型")
    

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_item_attr"):
      attrs.add(self._config["input_item_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_item_attr"):
      attrs.add(self._config["output_item_attr"])
    return attrs

class MerchantGroupListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "group_list_attr"
  
  def get_configs(self, is_common):
    configs = self._config["configs"]
    return [config for config in configs if config.get("is_common", True) == is_common]

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(False)
    for config in configs:
      ret.add(config["key_attr"])
      agg_configs = config["agg_configs"]
      ret.update([c["src_attr"] for c in agg_configs])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(False)
    for config in configs:
      agg_configs = config["agg_configs"]
      ret.update([c["dst_attr"] for c in agg_configs])
    return ret


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(True)
    for config in configs:
      ret.add(config["key_attr"])
      agg_configs = config["agg_configs"]
      ret.update([c["src_attr"] for c in agg_configs])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(True)
    for config in configs:
      agg_configs = config["agg_configs"]
      ret.update([c["dst_attr"] for c in agg_configs])
      ret.add(config["out_key_attr"])
      ret.add(config["out_count_attr"])
    return ret


class MerchantTransformListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "transform_list_attr"

  def input_attrs(self, is_common):
    ret = set()
    configs = self._config["configs"]
    for config in configs:
      if config.get("is_sort_attr_common", True) == is_common:
        if "sort_attr" in config:
          ret.add(config["sort_attr"])
      if "filter_configs" in config:
        for c in config["filter_configs"]:
          if c.get("is_attr_comon", True) == is_common:
            ret.add(c["attr"])
          if c.get("is_bound_attr_common", True) == is_common:
            if "lower_bound_attr" in c:
              ret.add(c["lower_bound_attr"])
            if "upper_bound_attr" in c:
              ret.add(c["upper_bound_attr"])
          if c.get("is_not_equal_to_attr_common", True) == is_common:
            if "not_equal_to_attr" in c:
              ret.add(c["not_equal_to_attr"])
      if "attr_configs" in config:
        ret.update([c["src_attr"] for c in config["attr_configs"]
                 if c.get("is_src_attr_common", True) == is_common])
    return ret
  
  def output_attrs(self, is_common):
    ret = set()
    configs = [config for config in self._config["configs"]
               if config.get("is_dst_common", True) == is_common]
    for config in configs:
      if "attr_configs" in config:
        for c in config["attr_configs"]:
          ret.add(c["dst_attr"])
      if "output_sort_indices_attr" in config:
        ret.add(config["output_sort_indices_attr"])
      if "output_mask_flags_attr" in config:
        ret.add(config["output_mask_flags_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.input_attrs(False)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.output_attrs(False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.input_attrs(True)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.output_attrs(True)

class BidFormulaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_formula_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("common_strategy_id_list_attr_name"))
    attrs.add(self._config.get("common_strategy_param_length_list_attr_name"))
    attrs.add(self._config.get("common_strategy_param_name_list_attr_name"))
    attrs.add(self._config.get("common_strategy_formula_list_attr_name"))
    attrs.add(self._config.get("common_strategy_name_list_attr_name"))
    attrs.add(self._config.get("biz_attr_name"))
    return attrs  
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("common_strategy_id_list_attr_name"), "`common_strategy_id_list_attr_name` 是必选项")
    check_arg(self._config.get("common_strategy_param_length_list_attr_name"), "`common_strategy_param_length_list_attr_name` 是必选项")
    check_arg(self._config.get("common_strategy_param_name_list_attr_name"), "`common_strategy_param_name_list_attr_name` 是必选项")
    check_arg(self._config.get("common_strategy_formula_list_attr_name"), "`common_strategy_formula_list_attr_name` 是必选项")
    check_arg(self._config.get("common_strategy_name_list_attr_name"), "`common_strategy_name_list` 是必选项")
    check_arg(self._config.get("item_strategy_depend_scores"), "`item_strategy_depend_scores` 是必选项")
    check_arg(self._config.get("item_strategy_id_list_attr_name"), "`item_strategy_id_list_attr_name` 是必选项")
    check_arg(self._config.get("item_strategy_params_value_list_attr_name"), "`item_strategy_params_value_list_attr_name` 是必选项")
    check_arg(self._config.get("bonus_attrs"), "`bonus_attrs` 是必选项")
    check_arg(self._config.get("stage"), "`stage` 是必选项")
    check_arg(self._config.get("biz_attr_name"), "`biz_attr_name` 是必选项")
    
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("item_strategy_depend_scores", []), "name")
    attrs.add(self._config.get("item_strategy_id_list_attr_name"))
    attrs.add(self._config.get("item_strategy_params_value_list_attr_name"))
    return attrs
    
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_strategy_bonus_list_attr_name"))
    for attr in self._config.get("bonus_attrs"):
      attrs.add(attr)
    for attr in self._config.get("bonus_attrs"):
      attrs.add(attr+"_params_value")
    for attr in self._config.get("bonus_attrs"):
      attrs.add(attr+"_strategy_id")
    return attrs

class MerchantLiveOrderNoncommonFeatureExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_order_noncommon_feature_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["order_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    ret.add(self._config["filter_time"])
    ret.add(self._config["aid_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:

    attrs = set()
    attrs.add(self._config.get("payprice_cnt_aid"))
    attrs.add(self._config.get("amtprice_cnt_aid"))
    attrs.add(self._config.get("order_cnt_aid"))
    attrs.add(self._config.get("order_payprice_cnt_aid"))
    attrs.add(self._config.get("order_amtprice_cnt_aid"))
    attrs.add(self._config.get("payprice_cnt_aid_bucket"))
    attrs.add(self._config.get("amtprice_cnt_aid_bucket"))
    attrs.add(self._config.get("order_cnt_aid_bucket"))
    attrs.add(self._config.get("order_payprice_cnt_aid_bucket"))
    attrs.add(self._config.get("order_amtprice_cnt_aid_bucket"))

    return attrs

class MerchantLiveOrderCommonFeatureExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_order_common_feature_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["order_item_colossus_resp_attr"])
    ret.add(self._config["leaf_request_ms_attr"])
    ret.add(self._config["filter_time"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([
      "order_cnt_last7day",
      "order_payprice_last7day",
      "order_amtprice_last7day",
      "order_cnt_last15day",
      "order_payprice_last15day",
      "order_amtprice_last15day",
      "order_cnt_last30day",
      "order_payprice_last30day",
      "order_amtprice_last30day",
      "order_cnt_last60day", 
      "order_payprice_last60day", 
      "order_amtprice_last60day", 
      "order_cnt_last90day", 
      "order_payprice_last90day", 
      "order_amtprice_last90day", 
      "order_cnt_last7day_bucket", 
      "order_payprice_last7day_bucket", 
      "order_amtprice_last7day_bucket", 
      "order_cnt_last15day_bucket", 
      "order_payprice_last15day_bucket", 
      "order_amtprice_last15day_bucket", 
      "order_cnt_last30day_bucket", 
      "order_payprice_last30day_bucket", 
      "order_amtprice_last30day_bucket", 
      "order_cnt_last60day_bucket", 
      "order_payprice_last60day_bucket", 
      "order_amtprice_last60day_bucket", 
      "order_cnt_last90day_bucket", 
      "order_has_gt1500", 
      "order_has_gt1000", 
      "order_has_gt500", 
      "order_has_gt200", 
      "order_has_gt100", 
      "order_has_gt80", 
      "order_has_gt30"
    ])
    return attrs

class MerchantLiveSliceFeatureExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_slice_feature_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["request_time_attr"])
    ret.add(self._config["enable_filter_request_time_attr"])
    ret.add(self._config["filter_offset_ms_attr"])
    ret.add(self._config["aid_list_attr"])
    ret.add(self._config["pid_list_attr"])
    ret.add(self._config["pid_real_list_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    # ret.add(self._config.get("price_cnt_live_valid_list_avg", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_avg_3min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov_3min", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg_3min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_avg_9min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov_9min", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg_9min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_avg_15min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov_15min", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg_15min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_avg_30min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov_30min", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg_30min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_avg_60min", ""))
    # ret.add(self._config.get("price_cnt_live_valid_list_cov_60min", ""))
    # ret.add(self._config.get("order_cnt_live_valid_list_avg_60min", ""))
    ret.add(self._config.get("slice_cnt", ""))
    ret.add(self._config.get("slice_cnt_valid", ""))
    ret.add(self._config.get("recent_timestamp", ""))
    ret.add(self._config.get("is_stable_15min", ""))
    ret.add(self._config.get("is_stable_30min", ""))
    ret.add(self._config.get("is_stable_60min", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_3min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_3min_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_3min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_9min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_9min_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_9min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_15min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_15min_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_15min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_30min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_30min_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_30min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_avg_60min_bucket", ""))
    ret.add(self._config.get("price_cnt_live_valid_list_cov_60min_bucket", ""))
    ret.add(self._config.get("order_cnt_live_valid_list_avg_60min_bucket", ""))

    return ret

class MerchantLiveScartPriceExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_scart_price_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sCartItemQuotedPriceList"])
    ret.add(self._config["sCartItemList"])
    ret.add(self._config["sCartItemCate1IdList"])
    ret.add(self._config["sCartItemCate2IdList"])
    ret.add(self._config["sCartItemCate3IdList"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("cart_item_price_list_lyc"))
    attrs.add(self._config.get("cart_item_price_list_bucket"))
    attrs.add(self._config.get("cart_item_top1_price"))
    attrs.add(self._config.get("cart_item_top1_price_bucket"))
    attrs.add(self._config.get("cart_item_top5_avg_price"))
    attrs.add(self._config.get("cart_item_top5_avg_price_bucket"))
    attrs.add(self._config.get("cart_item_all_avg_price"))
    attrs.add(self._config.get("cart_item_all_avg_price_bucket"))
    attrs.add(self._config.get("cart_item_all_cnt"))
    attrs.add(self._config.get("cart_item_price_max"))
    attrs.add(self._config.get("cart_item_price_min"))
    attrs.add(self._config.get("cart_item_price_diff"))
    attrs.add(self._config.get("cart_item_price_max_bucket"))
    attrs.add(self._config.get("cart_item_price_min_bucket"))
    attrs.add(self._config.get("cart_item_price_diff_bucket"))
    attrs.add(self._config.get("cart_item_price_std"))
    attrs.add(self._config.get("cart_item_price_std_bucket"))
    attrs.add(self._config.get("cart_price_1000"))
    attrs.add(self._config.get("cart_price_800"))
    attrs.add(self._config.get("cart_price_500"))
    attrs.add(self._config.get("cart_price_200"))
    attrs.add(self._config.get("cart_price_100"))
    attrs.add(self._config.get("cart_price_80"))
    attrs.add(self._config.get("cart_price_50"))

    return attrs
  
class StrategyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_bid_service_strategy_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("common_biz_attr"))
    ret.add(self._config.get("common_strategy_ids_attr"))
    ret.add(self._config.get("common_flow_mark_strategy_ids_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_id_attr"))
    # ret.add(self._config[""])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("item_id_attr")
    ret.add(prefix + "_common_strategy_list")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("item_id_attr")
    ret.add(prefix + "_item_strategy_list")
    ret.add(prefix + "_item_flow_mark_strategy_id_list")
    return ret

class StrategyConcatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_bid_service_strategy_concat_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    item_id_attrs = self._config.get("item_id_attrs", [])
    for prefix in item_id_attrs:
      ret.add(prefix + "_common_strategy_list")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    item_id_attrs = self._config.get("item_id_attrs", [])
    for prefix in item_id_attrs:
      ret.add(prefix + "_item_strategy_list")
      ret.add(prefix + "_item_flow_mark_strategy_id_list")
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("common_strategy_id_list")
    ret.add("common_strategy_param_length_list")
    ret.add("common_strategy_param_name_list")
    ret.add("common_strategy_formula_list")
    ret.add("common_strategy_name_list")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("item_strategy_id_list")
    ret.add("item_strategy_params_value_list")
    ret.add("item_flow_mark_strategy_id_list")
    return ret

class MerchantPurchasedFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_purchased_filter_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["seller_id_attr"])
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("strategy_kconf_key"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("merchant_purchased_filter_flag"))
    attrs.add(self._config.get("merchant_purchased_filter_reason"))

    return attrs

class MerchantColossusSerializeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_serialize_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_colossus_attr"))
    ret.add(self._config.get("input_colossus_type"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_serialize_attr"))
    ret.add(self._config.get("output_serialize_size"))
    return ret

class MerchantColossusDeserializeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_colossus_deserialize_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_serialize_attr"))
    ret.add(self._config.get("input_serialize_size"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_colossus_attr"))
    return ret

class MerchantPercentileMapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_percentile_map_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_type_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for x in self._config.get("convert_item_attrs"):
      ret.add(x)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for x in self._config.get("converted_item_attrs"):
      ret.add(x)
    return ret

class MerchantGoodsSlideBeamSearchEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_goods_slide_beam_search_enricher"

class MerchantMixRankPackMMUEmbdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_mix_rank_pack_mmu_embd_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("mmu_embd_attr"))
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("from_common_attr"))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_common_attr"))
    return ret

class MerchantMixAdXTREnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_mix_ad_xtr_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_result_attr"))
    return ret
class MerchantGetPreviewAdinfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_get_preview_adinfo_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_result_attr"))
    return ret
class MerchantSetAdresultInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_set_adresult_info_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_result_attr"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_result_attr"))
    return ret
class MerchantMixAdTraceLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_mix_ad_trace_log_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_request_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ad_result_attr"))
    ret.add(self._config.get("filter_reason_attr"))
    ret.add(self._config.get("pos_attr"))
    ret.add(self._config.get("mix_score_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr"))
    return ret
  
class MerchantMCPercentileMapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_mc_percentile_map_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_type_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for x in self._config.get("convert_item_attrs"):
      ret.add(x)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for x in self._config.get("converted_item_attrs"):
      ret.add(x)
    return ret

  
class CommonMerchantSliceFeatureColossusEnricherNew(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_slice_feature_colossus_enricher_new"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr", ""))
    ret.add(self._config.get("aid_list_attr", ""))
    ret.add(self._config.get("pid_list_attr", ""))
    ret.add(self._config.get("request_time_attr", ""))
    ret.add(self._config.get("enable_filter_request_time_attr", ""))
    ret.add(self._config.get("filter_offset_ms_attr", ""))
    return ret

class MerchantSliceNewFeatureExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_slice_new_feature_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_int_attr"])
    ret.add(self._config["pid_list"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("audience_cnt", ""))
    ret.add(self._config.get("click_cart_cnt", ""))
    ret.add(self._config.get("click_cnt", ""))
    ret.add(self._config.get("click_good_key_cnt", ""))
    ret.add(self._config.get("exit_cnt", ""))
    ret.add(self._config.get("gmv_cnt", ""))
    ret.add(self._config.get("good_key_category", ""))
    ret.add(self._config.get("live_id", ""))
    ret.add(self._config.get("order_cnt", ""))
    ret.add(self._config.get("realshow_cnt", ""))
    ret.add(self._config.get("timestamp", ""))

    return ret

class MerchantLeafshowSetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_leafshow_set"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config["ttl"]))
    ret.add(self._config["kv_str_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    action_type = self._config.get("action_type", 0)
    if action_type == 0:
      ret.add(self._config["id_list_attr"])
    if action_type == 1:
      ret.add(self._config["kv_str_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    action_type = self._config.get("action_type", 0)
    if action_type == 1:
        ret.add(self._config["key_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class MerchantDistributedCacheXtrScoreReaderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "distributed_cache_xtr_score_reader"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attr_name_types" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attr_name_types", []) }
    if "is_cache_hit" in self._config:
      attrs.add(self._config.get("is_cache_hit"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attr_name_types"), "缺少 attr_name_types")
    check_arg(self._config.get("photo_store_kconf_key"), "缺少 photo_store_kconf_key")
    check_arg(self._config.get("table_kconf_path"), "缺少 table_kconf_path")
    check_arg(self._config.get("table_name"), "缺少 table_name")

class MerchantIntentionExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_intention_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    ret.add(self._config["photo_id_list_attr"])
    ret.add(self._config["timestamp_list_attr"])
    ret.add(self._config["score_list_attr"])
    ret.add(self._config["cate1_list_attr"])
    ret.add(self._config["cate2_list_attr"])
    ret.add(self._config["cate3_list_attr"])
    ret.add(self._config["goods_id_list_attr"])
    ret.add(self._config["label_list_attr"])
    ret.add(self._config.get("duration_list_attr", ""))
    ret.add(self._config.get("play_time_list_attr"))
    ret.add(self._config.get("tag_list_attr"))
    ret.add(self._config.get("channel_list_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["photo_goods_iCate1Id_attr"])
    ret.add(self._config["photo_goods_iCate2Id_attr"])
    ret.add(self._config["photo_goods_iCate3Id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["resp_photo_id_list_attr"])
    ret.add(self._config["resp_score_list_attr"])
    ret.add(self._config["resp_cate1_list_attr"])
    ret.add(self._config["resp_cate2_list_attr"])
    ret.add(self._config["resp_cate3_list_attr"])
    ret.add(self._config["resp_day_lag_list_attr"])
    ret.add(self._config["resp_goods_id_list_attr"])
    ret.add(self._config.get("resp_label_list_attr", ""))
    ret.add(self._config.get("resp_duration_list_attr", ""))
    ret.add(self._config.get("resp_tag_list_attr", ""))
    ret.add(self._config.get("resp_play_time_list_attr", ""))
    ret.add(self._config.get("resp_channel_list_attr", ""))
    return ret

class MerchantTowerFetchDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_fetch_tower_dot_product_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_hetu_tag"]:
      if key in self._config:
        ret.add(self._config[key])
    if not self._config.get("use_item_key_as_embed_key", True):
      ret.add(self._config["item_embedding_key_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    ret.add(self._config.get("item_pxtr_label_attr", "return_pxtr_label"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class MerchantCateSimExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_cate_sim_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    ret.add(self._config["photo_id_list_attr"])
    ret.add(self._config["seller_id_list_attr"])
    ret.add(self._config["real_seller_id_list_attr"])
    ret.add(self._config["timestamp_list_attr"])
    ret.add(self._config["category_list_attr"])
    ret.add(self._config["flow_type_list_attr"])
    ret.add(self._config["from_list_attr"])
    ret.add(self._config["price_list_attr"])
    ret.add(self._config["good_detail_page_view_time_list_attr"])
    ret.add(self._config["good_origin_price_list_attr"])
    ret.add(self._config["label_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_goods_iCateXId_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["resp_item_id_list_attr"])
    ret.add(self._config["resp_aid_list_attr"])
    ret.add(self._config["resp_cate1_list_attr"])
    ret.add(self._config["resp_cate2_list_attr"])
    ret.add(self._config["resp_cate3_list_attr"])
    ret.add(self._config["resp_view_lag_list_attr"])
    ret.add(self._config["resp_seller_id_list_attr"])
    ret.add(self._config["resp_carry_type_list_attr"])
    ret.add(self._config["resp_click_type_list_attr"])
    ret.add(self._config["resp_click_from_list_attr"])
    ret.add(self._config["resp_price_list_attr"])
    ret.add(self._config["resp_origin_price_list_attr"])
    ret.add(self._config["resp_price_diff_list_attr"])
    ret.add(self._config["resp_page_view_time_attr"])
    ret.add(self._config["resp_label_list_attr"])

    return ret

class MerchantGsuWithIndexMerchantVideoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_index_merchant_video"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "sorted_item_pxtrs_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["timestamp_attr", "category_attr", 
                "field1_attr","field2_attr","field3_attr","field4_attr",
                "field5_attr","field6_attr","field7_attr","field8_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret


class MerchantLongViewExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_long_view_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    ret.add(self._config["photo_id_list_attr"])
    ret.add(self._config["seller_id_list_attr"])
    ret.add(self._config["real_seller_id_list_attr"])
    ret.add(self._config["timestamp_list_attr"])
    ret.add(self._config["category_list_attr"])
    ret.add(self._config["flow_type_list_attr"])
    ret.add(self._config["from_list_attr"])
    ret.add(self._config["price_list_attr"])
    ret.add(self._config["good_detail_page_view_time_list_attr"])
    ret.add(self._config["good_origin_price_list_attr"])
    ret.add(self._config["label_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["resp_item_id_list_attr"])
    ret.add(self._config["resp_aid_list_attr"])
    ret.add(self._config["resp_cate1_list_attr"])
    ret.add(self._config["resp_cate2_list_attr"])
    ret.add(self._config["resp_cate3_list_attr"])
    ret.add(self._config["resp_view_lag_list_attr"])
    ret.add(self._config["resp_seller_id_list_attr"])
    ret.add(self._config["resp_carry_type_list_attr"])
    ret.add(self._config["resp_click_type_list_attr"])
    ret.add(self._config["resp_click_from_list_attr"])
    ret.add(self._config["resp_price_list_attr"])
    ret.add(self._config["resp_origin_price_list_attr"])
    ret.add(self._config["resp_price_diff_list_attr"])
    ret.add(self._config["resp_page_view_time_attr"])
    ret.add(self._config["resp_label_list_attr"])

    return ret

class MerchantLongSessionExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_long_session_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    ret.add(self._config["photo_id_list_attr"])
    ret.add(self._config["aid_list_attr"])
    ret.add(self._config["duration_list_attr"])
    ret.add(self._config["play_time_list_attr"])
    ret.add(self._config["channel_list_attr"])
    ret.add(self._config["timestamp_list_attr"])
    ret.add(self._config["label_list_attr"])
    ret.add(self._config["spu_id_list_attr"])
    ret.add(self._config["category_list_attr"])
    ret.add(self._config["tag_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["resp_item_id_list_attr"])
    ret.add(self._config["resp_aid_list_attr"])
    ret.add(self._config.get("resp_play_time_list_attr", ""))
    ret.add(self._config.get("resp_duration_list_attr", ""))
    ret.add(self._config.get("resp_channel_list_attr", ""))
    ret.add(self._config.get("resp_label_list_attr", ""))
    ret.add(self._config.get("resp_spu_id_list_attr", ""))
    ret.add(self._config.get("resp_cate1_list_attr", ""))
    ret.add(self._config.get("resp_cate2_list_attr", ""))
    ret.add(self._config.get("resp_cate3_list_attr", ""))
    ret.add(self._config.get("resp_lag_day_list_attr", ""))
    ret.add(self._config.get("resp_lag_hour_list_attr", ""))
    ret.add(self._config.get("resp_play_percent_list_attr", ""))
    ret.add(self._config.get("resp_is_click_cart_list_attr", ""))
    ret.add(self._config.get("resp_is_order_list_attr", ""))
    ret.add(self._config.get("resp_is_interaction_list_attr", ""))
    ret.add(self._config.get("resp_is_living_list_attr", ""))
    ret.add(self._config.get("resp_is_enter_live_list_attr", ""))
    ret.add(self._config.get("resp_tag_list_attr", ""))
    ret.add(self._config.get("resp_is_like_list_attr", ""))
    ret.add(self._config.get("resp_is_follow_list_attr", ""))
    ret.add(self._config.get("resp_is_forward_list_attr", ""))
    ret.add(self._config.get("resp_is_comment_stay_list_attr", ""))
    ret.add(self._config.get("resp_is_enter_profile_list_attr", ""))
    ret.add(self._config.get("resp_is_commertial_intent_list_attr", ""))
    ret.add(self._config.get("resp_commertial_item_id_list_attr", ""))
    ret.add(self._config.get("resp_commertial_aid_list_attr", ""))
    ret.add(self._config.get("resp_commertial_play_time_list_attr", ""))
    ret.add(self._config.get("resp_commertial_duration_list_attr", ""))
    ret.add(self._config.get("resp_commertial_channel_list_attr", ""))
    ret.add(self._config.get("resp_commertial_label_list_attr", ""))
    ret.add(self._config.get("resp_commertial_spu_id_list_attr", ""))
    ret.add(self._config.get("resp_commertial_cate1_list_attr", ""))
    ret.add(self._config.get("resp_commertial_cate2_list_attr", ""))
    ret.add(self._config.get("resp_commertial_cate3_list_attr", ""))
    ret.add(self._config.get("resp_commertial_lag_day_list_attr", ""))
    ret.add(self._config.get("resp_commertial_lag_hour_list_attr", ""))
    ret.add(self._config.get("resp_commertial_play_percent_list_attr", ""))
    ret.add(self._config.get("resp_commertial_tag_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_like_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_follow_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_forward_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_comment_stay_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_enter_profile_list_attr", ""))
    ret.add(self._config.get("resp_commertial_is_commertial_intent_list_attr", ""))
    return ret

class ExtractSignProcessorEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "slot_id" not in self._config:
        self._config["slot_id"] = 0

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "extract_sign_processor"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set([self._config.get("item_id")])
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set([self._config.get("item_id_sign")])
      return attrs

class EshopVideoGsuExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eshop_video_gsu_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time"])
    ret.add(self._config["input_eshop_video_photo_id"])
    ret.add(self._config["input_eshop_video_author_id"])
    ret.add(self._config["input_eshop_video_play_time"])
    ret.add(self._config["input_eshop_video_duration"])
    ret.add(self._config["input_eshop_video_channel"])
    ret.add(self._config["input_eshop_video_label"])
    ret.add(self._config["input_eshop_video_timestamp"])
    ret.add(self._config["input_eshop_video_spu_id"])
    ret.add(self._config["input_eshop_video_category"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_aId"])
    ret.add(self._config["input_sCartItemCate1IdList"])
    ret.add(self._config["input_sCartItemCate2IdList"])
    ret.add(self._config["input_sCartItemCate3IdList"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_eshop_video_gsu_photo_id"])
    ret.add(self._config["output_eshop_video_gsu_author_id"])
    ret.add(self._config["output_eshop_video_gsu_play_time"])
    ret.add(self._config["output_eshop_video_gsu_duration"])
    ret.add(self._config["output_eshop_video_gsu_channel"])
    ret.add(self._config["output_eshop_video_gsu_label"])
    ret.add(self._config["output_eshop_video_gsu_lag"])
    ret.add(self._config["output_eshop_video_gsu_spu_id"])
    ret.add(self._config["output_eshop_video_gsu_cate1"])
    ret.add(self._config["output_eshop_video_gsu_cate2"])
    ret.add(self._config["output_eshop_video_gsu_cate3"])
    ret.add(self._config["output_eshop_video_match_aid_mcnt"])
    ret.add(self._config["output_eshop_video_match_cartcate3_mcnt"])
    ret.add(self._config["output_eshop_video_match_cartcate2_mcnt"])
    ret.add(self._config["output_eshop_video_match_cartcate1_mcnt"])
    return ret

class VideoMerchantIntentionExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "video_merchant_intention_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time"])
    ret.add(self._config["input_video_photo_id"])
    ret.add(self._config["input_video_author_id"])
    ret.add(self._config["input_video_play_time"])
    ret.add(self._config["input_video_duration"])
    ret.add(self._config["input_video_channel"])
    ret.add(self._config["input_video_label"])
    ret.add(self._config["input_video_timestamp"])
    ret.add(self._config["input_video_tag"])
    ret.add(self._config["input_video_merchant_intention_score_v3"])
    ret.add(self._config["input_video_merchant_intention_cate_1"])
    ret.add(self._config["input_video_merchant_intention_cate_2"])
    ret.add(self._config["input_video_merchant_intention_cate_3"])
    ret.add(self._config["input_video_merchant_intention_cate_leaf"])
    ret.add(self._config["input_video_merchant_intention_goods_id"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_aId"])
    ret.add(self._config["input_sCartItemCate1IdList"])
    ret.add(self._config["input_sCartItemCate2IdList"])
    ret.add(self._config["input_sCartItemCate3IdList"])
    ret.add(self._config["input_sCartItemCategoryLeafIdList"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_video_gsu_merge_photo_id"])
    ret.add(self._config["output_video_gsu_merge_author_id"])
    ret.add(self._config["output_video_gsu_merge_play_time"])
    ret.add(self._config["output_video_gsu_merge_duration"])
    ret.add(self._config["output_video_gsu_merge_channel"])
    ret.add(self._config["output_video_gsu_merge_label"])
    ret.add(self._config["output_video_gsu_merge_lag"])
    ret.add(self._config["output_video_gsu_merge_tag"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_score_v3"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_cate_1"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_cate_2"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_cate_3"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_cate_leaf"])
    ret.add(self._config["output_video_gsu_merge_merchant_intention_goods_id"])
    ret.add(self._config["output_video_match_aid_score_mcnt"])
    ret.add(self._config["output_video_match_cateleaf_mcnt"])
    ret.add(self._config["output_video_match_cate3_mcnt"])
    ret.add(self._config["output_video_match_cate2_mcnt"])
    ret.add(self._config["output_video_match_cate1_mcnt"])
    ret.add(self._config["output_video_match_score_mcnt"])
    return ret
  
class MerchantFrequencySamplingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_frequency_sampling_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sample_flag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_keep_flag_attr"])
    ret.add(self._config["real_sample_rate_attr"])
    return ret

class MerchantProfileResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_profile_response_enricher"
  @strict_types
  def is_async(self) -> bool:
    return False
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {"merchant_user_profile_rsp"}
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {}
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {}

class MerchantCommonGsuFeatureEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
      super().__init__(config)
      if "limit_num" not in self._config:
        self._config["limit_num"] = 50

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "merchant_common_gsu_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("common_action_gsu_attr_list"))
      ret.add(self._config.get("common_action_category_list"))
      ret.add(self._config.get("common_action_iid_list"))
      ret.add(self._config.get("common_action_aid_list"))
      ret.add(self._config.get("common_action_timestamp_list"))
      ret.add(self._config.get("common_action_price_list"))
      return ret
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("target_attr", "aId"))
      return ret
  
    @property
    @strict_types
    def output_item_attrs(self) -> set:
      ret = set()
      ret.add(self._config.get("gsu_match_iid_list", "gsu_match_iid_list"))
      ret.add(self._config.get("gsu_match_aid_list", "gsu_match_aid_list"))
      ret.add(self._config.get("gsu_match_cid1_list", "gsu_match_cid1_list"))
      ret.add(self._config.get("gsu_match_cid2_list", "gsu_match_cid2_list"))
      ret.add(self._config.get("gsu_match_cid3_list", "gsu_match_cid3_list"))
      ret.add(self._config.get("gsu_match_price_list", "gsu_match_price_list"))
      ret.add(self._config.get("gsu_match_lag_list", "gsu_match_lag_list"))
      ret.add(self._config.get("gsu_match_rank_list", "gsu_match_rank_list"))
      return ret