#!/usr/bin/env python3
# coding=utf-8
"""
filename: subdivision_api_mixin.py
description:
author: z<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2022-01-21 18:08:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .search_retriever import *
from .search_enricher import *
from .search_arranger import *

class SearchApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 search 相关的 Processor 接口

  背景： 目前该模型主要承接 搜索 提供 dragon 插件，包括搜索文本查询、向量查询等能力
  现有维护人：zhangcunyi liuzhanshan

  """
  def retrieve_by_starry_embedding(self, **kwargs):
    """
    StarryEmbeddingRetriever
    -----
    搜索繁星 starry 检索服务插件：实现 i2i,q2i,emb2i，有问题 可咨询 zhangcunyi liuzhanshan
    参数配置
    ------
    `kess_service`: [string] [必填] 服务的 kess 服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] [必填] 召回原因

    `item_type`: [int] [必填] item 类型

    `source`: [string] [必填] 区分调用方

    `vertical_id`: [string] [必填] 业务 ID 或 索引 id

    `input_type`: [string] [必填] `org_text`:q2i StringCommonAttr, `feature`:emb2i DoubleListCommonAttr, `text`: i2i IntListCommonAttr 或 IntCommonAttr

    `dim`: [int] [选填] 当 `input_type` = `feature` 时默认是一个embeding， 设置此值>0 表示 double list 里面可以是>=1 个 embeding ， 比如 len(doublelist) = 100, dim = 10, 则下发请求会分成 10 个子请求发送， 默认 = 0 不检测 list 长度

    `source_photo_attr`: [string] CommonAttr Name for `input_type`

    `batch_limit` : [int] [选填] 一次下发一个 batch 请求的上限条数，默认 INT_MAX

    `bucket_id` : [string] [动态参数] [选填] 直接指定 或 从 StringCommonAttr 获取 bucket_id，若获取是空字符串，则不设置，默认是不设置

    请求参数设定：下面两个参数一般 二选一 设置即可

      `total_limit`: [int] [动态参数] 总召回数, 用来预先决定 batch 总召回数；batch 内每个 item 的召回个数即 top_k = (total_limit + batch_num - 1) / batch_num, 如果已经设定了 top_k 则转义为收集 batch 结果，最大放入召回的个数，多的做截断

      `top_k`: [int] [动态参数] 强行 set top_k, 不管 batch 内有多少个 item, 每个 item 均取 top_k 个，> 0 为有效值；

    调用示例
    ------
    ``` python
    .retrieve_by_starry_embedding(
      kess_service = "grpc_se_gasForReco",
      timeout_ms = 200,
      reason = 29,
      item_type = 0,
      vertical_id = "recoDiscoverTabVertical",
      source_photo_attr = "commonRetrievalPhotos",
      total_limit = "200"
    )
    ```
    """
    self._add_processor(StarryEmbeddingRetriever(kwargs))
    return self



  def enrich_by_search_profile(self, **kwargs):
    """
    SearchProfileEnricher
    -----
    搜索 Profile 新版 (client3) 读取插件 zhangcunyi zhumingxiao
    参数配置
    ------
    `input_source_type`: [string] [必填] 输入的类型，目前支持 `common_attr`,`item_attr`,`item_key`,`item_id`

    `input_attr_name`: [string] 当 `input_source_type` in (`common_attr`,`item_attr`) 时必填, 从这里读取 attr 的 int_list > int > string_list > string 作为 profile 的 key

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间,单位 ms

    `biz`: [string] [必填] 业务

    `profile_type`: [int] [必填] 读取 profile 类型

    `batch_opt`:[bool] [选填] [默认False] 是否开启 batch 请求, batch <40 时 对耗时 cpu都有收益; batch > 60 对耗时有损害，对 cpu有收益, 具体可实验后决定是是否打开

    `batch_size`:[uint] [选填] [默认0] `batch_opt` = True 时, 拆包的大小, 0 表示不拆包，全部在一个包里(同步请求阻塞)；N 表示每 N 个组成一个包； 一般合理值在 40-60, 超过合理值 越大 cpu 消耗越低， 但耗时越高 (会使用线程池是异步请求)

    `features`: [list of dict] [必填] 读取列名的集合,及其对应的配置

      - `feature_name`: [string]  [必填] 从 profile 读取的列名 

      - `output_attr_name`: [string]  [必填] 表示读到的特征对应输出到的位置
      
      - `output_attr_type`: [string]  [必填] 表示读到的特征的类型，目前只能是 `int_list`、`double_list`、`string_list`、`int16_2_double_list`

      - `dim`: [int] [必填] 表示预期读到特征的维度，>=1 表示知道实际维度 `output_attr_name` 最终为 dim 的整数倍, 至于 输入维度 != `dim` 的情况如何处理，由 `is_truncate_pad` 参数控制; 若 dim <=0 则不做维度校验，全部填充

      - `fill_default_value`: [bool] [必填] True、False for py,  true、false for json; 表示如果取不到是否在 `output_attr_name` 中填充 `dim` 个默认值, 此时 dim<=0 就不会填充，因为不知道填几个哈

      - `is_truncate_pad` : [bool] [选填,默认 False] 是否对拿到的数据进行维度对齐；True：时, 要求同时满足 `dim`>=1 && `fill_default_value`==True， profile 拿到的数据不足 `dim` 个用 `fill_default_value` 填充, 超出截断后填充; False: `dim` >=1 时 严格 check profile 拿到的数据是否与 `dim` 一致，不一致丢弃数据, `fill_default_value` 控制丢弃后要不要填充; `output_attr_type` = `int16_2_double_list` 时 必须是 False 

      - `int_2_float` : [bool] [选填,默认 False] 是否对profile3中int/int_list类型的特征转成double_list

      - `default_value`: [int or float] [部分必填] `fill_default_value` = True ：`output_attr_type` in [`int_list`] 必填 int 值; `output_attr_type` in [`double_list`, `int16_2_double_list`] 必填 float 值（必须带 . 如 1.0 而不是 1）; `output_attr_type` in [`stirng_list`] 默认填充 空字符串 

      - `delimiter`: [string] [选填] 当 `output_attr_type` in [`string_list`] && `dim` == 1 && `is_truncate_pad` == False, 表示拿到string list 用此值进行字符串 join, 默认值 : 空格

    调用示例
    ------
    ``` python
    .enrich_by_search_profile(
            biz="se",
            profile_type="Photo",
            timeout_ms=10,
            input_source_type='common_attr',
            input_attr_name='pid_list',
            features=[
                {
                  'feature_name' : 'mm_v4_embedding',
                  'output_attr_name' : 'o_mm_v4_embedding',
                  'output_attr_type' : 'int16_2_double_list',
                  'fill_default_value' : False,
                  'dim': 64,
                  'default_value' : -3.0
                },
                {
                  'feature_name' : 'cluster_1w',
                  'output_attr_name' : 'o_cluster_1w',
                  'output_attr_type' : 'int_list',
                  'fill_default_value' : True,
                  'dim': 1,
                  'default_value' : -999
                },
          ]
    )
    ```
    """
    self._add_processor(SearchProfileEnricher(kwargs))
    return self 
  
  def get_embeddings_by_search_starry(self, **kwargs):
    """
    SearchStarryEmbeddingEnricher
    -----
    搜索繁星平台根据 id 获取 embeddings 的接口 读取插件 yanshihao
    参数配置
    ------
    `kess_service`: [string] [必填] 繁星代理服务的服务名

    `source`: [string] [必填] 请求来源

    `timeout_ms`: [int] [选填] 请求远程服务的超时时间,单位 ms

    `vertical_id`: [string] [必填] 索引名称

    `source_photo_attr`: [int list] [必填] 需要获取 embedding 的 photo id

    `dim`:[int] [必填] 向量维度

    `batch_limit`:[uint] [选填] [默认 int_max] 一批 photo id 的数量限制

    `output_photo_attr`: [int list] [必填] 输出能拿到的 photo id，不能拿到的不输出

    `output_embeddings_attr`: [double list] [必填] 输出 embedding, 维度 = return_photo_id_num * dim，与 output_photo_attr 的输出对应 1:dim

    调用示例
    ------
    ``` python
    .get_embeddings_by_search_starry(
            kess_service="grpc_mmu_visionSearchGas",
            source="test",
            vertical_id="photoWithGoodsNNRecall",
            source_photo_attr='input_photo_docids',
            dim=32,
            output_photo_attr='output_docids',
            output_embeddings_attr='output_embeddings'
    )
    ```
    """
    self._add_processor(SearchStarryEmbeddingEnricher(kwargs))
    return self 

  def get_embeddings_by_kai_stream_ann(self, **kwargs):
    """
    StreamAnnGsuEmbeddingEnricher
    -----
    根据 特征 读取 kai stream ann embedding yanshihao
    参数配置
    ------
    `kess_service`: [string] [必填] 繁星代理服务的服务名

    `timeout_ms`: [int] [选填] 请求远程服务的超时时间,单位 ms

    `request_type`:[string] [选填] request_type, 默认 se_sdr_model

    `output_embeddings_key`: [double list] [必填] 输出 embedding

    `user_id_attr`: [int] [选填] user_id_attr
    `se_q_pv_1d_pv`: [int] [选填] se_q_pv_1d_pv
    `se_q_pv_7d_pv`: [int] [选填] se_q_pv_7d_pv
    `q_user_score`: [int] [选填] q_user_score
    `se_q_1d_click_cnt`: [int] [选填] se_q_1d_click_cnt
    `se_q_1d_play_cnt`: [int] [选填] se_q_1d_play_cnt
    `se_q_1d_long_view_cnt`: [int] [选填] se_q_1d_long_view_cnt
    `se_q_1d_follow_cnt`: [int] [选填] se_q_1d_follow_cnt
    `se_q_1d_like_cnt`: [int] [选填] se_q_1d_like_cnt
    `se_q_7d_play_cnt`: [int] [选填] se_q_7d_play_cnt
    `se_q_7d_long_view_cnt`: [int] [选填] se_q_7d_long_view_cnt
    `se_q_7d_follow_cnt`: [int] [选填] se_q_7d_follow_cnt
    `se_q_7d_like_cnt`: [int] [选填] se_q_7d_like_cnt
    `se_q_1d_ctr`: [int] [选填] se_q_1d_ctr
    `se_q_1d_lvtr`: [int] [选填] se_q_1d_lvtr
    `se_q_7d_click_cnt`: [int] [选填] se_q_7d_click_cnt
    `se_q_7d_ctr`: [int] [选填] se_q_7d_ctr
    `se_q_7d_lvtr`: [int] [选填] se_q_7d_lvtr
    `city_name`: [int] [选填] city_name
    `city_level`: [int] [选填] city_level
    `gender`: [int] [选填] gender
    `age_segment`: [int] [选填] age_segment
    `normal_mod`: [int] [选填] normal_mod
    `query`: [string] [选填] query
    `query_emb_ebr`: [double lis] [必填] query_emb_ebr
    `photo_high_consume`: [int lis] [选填] photo_high_consume
    `photo_high_consume_mask`: [double lis] [选填] photo_high_consume_mask
    `refer_photo_id`: [int] [选填] refer_photo_id
    `refer_photo_token_id`: [int lis] [选填] refer_photo_token_id
    
    调用示例
    ------
    ``` python
    from dragonfly.common_leaf_dsl import LeafService, LeafFlow
    from dragonfly.ext.search.search_api_mixin import SearchApiMixin
    class MyFlow(LeafFlow, SearchApiMixin):
      pass

    flow = MyFlow(name="test")\
        .get_embeddings_by_kai_stream_ann(
                        kess_service = "kws-kuaishou-search-stream-sdr-ann-gsu-embedding-infer-server",
                        timeout_ms = 100,
                        request_type = "se_sdr_model",
                        user_id_attr = "user_id_attr",
                        se_q_pv_1d_pv = "se_q_pv_1d_pv",
                        se_q_pv_7d_pv = "se_q_pv_7d_pv",
                        q_user_score = "q_user_score",
                        se_q_1d_click_cnt = "se_q_1d_click_cnt",
                        se_q_1d_play_cnt = "se_q_1d_play_cnt",
                        se_q_1d_long_view_cnt = "se_q_1d_long_view_cnt",
                        se_q_1d_follow_cnt = "se_q_1d_follow_cnt",
                        se_q_1d_like_cnt = "se_q_1d_like_cnt",
                        se_q_7d_play_cnt = "se_q_7d_play_cnt",
                        se_q_7d_long_view_cnt = "se_q_7d_long_view_cnt",
                        se_q_7d_follow_cnt = "se_q_7d_follow_cnt",
                        se_q_7d_like_cnt = "se_q_7d_like_cnt",
                        se_q_1d_ctr = "se_q_1d_ctr",
                        se_q_1d_lvtr = "se_q_1d_lvtr",
                        se_q_7d_click_cnt = "se_q_7d_click_cnt",
                        se_q_7d_ctr = "se_q_7d_ctr",
                        se_q_7d_lvtr = "se_q_7d_lvtr",
                        city_name = "city_name",
                        city_level = "city_level",
                        gender = "gender",
                        age_segment = "age_segment",
                        normal_mod = "normal_mod",
                        query = "query",
                        query_emb_ebr = "query_emb_ebr",
                        photo_high_consume = "photo_high_consume",
                        photo_high_consume_mask = "photo_high_consume_mask",
                        refer_photo_id = "refer_photo_id",
                        refer_photo_token_id = "refer_photo_token_id",
                        output_embeddings_key = "output_embeddings"
                      )

    service = LeafService(kess_name="grpc_CommonLeafTest")
    service.add_leaf_flows(leaf_flows=[flow], request_type="default")


    # 获取运行 dragonfly 的 executor
    leaf = service.executor()

    leaf["user_id_attr"] = 2126190788  
    leaf["se_q_pv_1d_pv"] = 4 
    leaf["se_q_pv_7d_pv"] = 5 
    leaf["q_user_score"] = 13  
    leaf["se_q_1d_click_cnt"] = 3 
    leaf["se_q_1d_play_cnt"] = 3  
    leaf["se_q_1d_long_view_cnt"] = 3  
    leaf["se_q_1d_follow_cnt"] = 0     
    leaf["se_q_1d_like_cnt"] = 0       
    leaf["se_q_7d_click_cnt"] = 5      
    leaf["se_q_7d_play_cnt"] = 5       
    leaf["se_q_7d_long_view_cnt"] = 4  
    leaf["se_q_7d_follow_cnt"] = 0     
    leaf["se_q_7d_like_cnt"] = 0       
    leaf["se_q_1d_ctr"] = 28            
    leaf["se_q_1d_lvtr"] = 29           
    leaf["se_q_7d_ctr"] = 25            
    leaf["se_q_7d_lvtr"] = 30           
    leaf["city_name"] = 109             
    leaf["city_level"] = 4              
    leaf["gender"] = 2                  
    leaf["age_segment"] = 2             
    leaf["normal_mod"] = 41  
    leaf["query"] = '电视剧天天有喜'
    leaf["query_emb_ebr"] = [-0.059594743,0.025868244,0.16029422,-0.09323235,0.21163957,0.015645398,0.0515587,-0.051025335,-0.04729177,0.07086654,0.043060403,0.13476378,0.008391622,-0.057070147,-0.07086654,-0.20467025,0.03297979,-0.018792257,-0.04842962,-0.020107893,0.12125184,0.12786557,0.0796493,0.06201267,-0.034615442,-0.010356187,0.15631175,-0.0633283,0.09941939,-0.057710186,-0.099206045,-0.06873308,-0.09508135,-0.002311252,-0.052127622,-0.07495568,0.10539309,0.094156854,-0.04825183,0.067701906,-0.10375744,0.11840722,-0.12900342,0.039895765,-0.044624943,0.04217146,0.059736975,-0.041460305,0.10062836,0.030579641,-0.09565028,-0.09074331,-0.06485729,0.037904534,-0.08988993,-0.042598154,0.086476386,0.06873308,0.11961618,0.0015145368,0.027681688,0.06595958,0.1422309,-0.006751523,-0.16441892,-0.0980682,0.025814908,-0.03136191,0.026899418,-0.123527534,-0.06556844,-0.11840722,0.0243926,-0.061265957,-0.042669266,-0.029797371,-0.18817148,0.044233806,0.13071018,-0.018454459,0.028695082,0.06297273,-0.0036002195,-0.22088458,-0.005871469,-0.10468194,-0.042669266,0.105250865,0.030917441,0.03733561,-0.158303,-0.025726013,0.10290405,0.016801024,-0.09550805,0.017752193,-0.05955919,0.009627254,0.07993376,0.063114956,0.016614346,-0.11300245,0.049105216,0.095365815,-0.13447931,-0.28119048,-0.080218226,0.15531613,0.09778374,-0.040855825,-0.08206723,0.10809548,0.028019486,-0.06958646,0.12089626,0.10468194,0.09479689,-0.051167563,0.11008671,0.045727234,-0.018089991,-0.0011250686,-0.00026182152,-0.04512275,0.061621536,-0.11286022,0.05827911,-0.05326547]
    leaf["photo_high_consume"] = [104615069094,104542242941,93689188988,95516012902,88165180554]
    leaf["photo_high_consume_mask"] = [1.0 for _ in range(5)]
    leaf["refer_photo_id"] = 128897529955
    leaf["refer_photo_token_id"] = [1,2,491,141,1476,2193,15,311,34,341,832,7659,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]

    service.build()

    # 运行名为 test 的 LeafFlow
    leaf.run("test")

    print(f"output_embeddings: {leaf['output_embeddings']}")
    ```
    """
    self._add_processor(StreamAnnGsuEmbeddingEnricher(kwargs))
    return self 

  def get_prefix_embeddings_by_kai_stream_ann_sug(self, **kwargs):
    """
    StreamAnnSugPrefixEmbeddingEnricher
    -----
    根据 特征 读取 kai stream ann embedding xiaozejiang
    参数配置
    ------
    `kess_service`: [string] [必填] 繁星代理服务的服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间,单位 ms

    `request_type`:[string] [选填] request_type, 默认 se_sdr_model

    `output_embeddings_key`: [double list] [必填] 输出 embedding


    `prefix`: [string] [选填] prefix

    `user_id_attr`: [int] [选填] user_id_attr

    `prefix_click_cnt_d1_exp`: [int] [选填] prefix_click_cnt_d1_exp

    `prefix_ctr_d1_exp`: [int] [选填] prefix_ctr_d1_exp

    `prefix_click_cnt_exp`: [int] [选填] prefix_click_cnt_exp

    `prefix_ctr_d3_exp`: [int] [选填] prefix_ctr_d3_exp

    `prefix_click_cnt_d7`: [int] [选填] prefix_click_cnt_d7

    `prefix_ctr_d7`: [int] [选填] prefix_ctr_d7

    `u_gender`: [int] [选填] u_gender

    `u_country_hash`: [int] [选填] u_country_hash

    `u_city_id`: [int] [选填] u_city_id

    `u_city_hash`: [int] [选填] u_city_hash

    `u_age_segment`: [int] [选填] u_age_segment

    `prefix_embedding`: [double list] [选填] prefix_embedding



    调用示例
    ------
    ``` python
    from dragonfly.common_leaf_dsl import LeafService, LeafFlow
    from dragonfly.ext.search.search_api_mixin import SearchApiMixin
    class MyFlow(LeafFlow, SearchApiMixin):
      pass

    flow = MyFlow(name="test")\
        .get_prefix_embeddings_by_kai_stream_ann_sug(
                        kess_service = "kws-kuaishou-search-sug-stream-sdr-v1-prefix-infer-server",
                        timeout_ms = 100,
                        request_type = "se_sdr_model",
                        prefix="prefix",
                        user_id_attr = "user_id_attr",
                        prefix_click_cnt_d1_exp="prefix_click_cnt_d1_exp",
                        prefix_ctr_d1_exp="prefix_ctr_d1_exp",
                        prefix_click_cnt_exp="prefix_click_cnt_exp",
                        prefix_ctr_d3_exp="prefix_ctr_d3_exp",
                        prefix_click_cnt_d7="prefix_click_cnt_d7",
                        prefix_ctr_d7="prefix_ctr_d7",
                        u_gender="u_gender",
                        u_country_hash="u_country_hash",
                        u_city_id="u_city_id",
                        u_city_hash="u_city_hash",
                        u_age_segment="u_age_segment",
                        prefix_embedding="prefix_embedding",
                        output_embeddings_key = "output_embeddings"
                      )

    service = LeafService(kess_name="grpc_CommonLeafTest")
    service.add_leaf_flows(leaf_flows=[flow], request_type="default")


    # 获取运行 dragonfly 的 executor
    leaf = service.executor()

    leaf["prefix"] = "名片设计"
    leaf["prefix_click_cnt_d1_exp"] = 3
    leaf["prefix_ctr_d1_exp"] = 21
    leaf["prefix_click_cnt_exp"] = 3
    leaf["prefix_ctr_d3_exp"] = 25
    leaf["prefix_click_cnt_d7"] = 1
    leaf["prefix_ctr_d7"] = 1
    leaf["u_gender"] = 1
    leaf["u_country_hash"] = 0
    leaf["u_city_id"] = 17041152
    leaf["u_city_hash"] = 0
    leaf["u_age_segment"] = 3
    leaf["user_id_attr"] = 939924774
    leaf["prefix_embedding"] = [-0.024384575,-0.10118149,0.11100709,-0.045291554,-0.12232307,0.16118383,0.06016794,-0.17277582,0.07578952,0.039909557,0.05641434,0.013399786,-0.12276467,0.03822596,0.09234951,-0.11481588,0.014420985,-0.15632623,-0.002851422,0.020175578,0.05740794,0.19894059,-0.07545832,0.05840154,-0.12486227,0.0986423,-0.17288622,-0.12011507,-0.08274471,-0.07915672,0.108523086,0.084731914,0.08136471,0.034279164,-0.019526979,0.012944386,-0.040709957,0.1943038,-0.11558868,-0.108191885,0.011930088,-0.052660745,-0.042283155,-0.003774296,0.04965235,0.02755857,-0.04876915,-0.07076633,-0.12232307,0.08820951,-0.10449349,-0.046616353,-0.07010393,-0.029449169,-0.04697515,0.14164305,0.022742376,0.017801981,0.0894791,-0.068171926,-0.17498381,0.025171174,-0.08688471,0.11879028,0.1914334,-0.011412588,0.011384988,-0.05751834,-0.03684596,0.02862117,0.043249156,-0.1914334,0.0071759927,0.06773033,-0.08362791,-0.04104116,-0.037701562,-0.03524516,-0.01980298,-0.02867637,-0.043055955,0.071152724,-0.04992835,-0.12938866,-0.039053958,-0.04609195,0.24751654,0.11084148,0.06557753,-0.17542541,-0.051529147,-0.07556872,-0.0054130442,-0.04592635,0.07634152,-0.075127125,-0.029449169,0.042421155,0.020699978,-0.016090782,0.072367124,0.00974969,-0.045595154,-0.026482172,-0.183595,0.0043331953,-0.11669268,0.010370689,0.12199187,-0.046450753,0.058898337,-0.062099934,-0.15389743,-0.078991115,-0.053405944,-0.10482469,0.005968494,0.078107916,-0.017015383,-0.08903751,-0.009087291,-0.11536788,-0.012557987,-0.060388736,-0.025943972,0.13612306,-0.054620344,-0.12232307]

    
    service.build()

    # 运行名为 test 的 LeafFlow
    leaf.run("test")

    print(f"output_embeddings: {leaf['output_embeddings']}")
    ```
    """
    self._add_processor(StreamAnnSugPrefixEmbeddingEnricher(kwargs))
    return self 

  
  def enrich_by_search_gsu(self, **kwargs):
    """
    SearchGsuEnricher
    -----
    搜索 个性化模型 Gsu召回 读取插件 (训练 推理统一) peichaohan
    参数配置
    ------
    `input_source_type`: [string] [必填] 输入的类型，目前支持 `common_attr`

    `input_attr_name`: [string] [必填] 从这里读取 common attr 中的 int 作为访问 colossus 的 key, 目前支持 "user_id_attr"

    `user_id_attr`: [string] [必填] 从这里读取 common attr 中的 int 作为访问 colossus 的 key, 目前支持 "user_id_attr"

    `top_k_attr`: [string] [必填] 从这里读取 common attr 中的 intlist 作为请求点积服务的自定义 top_k 参数, 填入 intlist 大小为 3, 格式为 [top_n, top_k, top_m], top_m 表示先在 colossus 中取 top_m 个, top_n 在 top_m 内先取最近 top_n 个, top_k 表示在剩余的 photo 中取点积最大的 top_k 个

    `is_train`: [string] [必填] 从这里读取 common attr 中的 bool 值, 作为区分处理流程是训练还是推理的依据, 训练脚本需要在 common attr 将此参数设为 true

    `shards`: [int] [必填] 点积计算服务下游的 shard 数量,

    `kess_service`: [string] [必填] 点积服务的 kess 注册服务名,

    `user_embedding_attr`: [string] [必填] 从 common attr 中读取的，query embedding (double list), 作为点积服务的输入参数

    `emb_dim`: [int] [必填] query embedding 和 photo embedding 的维数，目前支持 64 维

    `kconf_timeout_ms_attr`: [int] [必填] 请求远程点积计算服务的超时时间,单位 ms

    `predict_labels`: [string list] [必填] 请求远程点积服务的预测 label, 目前只支持 ["dp"]

    `filter_future_attr`: [bool] [非必填] 是否开启时间戳过滤，默认为 false

    `filter_time_stamp_attr`: [string] [非必填] 时间戳过滤所需的时间戳存储的 common_attr, 默认为 "", 如果 filter_future_attr 为 True, 且 filter_time_stamp_attr 不为空时, 采用此 common attr 的 int 值作为时间戳，否则采用 request time

    `features`: [list of dict] [必填] 读取列名的集合,及其对应的配置

      - `output_attr_name`: [string]  [必填] 表示读到的特征对应输出到的 common attr 位置
      
      - `output_attr_type`: [string]  [必填] 表示读到的特征的类型，目前只能是 `int_list`、`float_list`、`string_list`、`int16_2_double_list`

      - 目前训练和推理所需的返回 common attr 特征名是固定的 
        
        - 训练需要配置 ["common_gsu_pid_list", "colossus_aid", "colossus_tag", "colossus_play", "colossus_duration", "colossus_label", "colossus_time"]

        - 推理需要配置 ["common_gsu_pid_list", "dot_distance", "colossus_result"]

    调用示例
    ------
    ``` python
      # 必填的 common attr 参数
      leaf["user_id_attr"] = 2376196771 # user_id
      leaf["top_k_attr"] = [10,40,1000] # gsu top k 配置
      leaf["is_train"] = True # 是否是训练调用
      leaf["filter_future_attr"] = True # 是否要根据时间戳过滤
      leaf["user_embedding_attr"]=[] # 64 维 query embedding
      leaf["filter_time_stamp_attr"]=1656390586000 # filter_time_stamp_attr 的参数不为空 就采用传入时间为过滤时的时间戳
      leaf["filter_future_attr"] = True # 采用时间戳过滤
      # 返回解析特征列表
      input_feature=[{'output_attr_name' : "common_gsu_pid_list",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_aid",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_tag",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_play",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_duration",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_label",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "colossus_time",
                      'output_attr_type' : "int_list"},
                      {'output_attr_name' : "dot_distance",
                      'output_attr_type' : "double_list"},
                      {'output_attr_name' : "colossus_result",
                      'output_attr_type' : "string_list"}]

      .enrich_by_search_gsu(
        input_source_type='common_attr',
        input_attr_name="user_id_attr",
        user_id_attr="user_id_attr",
        top_k_attr="top_k_attr",
        is_train="is_train",
        shards=8,
        kess_service="grpc_videosearch_gsu_online",
        user_embedding_attr="user_embedding_attr",
        emb_dim=64,
        kconf_timeout_ms_attr=50,
        predict_labels=["dp"],
        features=input_feature
    )
    ```
    """
    self._add_processor(SearchGsuEnricher(kwargs))
    return self 

  def live_se_gsu_retriever_with_colossus_resp_v4(self, **kwargs):
    """
    CommonLiveSeColossusV4RespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_page_type_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_optag_to_attr`: [string]

    `save_reward_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_only_out` : [boolean] 是否只取间内播放的colossus pid, 默认为false

    `merchant_item_only` : [boolean] 是否只保留电商的item, 默认false

    `reward_item_only` : [boolean] 是否只保留有打赏的item, 默认false

    示例
    ------
    ``` python
    .live_se_gsu_retriever_with_colossus_resp_v4(
      colossus_resp_attr="live_colossus_output",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_author_id_to_attr="live_colossus_aid",
      save_play_time_to_attr="live_colossus_play_time",
      save_auto_play_time_to_attr="live_colossus_auto_play_time",
      save_hetu_tag_to_attr="live_colossus_hetu_tag",
      save_cluster_id_to_attr="live_colossus_cluster_id",
      save_label_to_attr="live_colossus_label",
      save_order_price_to_attr="live_colossus_order_price"
      save_result_to_common_attr="live_colossus_pid",
      filter_future_attr=False,
      filter_only_out=False,
      merchant_item_only=False,
      reward_item_only=False)
    ```
    """
    self._add_processor(CommonLiveSeColossusV4RespRetriever(kwargs))
    return self


  def enrich_live_merchant_feat(self, **kwargs):
    """
    SearchLiveMerchantEnricher
    ------
    搜索直播电商定制化需求, 从 item attr 里读取 pb序列化特征，遍历所有 field 或者指定 field，将结果写回到 item attr。

    参数
    ------
    `input_attr`: [string] [必填] 从这里读取 item attr 中的 stringlist 作为待解析的pb序列化的特征

    `is_base64`: [boolean] input_attr 是否是base64编码后的结果

    `attrs`: [list] 需要抽取的属性列表，每项值为字符串或 {"name"="xxx", "path"="yyy"} 的 dict 格式。
      - `name`: [string] attr 的名称。
      - `path`: [string] protobuf 结构内数据路径，类似 json path 的句点分隔格式。

    `save_all_fields`: [bool] 选配项，是否保存所有 pb 字段（会同时忽略 `attrs` 配置），注意该功能性能消耗较大，**线上服务慎用**！默认为 False。

    `output_attr_prefix`: [string] 选配项，仅当 save_all_fields=True 时生效，用于给保存的 attr 名增加一个前缀，默认无前缀。

    示例
    ------
    ``` python
    .enrich_live_merchant_feat(
      input_attr="serialize_goods_feat_base64",
      is_base64=True,
      save_all_fields=True,
      )
    ```
    """
    self._add_processor(SearchLiveMerchantEnricher(kwargs))
    return self

  def live_se_author_list_from_colossus(self, **kwargs):
    """
    LiveSeAuthorListFromColossusEnricher
    -----
    将 colossus 里的直播间粒度的序列聚合为 Author 粒度的序列,
    目前有如下特征 ["author_id", "play_time_sum", "reward_sum", "reward_count", "unseen_days"]

    参数
    -----
    `colossus_resp_attr`: [string] colossus response attr name
    `output_prefix`: [string] 输入 attr prefix, 默认值为 "colossus_author_"
    `live_item_version`: [int] LiveItem proto 版本, 支持 [2, 4], 默认值 2
    `limit_author_num`: 输出 Author 序列的长度限制, 默认值50
    `reward_only`: 是否只保留有打赏行为的主播序列，默认值false
    `filter_future`: 是否过滤未来的序列，默认值false

    示例
    -----
    ```python
    .live_se_author_list_from_colossus(colossus_resp_attr='colossus_resp',
                              output_prefix='colossus_author_',
                              live_item_version=2,
                              limit_author_num=50,
                              reward_only=False,
                              filter_future=False)
    ```
    """
    self._add_processor(LiveSeAuthorListFromColossusEnricher(kwargs))
    return self

  def enrich_search_query_rewritter(self, **kwargs):
    """
    SearchQueryRewriterEnricher
    -----
    搜索query分析算子

    参数
    -----
    `cmd_list`: [string_list] 必填项, query rewritter 的 cmd name
    `vertical_source`: [string_list] 非必填项, 若填，则需满足size需与`cmd_list`的size保持一致
    `params`: [json] 必填项, 其中 `raw_query` 是必填项,且其为动态参数
    `service_name`: [string] 必填项, 调用query rewritter的服务名
    `export_common_attr`: [string] 必填项, 返回的response写入的common attr
    `export_config`:[list] 非必填项, 
      - `cmd_name` [string] 是要抽取哪个cmd
      - `attr_name`[string] 抽取后对应的特征名
      - `path`[string] 是 SingleQa的路径,若为空,则抽取整个singleqa
    `copy_config`:[json] 非必填项, 将singleqa copy到另一个字段中
    `timeout`: [int] 非必填项, 动态参数, 服务调用的超时时间

    示例
    -----
    ```python
    .enrich_search_query_rewritter(
      cmd_list=["QA_COMMAND_DISTRIBUTE"],
      vertical_source=["GOODS_VERTICAL_V1"],
      params={
        "raw_query": "王者"
      },
      service_name="grpc_mmu_queryRewriterNewSeg",
      export_common_attr="QueryRewritter",
      export_config=[
        {
          "cmd_name": "QA_COMMAND_DISTRIBUTE",
          "path": "qr_query_bert_query_match_embedding_v3",
          "attr_name": "query_match_embedding_v3"
        }
      ],
      copy_config={
        "qrtoparent_resp.query_ana_livestream":"QA_COMMAND_DISTRIBUTE"
      },
      timeout=100
    )
    ```
    """
    self._add_processor(SearchQueryRewriterEnricher(kwargs))
    return self

  def search_leaf_retriever(self, **kwargs):
    """
    SearchLeafRetriever
    -----
    搜索query分析算子

    参数
    -----
    `name`: [string] [必填项] leaf retriever 的名字
    `search_type`: [string] 召回来源
    `channel`: [int] 召回来源
    `query_parser_config`: [json] [必填项] 语法树相关配置
      - `qr_cmd`: [string] [必填] 用哪个qr的cmd的返回结果来构建语法树。
      - `qr_response_attr`: [string] [必填] query rewriter response 对应的common attr
		  - `build_qp_tree_func`: [string] [必填] 用具体的哪个函数来构建语法树。具体可以参照se/txt2vid_se/query/rewriters/public/query_rewriter_util.h
		  - `enable_omit_group`: [bool] [非必填]  [默认值:false] 是否使用省略召回
		  - `build_extend_node`: [bool] [非必填] [默认值:true] 是否下发扩展词
		  - `remain_optional_node`: [bool] [非必填] [默认值:true] 是否保留optional节点
		  - `enable_annotation`: [bool] [非必填] [默认值:true] 是否建高级语法
      - `shift_omit_group_num`: [int] [非必填] [默认值: 0] 省略召回时,对于omit_group要移的位数,通常设置为1即可
    `timeout`: [int] [非必填项] 动态参数, 服务调用的超时时间
    `retrieval_config`: [list] [必填项] 召回库种的相关配置
      - `enable_cache`: [int] [非必填] 是否启用cache
      - `cache_ttl`: [int] [非必填] cache的ttl
      - `leaf_type`: [int] [非必填] 标识库种类型,启用cache需填,且不同库种对应不同的值
      - `name`: [string] [必填] 库种名字
      - `service_name`: [string] [必填] leaf 库的服务名名
      - `min_heap_seek_timeout`: [int] [必填] 最小堆seek的超时时间
      - `min_heap_result_capacity`: [int] [必填] 最小堆大小
      - `min_heap_max_num_results_seeked`: [int] [必填] 最大seek数
      - `leaf_cutoff`: [int] [必填] 单分片截断数量
      - `cutoff_nums`: [int] [必填] 单库种截断数量
    `request_config`: [json] [非必填] 请求需要额外配置的参数
    `cutoff_nums`: [int] [必填] 多库种merge后的截断数量

    示例
    -----
    ```python
    .search_leaf_retriever(
      retriever_name="DistributeNormalQuerySearch",
      search_type="SEARCH_TYPE_GOODS",
      channel=1,
      query_parser_config={
        "qr_response_attr": "QueryRewritter",
        "qr_cmd": "QA_COMMAND_DISTRIBUTE",
        "build_qp_tree_func": "BuildQPTree",
        "enable_omit_group": False,
        "build_extend_node": True,
        "remain_optional_node": True,
        "enable_annotation": True,
        "shift_omit_group_num": 0
      },
      timeout="{{recall_timeout}}",
      retrieval_config=[
        {
          "enable_cache":1,
          "cache_ttl":3600,
          "leaf_type":0,
          "name":"Solar",
          "service_name" : "grpc_se_DistributionLeafIndex",
          "min_heap_seek_timeout": 30,
          "min_heap_result_capacity": 75,
          "min_heap_max_num_results_seeked": 250,
          "leaf_cutoff": 75,
          "cutoff_nums":2000
        },
        {
          "enable_cache":0,
          "cache_ttl":3600,
          "leaf_type":0,
          "name":"Roc",
          "service_name" : "grpc_mmu_GoodsMiddleRocLeafDistribution",
          "min_heap_seek_timeout": 30,
          "min_heap_result_capacity": 100,
          "min_heap_max_num_results_seeked": 8000,
          "leaf_cutoff": 100,
          "cutoff_nums":2000
        }
      ],
      request_config={
        "search_params.experiments.leaf_query_match_weight": 1000,
        "search_params.experiments.goods_querymatch_version": 1,
        "search_params.experiments.goods_querymatch_threshold": "0:0.5:0.7",
        "search_params.experiments.leaf_query_match_threshold": "0.3:0.7",
        "search_params.query_search_type_resp.sub_query_normal.goods_embeddings[1].name": "goods_query_match",
        "search_params.query_search_type_resp.sub_query_normal.goods_embeddings[1].datas": "{{query_match_embedding_v3}}"
      },
      cutoff_nums=2000
    )
    ```
    """
    self._add_processor(SearchLeafRetriever(kwargs))
    return self

  def search_recall_merger_retriever(self, **kwargs):
    """
    SearchRecallMergerRetriever
    -----
    多路召回结果merge算子

    参数
    -----
    `merge_tables`: [list] [必填项] 需要merger的结果表
    `merge_attr_names`: [list] [非必填项] 需要merge的 item attr

    示例
    -----
    ```python
    .search_recall_merger_retriever(
      merge_tables=["DistributeNormalQuerySearch"],
      merge_attr_names=["search_type"]
    )
    ```
    """
    self._add_processor(SearchRecallMergerRetriever(kwargs))
    return self

  def enrich_search_query_init(self, **kwargs):
    """
    SearchQueryInitEnricher
    -----
    搜索的初始化算子

    参数
    -----
    `kess_event_loop_size`: [int] [非必填项] event_loop的大小
    `common_thread_pool_size`: [int] [非必填项] common 线程池的大小
    `common_thread_pool_queue_size`: [int] [非必填项] common 线程池的队列大小
    `internal_thread_pool_size`: [int] [非必填项] internal 线程池的大小
    `internal_thread_pool_queue_size`: [int] [非必填项] internal 线程池的队列大小
    `bthread_use_pthread_num`: [int] [非必填项] bthread使用的pthread的大小
    `bthread_use_pthread_num`: [int] [非必填项] bthread线程池的大小
    `pthread_pool_size`: [int] [非必填项] pthread线程池的大小
    `data_client_need_replica`: [bool] [非必填项] dataclient的redis是否需要kafka副本
    `data_client_init_list`: [string] [非必填项] dataclient的初始化列表,多个用逗号分隔
    `redis_cluster_name_set`: [string] [非必填项] dataclient的初始化redis的列表,多个用逗号分隔
    `enable_dict_server`: [bool] [非必填项] 是否启用字典服务
    `enable_dict_server_ns`: [bool] [非必填项] 启用字典服务的目录功能
    `dict_service_name`: [string] [非必填项] 字典服务名
    `arena_max_block_size`: [int] [非必填项] arena申请的最大块的大小,单位kb
    示例
    -----
    ```python
    .enrich_search_query_init(
      kess_event_loop_size=20,
      common_thread_pool_size=300,
      common_thread_pool_queue_size=2000,
      internal_thread_pool_size=300,
      internal_thread_pool_queue_size=2000,
      bthread_use_pthread_num=100,
      num_bthread_size=1024,
      pthread_pool_size=100,
      data_client_need_replica=False,
      data_client_init_list="redis",
      redis_cluster_name_set="videoattr",
      enable_dict_server=False,
      enable_dict_server_ns=False,
      dict_service_name="search-distribute-dragon-parent",
      arena_max_block_size=64
    )
    ```
    """
    self._add_processor(SearchQueryInitEnricher(kwargs))
    return self
  
  def enrich_search_query_end(self, **kwargs):
    """
    SearchQueryEndEnricher
    -----
    搜索的收尾算子

    参数
    -----

    示例
    -----
    ```python
    .enrich_search_query_end(
    )
    ```
    """
    self._add_processor(SearchQueryEndEnricher(kwargs))
    return self

  def search_rank_feature(self, **kwargs):
    """
    SearchRankFeatureEnricher
    -----
    搜索RankFeature计算算子

    参数
    -----
    `service_name`: [string] 必填项, 调用RankFeature的服务名
    `timeout`: [int] 非必填项, RPC超时配置 default is 150ms
    `range_end`: [int] 必填项, 选送TopN to RankFeature
    `partition_size`: [int] 必填项, 拆包情况下，每个包Size
    `business_name`: [string] 必填项, 业务名
    `degraded_kconf`: [string] 非必填项, RankFeature降级算子KConf配置名
    `send_common_attrs`: [list] 非必填项, 请求RankFeature透传的Common特征名
    `send_item_attrs`: [list] 非必填项, 请求RankFeature透传的Item特征名
    `recv_common_attrs`: [list] 非必填项, 解析RankFeature返回的Common特征名
    `recv_item_attrs`: [list] 非必填项, 解析RankFeature返回的Item特征名(RSP形式)
    `request_config`: [json] [非必填] 请求需要额外配置的参数
    `export_config`: [json] [非必填] 解析RankFeature返回的Item特征名(已有业务, 反射PB, 非RSP形式)

    示例
    -----
    ```python
    .search_rank_feature(
      service_name="grpc_se_RankFeatureDistribution",
      timeout=100,
      business_name="relevance",
      range_end=300,
      partition_size=30,
      degraded_kconf="kconf_name",
      send_common_attrs=["attr_name"],
      send_item_attrs=["recall_source"],
      recv_common_attrs=["output_common_attr_name"],
      recv_item_attrs=["output_item_attr_name"],
      request_config={
        "pb_data_source_set.pb_data_source[0].goods_search.single_qa": "{{QA_COMMAND_DISTRIBUTE}}",
        "pb_data_source_set.pb_data_source[0].goods_search.preprocessed_query": "{{QR_raw_query}}"
      },
      export_config={
        "goods_response_body.items[].relevance_model_score": "relevance_model_score",
        "goods_response_body.items[].relevance_cate_match_score": "relevance_cate_match_score",
        "goods_response_body.items[].relevance_attr_score": "relevance_attr_score",
        "goods_response_body.items[].relevance_entity_score": "relevance_entity_score",
        "goods_response_body.items[].brand_match_score": "brand_match_score",
        "goods_response_body.items[].relevance_other_attr_score": "relevance_other_attr_score",
        "goods_response_body.items[].rel_termfound_ratio": "rel_termfound_ratio",
        "goods_response_body.items[].rel_qimp_ratio": "rel_qimp_ratio",
        "goods_response_body.items[].merge_relevance_score": "merge_relevance_score"
      }
    )
    ```
    """
    self._add_processor(SearchRankFeatureEnricher(kwargs))
    return self
  def enrich_search_debug(self, **kwargs):
    """
    SearchDebugEnricher
    -----
    debug 算子

    参数
    -----
    `debug_config`: [json] [必填项] debug相关配置
      - `debug_name`: [string] [必填] debug的名字
      - `owner`: [string] [必填] debug所属的owner
		  - `model_name`: [string] [必填] debug对应的阶段名
		  - `title`: [string] [非必填] debug的标题
		  - `comment`: [string] [非必填] debug的注释
		  - `priority`: [int] [非必填] [默认值:0] debug的priority
		  - `log_level`: [int] [非必填] [默认值:1] debug触发对应的log_level
      - `is_common`: [bool] [非必填] [默认值: True] 是否是common侧特征
      - `attr_name`: [list] [必填] 需要debug的特征
    示例
    -----
    ```python
    .enrich_search_debug(
      debug_config=[
        {
          "debug_name": "ItemDebug",
          "owner": "zjp",
          "model_name": "FINAL_ADJUST",
          "title": "召回信息",
          "comment": "",
          "priority": 2,
          "log_level": 1,
          "is_common": False,
          "attr_name": ["recall_channels"]
        }
      ]
    )
    ```
    """
    self._add_processor(SearchDebugEnricher(kwargs))
    return self

  def poi_unimatch_enricher(self, **kwargs):
    """
    PoiUnimatchEnricher
    -----
    poi调用kml算子

    参数
    -----

    示例
    -----
    ```python
    .poi_unimatch_enricher(
    )
    ```
    """
    self._add_processor(PoiUnimatchEnricher(kwargs))
    return self

  def poi_querymatch_enricher(self, **kwargs):
    """
    PoiQuerymatchEnricher
    -----
    poi调用kml算子

    参数
    -----

    示例
    -----
    ```python
    .poi_querymatch_enricher(
    )
    ```
    """
    self._add_processor(PoiQuerymatchEnricher(kwargs))
    return self

  def poi_photo_unimatch_enricher(self, **kwargs):
    """
    PoiPhotoUnimatchEnricher
    -----
    poi_photo调用kml算子

    参数
    -----

    示例
    -----
    ```python
    .poi_photo_unimatch_enricher(
    )
    ```
    """
    self._add_processor(PoiPhotoUnimatchEnricher(kwargs))
    return self
  
  def enrich_search_raw_sample_package_label(self, **kwargs):
    """
    SearchRawSamplePackageLabelEnricher
    -----
    解析 Feasury 特征 uSearchPhotoPlayList

    参数
    -----
    `search_play_input_attr`: [string] Feasury 特征输入 attr
    示例
    -----
    ```python
    .enrich_search_raw_sample_package_label(
      search_play_input_attr = "uSearchPhotoPlayList"
    )
    ```
    """
    self._add_processor(SearchRawSamplePackageLabelEnricher(kwargs))
    return self
  
  def search_merchant_author_list_extract(self, **kwargs):
    """
    SeMerchantAuthorListExtractEnricher
    ------
    获取 user 侧特征

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `long_play_threshold`:[int] 长播阈值
    `valid_play_threshold`:[int] 无效播放阈值

    示例
    ------
    ``` python
    .search_merchant_author_list_extract(
      colossus_resp_attr="colossus_resp",
      long_play_threshold = 60,
      valid_play_threshold =5
    )
    ```
    """
    self._add_processor(SeMerchantAuthorListExtractEnricher(kwargs))
    return self


  def search_merchant_live_feat_enricher(self, **kwargs):
    self._add_processor(SeMerchantLiveFeatEnricher(kwargs))
    return self
  
  
  def se_reco_colossus_resp_flatten(self, **kwargs):
    """
    SeRecoColossusRespFlattenEnricher
    ------
    铺平搜索colossus v2pid数据；
    参数
    ------
    
    `timestamp`: [必填] [string] 时间戳字段；
    `photo_id`: [必填] [string] photo id字段；
    `query`: [必填] [string] query 字段；
    `item_fields`: [必填] [dict] 请求的特征和放入的 commonAttr (当设置了 v1_colossus_resp, 也就是要将返回数据转成 v1 格式时，该字段的 value 不再生效）
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .se_reco_colossus_resp_flatten(timestamp="timestamp",
                            photo_id="photo_id",
                            item_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             tag="my_tag",
                                             channel="my_channel",
                                             label="my_label",
                                             timestamp="my_timestamp"))
    ```
    """
    self._add_processor(SeRecoColossusRespFlattenEnricher(kwargs))
    return self


  def se_reco_colossus_resp_retriever(self, **kwargs):
    """
    SeRecoColossusRespRetriever
    ------
    铺平搜索colossus v2数据；common特征 2 item 特征；

    参数
    ------
    
    `timestamp`: [必填] [string] 时间戳字段；
    `photo_id`: [必填] [string] photo id字段；
    `query`: [必填] [string] query 字段；
    `item_fields`: [必填] [dict] 请求的特征和放入的 commonAttr (当设置了 v1_colossus_resp, 也就是要将返回数据转成 v1 格式时，该字段的 value 不再生效）
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .se_reco_colossus_resp_retriever(timestamp="timestamp",
                            photo_id="photo_id",
                            item_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             tag="my_tag",
                                             channel="my_channel",
                                             label="my_label",
                                             timestamp="my_timestamp"))
    ```
    """
    self._add_processor(SeRecoColossusRespRetriever(kwargs))
    return self

  def enrich_search_text_segment(self, **kwargs):
    """
    SearchTextSegmentEnricher
    -----
    对文本进行分词

    参数
    -----
    `input_source_type`: [string] [必填] 输入的类型，目前支持 `common_attr`和 `item_attr`
    `input_attr_name`: [string] [必填] 从这里读取 attr 的 string 作为 profile 的 key
    `output_segment_text_attr`: [string] [必填] 输出分隔后的文本属性名称，使用空格分隔
    `output_token_id_attr`: [list] [必填] 输出token id属性名称
    `output_type_id_attr`: [list] [必填] 输出token type属性名称
    `output_pos_id_attr`: [list] [必填] 输出token position属性名称
    `output_mask_id_attr`: [list] [必填] 输出token mask属性名称
    `text_len_limit`: [int] 字符串长度, 默认为50
  
    示例
    -----
    ```python
    .enrich_search_text_segment(
      input_source_type = "common_attr",
      input_attr_name = "query",
      output_segment_text_attr = "segment_text",
      output_token_id_attr = "seq_toekn_ids",
      output_type_id_attr = "seq_toen_type",
      output_pos_id_attr = "seq_token_pos",
      output_mask_id_attr = "seq_token_masks",
      text_len_limit = 50
    )
    ```
    """
    self._add_processor(SearchTextSegmentEnricher(kwargs))
    return self
  
  def push_text_segment_enricher(self, **kwargs):
    """
    PushTextSegmentEnricher
    ------
    对item侧文本特征分词,在gflag push_segment_word_vocab_file_name和push_release_ksdata_prefix中指定配置文件路径

    参数配置
    ------
    `sentence_attr` : [string] 待分词的字符串【必填】

    `output_id_attr` : [string] 分词结果在词表中的对应id【必填】

    `output_token_attr` : [string] 分词结果的token

    `sentence_len_limit` : [动态参数] 字符串对应分词的最大长度,动态参数,默认20

    `is_text_attr` : [bool] 为true时代表文案特征,SEP_ID为5. 为false时代表作者名特征,SEP_ID为6,默认true

    调用示例
    ------
    ``` python
    .push_text_segment_enricher(
      sentence_attr="input_sentence",
      output_id_attr="output_token_ids"
    )
    ```
    """
    self._add_processor(PushTextSegmentEnricher(kwargs))
    return self

  def se_q2i_cate_click_v2(self, **kwargs):
    """
    SeQ2ICateClickV2Enricher
    ------
    基于query类目检索商品点击序列

    (基于clickv2)

    示例
    ------
    ``` python
    .search_merchant_author_list_extract(
      colossus_resp_attr="colossus_resp_attr",
      limit_num = 50,
      query_attr_cate1 = "c1_id_list",
      query_attr_cate2 = "c2_id_list",
      query_attr_cate3 = "c3_id_list",
      query_attr_cate_prob = "prob_list",
      filter_future_attr = true,
      filter_low_prob_attr = true,
      user_cate3 = true
    )
    ```
    """
    self._add_processor(SeQ2ICateClickV2Enricher(kwargs))
    return self


  def live_se_goods_feat_enricher(self, **kwargs):
    self._add_processor(SearchLiveGoodsFeatEnricher(kwargs))
    return self
  
  def common_se_colossus_resp_retriever(self, **kwargs):
    """
    CommonSeColossusRespRetriever
    -----
    参数
    -----
    `colossus_resp_attr`: [string] outupt from the colossus processor
    `save_result_to_attr`: [string] 存储结果指针的attr_name
    `colossus_result_type`: [string] 结果类型，目前仅支持 LiveItemV4,GoodClickItemV2,LiveItemV2
    `use_fake_item_key`: [bool] 使用伪造id作为item_key, 防止相同的id的特征被覆盖的问题, 默认值false,用序列id作为item_key

    示例
    -----
    ```python
    .common_se_colossus_resp_retriever(
      colossus_resp_attr="colossus_resp",
      save_result_to_attr="colossus_item_res"
      colossus_result_type="LiveItemV4"
    )
    ```
    """
    self._add_processor(CommonSeColossusRespRetriever(kwargs))
    return self
    
  def enrich_unified_local_rank_index_v2(self, **kwargs):
    """
    """
    self._add_processor(UnifiedLocalRankIndexV2Enricher(kwargs))
    return self
  
  def search_rsp_dump_enricher(self, **kwargs):
    """
    """
    self._add_processor(SearchRspDumpEnricher(kwargs))
    return self
  
  def search_goods_text_segment_enrich(self, **kwargs):
    """
    """
    self._add_processor(SearchGoodsTextSegmentEnricher(kwargs))
    return self

  def search_goods_age_intent_transform_enrich(self, **kwargs):
    self._add_processor(SearchGoodsAgeIntentTransformEnricher(kwargs))
    return self
  
  def search_goods_gender_intent_enrich(self, **kwargs):
    self._add_processor(SearchGoodsGenderIntentEnricher(kwargs))
    return self

  def search_goods_query_deal_price_seg_enricher(self, **kwargs):
      self._add_processor(SearchGoodsQueryDealPriceSegEnricher(kwargs))
      return self

  def enrich_search_abtest_session(self, **kwargs):
      """
      SearchAbtestSessionEnricher
      -----
      abtestSession 算子

      参数
      -----
      `abtest_config`: [json] [必填项] abtest_session相关配置
        - `type`: [string] [必填] ab的类型, 可选类型为: int64, bool, string, double
        - `key`: [string] [必填] ab的名字,同时也是输出common_attr的key
        - `default_val`: [int, double, bool, string] [必填] ab对应的默认值
      `vertical_source`: [string] vertical_source对应的attr_name
      `abtest_list`: [string] [必填项] 来自上游的abtest_list对应的attr_name
      `distribute_abtest_list`: [string] [必填项] 下发给下游服务的abtest_list对应的attr_name
      示例
      -----
      ```python
      .enrich_search_abtest_session(
        abtest_config=[
          {
            "type": "int64",
            "key": "model_version",
            "default_val": 2,
          },
          {
            "type": "double",
            "key": "pctr_weight",
            "default_val": 4.0,
          }
        ],
        vertical_source="vertical_source",
        abtest_list="abtest_list",
        distribute_abtest_list="distribute_abtest_list"
      )
      ```
      """
      self._add_processor(SearchAbtestSessionEnricher(kwargs))
      return self

  def search_fetch_tower_topn_dot_product_pxtr(self, **kwargs):
        """
        SearchFetchTowerTopNDotProductAttrEnricher
        ------
        向远端请求预估多个label (or target) 的 user * item 的pxtr
        发item_embedding_key_attr送user embedding 和 item key 列表, 获取各item对应label下的user * item 的pxtr
        user embedding : 每个label都有独有的user embedding，多个label的user embedding拼接成一个更长的embedding
        参数
        ------
        `kess_service`: [string] 预估服务kess 服务名
        `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION
        `shards`: [int] 预估服务shard 数量
        `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50
        `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名
        `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 ItemAttr 获取 embedding key
        `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 ItemAttr 获取 embedding key 列表的key
        `predict_labels`: [string list] 与预估的label名称列表
        `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数
        `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding
        `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller
        `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                                同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名
        `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                            1: 以 Doublelist 输出至 item attr
                            2: 以 Double 输出至对应 pxtr 的 item attr
        `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1
        `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值
        `item_hetu_tag_attr`: [string] item hetu tag 在 context item attr 中的字段名 ， 可选
        `total_hetu_tag_num`: [int] 所有 hetu tag 种类的数量 , 可选
        `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"
        `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`
        示例
        ------
        ``` python
        .search_fetch_tower_topn_dot_product_pxtr(
            user_embedding_attr="pack_item_mm_mmu_emb",
            item_list_from_attr="pack_click_seq_item_id",
            use_item_key_as_embed_key=True,
            predict_labels=["dp"],
            kess_service="kws-merchant-search-infer-goods-se-gsu-emb-v2-dp-cal-ol",
            shards=1,
            timeout_ms=20,
            sub_req_num_in_shard=1,
            server_request_type="calc_dot_product",
            item_pxtr_value_attr= "pack_seq_item_id_mm_score_ptr",
            req_common_embedding_attr="req_item_emb",
            return_pxtr_value_attr="distance",
            output_type=0,
            pxtr_type=1,
            emb_dim=128,
            use_cosine=False
        )
        ```
        """
        self._add_processor(SearchFetchTowerTopNDotProductAttrEnricher(kwargs))
        return self

  def search_distribution_calib_enricher(self, **kwargs):
      """
      SearchDistributionCalibEnricher
      -----
      计算数据校准权重算子

      参数
      -----
      `click_coefficient`: [double] 计算ctr样本权重的系数
      `order_coefficient`: [double] 计算cvr样本权重的系数
      `ctr_attr_name`: [string] [必填项] ctr特征名称
      `cvr_attr_name`: [string] [必填项] cvr特征名称
      `pos_attr_name`: [string] [必填项] 排序位置特征名称
      `clk_label_attr_name`: [string] [必填项] 点击label特征名称
      `order_label_attr_name`: [string] [必填项] 下单label特征名称
      `output_click_attr_name`: [string] [必填项] 输出ctr样本权重的特征名称
      `output_order_attr_name`: [string] [必填项] 输出cvr样本权重的特征名称
      示例
      -----
      ```python
      .search_distribution_calib_enricher(
        click_coefficient = 1.2,
        order_coefficient = 0.8,
        ctr_attr_name = "infer_ctr",
        cvr_attr_name = "infer_cvr",
        pos_attr_name = "position",
        clk_label_attr_name = "clk_label",
        order_label_attr_name = "ordr_label",
        output_click_attr_name = "sample_weight_clk",
        output_order_attr_name = "sample_weight_ordr"
      )
      ```
      """
      self._add_processor(SearchDistributionCalibEnricher(kwargs))
      return self
  
  def search_get_caller_enricher(self, **kwargs):
      self._add_processor(SearchGetCallerEnricher(kwargs))
      return self
  
  def search_goods_price_enricher(self, **kwargs):
      """
      SearchGoodsPriceEnricher
      -----
      获取商品券后价 

      参数
      -----
      `timeout`: [int] [非必填项] 动态参数, 服务调用的超时时间
      `item_id_attr`: [string] [非必填项] 商品id输出attr名称, 默认为item_id
      `price_attr`: [string] [必填项] 商品券后价输出attr名称
      示例
      -----
      ```python
      .search_goods_price_enricher(
          timeout = 20,
          item_id_attr = "item_id",
          price_attr = "price"
      )
      ```
      """
      self._add_processor(SearchGoodsPriceEnricher(kwargs))
      return self
  
  def search_vision_log_collector_enricher(self, **kwargs):
      self._add_processor(SearchVisionLogCollectorEnricher(kwargs))
      return self

  def vgs_goods_new_breaker_arranger(self, **kwargs):
      self._add_processor(VgsGoodsNewBreakerArranger(kwargs))
      return self
  
  def parse_context_se(self, **kwargs):
    """
    SeRecoParseContextRetriever
    ------
    将通过 `dump_context()` dump 后的 context，反解并 merge 至当前 context 中继续使用。

    其 item 侧数据将会补充在后面，common 数据如果重复将会直接覆盖掉。

    参数配置
    ------
    `parse_from_attr`: [string] 必配项，context 来自哪个 string 字段

    `data_format`: [string] 选配项，string 数据的格式，可选值 data_table 或 step_info，默认值为 step_info

    `extract_common_attrs`: [list] 用以填写需要解析的 common_attr 的名称列表，如果名称列表为空，默认不写。

    `extract_item_results`: [bool] 是否把 item 追加至当前结果集的最后，如果 extract_item_attrs 不为空，该项默认为 True，否则为 False。

    `extract_item_attrs`: [list] 解析对应的 item_attr 的 名称列表，如果名称列表为空，默认不写。

    调用示例
    ------
    ``` python
    .parse_context_se(
      parse_from_attr="context",
      extract_common_attrs=["test1", "test2"],
      extract_item_results=True,
      extract_item_attrs=["test_item1", "test_item2"]
    )
    ```
    """
    self._add_processor(SeRecoParseContextRetriever(kwargs))
    return self