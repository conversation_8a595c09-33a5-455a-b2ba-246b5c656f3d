#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class StarryEmbeddingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_starry_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("source_photo_attr"))
      for name in ["total_limit","top_k","bucket_id"]:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonLiveSeColossusV4RespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_se_gsu_retriever_with_colossus_resp_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
          "save_timestamp_to_attr", "save_author_id_to_attr", "save_play_time_to_attr",
          "save_auto_play_time_to_attr", "save_hetu_tag_to_attr", "save_channel_to_attr",
          "save_cluster_id_to_attr", "save_label_to_attr", "save_reward_to_attr",
          "save_reward_count_to_attr", "save_item_id_to_attr", "save_audience_count_to_attr",
          "save_user_latitude_to_attr", "save_user_longitude_to_attr", "save_author_latitude_to_attr",
          "save_author_longitude_to_attr", "save_order_price_to_attr"
        ] if key in self._config
    }

class SearchLeafRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_leaf_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["query_parser_config"]["qr_response_attr"])
    ret.update(self.extract_dynamic_params(self._config["timeout"]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["retriever_name"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("search_type")
    return ret
  
  @strict_types
  def is_async(self) -> bool:
    return True

  
class SearchRecallMergerRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_recall_merger_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("merge_tables", []))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("search_type")
    ret.update(self._config.get("merge_attr_names", []))
    return ret

class SeRecoColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
     return "se_reco_colossus_resp_retriever"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if 'timestamp' in self._config:
      ret.add(self._config["timestamp"])
    if 'photo_id' in self._config:
      ret.add(self._config["photo_id"])
    if 'query' in self._config:
      ret.add(self._config["query"])
    query_fields = self._config.get("query_fields", {})
    item_fields = self._config.get("item_fields", {})
    query_fields.update(item_fields)
    for k, _ in query_fields.items():
      ret.add(k)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    query_fields = self._config.get("query_fields", {})
    item_fields = self._config.get("item_fields", {})
    query_fields.update(item_fields)
    for _, v in query_fields.items():
      attrs.add(v)
    return attrs

class CommonSeColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_se_colossus_resp_retriever"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("colossus_result_type"), "`colossus_result_type` 是必选项")
    check_arg(self._config.get("save_result_to_attr"), "`save_result_to_attr` 是必选项")
    check_arg(self._config.get("colossus_resp_attr"), "`colossus_resp_attr` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["save_result_to_attr"])
    return ret

class SeRecoParseContextRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_context_se"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_common_attrs", []))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_item_attrs", []))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["parse_from_attr"])
    return attrs

  @strict_types
  def like_an_enricher(self) -> bool:
    return bool(not self._config.get("extract_item_results", len(self._config.get("extract_item_attrs", [])) > 0))
