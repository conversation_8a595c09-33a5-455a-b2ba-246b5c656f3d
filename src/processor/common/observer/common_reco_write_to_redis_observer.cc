#include "dragon/src/processor/common/observer/common_reco_write_to_redis_observer.h"
#include <memory>
#include <utility>
#include "absl/strings/str_join.h"
#include "kenv/service_meta.h"

DEFINE_int64(redis_async_pool_thread_num, 0, "thread num of redis async thread pool");
DEFINE_int64(redis_async_pool_task_queue_max_size, 0, "thread num of redis async thread pool");
DEFINE_int64(redis_async_pool_min_thred_num, 4, "min thread num of redis async thread pool");
DEFINE_int64(redis_async_pool_max_thred_num, 4, "max thread num of redis async thread pool");
DEFINE_int64(redis_async_pool_min_thread_num, 0, "min thread num of redis async thread pool");
DEFINE_int64(redis_async_pool_max_thread_num, 0, "max thread num of redis async thread pool");
DEFINE_bool(abort_stress_test_request_write_redis, false, "abort stress test request from ptp or not");

namespace ks {
namespace platform {

std::unique_ptr<AsyncTaskThreadPool<void *>> CommonRecoWriteToRedisObserver::local_async_pool_ = nullptr;
std::once_flag CommonRecoWriteToRedisObserver::oc_;

void CommonRecoWriteToRedisObserver::SaveValueWithRedis(ks::infra::RedisPipeline *redis_pipeline,
                                                        const std::string &key, const std::string &value,
                                                        int expire_second, const std::string &key_prefix) {
  if (key.empty()) {
    return;
  }
  if (expire_second > 0) {
    redis_pipeline->SetEx(key_prefix + key, value, expire_second);
  } else {
    // 小于等于 0 的过期时间则表示不设置过期时间
    redis_pipeline->Set(key_prefix + key, value);
  }
  return;
}

void CommonRecoWriteToRedisObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  if (FLAGS_abort_stress_test_request_write_redis) {
    bool is_stress_test_flow = ks::infra::kenv::IsStressTestFlow();
    if (is_stress_test_flow) {
      CL_LOG(INFO) << "write to redis cancelled: this is a stress test request from ptp.";
      return;
    }
  }
  if (!redis_client_) {
    redis_client_ = GetRedisClient();
    if (!redis_client_) {
      CL_LOG_ERROR_EVERY("write_to_redis", "client_lazy_init_fail:" + kcc_cluster_, 100)
          << "redis client init failed, cluster name: " << kcc_cluster_;
      return;
    }
  }

  auto redis_pipeline = std::make_shared<ks::infra::RedisPipeline>();
  redis_pipeline->SetTimeoutMs(timeout_ms_);
  bool write_nothing = true;
  std::string key_prefix = GetStringProcessorParameter(context, key_prefix_, "");

  if (WriteRedisForCommonAttrs(context, redis_pipeline.get(), key_prefix)) {
    write_nothing = false;
  }
  if (WriteRedisForItemAttrs(context, begin, end, redis_pipeline.get(), key_prefix)) {
    write_nothing = false;
  }
  int64 put_in_pool_ts = base::GetTimestamp();
  if (!write_nothing) {
    auto wait_redis_response = [put_in_pool_ts, request_type = context->GetRequestType(),
                                cluster = kcc_cluster_, client = redis_client_,
                                pipeline = std::move(redis_pipeline)]() -> void * {
      int64 execute_start_ts = base::GetTimestamp();
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          execute_start_ts - put_in_pool_ts, kPerfNs, "sub_flow_queue_time",
          GlobalHolder::GetServiceIdentifier(), request_type, "write_to_redis");
      ks::infra::RedisErrorCode err = client->Exec(pipeline.get());
      if (err != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        CL_LOG_WARNING("write_redis", cluster + ":" + ks::infra::err2str(err))
            << "CommonRecoWriteToRedisObserver write redis fail. kcc_cluster:" << cluster
            << ". Error info: " << ks::infra::err2str(err);
      }
      // XXX(fangjianbing): 这里的返回值其实没有用，用一个非 null 的值防止底层误判为请求失败
      return reinterpret_cast<void *>(0x1);
    };

    auto waiting_task_num = local_async_pool_->GetQueueSize();
    base::perfutil::PerfUtilWrapper::IntervalLogStash(waiting_task_num, kPerfNs, "sub_flow_waiting_task_num",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), "write_to_redis");
    std::future<void *> future = local_async_pool_->Async(
        context->GetRequestType(), GetName(), std::move(wait_redis_response), GetDownstreamProcessor());

    auto callback = [](void *do_not_access) {
      // NOTE(fangjianbing): 不要在 callback 里访问传入的指针，它恒为 wait_redis_response 返回的假地址
    };

    RegisterLocalAsyncCallback(
        context, std::move(future), std::move(callback), []() {}, "write_to_redis:" + kcc_cluster_);
  }
}

// 给定 key 的情况下，从 common_attr 获取 value 进行存储
// 存储逻辑判断类型顺序： int, double, string, int_list, double_list, string_list
bool CommonRecoWriteToRedisObserver::SaveWithCommonAttr(ReadableRecoContextInterface *context,
                                                        const std::vector<std::string> &keys,
                                                        ks::infra::RedisPipeline *redis_pipeline,
                                                        const std::string &key_prefix) {
  if (!value_) {
    return false;
  }
  int64 int_value = 0;
  double double_value = 0;
  std::string string_value;
  std::vector<int64> int_list_value;
  std::vector<double> double_list_value;
  std::vector<absl::string_view> string_list_value;
  int expire_second = GetIntProcessorParameter(context, expire_second_, -1);

  if (TryGetIntProcessorParameter(context, value_, &int_value)) {
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, std::to_string(int_value), expire_second, key_prefix);
    }
    return true;
  } else if (TryGetDoubleProcessorParameter(context, value_, &double_value)) {
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, std::to_string(double_value), expire_second, key_prefix);
    }
    return true;
  } else if (TryGetStringProcessorParameter(context, value_, &string_value)) {
    std::string save_value(string_value.data(), string_value.size());
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, save_value, expire_second, key_prefix);
    }
    return true;
  } else if (TryGetIntListProcessorParameter(context, value_, &int_list_value)) {
    std::string save_value = absl::StrJoin(int_list_value, list_separator_);
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, save_value, expire_second, key_prefix);
    }
    return true;
  } else if (TryGetDoubleListProcessorParameter(context, value_, &double_list_value)) {
    std::string save_value = absl::StrJoin(double_list_value, list_separator_);
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, save_value, expire_second, key_prefix);
    }
    return true;
  } else if (TryGetStringListProcessorParameter(context, value_, &string_list_value)) {
    std::string save_value = absl::StrJoin(string_list_value, list_separator_);
    for (const auto &key : keys) {
      SaveValueWithRedis(redis_pipeline, key, save_value, expire_second, key_prefix);
    }
    return true;
  }
  return false;
}

std::vector<std::string> CommonRecoWriteToRedisObserver::GetCommonKey(ReadableRecoContextInterface *context) {
  std::vector<std::string> keys;
  std::string key;
  int64 int_key = 0;
  std::vector<absl::string_view> string_list;
  std::vector<int64> int_list;

  if (TryGetStringProcessorParameter(context, key_, &key)) {
    keys.emplace_back(key.data(), key.size());
  } else if (TryGetIntProcessorParameter(context, key_, &int_key)) {
    keys.emplace_back(std::to_string(int_key));
  } else if (TryGetStringListProcessorParameter(context, key_, &string_list)) {
    for (auto sv : string_list) {
      keys.emplace_back(sv.data(), sv.size());
    }
  } else if (TryGetIntListProcessorParameter(context, key_, &int_list)) {
    for (auto &val : int_list) {
      keys.emplace_back(std::to_string(static_cast<uint64>(val)));
    }
  }
  return keys;
}

bool CommonRecoWriteToRedisObserver::WriteRedisForCommonAttrs(ReadableRecoContextInterface *context,
                                                              ks::infra::RedisPipeline *redis_pipeline,
                                                              const std::string &key_prefix) {
  std::vector<std::string> keys = GetCommonKey(context);
  if (keys.empty()) {
    return false;
  }
  return SaveWithCommonAttr(context, keys, redis_pipeline, key_prefix);
}

bool CommonRecoWriteToRedisObserver::WriteRedisForItemAttrs(ReadableRecoContextInterface *context,
                                                            RecoResultConstIter begin,
                                                            RecoResultConstIter end,
                                                            ks::infra::RedisPipeline *redis_pipeline,
                                                            const std::string &key_prefix) {
  std::string key_from_item_attr;
  if (!TryGetStringProcessorParameter(context, key_from_item_attr_, &key_from_item_attr)) {
    return false;
  }
  bool write_status = false;
  std::string value_from_item_attr = GetStringProcessorParameter(context, value_from_item_attr_, "");
  int expire_second = GetIntProcessorParameter(context, expire_second_, -1);
  auto *value_accessor = context->GetItemAttrAccessor(value_from_item_attr);
  auto *key_accessor = context->GetItemAttrAccessor(key_from_item_attr);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    std::vector<std::string> keys;
    if (auto int_val = result.GetIntAttr(key_accessor)) {
      keys.emplace_back(std::to_string(static_cast<uint64>(*int_val)));
    } else if (auto string_val = result.GetStringAttr(key_accessor)) {
      keys.emplace_back(string_val->data(), string_val->size());
    } else if (auto int_list = result.GetIntListAttr(key_accessor)) {
      for (const int64 &key : *int_list) {
        keys.emplace_back(std::to_string(static_cast<uint64>(key)));
      }
    } else if (auto string_list = result.GetStringListAttr(key_accessor)) {
      for (auto sv : *string_list) {
        keys.emplace_back(sv.data(), sv.size());
      }
    }
    if (!keys.empty()) {
      // 先判断从 common_attr 里面获取，如果没有值则从 item_attr 里面判断
      if (SaveWithCommonAttr(context, keys, redis_pipeline, key_prefix)) {
        write_status = true;
      } else if (!value_from_item_attr.empty()) {
        std::string save_value = "";
        bool has_value = false;
        if (auto int_val = result.GetIntAttr(value_accessor)) {
          save_value = std::to_string(static_cast<uint64>(*int_val));
          has_value = true;
        } else if (auto string_val = result.GetStringAttr(value_accessor)) {
          save_value.assign(string_val->data(), string_val->size());
          has_value = true;
        } else if (auto double_val = result.GetDoubleAttr(value_accessor)) {
          save_value = std::to_string(*double_val);
          has_value = true;
        } else if (auto int_list_val = result.GetIntListAttr(value_accessor)) {
          save_value = absl::StrJoin(*int_list_val, list_separator_);
          has_value = true;
        } else if (auto double_list_val = result.GetDoubleListAttr(value_accessor)) {
          save_value = absl::StrJoin(*double_list_val, list_separator_);
          has_value = true;
        } else if (auto string_list_val = result.GetStringListAttr(value_accessor)) {
          save_value = absl::StrJoin(*string_list_val, list_separator_);
          has_value = true;
        }
        if (has_value) {
          for (const auto &key : keys) {
            SaveValueWithRedis(redis_pipeline, key, save_value, expire_second, key_prefix);
          }
          write_status = true;
        }
      }
    }
  });
  return write_status;
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoWriteToRedisObserver, CommonRecoWriteToRedisObserver)
}  // namespace platform
}  // namespace ks
