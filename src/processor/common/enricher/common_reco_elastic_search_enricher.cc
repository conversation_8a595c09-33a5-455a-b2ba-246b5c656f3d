#include "dragon/src/processor/common/enricher/common_reco_elastic_search_enricher.h"
#include <iostream>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "folly/String.h"

namespace ks {
namespace platform {

void CommonRecoElasticSearchEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  if (!has_attr_accessor_init_) {
    for (auto &attr_value_config : item_attrs_) {
      attr_value_config.accessor = context->GetItemAttrAccessor(attr_value_config.attr_name);
    }
    if (!save_score_to_.empty()) {
      save_score_to_accessor_ = context->GetItemAttrAccessor(save_score_to_);
    }

    has_attr_accessor_init_ = true;
  }

  std::string cluster_str = GetStringProcessorParameter(context, "clusters");
  std::string index = GetStringProcessorParameter(context, "index");
  std::string query = GetStringProcessorParameter(context, "query");
  if (cluster_str.empty() || index.empty() || query.empty()) {
    CL_LOG(WARNING) << "skipped processor: " << GetName() << ", any of [clusters, index, query] is empty";
    return;
  }
  kshttplib::Headers headers;
  for (auto &header_cfg : headers_config_) {
    std::string value = GetStringProcessorParameter(context, header_cfg.value_config, "");
    if (value.empty()) {
      CL_LOG(WARNING) << "header value is empty: " << header_cfg.name;
    }
    headers.emplace(header_cfg.name, std::move(value));
  }
  std::vector<std::string> clusters;
  folly::split(',', cluster_str.data(), clusters);
  if (clusters.size() == 0) {
    CL_LOG_WARNING("enrich_by_elastic_search", "clusters_empty") << "clusters is empty";
    return;
  }
  int cluster_index = std::rand() % clusters.size();
  std::string rsp = SendRequestToES(clusters[cluster_index], index, headers, query);
  if (rsp.empty()) {
    return;
  }

  HandleResponse(context, begin, end, rsp);
  if (!save_response_to_.empty()) {
    context->SetStringCommonAttr(save_response_to_, std::move(rsp));
  }
}

std::string CommonRecoElasticSearchEnricher::SendRequestToES(const std::string &cluster,
                                                           const std::string &index,
                                                           const kshttplib::Headers &headers,
                                                           const std::string &query) {
  std::string url = index + "/_search";
  CL_LOG(INFO) << "cluster:" << cluster << ", es query: " << query << ", url:" << url;
  kshttplib::Client cli(cluster.c_str());
  cli.set_keep_alive(true);
  cli.set_read_timeout(std::chrono::milliseconds(timeout_ms_));
  auto res = cli.Post(url.c_str(), headers, query, "application/json");
  if (res) {
    CL_LOG(INFO) << "status: " << res->status << " content:" << res->body;
    if (res->status == 200) {
      return res->body;
    } else {
      CL_LOG_ERROR("enrich_by_elastic_search", "request_failed")
          << "HTTP request failed (status: " << res->status << ").";
      return "";
    }
  } else {
    CL_LOG_ERROR("enrich_by_elastic_search", "request_failed")
        << "HTTP request failed (URL: " << url << "). Error: " << res.error();
    return "";
  }
}

void CommonRecoElasticSearchEnricher::HandleResponse(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end,
                                                     const std::string &response) {
  base::Json es_json(base::StringToJson(response));
  auto *es_hits = es_json.Get("hits");
  if (!es_hits) {
    CL_LOG_ERROR("enrich_by_elastic_search", "miss_hits")
        << "Missing 'hits' field in ElasticSearch response.";
    return;
  }
  auto *real_list = es_hits->Get("hits");
  if (!real_list || !real_list->IsArray()) {
    CL_LOG_ERROR("enrich_by_elastic_search", "miss_hits")
        << "Missing 'hits' field in ElasticSearch response.";
    return;
  }
  std::unordered_map<uint64, const base::Json *> item_infos;
  item_infos.reserve(real_list->size());
  for (const auto *item_json : real_list->array()) {
    if (!item_json) {
      continue;
    }
    std::string id_str = item_json->GetString("_id", "0");
    uint64 id;
    if (!absl::SimpleAtoi(id_str, &id)) continue;
    item_infos.emplace(id, item_json);
  }

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto it = item_infos.find(result.ItemKey());
    const bool found = (it != item_infos.end());
    if (save_score_to_accessor_) {
      double score = found ? it->second->GetNumber("_score", default_score_) : default_score_;
      context->SetDoubleItemAttr(result, save_score_to_accessor_, score);
    }
    if (found) {
      FillItemAttrs(context, result, it->second->Get("_source"));
    } else {
      FillItemAttrs(context, result);
    }
  });
}

void CommonRecoElasticSearchEnricher::FillItemAttrs(MutableRecoContextInterface *context,
                                                    const CommonRecoResult &result,
                                                    const base::Json *source) {
  for (const auto &config : item_attrs_) {
    switch (config.attr_type) {
      case AttrType::INT: {
        int64 val = source ? source->GetInt(config.attr_name, config.int_value) : config.int_value;
        context->SetIntItemAttr(result, config.accessor, val);
        break;
      }
      case AttrType::FLOAT: {
        double val = source ? source->GetNumber(config.attr_name, config.double_value) : config.double_value;
        context->SetDoubleItemAttr(result, config.accessor, val);
        break;
      }
      case AttrType::STRING: {
        std::string val =
            source ? source->GetString(config.attr_name, config.string_value) : config.string_value;
        context->SetStringItemAttr(result, config.accessor, std::move(val));
        break;
      }
      case AttrType::FLOAT_LIST: {
        std::vector<double> vals;
        if (source) {
          auto *val_list = source->Get(config.attr_name);
          if (val_list && val_list->IsArray()) {
            vals.reserve(val_list->size());
            for (const auto *attr : val_list->array()) {
              vals.push_back(attr->NumberValue(0.0f));
            }
          } else {
            vals = config.double_list_value;
          }
        } else {
          vals = config.double_list_value;
        }
        context->SetDoubleListItemAttr(result, config.accessor, std::move(vals));
        break;
      }
      default:
        break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoElasticSearchEnricher, CommonRecoElasticSearchEnricher);
}  // namespace platform
}  // namespace ks
