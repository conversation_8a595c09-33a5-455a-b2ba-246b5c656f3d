#pragma once
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/httplib.h"

namespace ks {
namespace platform {
class CommonRecoElasticSearchEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoElasticSearchEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct HttpHeaderConfig {
    std::string name;
    const base::Json *value_config = nullptr;
  };

  struct AttrValueConfig {
    int64 int_value = 0;
    double double_value = 0.0;
    std::string string_value;
    std::vector<double> double_list_value;
    AttrType attr_type = AttrType::UNKNOWN;
    std::string attr_name;
    ItemAttr *accessor = nullptr;
  };

  bool InitProcessor() override {
    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ < 0) {
      LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! timeout_ms should be >0.";
      return false;
    }
    auto *item_attrs = config()->Get("item_attrs");

    if (item_attrs && item_attrs->IsArray()) {
      for (const auto *value_config : item_attrs->array()) {
        if (!value_config || !value_config->IsObject()) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! Values of 'item_attrs' "
                     << "should be an object";
          return false;
        }
        AttrValueConfig attr_value_config;
        if (!ParseAttrsConfig(value_config, &attr_value_config)) {
          return false;
        }
        item_attrs_.emplace_back(std::move(attr_value_config));
      }
    }

    auto *json_headers = config()->Get("headers");
    if (!ParseHttpHeaders(json_headers, &headers_config_)) {
      LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! Failed to extract headers.";
      return false;
    }

    save_response_to_ = config()->GetString("save_response_to");
    save_score_to_ = config()->GetString("save_score_to");
    default_score_ = config()->GetNumber("default_score", 0.0);

    return true;
  }

  bool ParseAttrsConfig(const base::Json *config, AttrValueConfig *value) {
    value->attr_name = config->GetString("name");
    if (value->attr_name.empty()) {
      LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! name should be a string";
      return false;
    }
    auto value_json = config->Get("value");
    if (!value_json) {
      LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! value should not be emtpy";
      return false;
    }
    value->attr_type = RecoUtil::ParseAttrType(config->GetString("type"));
    switch (value->attr_type) {
      case AttrType::INT:
        if (!value_json->IntValue(&value->int_value)) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! type and value's type "
                        "mismatch, type: int, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::FLOAT:
        if (!value_json->NumberValue(&value->double_value)) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! type and value's type "
                        "mismatch, type: float, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::STRING:
        if (!value_json->StringValue(&value->string_value)) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! type and value's type "
                        "mismatch, type: string, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::FLOAT_LIST:
        if (!RecoUtil::ExtractDoubleListFromJsonConfig(value_json, &value->double_list_value, true)) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! type and value's type "
                        "mismatch, type: float_list, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      default:
        LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed! type not supported";
        return false;
    }
    return true;
  }

  bool ParseHttpHeaders(const base::Json *json_headers, std::vector<HttpHeaderConfig> *heads_config) {
    if (json_headers && json_headers->IsArray()) {
      for (const auto *cfg : json_headers->array()) {
        if (!cfg || !cfg->IsObject()) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed!"
                     << " 'headers' config should be an array of objects.";
          return false;
        }
        HttpHeaderConfig header_cfg;
        header_cfg.name = cfg->GetString("name");
        if (header_cfg.name.empty()) {
          LOG(ERROR) << "CommonRecoElasticSearchEnricher init failed!"
                     << " Missing 'name' in 'header' config.";
          return false;
        }
        header_cfg.value_config = cfg->Get("value");
        heads_config->push_back(std::move(header_cfg));
      }
    }
    return true;
  }

  std::string SendRequestToES(const std::string &cluster, const std::string &index,
                            const kshttplib::Headers &headers, const std::string &query);

  void FillItemAttrs(MutableRecoContextInterface *context, const CommonRecoResult &result,
                     const base::Json *source = nullptr);
  void HandleResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end, const std::string &response);

 private:
  bool has_attr_accessor_init_ = false;
  int32 timeout_ms_ = 200;
  std::vector<AttrValueConfig> item_attrs_;
  std::vector<HttpHeaderConfig> headers_config_;
  std::string save_response_to_;
  std::string save_score_to_;
  double default_score_ = 0.0;
  ItemAttr *save_score_to_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoElasticSearchEnricher);
};
}  // namespace platform
}  // namespace ks
