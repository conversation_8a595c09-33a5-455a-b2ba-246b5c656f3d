#include "dragon/src/processor/ext/explore/enricher/explore_short_uninterest_tagger_enricher.h"
#include <unordered_map>
#include <vector>

namespace ks {
namespace platform {

void ExploreShortUninterestTaggerEnricher::<PERSON>rich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  auto prev_items = explore::GetIntListCommonAttr(context, config(), "prev_item_from_attr");
  auto prev_items_timestamp = explore::GetIntListCommonAttr(context,
      config(), "prev_item_from_attr_timestamp");
  auto prev_items_label = explore::GetIntListCommonAttr(context,
      config(), "prev_item_label_from_attr");
  if (!prev_items || !prev_items_timestamp || !prev_items_label) {
    return;
  }
  if (prev_items->size() != prev_items_timestamp->size() ||
      prev_items->size() != prev_items_label->size()) {
    return;
  }

  auto time_window = GetIntProcessorParameter(context, "time_window", 0);
  auto realshow_num_threshold = GetIntProcessorParameter(context, "realshow_num_threshold", 0);
  auto realshow_no_click_threshold = GetIntProcessorParameter(context, "realshow_no_click_threshold", 0);
  bool enable_cluster_id_uninterest_tagger = GetIntProcessorParameter(context, "enable_cluster_id", 0) > 0;
  ItemAttr* cluster_id_accessor = nullptr;
  if (!cluster_id_attr_.empty()) {
    cluster_id_accessor = context->GetItemAttrAccessor(cluster_id_attr_);
  } else {
    enable_cluster_id_uninterest_tagger = false;
  }

  bool enable_hetu_level_five_uninterest_tagger =
      GetIntProcessorParameter(context, "enable_hetu_level_five", 0) > 0;
  ItemAttr* hetu_level_five_accessor = nullptr;
  if (!hetu_level_five_attr_.empty()) {
    hetu_level_five_accessor = context->GetItemAttrAccessor(hetu_level_five_attr_);
  } else {
    enable_hetu_level_five_uninterest_tagger = false;
  }

  int64 stop_timestamp = base::GetTimestamp() / base::Time::kMicrosecondsPerSecond - time_window;
  folly::F14FastMap<int64, int> hetu_id_cnt_mp, hetu_level_five_cnt_mp;
  folly::F14FastSet<int64> prev_click_items_set, meet_hetu_id_set, meet_hetu_level_five_set;

  for (size_t i = 0; i < prev_items->size(); i++) {
    if (prev_items_label->at(i) & 1) {
      prev_click_items_set.insert(prev_items->at(i));
    }
  }
  // 本身兴趣 + 非兴趣点击
  int64 list_length = std::min(prev_items->size(), prev_items_timestamp->size());
  int64 limit_num = std::min(list_length, realshow_num_threshold);
  for (int idx = 0; idx < limit_num; idx++) {
    auto item_key = prev_items->at(idx);
    int64 realshow_timestamp = prev_items_timestamp->at(idx);
    if (realshow_timestamp / 1000 < stop_timestamp) {
      break;
    }
    if (enable_cluster_id_uninterest_tagger) {
      auto hetu_cluster_id = context->GetIntItemAttr(item_key, cluster_id_accessor);
      if (hetu_cluster_id) {
        if (!prev_click_items_set.count(item_key) && !meet_hetu_id_set.count(*hetu_cluster_id)) {
          hetu_id_cnt_mp[*hetu_cluster_id]++;
        } else {
          meet_hetu_id_set.insert(*hetu_cluster_id);
        }
      }
    }
    if (enable_hetu_level_five_uninterest_tagger) {
      auto hetu_level_five = context->GetIntListItemAttr(item_key, hetu_level_five_accessor);
      if (hetu_level_five && hetu_level_five->size()) {
        int64 hetu_five_id = hetu_level_five->at(0);
        if (!prev_click_items_set.count(item_key) && !meet_hetu_level_five_set.count(hetu_five_id)) {
          hetu_level_five_cnt_mp[hetu_five_id]++;
        } else {
          meet_hetu_level_five_set.insert(hetu_five_id);
        }
      }
    }
  }

  bool need_to_save_cluster_detail = context->GetNeedStepInfo();
  if (need_to_save_cluster_detail) {
    if (!output_cid_stat_attr_.empty()) {
      std::vector<int64> uninterest_hetu_id_list;
      uninterest_hetu_id_list.reserve(hetu_id_cnt_mp.size());
      for (auto iter = hetu_id_cnt_mp.begin(); iter != hetu_id_cnt_mp.end(); ++iter) {
        uninterest_hetu_id_list.emplace_back(iter->first);
      }
      context->SetIntListCommonAttr(output_cid_stat_attr_, std::move(uninterest_hetu_id_list));
    }
    if (!output_hetu5_stat_attr_.empty()) {
      std::vector<int64> uninterest_hetu5_list;
      uninterest_hetu5_list.reserve(hetu_level_five_cnt_mp.size());
      for (auto iter = hetu_level_five_cnt_mp.begin(); iter != hetu_level_five_cnt_mp.end(); ++iter) {
        uninterest_hetu5_list.emplace_back(iter->first);
      }
      context->SetIntListCommonAttr(output_hetu5_stat_attr_, std::move(uninterest_hetu5_list));
    }
  }

  auto output_attr_accessor = context->GetItemAttrAccessor(output_flag_attr_);
  if (output_attr_accessor) {
    std::for_each(begin, end, [this, context, output_attr_accessor, &hetu_id_cnt_mp,
        &hetu_level_five_cnt_mp, &realshow_no_click_threshold,
        &cluster_id_accessor, &hetu_level_five_accessor,
        &enable_cluster_id_uninterest_tagger, &enable_hetu_level_five_uninterest_tagger]
        (const CommonRecoResult &result) {
      int64 hetu_id = -1;
      int hetu_hitting_num = 0;
      if (enable_cluster_id_uninterest_tagger) {
        auto hetu_cluster_id = context->GetIntItemAttr(result, cluster_id_accessor);
        if (hetu_cluster_id) {
          hetu_id = *hetu_cluster_id;
          hetu_hitting_num = hetu_id_cnt_mp.count(hetu_id) ? hetu_id_cnt_mp[hetu_id] : hetu_hitting_num;
        }
      }
      if (enable_hetu_level_five_uninterest_tagger) {
        auto hetu_level_five = context->GetIntListItemAttr(result, hetu_level_five_accessor);
        if (hetu_level_five && hetu_level_five->size()) {
          int64 hetu_id = hetu_level_five->at(0);
          hetu_hitting_num = hetu_level_five_cnt_mp.count(hetu_id) ?
              hetu_level_five_cnt_mp[hetu_id] : hetu_hitting_num;
        }
      }
      if (hetu_hitting_num >= realshow_no_click_threshold) {
        context->SetIntItemAttr(result, output_attr_accessor, hetu_hitting_num);
      }
    });
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreShortUninterestTaggerEnricher,
                 ExploreShortUninterestTaggerEnricher)

}  // namespace platform
}  // namespace ks
