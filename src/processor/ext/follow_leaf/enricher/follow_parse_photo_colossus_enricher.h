#pragma once

#include <memory>
#include <string>
#include <vector>

#include "ks/reco_proto/proto/realtime_reco.pb.h"

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class FollowParsePhotoColossusEnricher : public CommonRecoBaseEnricher {
 public:
  FollowParsePhotoColossusEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  // author top list map cnt
  struct AuthorTopCntInfo {
    uint32 effect_vv_14D = 0;
    uint32 play_vv_14D = 0;
    uint32 play_time_14D = 0;
    uint32 like_14D = 0;
    uint32 comment_14D = 0;
    uint32 effect_vv_7D = 0;
    uint32 play_vv_7D = 0;
    uint32 play_time_7D = 0;
    uint32 like_7D = 0;
    uint32 comment_7D = 0;
    uint32 follow_effect_vv_14D = 0;
    uint32 follow_play_vv_14D = 0;
    uint32 follow_play_vv_14D_v2 = 0;
    uint32 follow_play_time_14D = 0;
    uint32 follow_like_14D = 0;
    uint32 follow_comment_14D = 0;
    uint32 follow_effect_vv_7D = 0;
    uint32 follow_play_vv_7D = 0;
    uint32 follow_play_vv_7D_v2 = 0;
    uint32 follow_play_time_7D = 0;
    uint32 follow_like_7D = 0;
    uint32 follow_comment_7D = 0;
    bool is_friend = false;
  };
  bool InitProcessor() override {
    colossus_info_path_ = config()->GetString("colossus_info_path", "");
    if (colossus_info_path_.empty()) {
      LOG(ERROR) << "FollowLeafCascadingEnricher colossus_info_path is empty";
        return false;
    }
    return true;
  }
  bool ParsePhotoColossus(MutableRecoContextInterface *context);
  void FillColossusToAttr(MutableRecoContextInterface *context,
                          RecoResultConstIter begin, RecoResultConstIter end);
  bool FillRecentPlayPhotoInfo(MutableRecoContextInterface *context);
  void FillRecentPlayToAttr(MutableRecoContextInterface *context,
                            RecoResultConstIter begin, RecoResultConstIter end);
  void FillSimUserToAttr(MutableRecoContextInterface *context,
                         RecoResultConstIter begin, RecoResultConstIter end);
  void FillUserAttrSimInfo(MutableRecoContextInterface *context);
  uint32 GetValueFromMap(const folly::F14FastMap<uint64, uint32>& value_map, uint64 id);
  void SafeUpdateInfoMap(
    folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>>* user_info_map_ptr,
    uint64 key, uint32 play, uint32 last_time_gap_h);
  void Clear() {
    author_top_cnt_info_map_.clear();
    user_recent_14D_playcnt_follow_author_map_.clear();
    user_recent_14D_playcnt_v2_follow_author_map_.clear();
    user_recent_14D_playtime_follow_author_map_.clear();
    user_recent_7D_playcnt_follow_author_map_.clear();
    user_recent_7D_playcnt_follow_author_map_v2_.clear();
    user_recent_7D_playtime_follow_author_map_.clear();
    user_recent_14D_effectcnt_follow_author_map_.clear();
    user_recent_7D_effectcnt_follow_author_map_.clear();
    user_recent_14D_likecnt_follow_author_map_.clear();
    user_recent_7D_likecnt_follow_author_map_.clear();
    user_recent_14D_commentcnt_follow_author_map_.clear();
    user_recent_7D_commentcnt_follow_author_map_.clear();
    sim_user_static_info_map_.clear();
    sim_interact_tag_info_map_1d_.clear();
    sim_interact_tag_info_map_3d_.clear();
    sim_interact_tag_info_map_7d_.clear();
    sim_interact_tag_info_map_14d_.clear();
    sim_interact_tag_info_map_30d_.clear();
    sim_short_view_tag_info_map_1d_.clear();
    sim_short_view_tag_info_map_3d_.clear();
    sim_short_view_tag_info_map_7d_.clear();
    sim_short_view_tag_info_map_14d_.clear();
    sim_short_view_tag_info_map_30d_.clear();
    sim_effective_view_tag_info_map_1d_.clear();
    sim_effective_view_tag_info_map_3d_.clear();
    sim_effective_view_tag_info_map_7d_.clear();
    sim_effective_view_tag_info_map_14d_.clear();
    sim_effective_view_tag_info_map_30d_.clear();
    sim_long_view_tag_info_map_1d_.clear();
    sim_long_view_tag_info_map_3d_.clear();
    sim_long_view_tag_info_map_7d_.clear();
    sim_long_view_tag_info_map_14d_.clear();
    sim_long_view_tag_info_map_30d_.clear();
    sim_negative_tag_info_map_1d_.clear();
    sim_negative_tag_info_map_3d_.clear();
    sim_negative_tag_info_map_7d_.clear();
    sim_negative_tag_info_map_14d_.clear();
    sim_negative_tag_info_map_30d_.clear();

    sim_interact_author_info_map_1d_.clear();
    sim_interact_author_info_map_3d_.clear();
    sim_interact_author_info_map_7d_.clear();
    sim_interact_author_info_map_14d_.clear();
    sim_interact_author_info_map_30d_.clear();
    sim_short_view_author_info_map_1d_.clear();
    sim_short_view_author_info_map_3d_.clear();
    sim_short_view_author_info_map_7d_.clear();
    sim_short_view_author_info_map_14d_.clear();
    sim_short_view_author_info_map_30d_.clear();
    sim_effective_view_author_info_map_1d_.clear();
    sim_effective_view_author_info_map_3d_.clear();
    sim_effective_view_author_info_map_7d_.clear();
    sim_effective_view_author_info_map_14d_.clear();
    sim_effective_view_author_info_map_30d_.clear();
    sim_long_view_author_info_map_1d_.clear();
    sim_long_view_author_info_map_3d_.clear();
    sim_long_view_author_info_map_7d_.clear();
    sim_long_view_author_info_map_14d_.clear();
    sim_long_view_author_info_map_30d_.clear();
    sim_negative_author_info_map_1d_.clear();
    sim_negative_author_info_map_3d_.clear();
    sim_negative_author_info_map_7d_.clear();
    sim_negative_author_info_map_14d_.clear();
    sim_negative_author_info_map_30d_.clear();
    sim_user_follow_photo_play_sum_28d_ = 0;
    friend_set_.clear();
  }

 private:
  std::string colossus_info_path_;
  folly::F14FastMap<uint64, AuthorTopCntInfo> author_top_cnt_info_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_playcnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_playcnt_v2_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_playtime_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_playcnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_playcnt_follow_author_map_v2_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_playtime_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_effectcnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_effectcnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_likecnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_likecnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_14D_commentcnt_follow_author_map_;
  folly::F14FastMap<uint64, uint32> user_recent_7D_commentcnt_follow_author_map_;

  //  用户历史统计信息，因为需要解析 sim v2 结果
  folly::F14FastMap<std::string, folly::F14FastMap<std::string, uint32>> sim_user_static_info_map_;
  // 用户最近的河图 tag 粒度互动 map
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_tag_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_tag_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_tag_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_tag_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_tag_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_tag_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_tag_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_tag_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_tag_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_tag_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_tag_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_tag_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_tag_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_tag_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_tag_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_tag_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_tag_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_tag_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_tag_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_tag_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_tag_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_tag_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_tag_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_tag_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_tag_info_map_30d_;

  // 用户最近的作者粒度互动 map
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_author_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_author_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_author_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_author_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_interact_author_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_author_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_author_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_author_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_author_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_short_view_author_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_author_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_author_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_author_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_author_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_effective_view_author_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_author_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_author_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_author_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_author_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_long_view_author_info_map_30d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_author_info_map_1d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_author_info_map_3d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_author_info_map_7d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_author_info_map_14d_;
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>> sim_negative_author_info_map_30d_;
  // colossus 返回全域，用户历史曝光过的 item 序列
  folly::F14FastMap<uint64, std::vector<int64>> sim_user_author_playtime_list_7d_map_;
  // colossus 返回全域，用户历史曝光过的 item 序列
  folly::F14FastMap<uint64, std::vector<int64>> sim_user_author_duration_list_7d_map_;
  // 关注页内，用户历史曝光过的 item 序列
  folly::F14FastMap<uint64, std::vector<int64>> sim_user_author_follow_playtime_list_7d_map_;
  // 关注页内，用户历史曝光过的 item 序列
  folly::F14FastMap<uint64, std::vector<int64>> sim_user_author_follow_duration_list_7d_map_;
  int64 sim_user_follow_photo_play_sum_28d_ = 0;
  // 双关好友集合
  folly::F14FastSet<uint64> friend_set_;

 private:
  DISALLOW_COPY_AND_ASSIGN(FollowParsePhotoColossusEnricher);
};
}  // namespace platform
}  // namespace ks
