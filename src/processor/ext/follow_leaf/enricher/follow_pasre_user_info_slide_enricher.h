#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/follow_leaf/util/json_helper.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"
#include "ks/reco_pub/reco/util/util.h"

namespace ks {
namespace platform {
// fill common attr
class FollowParseUserInfoSlideEnricher : public CommonRecoBaseEnricher {
 public:
  FollowParseUserInfoSlideEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    user_info_pb_ = config()->GetString("user_info_pb", "");
    if (user_info_pb_.empty()) {
      LOG(ERROR) << "FollowParseUserInfoSlideEnricher init failed! user_info_pb is empty.";
      return false;
    }
    notify_response_path_ = config()->GetString("notify_response_path", "");
    if (notify_response_path_.empty()) {
      LOG(ERROR) << "FollowParseUserInfoEnricher: empty notify_response_path!";
      return false;
    }
    return true;
  }
  void FillCommonAttr(MutableRecoContextInterface *context);
  void ParseSamplelistUserAttr(MutableRecoContextInterface *context);
  void FillCommonAttrInCommonLeaf(MutableRecoContextInterface *context);
  void FillRelationColossusList(MutableRecoContextInterface *context);
  void FillRelationActionList(MutableRecoContextInterface *context);
  void FillRecentSessionFeedbackFeatures(MutableRecoContextInterface *context);
  void Clear() {}

 private:
  std::string user_info_pb_;
  std::string notify_response_path_;
  const ks::reco::RealTimeFollowRecoUserInfo * user_info_ = nullptr;
  int request_type_ = 0;
  int64 current_ts_ = 0;

  DISALLOW_COPY_AND_ASSIGN(FollowParseUserInfoSlideEnricher);
};
}  // namespace platform
}  // namespace ks
