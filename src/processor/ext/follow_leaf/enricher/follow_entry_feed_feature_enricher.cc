#include "dragon/src/processor/ext/follow_leaf/enricher/follow_entry_feed_feature_enricher.h"

#include <unordered_map>
#include <utility>

#include "ks/reco_proto/proto/realtime_reco.pb.h"

#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {
void FollowEntryFeedFeatureEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(user_info_path_);
  if (!message) {
    CL_LOG_ERROR("FollowEntryFeedFeatureEnricher", "attr_not_found:" + user_info_path_)
        << "FollowEntryFeedFeatureEnricher user_info is null";
    return;
  }
  const auto* user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
  if (!user_info) {
    CL_LOG_ERROR("FollowEntryFeedFeatureEnricher", "attr_cast_fail:" + user_info_path_)
        << "FollowEntryFeedFeatureEnricher user_info dynamic_cast failed";
    return;
  }
  uint64 entry_feed_id = user_info->slide_mode_context_info().entry_feed_id();
  reco::RecoEnum::ItemType entry_feed_type = user_info->slide_mode_context_info().entry_feed_type();
  int64 item_key = 0;
  bool is_live = false;
  if (entry_feed_type == reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
    item_key = ks::platform::Util::GenKeysign(3, entry_feed_id);
    is_live = true;
  } else {
    item_key = ks::platform::Util::GenKeysign(2, entry_feed_id);
  }
  // 上下滑入口 feed 特征，补充到 user 侧
  context->SetIntCommonAttr("uEntryPId", entry_feed_id);
  context->SetIntCommonAttr("uEntryAId", context->GetIntItemAttr(item_key, "author_id").value_or(0));
  context->SetIntCommonAttr("uEntryIsLive", is_live);
  int is_picture = 0;
  int photo_type = 1;
  auto photo_type_ptr = context->GetIntItemAttr(item_key, "photo_type");
  if (photo_type_ptr) {
    if (*photo_type_ptr == 7 || *photo_type_ptr == 10 || *photo_type_ptr == 11 || *photo_type_ptr == 70) {
      is_picture = 1;
      // Picture 类型
      photo_type = 7;
    } else if (*photo_type_ptr == 1) {
      is_picture = 1;
      // Camera 类型 (默认类型)
      photo_type = 1;
    } else if (*photo_type_ptr == 2 || *photo_type_ptr == 20) {
      is_picture = 1;
      // Import 类型
      photo_type = 2;
    }
  }
  context->SetIntCommonAttr("uEntryIsPictureType", is_picture);
  context->SetIntCommonAttr("uEntryPhotoType", photo_type);
  context->SetIntCommonAttr("uEntryVideoDurationMs",
    context->GetIntItemAttr(item_key, "duration_ms").value_or(0));
  SetIntListAttrItem(context, "uEntryPAuthorHetuLevel1Id",
    "photo_author_hetu_tag_info__hetu_level_one", item_key);
  SetIntListAttrItem(context, "uEntryPAuthorHetuLevel2Id",
    "photo_author_hetu_tag_info__hetu_level_two", item_key);
  SetIntListAttrItem(context, "uEntryPAuthorHetuLevel3Id",
    "photo_author_hetu_tag_info__hetu_level_three", item_key);
  SetIntListAttrItem(context, "uEntryPAuthorHetuLevel4Id",
    "photo_author_hetu_tag_info__hetu_level_four", item_key);
  SetIntListAttrItem(context, "uEntryPAuthorHetuLevel5Id",
    "photo_author_hetu_tag_info__hetu_level_five", item_key);
  SetIntListAttrItem(context, "uEntryPHetuLevel1Id", "photo_hetu_tag_info__hetu_level_one", item_key);
  SetIntListAttrItem(context, "uEntryPHetuLevel2Id", "photo_hetu_tag_info__hetu_level_two", item_key);
  SetIntListAttrItem(context, "uEntryPHetuLevel3Id", "photo_hetu_tag_info__hetu_level_three", item_key);
  SetIntListAttrItem(context, "uEntryPHetuLevel4Id", "photo_hetu_tag_info__hetu_level_four", item_key);
  SetIntListAttrItem(context, "uEntryPHetuLevel5Id", "photo_hetu_tag_info__hetu_level_five", item_key);
  SetIntListAttrItem(context, "uEntryPHetuTagId", "photo_hetu_tag_info__hetu_tag", item_key);
  SetIntListAttrItem(context, "uEntryPHetuFaceId", "photo_hetu_tag_info__hetu_face_id", item_key);
  SetIntListAttrItem(context, "uEntryLAuthorHetuLevel1Id",
    "live_author_hetu_tag_info__hetu_level_one", item_key);
  SetIntListAttrItem(context, "uEntryLAuthorHetuLevel2Id",
    "live_author_hetu_tag_info__hetu_level_two", item_key);
  SetIntListAttrItem(context, "uEntryLAuthorHetuLevel3Id",
    "live_author_hetu_tag_info__hetu_level_three", item_key);
  SetIntListAttrItem(context, "uEntryLAuthorHetuLevel4Id",
    "live_author_hetu_tag_info__hetu_level_four", item_key);
  SetIntListAttrItem(context, "uEntryLAuthorHetuLevel5Id",
    "live_author_hetu_tag_info__hetu_level_five", item_key);
  SetIntListAttrItem(context, "uEntryLHetuLevel1Id", "live_hetu_tag_info__hetu_level_one", item_key);
  SetIntListAttrItem(context, "uEntryLHetuLevel2Id", "live_hetu_tag_info__hetu_level_two", item_key);
  SetIntListAttrItem(context, "uEntryLHetuLevel3Id", "live_hetu_tag_info__hetu_level_three", item_key);
  SetIntListAttrItem(context, "uEntryLHetuLevel4Id", "live_hetu_tag_info__hetu_level_four", item_key);
  SetIntListAttrItem(context, "uEntryLHetuLevel5Id", "live_hetu_tag_info__hetu_level_five", item_key);
  SetFloatListAttrItem(context, "uEntryLMmuLiveEmbedding", "mmu_live_embedding", item_key);
  SetFloatListAttrItem(context, "uEntryPMmuPhotoEmbedding", "mmu_photo_embedding", item_key);
}

void FollowEntryFeedFeatureEnricher::SetIntListAttrItem(MutableRecoContextInterface *context,
    const std::string& save_name, const std::string& item_attr_name, int64 item_key) {
  std::vector<int64> int_list;
  auto ptr = context->GetIntListItemAttr(item_key, item_attr_name);
  if (ptr) {
    int_list.reserve(ptr->size());
    for (auto id : *ptr) {
      int_list.push_back(id);
    }
  }
  context->SetIntListCommonAttr(save_name, std::move(int_list));
}

void FollowEntryFeedFeatureEnricher::SetFloatListAttrItem(MutableRecoContextInterface *context,
    const std::string& save_name, const std::string& item_attr_name, int64 item_key) {
  std::vector<double> double_list;
  auto ptr = context->GetDoubleListItemAttr(item_key, item_attr_name);
  if (ptr) {
    double_list.reserve(ptr->size());
    for (auto vl : *ptr) {
      double_list.push_back(vl);
    }
  }
  context->SetDoubleListCommonAttr(save_name, std::move(double_list));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowEntryFeedFeatureEnricher, FollowEntryFeedFeatureEnricher);

}  // namespace platform
}  // namespace ks
