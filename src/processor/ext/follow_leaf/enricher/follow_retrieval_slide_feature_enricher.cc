#include "dragon/src/processor/ext/follow_leaf/enricher/follow_retrieval_slide_feature_enricher.h"

#include <algorithm>
#include <map>
#include <string>
#include <utility>
#include <vector>

#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {
static const std::vector<std::string> clicked_item_feature_fields = {
    "uClickListFollow",          "uClickListSlFollow",
    "uClickListBlFollow",        "uClickListFollowLive",
    "uClickListSlFollowLive",    "uLikeListFollow",
    "uLikeListBlFollow",         "uLikeListSlFollow",
    "uLikeListFollowLive",       "uLikeListSlFollowLive",
    "uCommentPhotoListFollow",   "uCommentListFollowLive",
    "uCommentPhotoListSlFollow", "uForwardList",
    "uForwardListFollowLive",    "uForwardListSlFollowLive",
};
static const std::vector<std::string> browsed_photos_feature_fields = {
    "uLikeListSlFollow",
    "uForwardListSlFollow",
    "uCommentPhotoListSlFollow",
};

void FollowRetrievalSlideFeatureEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(user_info_pb_);
  if (!message) {
    CL_LOG_ERROR("FollowRetrievalSlideFeatureEnricher", "attr_not_found:" + user_info_pb_)
        << "FollowRetrievalSlideFeatureEnricher user_info is null";
    return;
  }
  const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
  if (!user_info) {
    CL_LOG_ERROR("FollowRetrievalSlideFeatureEnricher", "attr_cast_fail:" + user_info_pb_)
        << "FollowRetrievalSlideFeatureEnricher user_info dynamic_cast failed";
    return;
  }
  InitVariable(context, *user_info);

  bool is_nebula_user = product_type_ == ks::reco::RealTimeFollowRecoUserInfo::SINGLE_COLUMN_NEBULA;
  context->SetIntCommonAttr("is_nebula_user", is_nebula_user);
  std::string retrieval_request_type;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    retrieval_request_type = "follow_live_first_retrieval";
  } else {
    retrieval_request_type =
        std::string(context->GetStringCommonAttr("follow_v5_common_retrieval_request_type").value_or(""));
  }
  context->SetStringCommonAttr("common_retrieval_request_type", retrieval_request_type);
  ReFillUserInfo(context, *user_info);
  GenListSet(context, *user_info);
  FillAuthorList(context, *user_info);
}

void FollowRetrievalSlideFeatureEnricher::InitVariable(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  follow_author_set_.clear();
  unfollow_author_set_.clear();
  slide_extra_add_long_gift_model_author_set_.clear();
  browsed_author_ids_in_session_.clear();
  hate_author_timestamp_map_.clear();
  slide_extra_user_reason_map_.clear();
  user_id_ = context->GetIntCommonAttr("uid").value_or(0);
  product_type_ = user_info.follow_page_product_type();
  cursor_index_ = user_info.slide_mode_context_info().cursor_index();
  enable_filter_hate_unfollow_author_ =
      context->GetIntCommonAttr("enable_filter_hate_unfollow_author").value_or(0) == 1;
  filter_hate_unfollow_author_timestamp_threshold_ =
      context->GetIntCommonAttr("filter_hate_unfollow_author_timestamp_threshold").value_or(0);
  folly::F14FastSet<uint64> browsed_photo_ids_in_session;
  for (int i = 0; i < user_info.browsed_photo_ids_in_session_size(); ++i) {
    browsed_photo_ids_in_session.insert(user_info.browsed_photo_ids_in_session(i));
  }
  for (int i = 0; i < user_info.recent_inner_session_follow_reco_result_size(); ++i) {
    auto &reco_result = user_info.recent_inner_session_follow_reco_result(i);
    for (int j = 0; j < reco_result.myfollow_reco_feed_size(); ++j) {
      auto &feed = reco_result.myfollow_reco_feed(j);
      if (feed.type() == reco::RecoEnum::ITEM_TYPE_PHOTO &&
          browsed_photo_ids_in_session.find(feed.id()) != browsed_photo_ids_in_session.end()) {
        browsed_author_ids_in_session_.insert(feed.author_id());
      }
    }
  }
  // 填充 hate author
  for (auto iter = user_info.hate_author_info_list().begin(); iter != user_info.hate_author_info_list().end();
       ++iter) {
    // hate_author_set_.insert(iter->author_id());
    hate_author_timestamp_map_.insert({iter->author_id(), iter->timestamp()});
  }
}

void FollowRetrievalSlideFeatureEnricher::ReFillUserInfo(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  // 重填 user info 只传输使用的内容
  ks::reco::RealTimeFollowRecoUserInfo user_info_new;
  user_info_new.mutable_api_request_info()->CopyFrom(user_info.api_request_info());
  for (int i = 0; i < user_info.feed_list_size(); ++i) {
    auto *feed_prt = user_info_new.add_feed_list();
    feed_prt->CopyFrom(user_info.feed_list(i));
  }
  for (int i = 0; i < user_info.follow_list_size(); ++i) {
    auto *follow_ptr = user_info_new.add_follow_list();
    follow_ptr->CopyFrom(user_info.follow_list(i));
  }
  user_info_new.mutable_browse_set_all_result()->CopyFrom(user_info.browse_set_all_result());
  user_info_new.mutable_global_browse_set_all_result()->CopyFrom(user_info.global_browse_set_all_result());
  user_info_new.set_last_feed_ms(user_info.last_feed_ms());
  if (user_info.has_user_activity()) {
    user_info_new.set_user_activity(user_info.user_activity());
  }
  for (int i = 0; i < user_info.browsed_photo_ids_size(); ++i) {
    user_info_new.add_browsed_photo_ids(user_info.browsed_photo_ids(i));
  }
  for (int i = 0; i < user_info.global_play_browsed_photo_ids_size(); ++i) {
    user_info_new.add_global_play_browsed_photo_ids(user_info.global_play_browsed_photo_ids(i));
  }
  for (int i = 0; i < user_info.global_show_browsed_photo_ids_size(); ++i) {
    user_info_new.add_global_show_browsed_photo_ids(user_info.global_show_browsed_photo_ids(i));
  }
  for (int i = 0; i < user_info.filter_photo_id_size(); ++i) {
    user_info_new.add_filter_photo_id(user_info.filter_photo_id(i));
  }
  for (int i = 0; i < user_info.filter_author_id_size(); ++i) {
    user_info_new.add_filter_author_id(user_info.filter_author_id(i));
  }
  for (int i = 0; i < user_info.filter_live_author_id_size(); ++i) {
    user_info_new.add_filter_live_author_id(user_info.filter_live_author_id(i));
  }
  for (int i = 0; i < user_info.viewed_photo_id_size(); ++i) {
    user_info_new.add_viewed_photo_id(user_info.viewed_photo_id(i));
  }
  for (int i = 0; i < user_info.user_recent_status_v2_size(); ++i) {
    auto *user_recent_status_v2_prt = user_info_new.add_user_recent_status_v2();
    user_recent_status_v2_prt->CopyFrom(user_info.user_recent_status_v2(i));
  }
  for (int i = 0; i < user_info.extra_reco_list_size(); ++i) {
    auto *extra_reco_list_prt = user_info_new.add_extra_reco_list();
    extra_reco_list_prt->CopyFrom(user_info.extra_reco_list(i));
  }
  for (int i = 0; i < user_info.browsed_photo_ids_in_frequent_author_size(); ++i) {
    user_info_new.add_browsed_photo_ids_in_frequent_author(user_info.browsed_photo_ids_in_frequent_author(i));
  }
  for (int i = 0; i < user_info.browse_author_info_size(); ++i) {
    auto *browse_author_ptr = user_info_new.add_browse_author_info();
    browse_author_ptr->CopyFrom(user_info.browse_author_info(i));
  }
  for (int i = 0; i < user_info.browse_author_live_info_v2_size(); ++i) {
    auto *browse_author_live_v2_ptr = user_info_new.add_browse_author_live_info_v2();
    browse_author_live_v2_ptr->CopyFrom(user_info.browse_author_live_info_v2(i));
  }
  for (int i = 0; i < user_info.browse_author_photo_info_v2_size(); ++i) {
    auto *browse_author_photo_v2_ptr = user_info_new.add_browse_author_photo_info_v2();
    browse_author_photo_v2_ptr->CopyFrom(user_info.browse_author_photo_info_v2(i));
  }
  for (int i = 0; i < user_info.browse_live_info_v2_size(); ++i) {
    auto *browse_live_v2_ptr = user_info_new.add_browse_live_info_v2();
    browse_live_v2_ptr->CopyFrom(user_info.browse_live_info_v2(i));
  }
  for (int i = 0; i < user_info.browse_photo_info_v2_size(); ++i) {
    auto *browse_photo_v2_ptr = user_info_new.add_browse_photo_info_v2();
    browse_photo_v2_ptr->CopyFrom(user_info.browse_photo_info_v2(i));
  }
  if (user_info.has_social_related_info()) {
    user_info_new.mutable_social_related_info()->CopyFrom(user_info.social_related_info());
  }
  for (int i = 0; i < user_info.discard_show_photo_ids_size(); ++i) {
    user_info_new.add_discard_show_photo_ids(user_info.discard_show_photo_ids(i));
  }
  for (int i = 0; i < user_info.browse_video_info_size(); ++i) {
    auto *browse_video_ptr = user_info_new.add_browse_video_info();
    browse_video_ptr->CopyFrom(user_info.browse_video_info(i));
  }
  for (int i = 0; i < user_info.browse_live_info_size(); ++i) {
    auto *browse_live_ptr = user_info_new.add_browse_live_info();
    browse_live_ptr->CopyFrom(user_info.browse_live_info(i));
  }
  for (int i = 0; i < user_info.valid_top_showed_author_id_list_size(); ++i) {
    user_info_new.add_valid_top_showed_author_id_list(user_info.valid_top_showed_author_id_list(i));
  }
  for (int i = 0; i < user_info.enter_profile_author_list_size(); ++i) {
    auto *enter_profile_author_ptr = user_info_new.add_enter_profile_author_list();
    enter_profile_author_ptr->CopyFrom(user_info.enter_profile_author_list(i));
  }
  if (user_info.has_user_relation_info()) {
    user_info_new.mutable_user_relation_info()->CopyFrom(user_info.user_relation_info());
  }
  user_info_new.mutable_reco_param_info()->CopyFrom(user_info.reco_param_info());
  if (user_info.has_merchant_user_shop_consume_info()) {
    user_info_new.mutable_merchant_user_shop_consume_info()->CopyFrom(
        user_info.merchant_user_shop_consume_info());
  }
  if (user_info.has_follow_live_revenue_pay_tags()) {
    user_info_new.mutable_follow_live_revenue_pay_tags()->CopyFrom(user_info.follow_live_revenue_pay_tags());
  }
  for (int i = 0; i < user_info.backup_photo_inverted_index_id_size(); ++i) {
    user_info_new.add_backup_photo_inverted_index_id(user_info.backup_photo_inverted_index_id(i));
  }
  if (user_info.has_follow_author_info_aggr()) {
    user_info_new.mutable_follow_author_info_aggr()->CopyFrom(user_info.follow_author_info_aggr());
  }

  std::string user_info_str;
  user_info_new.SerializeToString(&user_info_str);
  context->SetStringCommonAttr("retrieval_user_info", user_info_str);
}

void FollowRetrievalSlideFeatureEnricher::GenListSet(MutableRecoContextInterface *context,
                                                     const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  folly::F14FastSet<int64> discard_show_set;
  for (int i = 0; i < user_info.discard_show_photo_ids_size(); ++i) {
    discard_show_set.insert(user_info.discard_show_photo_ids(i));
  }

  folly::F14FastSet<int64> action_list_set;
  folly::F14FastSet<int64> fine_browsed_set;
  folly::F14FastSet<int64> live_browsed_set;
  for (int i = 0; i < user_info.feed_list_size(); i++) {
    auto &feed = user_info.feed_list(i);
    if (feed.type() == reco::RecoEnum::ITEM_TYPE_LIVESTREAM && feed.is_realshow()) {
      if (user_info.api_request_info().is_down_fresh() &&
          (i < user_info.api_request_info().corrected_memcache_cursor())) {
        // 底部刷新时，填充顶部曝光直播
        live_browsed_set.insert(feed.id());
      }
    }
  }
  int64 entry_feed_id = user_info.slide_mode_context_info().entry_feed_id();
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    // 内部流通过 show index 和 feed id 重新计算
    live_browsed_set.clear();
    // 填充入口 feed 保证不重复下发
    live_browsed_set.insert(entry_feed_id);
  } else {
    fine_browsed_set.insert(entry_feed_id);
  }
  // 极速改版直播过滤 只使用 session browse set
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::SINGLE_COLUMN_NEBULA) {
    live_browsed_set.clear();
  }
  int slide_mode_retry_count = user_info.slide_mode_context_info().retry_request_count();
  if (cursor_index_ > 0 || slide_mode_retry_count > 0) {
    for (int i = 0; i < user_info.browsed_live_ids_in_session_size(); ++i) {
      live_browsed_set.insert(user_info.browsed_live_ids_in_session(i));
    }
    for (int i = 0; i < user_info.browsed_photo_ids_in_session_size(); ++i) {
      action_list_set.insert(user_info.browsed_photo_ids_in_session(i));
      fine_browsed_set.insert(user_info.browsed_photo_ids_in_session(i));
    }
  }
  action_list_set.insert(user_info.viewed_photo_id().begin(), user_info.viewed_photo_id().end());
  for (const auto &str : clicked_item_feature_fields) {
    auto action_photo_list = context->GetIntListCommonAttr(str);
    if (action_photo_list) {
      action_list_set.insert(action_photo_list->begin(), action_photo_list->end());
    }
  }
  for (int i = 0; i < user_info.browsed_photo_ids_size(); ++i) {
    int64 id = user_info.browsed_photo_ids(i);
    fine_browsed_set.insert(id);
  }
  for (const auto &str : browsed_photos_feature_fields) {
    auto action_photo_list = context->GetIntListCommonAttr(str);
    if (action_photo_list) {
      fine_browsed_set.insert(action_photo_list->begin(), action_photo_list->end());
    }
  }

  std::vector<int64> action_list_vec(action_list_set.begin(), action_list_set.end());
  context->SetIntListCommonAttr("retrieval_action_list_set", std::move(action_list_vec));
  std::vector<int64> fine_browsed_vec(fine_browsed_set.begin(), fine_browsed_set.end());
  context->SetIntListCommonAttr("retrieval_fine_browsed_set", std::move(fine_browsed_vec));
  std::vector<int64> live_browsed_vec(live_browsed_set.begin(), live_browsed_set.end());
  context->SetIntListCommonAttr("retrieval_live_browsed_set", std::move(live_browsed_vec));

  std::string global_action_strs =
      std::string(context->GetStringCommonAttr("fill_feasury_global_action_strs").value_or("uAllLikeLIST"));
  std::vector<std::string> action_strs_vec;
  base::SplitString(global_action_strs, ";", &action_strs_vec);
  folly::F14FastSet<int64> global_action_set;
  for (auto &str : action_strs_vec) {
    auto attr_list = context->GetIntListCommonAttr(str);
    if (attr_list) {
      global_action_set.insert(attr_list->begin(), attr_list->end());
    }
  }
  std::vector<int64> global_action_vec(global_action_set.begin(), global_action_set.end());
  context->SetIntListCommonAttr("retrieval_global_action_list_set", std::move(global_action_vec));
}

void FollowRetrievalSlideFeatureEnricher::FillAuthorList(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  for (int i = 0; i < user_info.follow_list_size(); ++i) {
    follow_author_set_.insert(user_info.follow_list(i).id());
  }
  std::vector<int64> follow_list(follow_author_set_.begin(), follow_author_set_.end());
  context->SetIntListCommonAttr("retrieval_follow_list", std::move(follow_list));

  FillSlideExtraAuthorInfo(context, user_info);
  FillSlideUnfollowRevenueAuthorInfo(context, user_info);
  FillPymkUnfollowAuthor(context, user_info);
  FillVisitProfileUnfollowAuthor(context, user_info);
  FillRealTimeGlobalInteractUnfollowAuthor(context, user_info);
  FillSlideUnfollowHighGpmAuthorInfo(context, user_info);
  FillSlideUnfollowHistoryLongPlayAuthorInfo(context, user_info);
  // 未关频控
  int64 unfollow_show_gap = context->GetIntCommonAttr("inner_slide_unfollow_show_gap").value_or(0) * 1000;
  // 用户上一次曝光常看人曝光时间戳
  int64 last_unfollow_show_ts = 0;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX ||
      product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX) {
    for (int i = 0; i < user_info.browse_author_info_size(); i++) {
      if (follow_author_set_.find(user_info.browse_author_info(i).author_id()) == follow_author_set_.end() &&
          user_info.browse_author_info(i).last_show_timestamp() > last_unfollow_show_ts) {
        last_unfollow_show_ts = user_info.browse_author_info(i).last_show_timestamp();
      }
    }
    for (int i = 0; i < user_info.browse_author_live_info_size(); i++) {
      if (follow_author_set_.find(user_info.browse_author_live_info(i).author_id()) ==
              follow_author_set_.end() &&
          user_info.browse_author_live_info(i).last_show_timestamp() > last_unfollow_show_ts) {
        last_unfollow_show_ts = user_info.browse_author_live_info(i).last_show_timestamp();
      }
    }
  }
  // 上一刷中是否曝光过未关
  bool is_unfollow_showed_last_feed = false;
  if (user_info.recent_inner_session_follow_reco_result_size() > 0) {
    auto &reco_result = user_info.recent_inner_session_follow_reco_result(
        user_info.recent_inner_session_follow_reco_result_size() - 1);
    for (auto &feed : reco_result.myfollow_reco_feed()) {
      if (follow_author_set_.find(feed.author_id()) == follow_author_set_.end()) {
        is_unfollow_showed_last_feed = true;
      }
    }
  }
  std::vector<int64> unfollow_list;
  if (!(base::GetTimestamp() / 1000 - last_unfollow_show_ts < unfollow_show_gap ||
        is_unfollow_showed_last_feed)) {
    unfollow_list.assign(unfollow_author_set_.begin(), unfollow_author_set_.end());
  }
  context->SetIntListCommonAttr("retrieval_unfollow_list", std::move(unfollow_list));
  int follow_cnt = follow_author_set_.size() + unfollow_author_set_.size();
  context->SetIntCommonAttr("follow_author_cnt", follow_cnt);
  // 全局上下滑 mmu a2a 召回主播, 填充逻辑未开, 暂不迁移直接为空
  std::vector<int64> slide_extra_add_mmu_similar_author_list;
  context->SetIntListCommonAttr("extra_add_mmu_revenue_author_ids",
                                std::move(slide_extra_add_mmu_similar_author_list));
  // 全局上下滑大 G pk 双关召回主播, 填充逻辑未开, 暂不迁移直接为空
  std::vector<int64> slide_extra_add_bigr_gift_pk_author_list;
  context->SetIntListCommonAttr("extra_add_bigr_pk_author_ids",
                                std::move(slide_extra_add_bigr_gift_pk_author_list));
  // 全局上下滑打赏图网络 gnn 召回, 填充逻辑未开, 暂不迁移直接为空
  std::vector<int64> slide_extra_add_gnn_revenue_author_list;
  context->SetIntListCommonAttr("extra_add_gnn_revenue_author_ids",
                                std::move(slide_extra_add_gnn_revenue_author_list));
  // 全局上下滑长期打赏模型召回主播
  std::vector<int64> slide_extra_add_long_gift_model_author_list(
      slide_extra_add_long_gift_model_author_set_.begin(), slide_extra_add_long_gift_model_author_set_.end());
  context->SetIntListCommonAttr("extra_add_long_gift_model_author_ids",
                                std::move(slide_extra_add_long_gift_model_author_list));
  std::vector<int64> extra_aids_list;
  std::vector<int64> extra_aid_reason_list;
  for (auto &pair : slide_extra_user_reason_map_) {
    extra_aids_list.push_back(pair.first);
    extra_aid_reason_list.push_back(pair.second);
  }
  context->SetIntListCommonAttr("extra_aids", std::move(extra_aids_list));
  context->SetIntListCommonAttr("extra_aids_reason", std::move(extra_aid_reason_list));
  std::vector<int64> hate_author_list;
  std::vector<int64> hate_author_timestamp_list;
  for (auto &pair : hate_author_timestamp_map_) {
    hate_author_list.push_back(pair.first);
    hate_author_timestamp_list.push_back(pair.second);
  }
  context->SetIntListCommonAttr("hate_author", std::move(hate_author_list));
  context->SetIntListCommonAttr("hate_author_timestamp", std::move(hate_author_timestamp_list));
}

void FollowRetrievalSlideFeatureEnricher::FillSlideExtraAuthorInfo(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  if (!(context->GetIntCommonAttr("enable_add_unfollow_extra_item").value_or(0) == 1)) {
    return;
  }
  int show_unfollow_extra_item_cursor = 2;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    show_unfollow_extra_item_cursor = 0;
  }
  int add_extra_item_follow_num_threshold = 10000;
  // 以一定概率来加入为关注作者，默认 100%
  base::PseudoRandom random(base::GetTimestamp());
  int unfollow_insert_prob = 100;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX) {
    unfollow_insert_prob = 40;
  }
  bool is_low_follow = false;
  int gap = 2;
  if (follow_author_set_.size() <= 100) {
    if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX ||
        product_type_ == reco::RealTimeFollowRecoUserInfo::SINGLE_COLUMN_NEBULA) {
      gap = 4;
    }
    is_low_follow = true;
  } else {
    if (cursor_index_ < show_unfollow_extra_item_cursor) {
      return;
    }
    if (follow_author_set_.size() > add_extra_item_follow_num_threshold) {
      return;
    }
  }
  if (!(random.GetInt(0, 100) <= unfollow_insert_prob)) {
    return;
  }
  // 全局上下滑补充未关注作者实验，包括常看的人、Pymk、pyml
  bool enable_inner_slide_unfollow_show_cnt_limit = false;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX ||
      product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    enable_inner_slide_unfollow_show_cnt_limit = true;
  }
  int inner_slide_unfollow_show_cnt_threshold = 100;
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX ||
      product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    inner_slide_unfollow_show_cnt_threshold = 0;
  }
  folly::F14FastMap<uint64, int32> author_show_cnt_map;
  for (int i = 0; i < user_info.browse_author_info_size(); ++i) {
    auto &browse_author_info = user_info.browse_author_info(i);
    author_show_cnt_map.insert({browse_author_info.author_id(), browse_author_info.show_cnt()});
  }
  for (int i = 0; i < user_info.inner_slide_extra_item_list_size(); ++i) {
    auto &feed = user_info.inner_slide_extra_item_list(i);
    if (follow_author_set_.find(feed.author_id()) != follow_author_set_.end()) {
      continue;
    }
    // 当前 session 里已曝光过，过滤
    if (browsed_author_ids_in_session_.find(feed.author_id()) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    // 低关群体，控制是否透出 pymk / pyml
    if (is_low_follow &&
        (cursor_index_ % gap != 0 && feed.reason() != ks::reco::FollowExtraRecoItem::FREQUENT_FEED)) {
      continue;
    }
    // 非低关，过滤 pymk，控制是否出 pyml
    if (!is_low_follow) {
      if (feed.reason() == ks::reco::FollowExtraRecoItem::PYMK_FEED) {
        continue;
      }
      if (feed.reason() == ks::reco::FollowExtraRecoItem::PYML_FEED) {
        continue;
      }
    }
    const auto &iter = author_show_cnt_map.find(feed.author_id());
    if (iter != author_show_cnt_map.end() && enable_inner_slide_unfollow_show_cnt_limit &&
        iter->second > inner_slide_unfollow_show_cnt_threshold) {
      continue;
    }
    unfollow_author_set_.insert(feed.author_id());
    slide_extra_user_reason_map_.insert({feed.author_id(), feed.reason()});
  }
}

void FollowRetrievalSlideFeatureEnricher::FillSlideUnfollowRevenueAuthorInfo(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  if (product_type_ != reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    return;
  }
  // 全局上下滑补充未关注打赏作者实验
  auto &live_revenue_reward_authors =
      user_info.follow_revenue_history_info().live_revenue_reward_authors_180d();
  for (auto itr = live_revenue_reward_authors.begin(); itr != live_revenue_reward_authors.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(itr->first) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(itr->first) == follow_author_set_.end()) {
      if (slide_extra_user_reason_map_.find(itr->first) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(itr->first);
        slide_extra_user_reason_map_.insert(
            {itr->first, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
      }
    }
  }
  auto &similar_revenue_author = user_info.similar_revenue_author();
  folly::F14FastSet<uint64> unfollow_similar_author;
  for (auto itr = similar_revenue_author.begin(); itr != similar_revenue_author.end(); ++itr) {
    auto &similar_author = itr->similar_author();
    for (int similar_author_index = 0; similar_author_index < itr->similar_author_size();
         ++similar_author_index) {
      unfollow_similar_author.insert(similar_author[similar_author_index]);
    }
  }
  for (auto itr = unfollow_similar_author.begin(); itr != unfollow_similar_author.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(*itr) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(*itr) == follow_author_set_.end()) {
      if (slide_extra_user_reason_map_.find(*itr) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(*itr);
        slide_extra_user_reason_map_.insert(
            {*itr, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
      }
    }
  }
  auto &swing_revenue_author = user_info.swing_revenue_author();
  folly::F14FastSet<uint64> unfollow_swing_author;
  for (auto itr = swing_revenue_author.begin(); itr != swing_revenue_author.end(); ++itr) {
    auto &similar_author = itr->similar_author();
    for (int similar_author_index = 0; similar_author_index < itr->similar_author_size();
         ++similar_author_index) {
      unfollow_swing_author.insert(similar_author[similar_author_index]);
    }
  }
  for (auto itr = unfollow_swing_author.begin(); itr != unfollow_swing_author.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(*itr) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(*itr) == follow_author_set_.end() && *itr != user_id_) {
      if (slide_extra_user_reason_map_.find(*itr) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(*itr);
        slide_extra_user_reason_map_.insert(
            {*itr, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
      }
    }
  }
  // 全局上下滑补充大 G 用户的 pk 双关作者实验
  auto &long_gift_model_author_list = user_info.long_gift_model_author_list();
  for (auto itr = long_gift_model_author_list.begin(); itr != long_gift_model_author_list.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(*itr) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(*itr) == follow_author_set_.end() && *itr != user_id_) {
      if (slide_extra_user_reason_map_.find(*itr) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(*itr);
        slide_extra_user_reason_map_.insert(
            {*itr, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
        slide_extra_add_long_gift_model_author_set_.insert(*itr);
      }
    }
  }
}

void FollowRetrievalSlideFeatureEnricher::FillPymkUnfollowAuthor(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  folly::F14FastSet<uint64> pymk_author_set;
  // 亲密关系
  if (!user_info.has_user_relation_info()) {
    return;
  }
  const reco::UserRelationInfo &relationInfo = user_info.user_relation_info();
  for (auto intimateRelationItem : relationInfo.intimate_relation_list()) {
    if (intimateRelationItem.has_intimate_user()) {
      pymk_author_set.insert(intimateRelationItem.intimate_user());
    }
  }
  // 亲密关系列表
  for (auto userRelationRecord : relationInfo.relation_score_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 用户亲属关系列表
  for (auto userRelationRecord : relationInfo.family_relation_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 通讯录联系人列表
  for (auto userRelationRecord : relationInfo.contact_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 微信好友关系列表
  for (auto userRelationRecord : relationInfo.wechat_friend_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 微信关系链列表
  for (auto userRelationRecord : relationInfo.wechat_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // QQ 关系链列表
  for (auto userRelationRecord : relationInfo.qq_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 喜欢的视频作者列表
  for (auto userRelationRecord : relationInfo.like_photo_author_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 不喜欢的视频作者列表
  for (auto userRelationRecord : relationInfo.dislike_photo_author_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 喜欢的直播作者列表
  for (auto userRelationRecord : relationInfo.like_live_author_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  // 不喜欢的直播作者列表
  for (auto userRelationRecord : relationInfo.dislike_live_author_list()) {
    pymk_author_set.insert(userRelationRecord.user_id());
  }
  int author_cnt = 0;
  int64 author_limit = context->GetIntCommonAttr("fill_pymk_unfollow_author_limit").value_or(0);
  uint64 current_ts = base::GetTimestamp() / 1000;
  folly::F14FastSet<int64> unfollow_pymk_author_set;
  for (auto iter = pymk_author_set.begin(); iter != pymk_author_set.end(); iter++) {
    if (author_cnt >= author_limit) {
      break;
    }
    if (enable_filter_hate_unfollow_author_ &&
        current_ts - GetHateAuthorTimestamp(*iter) <=
            3600 * 1000 * filter_hate_unfollow_author_timestamp_threshold_) {
      continue;
    }
    if (follow_author_set_.find(*iter) == follow_author_set_.end()) {
      unfollow_pymk_author_set.insert(*iter);
      unfollow_author_set_.insert(*iter);
      author_cnt += 1;
    }
  }
  std::vector<int64> unfollow_pymk_author_list(unfollow_pymk_author_set.begin(),
                                               unfollow_pymk_author_set.end());
  context->SetIntListCommonAttr("unfollow_pymk_author_list", std::move(unfollow_pymk_author_list));
}

void FollowRetrievalSlideFeatureEnricher::FillVisitProfileUnfollowAuthor(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  std::string author_info_str(context->GetStringCommonAttr("visit_profile_author_info_str").value_or(""));
  std::vector<std::string> author_info_list;
  base::SplitStringWithOptions(author_info_str, ",", true, true, &author_info_list);
  int author_cnt = 0;
  int64 author_limit = context->GetIntCommonAttr("fill_profile_unfollow_author_limit").value_or(0);
  uint64 current_ts = base::GetTimestamp() / 1000;
  folly::F14FastSet<int64> unfollow_visited_profile_author_set;
  for (const auto &author_info : author_info_list) {
    if (author_cnt >= author_limit) {
      break;
    }
    std::vector<std::string> visit_info;
    int entry_cnt = 0;
    double stay_duration = 0.0;
    int64 aid = 0;
    base::SplitStringWithOptions(author_info, "_", true, true, &visit_info);
    if (visit_info.size() < 3) {
      continue;
    }
    if (!absl::SimpleAtoi(visit_info.at(0), &aid)) {
      aid = 0;
    }
    if (!absl::SimpleAtoi(visit_info.at(1), &entry_cnt)) {
      entry_cnt = 0;
    }
    if (!absl::SimpleAtod(visit_info.at(2), &stay_duration)) {
      stay_duration = 0.0;
    }
    if (follow_author_set_.find(aid) != follow_author_set_.end()) {
      continue;
    }
    if (enable_filter_hate_unfollow_author_ &&
        current_ts - GetHateAuthorTimestamp(aid) <=
            3600 * 1000 * filter_hate_unfollow_author_timestamp_threshold_) {
      continue;
    }
    if (entry_cnt >= 0 && stay_duration >= 0.0) {
      unfollow_visited_profile_author_set.insert(aid);
      unfollow_author_set_.insert(aid);
      author_cnt += 1;
    }
  }
  std::vector<int64> unfollow_visited_profile_author_list(unfollow_visited_profile_author_set.begin(),
                                                          unfollow_visited_profile_author_set.end());
  context->SetIntListCommonAttr("unfollow_visited_profile_author_list",
                                std::move(unfollow_visited_profile_author_list));
}

void FollowRetrievalSlideFeatureEnricher::FillRealTimeGlobalInteractUnfollowAuthor(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  if (!(context->GetIntCommonAttr("enable_fill_realtime_global_interact_author").value_or(0) == 1)) {
    return;
  }
  const auto *colossus_sim_v2_photo_items =
      context->GetPtrCommonAttr<std::vector<const google::protobuf::Message *>>(colossus_info_path_);
  if (colossus_sim_v2_photo_items == nullptr || colossus_sim_v2_photo_items->empty()) {
    return;
  }
  int64 item_limit =
      context->GetIntCommonAttr("fill_realtime_global_interact_author_sim_item_limit").value_or(0);
  int item_cnt = 0;
  double score = 0.0;
  std::map<uint64, double> ua_interact_score;
  for (auto sim_iter = colossus_sim_v2_photo_items->rbegin(); sim_iter != colossus_sim_v2_photo_items->rend();
       sim_iter++) {
    if (item_cnt > item_limit) {
      break;
    }
    const colossus::SimItemV2 *sim_v2_photo = static_cast<const colossus::SimItemV2 *>(*sim_iter);
    if (sim_v2_photo == nullptr) {
      continue;
    }
    const colossus::SimItemV2 &item = *sim_v2_photo;
    if (item.photo_id() <= 0) {
      continue;
    }
    if (item.author_id() <= 0) {
      continue;
    }
    // 用户已关 过滤
    if (follow_author_set_.find(item.author_id()) != follow_author_set_.end()) {
      continue;
    }
    uint32 item_label = item.label();
    bool like_label = item_label & 0x01;
    bool comment_label = item_label & (1 << 4);
    bool forward_label = item_label & (1 << 2);
    bool enter_profile_label = item_label & (1 << 6);
    score = 0.3 * like_label + 0.3 * comment_label + 0.3 * forward_label + 0.5 * enter_profile_label;
    if (score <= 0.0) {
      continue;
    }
    auto iter = ua_interact_score.find(item.author_id());
    if (iter != ua_interact_score.end()) {
      ua_interact_score[item.author_id()] = iter->second + score;
    } else {
      ua_interact_score.insert({item.author_id(), score});
    }
    item_cnt += 1;
  }
  std::vector<std::pair<uint64, double>> ua_interact_score_vec;
  for (const auto &iter : ua_interact_score) {
    ua_interact_score_vec.push_back(std::make_pair(iter.first, iter.second));
  }
  std::sort(ua_interact_score_vec.begin(), ua_interact_score_vec.end(),
            [](const std::pair<uint64, double> &r1, const std::pair<uint64, double> &r2) {
              return r1.second > r2.second;
            });
  int author_cnt = 0;
  int64 author_limit = context->GetIntCommonAttr("fill_realtime_global_interact_author_limit").value_or(0);
  folly::F14FastSet<uint64> global_interact_author_set;
  for (const auto &iter : ua_interact_score_vec) {
    if (author_cnt > author_limit) {
      break;
    }
    global_interact_author_set.insert(iter.first);
    author_cnt += 1;
  }
  // 用户 hate 过滤
  uint64 current_ts = base::GetTimestamp() / 1000;
  folly::F14FastSet<int64> unfollow_global_interact_author_set;
  for (auto iter = global_interact_author_set.begin(); iter != global_interact_author_set.end(); iter++) {
    if (enable_filter_hate_unfollow_author_ &&
        current_ts - GetHateAuthorTimestamp(*iter) <=
            3600 * 1000 * filter_hate_unfollow_author_timestamp_threshold_) {
      continue;
    }
    if (follow_author_set_.find(*iter) == follow_author_set_.end()) {
      unfollow_global_interact_author_set.insert(*iter);
      unfollow_author_set_.insert(*iter);
    }
  }
  std::vector<int64> unfollow_global_interact_author_list(unfollow_global_interact_author_set.begin(),
                                                          unfollow_global_interact_author_set.end());
  context->SetIntListCommonAttr("unfollow_global_interact_author_list",
                                std::move(unfollow_global_interact_author_list));
}

void FollowRetrievalSlideFeatureEnricher::FillSlideUnfollowHighGpmAuthorInfo(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX) {
    if (!(context->GetIntCommonAttr("enable_add_unfollow_high_gpm_author_for_video_entry_mix").value_or(0) ==
          1)) {
      return;
    }
  } else if (product_type_ ==
             reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    if (!(context->GetIntCommonAttr("enable_add_unfollow_high_gpm_author_for_live_entry_mix").value_or(0) ==
          1)) {
      return;
    }
  } else {
    return;
  }
  // 全局上下滑补充未关注高 GPM 电商作者实验
  auto &unfollow_merchant_author_list = user_info.unfollow_merchant_author_list();
  for (auto itr = unfollow_merchant_author_list.begin(); itr != unfollow_merchant_author_list.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(*itr) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(*itr) == follow_author_set_.end() && *itr != user_id_) {
      if (slide_extra_user_reason_map_.find(*itr) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(*itr);
        slide_extra_user_reason_map_.insert(
            {*itr, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
      }
    }
  }
}

void FollowRetrievalSlideFeatureEnricher::FillSlideUnfollowHistoryLongPlayAuthorInfo(
    MutableRecoContextInterface *context, const ks::reco::RealTimeFollowRecoUserInfo &user_info) {
  if (product_type_ == reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX) {
    if (!(context->GetIntCommonAttr("enable_add_unfollow_history_long_play_author_for_video_entry_mix")
              .value_or(0) == 1)) {
      return;
    }
  } else if (product_type_ ==
             reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    if (!(context->GetIntCommonAttr("enable_add_unfollow_history_long_play_author_for_live_entry_mix")
              .value_or(0) == 1)) {
      return;
    }
  } else {
    return;
  }
  // 历史长播 author 列表
  folly::F14FastSet<uint64> history_longplay_authors;
  auto action_list = context->GetIntListCommonAttr("uLongPlayLiveAuthorList");
  if (action_list) {
    history_longplay_authors.insert(action_list->begin(), action_list->end());
  }
  for (auto itr = history_longplay_authors.begin(); itr != history_longplay_authors.end(); ++itr) {
    // 当前 session 视频作者观看过，过滤
    if (browsed_author_ids_in_session_.find(*itr) != browsed_author_ids_in_session_.end()) {
      continue;
    }
    if (follow_author_set_.find(*itr) == follow_author_set_.end() && *itr != user_id_) {
      if (slide_extra_user_reason_map_.find(*itr) == slide_extra_user_reason_map_.end()) {
        unfollow_author_set_.insert(*itr);
        slide_extra_user_reason_map_.insert(
            {*itr, reco::FollowExtraRecoItem_Reason::FollowExtraRecoItem_Reason_FREQUENT_FEED});
      }
    }
  }
}

uint64 FollowRetrievalSlideFeatureEnricher::GetHateAuthorTimestamp(uint64 author_id) {
  const auto &it = hate_author_timestamp_map_.find(author_id);
  if (it != hate_author_timestamp_map_.end()) {
    return it->second;
  } else {
    return 0;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowRetrievalSlideFeatureEnricher, FollowRetrievalSlideFeatureEnricher);

}  // namespace platform
}  // namespace ks
