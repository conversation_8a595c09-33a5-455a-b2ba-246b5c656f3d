#include "dragon/src/processor/ext/follow_leaf/enricher/follow_mix_log_enricher.h"

#include <algorithm>
#include <vector>
#include <utility>
#include <unordered_set>

#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_rank_type.h"

namespace ks {
namespace platform {
#define SetIntAttr(feed, name, access, item_key)                        \
  auto value__##name = context->GetIntItemAttr(item_key, access);       \
  if (value__##name) {                                                  \
    feed->set_##name(*value__##name);                                   \
  }

void FollowMixLogEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
  Clear();
  Init(context);
  if (!enable_log_) {
    return;
  }
  std::vector<int64> item_keys;
  FillTraceItem(context, begin, end, &item_keys);

  InitTraceLog(context, item_keys);
  // 填充公共字段
  FillCommonTraceLog(context, item_keys);
  // ad
  FillAdTraceLog(context, item_keys);
  // merchant
  FillMerchantTraceLog(context, item_keys);
  // gift
  FillGiftTraceLog(context, item_keys);
  // local
  FillLocalLifeTraceLog(context, item_keys);

  std::string mix_log;
  follow_mix_log_.SerializeToString(&mix_log);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(mix_log.size(),
      "reco.follow", "follow.mix.rank.log.size");
  context->SetStringCommonAttr(mix_rank_log_attr_name_, std::move(mix_log));
}

void FollowMixLogEnricher::Init(MutableRecoContextInterface *context) {
  // max_trace_item_size_ = 12;
  max_trace_photo_size_ = context->GetIntCommonAttr("mix_log_max_trace_photo_size").value_or(25);
  max_trace_live_size_ = context->GetIntCommonAttr("mix_log_max_trace_live_size").value_or(25);
  enable_log_ = true;
}

void FollowMixLogEnricher::FillTraceItem(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end, std::vector<int64> *item_keys) {
  std::unordered_set<int64> item_key_set;
  auto* item_type_accessor = context->GetItemAttrAccessor("item_type");
  int live_cnt = 0;
  int photo_cnt = 0;
  for (auto it = begin; it != end; it++) {
    bool is_live = context->GetIntItemAttr(it->item_key, item_type_accessor).value_or(0) == follow::LIVE_TYPE;
    if (is_live) {
      live_cnt++;
      if (live_cnt > max_trace_live_size_) {
        continue;
      }
    } else {
      photo_cnt++;
      if (photo_cnt > max_trace_photo_size_) {
        continue;
      }
    }
    item_keys->push_back(it->item_key);
    item_key_set.insert(it->item_key);
  }

  // 格外 trace 的 item append 在 item_keys 里

  // if (item_keys->size() > max_trace_item_size_) {
  //   item_keys->resize(max_trace_item_size_);
  // }
}

void FollowMixLogEnricher::InitTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  for (int i = 0; i < item_keys.size(); i++) {
    auto* feed = follow_mix_log_.add_list();
    feed->set_feed_id(Util::GetId(item_keys[i]));
  }
}

void FollowMixLogEnricher::FillCommonTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  int mix_page_size = context->GetIntCommonAttr("page_size").value_or(12);

  // ItemAttr
  auto* exp_tag_accessor = context->GetItemAttrAccessor("exp_tag");
  auto* author_id_accessor = context->GetItemAttrAccessor("author_id");
  auto* position_in_nature_accessor = context->GetItemAttrAccessor("natural_index");
  auto* natural_score_accessor = context->GetItemAttrAccessor("natural_score");
  auto* biz_type_list_accessor = context->GetItemAttrAccessor("biz_type_list");
  auto* biz_type_accessor = context->GetItemAttrAccessor("biz_type");
  auto* item_type_accessor = context->GetItemAttrAccessor("item_type");
  auto* position_in_live_accessor = context->GetItemAttrAccessor("position_in_live");
  auto* position_in_photo_accessor = context->GetItemAttrAccessor("position_in_photo");

  // CommonAttr
  int32_t user_id = context->GetIntCommonAttr("uId").value_or(0);
  int32_t product_type = context->GetIntCommonAttr("uFollowPageProductType").value_or(0);
  std::string device_id(context->GetStringCommonAttr("dId").value_or(""));
  std::string llsid_raw(context->GetStringCommonAttr("llsid").value_or(""));
  int32_t cursor = context->GetIntCommonAttr("uLastFeedCursor").value_or(0);
  std::string user_type;
  UserTypeToJsonString(context, &user_type);

  for (int i = 0; i < item_keys.size(); i++) {
    auto* feed = follow_mix_log_.mutable_list(i);
    int64 item_key = item_keys[i];
    bool is_live = context->GetIntItemAttr(item_key, item_type_accessor).value_or(0) == follow::LIVE_TYPE;

    // ItemAttr
    SetIntAttr(feed, reason, exp_tag_accessor, item_key);
    SetIntAttr(feed, author_id, author_id_accessor, item_key);
    SetIntAttr(feed, position_in_live, position_in_live_accessor, item_key);
    SetIntAttr(feed, position_in_photo, position_in_photo_accessor, item_key);
    feed->set_is_live(is_live);
    feed->set_natural_score(context->GetDoubleItemAttr(item_key, natural_score_accessor).value_or(0.0));
    feed->set_ue_score(context->GetDoubleItemAttr(item_key, natural_score_accessor).value_or(0.0));
    feed->set_in_page(i < mix_page_size);
    feed->set_mix_index(i);
    auto biz_type_list = context->GetIntListItemAttr(item_key, biz_type_list_accessor);
    if (biz_type_list) {
      for (auto &biz_type : *biz_type_list) {
        feed->add_ecology_types(biz_type);
      }
    }
    SetIntAttr(feed, mix_ecology_type, biz_type_accessor, item_key);

    std::string pxtr_detail;
    PxtrDetailToJsonString(context, item_key, &pxtr_detail);
    feed->set_pxtr_detail(pxtr_detail);
    std::string mix_pxtr_detail;
    MixPxtrToJsonString(context, item_key, &mix_pxtr_detail);
    feed->set_mix_pxtr_detail(mix_pxtr_detail);

    // CommonAttr
    uint64 llsid = 0;
    base::StringToUint64(llsid_raw, &llsid);
    feed->set_llsid(llsid);
    feed->set_user_id(user_id);
    feed->set_product_type(product_type);
    feed->set_device_id(device_id);
    feed->set_user_type(user_type);
    feed->set_cursor(cursor);
  }
}

void FollowMixLogEnricher::UserTypeToJsonString(
    MutableRecoContextInterface *context, std::string* res) {
  base::Json type(base::StringToJson("{}"));
  type.set("is_common_leaf", 1);

  std::string merchant_user_type(context->GetStringCommonAttr("uBuyerEffectiveType").value_or("U0-null"));
  type.set("buyer_type", merchant_user_type);

  int32_t pay_value = context->GetIntCommonAttr("uUserClassLivePayingTypeMainApp").value_or(0);
  type.set("pay_degree", std::to_string(pay_value));

  int32_t is_big_g = context->GetIntCommonAttr("uIsBigGForFollowKV").value_or(0);
  type.set("bigG", std::to_string(is_big_g));

  int32_t is_big_r = context->GetIntCommonAttr("uIsBigRForFollowKV").value_or(0);
  type.set("bigR", std::to_string(is_big_r));

  int32_t follow_watch_live_times_28d = context->GetIntCommonAttr("follow_watch_live_times_28d").value_or(0);
  type.set("follow_watch_live_times_28d", std::to_string(follow_watch_live_times_28d));

  int32_t follow_photo_play_time_28d = context->GetIntCommonAttr("follow_photo_play_time_28d").value_or(0);
  type.set("follow_photo_play_time_28d", std::to_string(follow_photo_play_time_28d));

  *res = base::JsonToString(type.get());
}

void FollowMixLogEnricher::PxtrDetailToJsonString(
    MutableRecoContextInterface *context, int64 item_key, std::string* res) {
  base::Json type(base::StringToJson("{}"));

  auto* fr_pctr_accessor = context->GetItemAttrAccessor("fr_pctr");
  double fr_pctr = context->GetDoubleItemAttr(item_key, fr_pctr_accessor).value_or(0.0);
  type.set("fr_pctr", std::to_string(fr_pctr));

  auto* fr_pwatch_time_accessor = context->GetItemAttrAccessor("fr_pwatch_time");
  double fr_pwatch_time = context->GetDoubleItemAttr(item_key, fr_pwatch_time_accessor).value_or(0.0);
  type.set("fr_pwatch_time", std::to_string(fr_pwatch_time));

  auto* fr_plvtr_accessor = context->GetItemAttrAccessor("fr_plvtr");
  double fr_plvtr = context->GetDoubleItemAttr(item_key, fr_plvtr_accessor).value_or(0.0);
  type.set("fr_plvtr", std::to_string(fr_plvtr));

  auto* fr_pltr_accessor = context->GetItemAttrAccessor("fr_pltr");
  double fr_pltr = context->GetDoubleItemAttr(item_key, fr_pltr_accessor).value_or(0.0);
  type.set("fr_pltr", std::to_string(fr_pltr));

  auto* fr_pcmtr_accessor = context->GetItemAttrAccessor("fr_pcmtr");
  double fr_pcmtr = context->GetDoubleItemAttr(item_key, fr_pcmtr_accessor).value_or(0.0);
  type.set("fr_pcmtr", std::to_string(fr_pcmtr));

  *res = base::JsonToString(type.get());
}

void FollowMixLogEnricher::MixPxtrToJsonString(
    MutableRecoContextInterface *context, int64 item_key, std::string* res) {
  base::Json type(base::StringToJson("{}"));

  auto* mix_pctr_accessor = context->GetItemAttrAccessor("mix_pctr");
  double mix_pctr = context->GetDoubleItemAttr(item_key, mix_pctr_accessor).value_or(0.0);
  type.set("mix_pctr", std::to_string(mix_pctr));

  auto* mix_pwt_accessor = context->GetItemAttrAccessor("mix_pwt");
  double mix_pwt = context->GetDoubleItemAttr(item_key, mix_pwt_accessor).value_or(0.0);
  type.set("mix_pwt", std::to_string(mix_pwt));

  auto* mix_plvtr_accessor = context->GetItemAttrAccessor("mix_plvtr");
  double mix_plvtr = context->GetDoubleItemAttr(item_key, mix_plvtr_accessor).value_or(0.0);
  type.set("mix_plvtr", std::to_string(mix_plvtr));

  auto* mix_pltr_accessor = context->GetItemAttrAccessor("mix_pltr");
  double mix_pltr = context->GetDoubleItemAttr(item_key, mix_pltr_accessor).value_or(0.0);
  type.set("mix_pltr", std::to_string(mix_pltr));

  auto* mix_pcmtr_accessor = context->GetItemAttrAccessor("mix_pcmtr");
  double mix_pcmtr = context->GetDoubleItemAttr(item_key, mix_pcmtr_accessor).value_or(0.0);
  type.set("mix_pcmtr", std::to_string(mix_pcmtr));

  auto* mix_uecsore_accessor = context->GetItemAttrAccessor("mix_uecsore");
  double mix_uecsore = context->GetDoubleItemAttr(item_key, mix_uecsore_accessor).value_or(0.0);
  type.set("mix_uecsore", std::to_string(mix_uecsore));

  auto* mix_final_score_accessor = context->GetItemAttrAccessor("mix_final_score");
  double mix_final_score = context->GetDoubleItemAttr(item_key, mix_final_score_accessor).value_or(0.0);
  type.set("mix_final_score", std::to_string(mix_final_score));

  auto* mix_merchant_score_accessor = context->GetItemAttrAccessor("mix_merchant_score");
  double mix_merchant_score = context->GetDoubleItemAttr(item_key,
    mix_merchant_score_accessor).value_or(0.0);
  type.set("mix_merchant_score", std::to_string(mix_merchant_score));

  auto* wtype_accessor = context->GetItemAttrAccessor("wtype");
  double wtype = context->GetDoubleItemAttr(item_key, wtype_accessor).value_or(0.0);
  type.set("wtype", std::to_string(wtype));

  *res = base::JsonToString(type.get());
}

void FollowMixLogEnricher::FillAdTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  auto* is_ad_accessor = context->GetItemAttrAccessor("is_ad");
  auto *ad_index_accessor = context->GetItemAttrAccessor("ad_index");
  auto *cpm_accessor = context->GetItemAttrAccessor("ad_cpm");
  auto *ad_score_accessor = context->GetItemAttrAccessor("ad_score");
  auto *reco_rank_benefit_accessor = context->GetItemAttrAccessor("ad_reco_rank_benefit");
  for (int i = 0; i < item_keys.size(); i++) {
    int64 item_key = item_keys[i];
    if (context->GetIntItemAttr(item_key, is_ad_accessor).value_or(0) != 1) {
      continue;
    }
    auto* feed = follow_mix_log_.mutable_list(i);
    SetIntAttr(feed, position_in_ad, ad_index_accessor, item_key);
    feed->set_ecpm(context->GetDoubleItemAttr(item_key, cpm_accessor).value_or(0.0));
    feed->set_ad_rank_benefit(context->GetDoubleItemAttr(
      item_key, reco_rank_benefit_accessor).value_or(0.0) * 1e+6);
    feed->set_ad_score(context->GetDoubleItemAttr(item_key, ad_score_accessor).value_or(0.0));
  }
}

void FollowMixLogEnricher::FillMerchantTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  auto* is_merchant_accessor = context->GetItemAttrAccessor("is_merchant");
  auto* is_merchant_cart_accessor = context->GetItemAttrAccessor("is_merchant_cart");
  auto* is_merchant_living_photo_accessor = context->GetItemAttrAccessor("is_merchant_living_photo");
  auto *merchant_index_accessor = context->GetItemAttrAccessor("merchant_index");
  auto *cpm_accessor = context->GetItemAttrAccessor("merchant_cpm");
  auto *merchant_score_accessor = context->GetItemAttrAccessor("merchant_score");
  auto *merchant_bonus_accessor = context->GetItemAttrAccessor("merchant_bonus");
  auto *merchant_cart_cpm_accessor = context->GetItemAttrAccessor("merchant_cart_cpm");
  auto *merchant_cart_score_accessor = context->GetItemAttrAccessor("merchant_cart_score");
  auto *eshop_cart_cvr_accessor = context->GetItemAttrAccessor("eshop_cart_cvr");
  auto *eshop_live_ctr_accessor = context->GetItemAttrAccessor("eshop_live_ctr");
  auto *eshop_live_price_accessor = context->GetItemAttrAccessor("eshop_live_price");
  auto *merchant_cart_pctr_accessor = context->GetItemAttrAccessor("merchant_cart_pctr");
  auto *merchant_cart_pcvr_accessor = context->GetItemAttrAccessor("merchant_cart_pcvr");
  auto *merchant_cart_pgmv_accessor = context->GetItemAttrAccessor("merchant_cart_pgmv");
  auto *merchant_living_photo_pctr_accessor = context->GetItemAttrAccessor("merchant_living_photo_pctr");
  auto *merchant_living_photo_pcvr_accessor = context->GetItemAttrAccessor("merchant_living_photo_pcvr");
  auto *merchant_living_photo_pgmv_accessor = context->GetItemAttrAccessor("merchant_living_photo_pgmv");

  for (int i = 0; i < item_keys.size(); i++) {
    int64 item_key = item_keys[i];
    if (context->GetIntItemAttr(item_key, is_merchant_accessor).value_or(0) != 1
        && context->GetIntItemAttr(item_key, is_merchant_cart_accessor).value_or(0) != 1
        && context->GetIntItemAttr(item_key, is_merchant_living_photo_accessor).value_or(0) != 1) {
      continue;
    }
    auto* feed = follow_mix_log_.mutable_list(i);
    SetIntAttr(feed, position_in_merchant, merchant_index_accessor, item_key);
    feed->set_merchant_cpm(context->GetDoubleItemAttr(item_key, cpm_accessor).value_or(0.0));
    feed->set_merchant_score(context->GetDoubleItemAttr(item_key, merchant_score_accessor).value_or(0.0));
    feed->set_merchant_bonus(context->GetDoubleItemAttr(item_key, merchant_bonus_accessor).value_or(0.0));
    feed->set_merchant_cart_score(
      context->GetDoubleItemAttr(item_key, merchant_cart_score_accessor).value_or(0.0));
    SetIntAttr(feed, is_photo_with_cart, is_merchant_cart_accessor, item_key);
    SetIntAttr(feed, is_merchant_live_head, is_merchant_living_photo_accessor, item_key);
    feed->set_cvr(context->GetDoubleItemAttr(item_key, eshop_cart_cvr_accessor).value_or(0.0));
    feed->set_price(context->GetDoubleItemAttr(item_key, eshop_live_price_accessor).value_or(0.0));
    feed->set_merchant_cart_ctr(
      context->GetDoubleItemAttr(item_key, merchant_cart_pctr_accessor).value_or(0.0));
    feed->set_merchant_cart_cvr(
      context->GetDoubleItemAttr(item_key, merchant_cart_pcvr_accessor).value_or(0.0));
  }
}

void FollowMixLogEnricher::FillGiftTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  auto* is_gift_accessor = context->GetItemAttrAccessor("is_gift");
  auto *gift_index_accessor = context->GetItemAttrAccessor("gift_index");
  auto *cpm_accessor = context->GetItemAttrAccessor("gift_cpm");
  auto *gift_score_accessor = context->GetItemAttrAccessor("gift_score");
  auto *fr_pgtr_accessor = context->GetItemAttrAccessor("fr_pgtr");
  auto *fr_pgift_value_accessor = context->GetItemAttrAccessor("fr_pgift_value");
  for (int i = 0; i < item_keys.size(); i++) {
    int64 item_key = item_keys[i];
    if (context->GetIntItemAttr(item_key, is_gift_accessor).value_or(0) != 1) {
      continue;
    }
    auto* feed = follow_mix_log_.mutable_list(i);
    SetIntAttr(feed, position_in_gift, gift_index_accessor, item_key);
    feed->set_gift_cpm(context->GetDoubleItemAttr(item_key, cpm_accessor).value_or(0.0));
    feed->set_gift_score(context->GetDoubleItemAttr(item_key, gift_score_accessor).value_or(0.0));
    feed->set_gtr(context->GetDoubleItemAttr(item_key, fr_pgtr_accessor).value_or(0.0));
    feed->set_gift_price(context->GetDoubleItemAttr(item_key, fr_pgift_value_accessor).value_or(0.0));
  }
}

void FollowMixLogEnricher::FillLocalLifeTraceLog(
    MutableRecoContextInterface *context, const std::vector<int64> &item_keys) {
  auto* is_local_life_accessor = context->GetItemAttrAccessor("is_locallife_live");
  auto *local_life_score_accessor = context->GetItemAttrAccessor("locallife_live_score");
  for (int i = 0; i < item_keys.size(); i++) {
    int64 item_key = item_keys[i];
    if (context->GetIntItemAttr(item_key, is_local_life_accessor).value_or(0) != 1) {
      continue;
    }
    auto* feed = follow_mix_log_.mutable_list(i);
    feed->set_local_life_score(
        context->GetDoubleItemAttr(item_key, local_life_score_accessor).value_or(0.0));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMixLogEnricher, FollowMixLogEnricher);

}  // namespace platform
}  // namespace ks
