#include "dragon/src/processor/ext/follow_leaf/enricher/follow_feed_back_feature_enricher.h"

#include <unordered_map>
#include <utility>

#include "ks/reco_proto/proto/realtime_reco.pb.h"

#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {
void FollowFeedbackFeatureEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  auto feedback_feed_ids_ptr = context->GetIntListCommonAttr("uFeedbackPhotoIds");
  auto feedback_feed_is_live_ptr = context->GetIntListCommonAttr("uFeedbackIsLive");
  if (!feedback_feed_ids_ptr || !feedback_feed_is_live_ptr) {
    return;
  }
  if (feedback_feed_ids_ptr->empty()) {
    return;
  }
  if (feedback_feed_ids_ptr->size() != feedback_feed_is_live_ptr->size()) {
    return;
  }
  std::vector<int64> recent_feedback_live_hetu_level_one_list;
  std::vector<int64> recent_feedback_live_hetu_level_two_list;
  std::vector<int64> recent_feedback_photo_hetu_level_one_list;
  std::vector<int64> recent_feedback_photo_hetu_level_two_list;
  for (int i = 0; i < feedback_feed_ids_ptr->size(); ++i) {
    int64 feed_id = feedback_feed_ids_ptr->at(i);
    if (feedback_feed_is_live_ptr->at(i)) {
      int64 item_key = ks::platform::Util::GenKeysign(3, feed_id);
      auto live_hetu_level_one = context->GetIntListItemAttr(item_key, "live_hetu_tag_info__hetu_level_one");
      if (live_hetu_level_one && !live_hetu_level_one->empty()) {
        recent_feedback_live_hetu_level_one_list.emplace_back(live_hetu_level_one->at(0));
      } else {
        recent_feedback_live_hetu_level_one_list.emplace_back(0);
      }
      auto live_hetu_level_two = context->GetIntListItemAttr(item_key, "live_hetu_tag_info__hetu_level_two");
      if (live_hetu_level_two && !live_hetu_level_two->empty()) {
        recent_feedback_live_hetu_level_two_list.emplace_back(live_hetu_level_two->at(0));
      } else {
        recent_feedback_live_hetu_level_two_list.emplace_back(0);
      }
      recent_feedback_photo_hetu_level_one_list.emplace_back(0);
      recent_feedback_photo_hetu_level_two_list.emplace_back(0);
    } else {
      int64 item_key = ks::platform::Util::GenKeysign(2, feed_id);
      auto hetu_level_one = context->GetIntListItemAttr(item_key, "photo_hetu_tag_info__hetu_level_one");
      if (hetu_level_one && !hetu_level_one->empty()) {
        recent_feedback_photo_hetu_level_one_list.emplace_back(hetu_level_one->at(0));
      } else {
        recent_feedback_photo_hetu_level_one_list.emplace_back(0);
      }
      auto hetu_level_two = context->GetIntListItemAttr(item_key, "photo_hetu_tag_info__hetu_level_two");
      if (hetu_level_two && !hetu_level_two->empty()) {
        recent_feedback_photo_hetu_level_two_list.emplace_back(hetu_level_two->at(0));
      } else {
        recent_feedback_photo_hetu_level_two_list.emplace_back(0);
      }
      recent_feedback_live_hetu_level_one_list.emplace_back(0);
      recent_feedback_live_hetu_level_two_list.emplace_back(0);
    }
  }

  context->SetIntListCommonAttr("uFeedbackLiveHetuLevel1Ids",
    std::move(recent_feedback_live_hetu_level_one_list));
  context->SetIntListCommonAttr("uFeedbackLiveHetuLevel2Ids",
    std::move(recent_feedback_live_hetu_level_two_list));
  context->SetIntListCommonAttr("uFeedbackPhotoHetuLevel1Ids",
    std::move(recent_feedback_photo_hetu_level_one_list));
  context->SetIntListCommonAttr("uFeedbackPhotoHetuLevel2Ids",
    std::move(recent_feedback_photo_hetu_level_two_list));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowFeedbackFeatureEnricher, FollowFeedbackFeatureEnricher);

}  // namespace platform
}  // namespace ks
