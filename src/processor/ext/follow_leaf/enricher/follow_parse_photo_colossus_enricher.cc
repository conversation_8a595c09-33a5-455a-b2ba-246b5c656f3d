#include "dragon/src/processor/ext/follow_leaf/enricher/follow_parse_photo_colossus_enricher.h"

#include <algorithm>
#include <vector>
#include <utility>

#include "dragon/src/processor/ext/follow_leaf/context/util.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

using google::protobuf::Message;

namespace ks {
namespace platform {
void FollowParsePhotoColossusEnricher::Enrich(
  MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  Clear();
  auto friend_list_ptr = context->GetIntListCommonAttr("friend_author_list");
  if (friend_list_ptr) {
    for (auto aid : *friend_list_ptr) {
      friend_set_.insert(aid);
    }
  }
  if (!ParsePhotoColossus(context) || !FillRecentPlayPhotoInfo(context)) {
    return;
  }
  FillColossusToAttr(context, begin, end);
  FillRecentPlayToAttr(context, begin, end);
  FillSimUserToAttr(context, begin, end);
  FillUserAttrSimInfo(context);
}

void FollowParsePhotoColossusEnricher::FillSimUserToAttr(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  int play_cnt_30d = sim_user_static_info_map_["play_cnt"]["30d"];
  int like_cnt_30d = sim_user_static_info_map_["like_cnt"]["30d"];
  int comment_cnt_30d = sim_user_static_info_map_["comment_cnt"]["30d"];
  double like_ratio_30d = play_cnt_30d >= 100 ? 1.0 * like_cnt_30d / play_cnt_30d : 0.0;
  double comment_ratio_30d = play_cnt_30d >= 100 ? 1.0 * comment_cnt_30d / play_cnt_30d : 0.0;
  context->SetIntCommonAttr("sim_play_cnt_30d", play_cnt_30d);
  context->SetDoubleCommonAttr("sim_like_ratio_30d", like_ratio_30d);
  context->SetDoubleCommonAttr("sim_comment_ratio_30d", comment_ratio_30d);
}

void FollowParsePhotoColossusEnricher::FillColossusToAttr(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  // read
  auto* author_id_accessor = context->GetItemAttrAccessor("author_id");
  // write
  auto* sim_user_author_playtime_7d_accessor = context->GetItemAttrAccessor("sim_user_author_playtime_7d");
  auto* sim_user_author_effectcnt_7d_accessor = context->GetItemAttrAccessor("sim_user_author_effectcnt_7d");

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto author_id = context->GetIntItemAttr(result, author_id_accessor).value_or(0);
    int64 sim_user_author_playtime_7d = GetValueFromMap(
      user_recent_7D_playcnt_follow_author_map_, author_id);
    int64 sim_user_author_effectcnt_7d = GetValueFromMap(
      user_recent_7D_effectcnt_follow_author_map_, author_id);
    context->SetIntItemAttr(result, sim_user_author_playtime_7d_accessor, sim_user_author_playtime_7d);
    context->SetIntItemAttr(result, sim_user_author_effectcnt_7d_accessor, sim_user_author_effectcnt_7d);
  });
}

uint32 FollowParsePhotoColossusEnricher::GetValueFromMap(
    const folly::F14FastMap<uint64, uint32>& value_map, uint64 id) {
  auto it = value_map.find(id);
  if (it != value_map.end()) {
    return it->second;
  }
  return 0;
}

bool FollowParsePhotoColossusEnricher::ParsePhotoColossus(MutableRecoContextInterface *context) {
  const auto* colossus_sim_v2_photo_items = context->GetPtrCommonAttr<std::vector<const Message *>>(
      colossus_info_path_);
  if (colossus_sim_v2_photo_items == nullptr || colossus_sim_v2_photo_items->empty()) {
    return false;
  }
  bool enable_gen_kuiba_attr_in_common_leaf = context->GetIntCommonAttr(
    "gen_kuiba_attr_in_common_leaf").value_or(0);
  uint64 cur_time = base::GetTimestamp() / 1000000;

  uint32 effect_vv_14D = 0;
  uint32 play_vv_14D = 0;
  uint32 play_time_14D = 0;
  uint32 like_14D = 0;
  uint32 comment_14D = 0;
  uint32 effect_vv_7D = 0;
  uint32 play_vv_7D = 0;
  uint32 play_time_7D = 0;
  uint32 like_7D = 0;
  uint32 comment_7D = 0;

  uint32 follow_effect_vv_14D = 0;
  uint32 follow_play_vv_14D = 0;
  uint32 follow_play_vv_14D_v2 = 0;
  uint32 follow_play_time_14D = 0;
  uint32 follow_like_14D = 0;
  uint32 follow_comment_14D = 0;
  uint32 follow_effect_vv_7D = 0;
  uint32 follow_play_vv_7D = 0;
  uint32 follow_play_vv_7D_v2 = 0;
  uint32 follow_play_time_7D = 0;
  uint32 follow_like_7D = 0;
  uint32 follow_comment_7D = 0;
  int recent_ua_photo_max_num = ks::infra::KConf().Get(
    "reco.follow.fillRecentAuthorTopListInfoMaxNumSlide", 0)->Get();
  int recent_ua_photo_cnt = 0;

  std::vector<int64> sim_user_duration_list_7d;
  std::vector<int64> sim_user_play_list_7d;
  std::vector<int64> sim_user_follow_play_list_7d;

  for (const Message * msg : *colossus_sim_v2_photo_items) {
    const colossus::SimItemV2* sim_v2_photo = static_cast<const colossus::SimItemV2*>(msg);
    if (sim_v2_photo == nullptr) {
      continue;
    }
    const colossus::SimItemV2& item = *sim_v2_photo;
    if (item.photo_id() <= 0) {
      continue;
    }
    if (item.author_id() <= 0) {
      continue;
    }
    if (recent_ua_photo_cnt >= recent_ua_photo_max_num) {
      break;
    } else {
      recent_ua_photo_cnt += 1;
    }

    // int64 photo_id = item.photo_id();
    int64 author_id = item.author_id();
    uint32 item_label = item.label();
    uint64 timestamp = item.timestamp();
    uint32 play_time = item.play_time();   // 单位 second，这个是播放时长
    uint32 duration = item.duration();   // 单位 second
    uint32 channel_type = item.channel();
    // int64 hetu_tag = item.tag();  // hetu 最高 level 的 tag 值

    bool is_friend = friend_set_.find(author_id) != friend_set_.end();
    bool is_follow = channel_type == 3 || channel_type == 32
      || channel_type == 33 || channel_type == 86 || channel_type == 87;

    // photo 距今天数
    int play_time_gap_day = static_cast<int>((cur_time - timestamp) / (24 * 3600));
    bool like_label = item_label & 0x01;
    bool comment_label = item_label & (1 << 4);
    bool effective_view = false;
    bool is_picture = item_label & (1 << 9);

    effect_vv_14D = 0;
    play_vv_14D = 0;
    play_time_14D = 0;
    like_14D = 0;
    comment_14D = 0;
    effect_vv_7D = 0;
    play_vv_7D = 0;
    play_time_7D = 0;
    like_7D = 0;
    comment_7D = 0;

    follow_effect_vv_14D = 0;
    follow_play_vv_14D = 0;
    follow_play_time_14D = 0;
    follow_like_14D = 0;
    follow_comment_14D = 0;
    follow_effect_vv_7D = 0;
    follow_play_vv_7D_v2 = 0;
    follow_play_time_7D = 0;
    follow_like_7D = 0;
    follow_comment_7D = 0;

    // 落盘 UA 粒度，用户序列特征
    if (is_picture) {
      effective_view = play_time >= 7;
    } else if (item.duration() <= 0) {
      effective_view = false;
    } else if (item.duration() < 7) {
      effective_view = play_time >= item.duration();
    } else if (item.duration() > 18) {
      effective_view = play_time >= 18;
    } else {
      effective_view = play_time >= 7;
    }
    if (play_time_gap_day >= 14) {
      continue;
    }
    if (play_time_gap_day < 7) {
      play_vv_7D++;
      play_time_7D += play_time;
      if (like_label) {
        like_7D++;
      }
      if (comment_label) {
        comment_7D++;
      }
      if (effective_view) {
        effect_vv_7D++;
      }
    } else {
      play_vv_14D++;
      play_time_14D += play_time;
      if (like_label) {
        like_14D++;
      }
      if (comment_label) {
        comment_14D++;
      }
      if (effective_view) {
        effect_vv_14D++;
      }
    }
    if (is_follow) {
      if (play_time_gap_day < 7) {
        follow_play_vv_7D++;
        follow_play_vv_7D_v2++;
        follow_play_time_7D += play_time;
        if (like_label) {
          follow_like_7D++;
        }
        if (comment_label) {
          follow_comment_7D++;
        }
        if (effective_view) {
          follow_effect_vv_7D++;
        }
      }
      if (play_time_gap_day < 14) {
        follow_play_vv_14D++;
        follow_play_vv_14D_v2++;
        follow_play_time_14D += play_time;
        if (like_label) {
          follow_like_14D++;
        }
        if (comment_label) {
          follow_comment_14D++;
        }
        if (effective_view) {
          follow_effect_vv_14D++;
        }
      }
    }
    if (author_top_cnt_info_map_.find(author_id) == author_top_cnt_info_map_.end()) {
      AuthorTopCntInfo author_top_cnt_info;
      author_top_cnt_info_map_.emplace(author_id, author_top_cnt_info);
    }
    AuthorTopCntInfo& author_top_cnt_info = author_top_cnt_info_map_[author_id];
    author_top_cnt_info.play_vv_7D += play_vv_7D;
    author_top_cnt_info.play_vv_14D += play_vv_14D;
    author_top_cnt_info.play_time_7D += play_time_7D;
    author_top_cnt_info.play_time_14D += play_time_14D;
    author_top_cnt_info.like_7D += like_7D;
    author_top_cnt_info.like_14D += like_14D;
    author_top_cnt_info.comment_7D += comment_7D;
    author_top_cnt_info.comment_14D += comment_14D;
    author_top_cnt_info.effect_vv_7D += effect_vv_7D;
    author_top_cnt_info.effect_vv_14D += effect_vv_14D;
    author_top_cnt_info.follow_play_vv_7D += follow_play_vv_7D;
    author_top_cnt_info.follow_play_vv_7D_v2 += follow_play_vv_7D_v2;
    author_top_cnt_info.follow_play_vv_14D += follow_play_vv_14D;
    author_top_cnt_info.follow_play_vv_14D_v2 += follow_play_vv_14D_v2;
    author_top_cnt_info.follow_play_time_7D += follow_play_time_7D;
    author_top_cnt_info.follow_play_time_14D += follow_play_time_14D;
    author_top_cnt_info.follow_like_7D += follow_like_7D;
    author_top_cnt_info.follow_like_14D += follow_like_14D;
    author_top_cnt_info.follow_comment_7D += follow_comment_7D;
    author_top_cnt_info.follow_comment_14D += follow_comment_14D;
    author_top_cnt_info.follow_effect_vv_7D += follow_effect_vv_7D;
    author_top_cnt_info.follow_effect_vv_14D += follow_effect_vv_14D;
    author_top_cnt_info.is_friend = is_friend;

    // U 粒度历史播放时长序列
    if (play_time_gap_day <= 7) {
      sim_user_duration_list_7d.push_back(duration);
      sim_user_play_list_7d.push_back(play_time);
      if (is_follow) {
        sim_user_follow_play_list_7d.push_back(play_time);
      }
    }
  }
  if (enable_gen_kuiba_attr_in_common_leaf) {
    context->SetIntListCommonAttr("sim_user_duration_list_7d", std::move(sim_user_duration_list_7d));
    context->SetIntListCommonAttr("sim_user_play_list_7d", std::move(sim_user_play_list_7d));
    context->SetIntListCommonAttr("sim_user_follow_duration_list_7d", std::move(std::vector<int64>()));
    context->SetIntListCommonAttr("sim_user_follow_play_list_7d", std::move(sim_user_follow_play_list_7d));
    context->SetIntListCommonAttr("sim_user_follow_pid_list_7d", std::move(std::vector<int64>()));
    context->SetIntListCommonAttr("sim_user_follow_aid_list_7d", std::move(std::vector<int64>()));
    context->SetIntListCommonAttr("sim_user_follow_timestamp_list_7d", std::move(std::vector<int64>()));
    context->SetIntListCommonAttr("sim_user_follow_hetu_tag_list_7d", std::move(std::vector<int64>()));
  }

  if (!enable_gen_kuiba_attr_in_common_leaf) {
    return true;
  }

  // 这里的 author info playcnt_7d 表示的是 vv，不是时长
  // 且统计逻辑有问题，线上已经生效，所以在后面加了 v2 改成正确统计逻辑
  std::vector<std::pair<uint64, AuthorTopCntInfo>> author_info_vec(
    author_top_cnt_info_map_.begin(), author_top_cnt_info_map_.end());
  uint64 author_list_size = author_info_vec.size() > 10 ? 10 : author_info_vec.size();
  uint64 follow_author_list_size = author_info_vec.size() > 50 ? 50 : author_info_vec.size();
  uint64 author_vec_size = author_info_vec.size();
  for (uint64 i = 0; i < author_info_vec.size(); i++) {
    uint64 aid = author_info_vec[i].first;
    user_recent_14D_playcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_play_vv_14D);
    user_recent_14D_playcnt_v2_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_play_vv_14D_v2);
    user_recent_14D_playtime_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_play_time_14D);
    user_recent_7D_playcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_play_vv_7D);
    user_recent_7D_playcnt_follow_author_map_v2_.emplace(
      aid, author_info_vec[i].second.follow_play_vv_7D_v2);
    user_recent_7D_playtime_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_play_time_7D);
    user_recent_14D_effectcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_effect_vv_14D);
    user_recent_7D_effectcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_effect_vv_7D);
    user_recent_14D_likecnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_like_14D);
    user_recent_7D_likecnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_like_7D);
    user_recent_14D_commentcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_comment_14D);
    user_recent_7D_commentcnt_follow_author_map_.emplace(
      aid, author_info_vec[i].second.follow_comment_7D);
  }
  std::vector<int64> user_recent_14D_play_top10_author_list;
  std::vector<int64> user_recent_7D_play_top10_author_list;
  std::vector<int64> user_recent_14D_effect_vv_top10_author_list;
  std::vector<int64> user_recent_7D_effect_vv_top10_author_list;
  std::vector<int64> user_recent_14D_like_top10_author_list;
  std::vector<int64> user_recent_7D_like_top10_author_list;
  std::vector<int64> user_recent_14D_comment_top10_author_list;
  std::vector<int64> user_recent_7D_comment_top10_author_list;
  std::vector<int64> user_recent_14D_play_top50_follow_author_list;
  std::vector<int64> user_recent_7D_play_top50_follow_author_list;
  std::vector<int64> user_recent_14D_effect_vv_top50_follow_author_list;
  std::vector<int64> user_recent_7D_effect_vv_top50_follow_author_list;
  std::vector<int64> user_recent_14D_like_top50_follow_author_list;
  std::vector<int64> user_recent_7D_like_top50_follow_author_list;
  std::vector<int64> user_recent_14D_comment_top50_follow_author_list;
  std::vector<int64> user_recent_7D_comment_top50_follow_author_list;
  std::vector<int64> user_recent_14D_play_top50_follow_single_author_list;
  std::vector<int64> user_recent_7D_play_top50_follow_single_author_list;
  std::vector<int64> user_recent_14D_effect_vv_top50_follow_single_author_list;
  std::vector<int64> user_recent_7D_effect_vv_top50_follow_single_author_list;
  std::vector<int64> user_recent_14D_like_top50_follow_single_author_list;
  std::vector<int64> user_recent_7D_like_top50_follow_single_author_list;
  std::vector<int64> user_recent_14D_comment_top50_follow_single_author_list;
  std::vector<int64> user_recent_7D_comment_top50_follow_single_author_list;
  std::vector<int64> user_recent_14D_play_top50_follow_friend_author_list;
  std::vector<int64> user_recent_7D_play_top50_follow_friend_author_list;
  std::vector<int64> user_recent_14D_effect_vv_top50_follow_friend_author_list;
  std::vector<int64> user_recent_7D_effect_vv_top50_follow_friend_author_list;
  std::vector<int64> user_recent_14D_like_top50_follow_friend_author_list;
  std::vector<int64> user_recent_7D_like_top50_follow_friend_author_list;
  std::vector<int64> user_recent_14D_comment_top50_follow_friend_author_list;
  std::vector<int64> user_recent_7D_comment_top50_follow_friend_author_list;
  // play time 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.play_time_14D > b.second.play_time_14D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_14D_play_top10_author_list.push_back(author_info_vec[i].first);
  }
  // play time 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.play_time_7D > b.second.play_time_7D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_7D_play_top10_author_list.push_back(author_info_vec[i].first);
  }
  // effect vv 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.effect_vv_14D > b.second.effect_vv_14D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_14D_effect_vv_top10_author_list.push_back(author_info_vec[i].first);
  }
  // effect vv 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.effect_vv_7D > b.second.effect_vv_7D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_7D_effect_vv_top10_author_list.push_back(author_info_vec[i].first);
  }
  // like 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.like_14D > b.second.like_14D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_14D_like_top10_author_list.push_back(author_info_vec[i].first);
  }
  // like 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.like_7D > b.second.like_7D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_7D_like_top10_author_list.push_back(author_info_vec[i].first);
  }
  // comment 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.comment_14D > b.second.comment_14D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_14D_comment_top10_author_list.push_back(author_info_vec[i].first);
  }
  // comment 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.comment_7D > b.second.comment_7D;
  });
  for (uint64 i = 0; i < author_list_size; i++) {
    user_recent_7D_comment_top10_author_list.push_back(author_info_vec[i].first);
  }

  // follow play time 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_play_time_14D > b.second.follow_play_time_14D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_14D_play_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_14D_play_top50_follow_friend_author_list.size() >= 50) {
        continue;
      }
      user_recent_14D_play_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_14D_play_top50_follow_single_author_list.size() >= 50) {
        continue;
      }
      user_recent_14D_play_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow play time 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_play_time_7D > b.second.follow_play_time_7D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_7D_play_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_7D_play_top50_follow_friend_author_list.size() >= 50) {
        continue;
      }
      user_recent_7D_play_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_7D_play_top50_follow_single_author_list.size() >= 50) {
        continue;
      }
      user_recent_7D_play_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow effect vv 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_effect_vv_14D > b.second.follow_effect_vv_14D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_14D_effect_vv_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_14D_effect_vv_top50_follow_friend_author_list.size() >= 50) {
        continue;
      }
      user_recent_14D_effect_vv_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_14D_effect_vv_top50_follow_single_author_list.size() >= 50) {
        continue;
      }
      user_recent_14D_effect_vv_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow effect vv 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
   [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_effect_vv_7D > b.second.follow_effect_vv_7D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_7D_effect_vv_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_7D_effect_vv_top50_follow_friend_author_list.size() >= 50) {
        continue;
      }
      user_recent_7D_effect_vv_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_7D_effect_vv_top50_follow_single_author_list.size() >= 50) {
        continue;
      }
      user_recent_7D_effect_vv_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow like 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_like_14D > b.second.follow_like_14D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_14D_like_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_14D_like_top50_follow_friend_author_list.size() > 50) {
        continue;
      }
      user_recent_14D_like_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_14D_like_top50_follow_single_author_list.size() > 50) {
        continue;
      }
      user_recent_14D_like_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow like 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
   [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_like_7D > b.second.follow_like_7D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_7D_like_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_7D_like_top50_follow_friend_author_list.size() > 50) {
        continue;
      }
      user_recent_7D_like_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_7D_like_top50_follow_single_author_list.size() > 50) {
        continue;
      }
      user_recent_7D_like_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow comment 14D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
   [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_comment_14D > b.second.follow_comment_14D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_14D_comment_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_14D_comment_top50_follow_friend_author_list.size() > 50) {
        continue;
      }
      user_recent_14D_comment_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_14D_comment_top50_follow_single_author_list.size() > 50) {
        continue;
      }
      user_recent_14D_comment_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  // follow comment 7D
  std::sort(author_info_vec.begin(), author_info_vec.end(),
  [](const std::pair<uint64, AuthorTopCntInfo>& a, const std::pair<uint64, AuthorTopCntInfo>& b) {
    return a.second.follow_comment_7D > b.second.follow_comment_7D;
  });
  for (uint64 i = 0; i < follow_author_list_size; i++) {
    user_recent_7D_comment_top50_follow_author_list.push_back(author_info_vec[i].first);
  }
  for (uint64 i = 0; i < author_vec_size; i++) {
    if (author_info_vec[i].second.is_friend) {
      if (user_recent_7D_comment_top50_follow_friend_author_list.size() > 50) {
        continue;
      }
      user_recent_7D_comment_top50_follow_friend_author_list.push_back(author_info_vec[i].first);
    } else {
      if (user_recent_7D_comment_top50_follow_single_author_list.size() > 50) {
        continue;
      }
      user_recent_7D_comment_top50_follow_single_author_list.push_back(author_info_vec[i].first);
    }
  }
  context->SetIntListCommonAttr("uAuthorPlayTop10List14D",
    std::move(user_recent_14D_play_top10_author_list));
  context->SetIntListCommonAttr("uAuthorPlayTop10List7D",
    std::move(user_recent_7D_play_top10_author_list));
  context->SetIntListCommonAttr("uAuthorEffectVVTop10List14D",
    std::move(user_recent_14D_effect_vv_top10_author_list));
  context->SetIntListCommonAttr("uAuthorEffectVVTop10List7D",
    std::move(user_recent_7D_effect_vv_top10_author_list));
  context->SetIntListCommonAttr("uAuthorLikeTop10List14D",
    std::move(user_recent_14D_like_top10_author_list));
  context->SetIntListCommonAttr("uAuthorLikeTop10List7D",
    std::move(user_recent_7D_like_top10_author_list));
  context->SetIntListCommonAttr("uAuthorCommentTop10List14D",
    std::move(user_recent_14D_comment_top10_author_list));
  context->SetIntListCommonAttr("uAuthorCommentTop10List7D",
    std::move(user_recent_7D_comment_top10_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlayTop50List14D",
    std::move(user_recent_14D_play_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlayTop50List7D",
    std::move(user_recent_7D_play_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVTop50List14D",
    std::move(user_recent_14D_effect_vv_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVTop50List7D",
    std::move(user_recent_7D_effect_vv_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeTop50List14D",
    std::move(user_recent_14D_like_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeTop50List7D",
    std::move(user_recent_7D_like_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentTop50List14D",
    std::move(user_recent_14D_comment_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentTop50List7D",
    std::move(user_recent_7D_comment_top50_follow_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlayFriendTop50List14D",
    std::move(user_recent_14D_play_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlayFriendTop50List7D",
    std::move(user_recent_7D_play_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVFriendTop50List14D",
    std::move(user_recent_14D_effect_vv_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVFriendTop50List7D",
    std::move(user_recent_7D_effect_vv_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeFriendTop50List14D",
    std::move(user_recent_14D_like_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeFriendTop50List7D",
    std::move(user_recent_7D_like_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentFriendTop50List14D",
    std::move(user_recent_14D_comment_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentFriendTop50List7D",
    std::move(user_recent_7D_comment_top50_follow_friend_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlaySingleTop50List14D",
    std::move(user_recent_14D_play_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorPlaySingleTop50List7D",
    std::move(user_recent_7D_play_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVSingleTop50List14D",
    std::move(user_recent_14D_effect_vv_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorEffectVVSingleTop50List7D",
    std::move(user_recent_7D_effect_vv_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeSingleTop50List14D",
    std::move(user_recent_14D_like_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorLikeSingleTop50List7D",
    std::move(user_recent_7D_like_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentSingleTop50List14D",
    std::move(user_recent_14D_comment_top50_follow_single_author_list));
  context->SetIntListCommonAttr("uFollowAuthorCommentSingleTop50List7D",
    std::move(user_recent_7D_comment_top50_follow_single_author_list));
  return true;
}  // NOLINT

bool FollowParsePhotoColossusEnricher::FillRecentPlayPhotoInfo(MutableRecoContextInterface *context) {
  const auto* colossus_sim_v2_photo_items = context->GetPtrCommonAttr<std::vector<const Message *>>(
      colossus_info_path_);
  if (colossus_sim_v2_photo_items == nullptr || colossus_sim_v2_photo_items->empty()) {
    return false;
  }
  uint64 cur_time = base::GetTimestamp() / 1000000;
  static auto kconf_meta = ks::infra::KConf().Get("reco.follow.fillRecentPlayPhotoInfoMaxNumSlide", 0);
  int recent_photo_max_num = kconf_meta->Get();
  int recent_photo_cnt = 0;
  for (const Message * msg : *colossus_sim_v2_photo_items) {
    const colossus::SimItemV2* sim_v2_photo = static_cast<const colossus::SimItemV2*>(msg);
    if (sim_v2_photo == nullptr) {
      continue;
    }
    const colossus::SimItemV2& item = *sim_v2_photo;
    if (recent_photo_cnt >= recent_photo_max_num) {
      break;
    } else {
      recent_photo_cnt += 1;
    }

    if (item.photo_id() <= 0) {
      continue;
    }
    if (item.author_id() <= 0) {
      continue;
    }
    uint32 item_label = item.label();
    uint64 timestamp = item.timestamp();
    int play_time_gap_day = static_cast<int>((cur_time - timestamp) / (24 * 3600));
    int play_time_gap_hour = static_cast<int>((cur_time - timestamp) / 3600);
    bool like_label = item_label & 0x01;
    bool comment_label = item_label & (1 << 4);
    bool forward_label = item_label & (1 << 2);
    bool hate_label = item_label & (1 << 3);
    bool enter_profile_label = item_label & (1 << 6);
    bool negative_label = item_label & (1 << 13);
    bool is_picture = item_label & (1 << 9);
    uint32 channel_type = item.channel();
    bool is_follow = channel_type == 3 || channel_type == 32 || channel_type == 33
      || channel_type == 86 || channel_type == 87;
    bool effective_view = false;
    if (is_picture) {
      effective_view = item.play_time() >= 7;
    } else if (item.duration() <= 0) {
      effective_view = false;
    } else if (item.duration() < 7) {
      effective_view = item.play_time() >= item.duration();
    } else if (item.duration() > 18) {
      effective_view = item.play_time() >= 18;
    } else {
      effective_view = item.play_time() >= 7;
    }

    bool short_view = false;
    if (is_picture || item.duration() > 3) {
      short_view = item.play_time() <= 3;
    }

    bool long_view = item.play_time() >= 18;
    if (play_time_gap_day < 28 && is_follow) {
      sim_user_follow_photo_play_sum_28d_ += item.play_time();
    }
    if (play_time_gap_day < 30 && is_follow) {
      if (item.play_time() > 0) {
        sim_user_static_info_map_["play_cnt"]["30d"] += 1;
      }
      if (like_label) {
        sim_user_static_info_map_["like_cnt"]["30d"] += 1;
      }
      if (comment_label) {
        sim_user_static_info_map_["comment_cnt"]["30d"] += 1;
      }
      if (forward_label) {
        sim_user_static_info_map_["forward_cnt"]["30d"] += 1;
      }
    }

    // interact
    if ((like_label || comment_label || forward_label || enter_profile_label) && is_follow) {
      if (play_time_gap_day < 1) {
        SafeUpdateInfoMap(&sim_interact_tag_info_map_1d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_interact_author_info_map_1d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["interact"]["1d"] += 1;
      }
      if (play_time_gap_day < 3) {
        SafeUpdateInfoMap(&sim_interact_tag_info_map_3d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_interact_author_info_map_3d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["interact"]["3d"] += 1;
      }
      if (play_time_gap_day < 7) {
        SafeUpdateInfoMap(&sim_interact_tag_info_map_7d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_interact_author_info_map_7d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["interact"]["7d"] += 1;
      }
      if (play_time_gap_day < 14) {
        SafeUpdateInfoMap(&sim_interact_tag_info_map_14d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_interact_author_info_map_14d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["interact"]["14d"] += 1;
      }
      if (play_time_gap_day < 30) {
        SafeUpdateInfoMap(&sim_interact_tag_info_map_30d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_interact_author_info_map_30d_,
         item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["interact"]["30d"] += 1;
      }
    }
    // short_view
    if (short_view && is_follow) {
      if (play_time_gap_day < 1) {
        SafeUpdateInfoMap(&sim_short_view_tag_info_map_1d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_short_view_author_info_map_1d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["short_view"]["1d"] += 1;
      }
      if (play_time_gap_day < 3) {
        SafeUpdateInfoMap(&sim_short_view_tag_info_map_3d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_short_view_author_info_map_3d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["short_view"]["3d"] += 1;
      }
      if (play_time_gap_day < 7) {
        SafeUpdateInfoMap(&sim_short_view_tag_info_map_7d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_short_view_author_info_map_7d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["short_view"]["7d"] += 1;
      }
      if (play_time_gap_day < 14) {
        SafeUpdateInfoMap(&sim_short_view_tag_info_map_14d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_short_view_author_info_map_14d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["short_view"]["14d"] += 1;
      }
      if (play_time_gap_day < 30) {
        SafeUpdateInfoMap(&sim_short_view_tag_info_map_30d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_short_view_author_info_map_30d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["short_view"]["30d"] += 1;
      }
    }
    // effective_view
    if (effective_view && is_follow) {
      if (play_time_gap_day < 1) {
        SafeUpdateInfoMap(&sim_effective_view_tag_info_map_1d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_effective_view_author_info_map_1d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["effective_view"]["1d"] += 1;
        sim_user_static_info_map_["effective_play"]["1d"] += item.play_time();
      }
      if (play_time_gap_day < 3) {
        SafeUpdateInfoMap(&sim_effective_view_tag_info_map_3d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_effective_view_author_info_map_3d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["effective_view"]["3d"] += 1;
        sim_user_static_info_map_["effective_play"]["3d"] += item.play_time();
      }
      if (play_time_gap_day < 7) {
        SafeUpdateInfoMap(&sim_effective_view_tag_info_map_7d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_effective_view_author_info_map_7d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["effective_view"]["7d"] += 1;
        sim_user_static_info_map_["effective_play"]["7d"] += item.play_time();
      }
      if (play_time_gap_day < 14) {
        SafeUpdateInfoMap(&sim_effective_view_tag_info_map_14d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_effective_view_author_info_map_14d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["effective_view"]["14d"] += 1;
        sim_user_static_info_map_["effective_play"]["14d"] += item.play_time();
      }
      if (play_time_gap_day < 30) {
        SafeUpdateInfoMap(&sim_effective_view_tag_info_map_30d_,
          item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_effective_view_author_info_map_30d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["effective_view"]["30d"] += 1;
        sim_user_static_info_map_["effective_play"]["30d"] += item.play_time();
      }
    }
    // long_view
    if (long_view && is_follow) {
      if (play_time_gap_day < 1) {
        SafeUpdateInfoMap(&sim_long_view_tag_info_map_1d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_long_view_author_info_map_1d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["long_view"]["1d"] += 1;
      }
      if (play_time_gap_day < 3) {
        SafeUpdateInfoMap(&sim_long_view_tag_info_map_3d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_long_view_author_info_map_3d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["long_view"]["3d"] += 1;
      }
      if (play_time_gap_day < 7) {
        SafeUpdateInfoMap(&sim_long_view_tag_info_map_7d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_long_view_author_info_map_7d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["long_view"]["7d"] += 1;
      }
      if (play_time_gap_day < 14) {
        SafeUpdateInfoMap(&sim_long_view_tag_info_map_14d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_long_view_author_info_map_14d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["long_view"]["14d"] += 1;
      }
      if (play_time_gap_day < 30) {
        SafeUpdateInfoMap(&sim_long_view_tag_info_map_30d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_long_view_author_info_map_30d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["long_view"]["30d"] += 1;
      }
    }
    // negative
    if ((hate_label || negative_label) && is_follow) {
      if (play_time_gap_day < 1) {
        SafeUpdateInfoMap(&sim_negative_tag_info_map_1d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_negative_author_info_map_1d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["negative"]["1d"] += 1;
      }
      if (play_time_gap_day < 3) {
        SafeUpdateInfoMap(&sim_negative_tag_info_map_3d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_negative_author_info_map_3d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["negative"]["3d"] += 1;
      }
      if (play_time_gap_day < 7) {
        SafeUpdateInfoMap(&sim_negative_tag_info_map_7d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_negative_author_info_map_7d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["negative"]["7d"] += 1;
      }
      if (play_time_gap_day < 14) {
        SafeUpdateInfoMap(&sim_negative_tag_info_map_14d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_negative_author_info_map_14d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["negative"]["14d"] += 1;
      }
      if (play_time_gap_day < 30) {
        SafeUpdateInfoMap(&sim_negative_tag_info_map_30d_, item.tag(), item.play_time(), play_time_gap_hour);
        SafeUpdateInfoMap(&sim_negative_author_info_map_30d_,
          item.author_id(), item.play_time(), play_time_gap_hour);
        sim_user_static_info_map_["negative"]["30d"] += 1;
      }
    }
  }
  return true;
}

void FollowParsePhotoColossusEnricher::SafeUpdateInfoMap(
    folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>>* user_info_map_ptr,
    uint64 key, uint32 play, uint32 last_time_gap_h) {
  folly::F14FastMap<uint64, folly::F14FastMap<std::string, uint32>>& user_info_map = *user_info_map_ptr;
  if (user_info_map.find(key) != user_info_map.end()) {
    auto info_map = user_info_map[key];
    info_map["cnt"] += 1;
    info_map["play"] += play;
    if (last_time_gap_h < info_map["lag"]) {
      info_map["lag"] = last_time_gap_h;
    }
    user_info_map[key] = info_map;
  } else {
    folly::F14FastMap<std::string, uint32> info_map;
    info_map["cnt"] = 1;
    info_map["play"] = play;
    info_map["lag"] = last_time_gap_h;
    user_info_map[key] = info_map;
  }
}

void FollowParsePhotoColossusEnricher::FillRecentPlayToAttr(MutableRecoContextInterface *context,   //  NOLINT
    RecoResultConstIter begin, RecoResultConstIter end) {     //  NOLINT
  // 互动交叉特征
  // read
  auto* author_id_accessor = context->GetItemAttrAccessor("author_id");
  auto* hetu_tag_accessor = context->GetItemAttrAccessor("photo_hetu_tag_info__hetu_level_one");
  // write
  auto* sim_interact_tag_cnt_1d_accessor = context->GetItemAttrAccessor("sim_interact_tag_cnt_1d");
  auto* sim_interact_tag_cnt_3d_accessor = context->GetItemAttrAccessor("sim_interact_tag_cnt_3d");
  auto* sim_interact_tag_cnt_7d_accessor = context->GetItemAttrAccessor("sim_interact_tag_cnt_7d");
  auto* sim_interact_tag_cnt_14d_accessor = context->GetItemAttrAccessor("sim_interact_tag_cnt_14d");
  auto* sim_interact_tag_cnt_30d_accessor = context->GetItemAttrAccessor("sim_interact_tag_cnt_30d");
  auto* sim_interact_tag_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_interact_tag_last_lag_30d");
  auto* sim_short_view_tag_cnt_1d_accessor = context->GetItemAttrAccessor("sim_short_view_tag_cnt_1d");
  auto* sim_short_view_tag_cnt_3d_accessor = context->GetItemAttrAccessor("sim_short_view_tag_cnt_3d");
  auto* sim_short_view_tag_cnt_7d_accessor = context->GetItemAttrAccessor("sim_short_view_tag_cnt_7d");
  auto* sim_short_view_tag_cnt_14d_accessor = context->GetItemAttrAccessor("sim_short_view_tag_cnt_14d");
  auto* sim_short_view_tag_cnt_30d_accessor = context->GetItemAttrAccessor("sim_short_view_tag_cnt_30d");
  auto* sim_short_view_tag_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_tag_last_lag_30d");
  auto* sim_effective_view_tag_cnt_1d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_cnt_1d");
  auto* sim_effective_view_tag_play_1d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_play_1d");
  auto* sim_effective_view_tag_cnt_3d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_cnt_3d");
  auto* sim_effective_view_tag_play_3d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_play_3d");
  auto* sim_effective_view_tag_cnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_cnt_7d");
  auto* sim_effective_view_tag_play_7d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_play_7d");
  auto* sim_effective_view_tag_cnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_cnt_14d");
  auto* sim_effective_view_tag_play_14d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_play_14d");
  auto* sim_effective_view_tag_cnt_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_cnt_30d");
  auto* sim_effective_view_tag_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_last_lag_30d");
  auto* sim_effective_view_tag_play_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_tag_play_30d");
  auto* sim_long_view_tag_cnt_1d_accessor = context->GetItemAttrAccessor("sim_long_view_tag_cnt_1d");
  auto* sim_long_view_tag_cnt_3d_accessor = context->GetItemAttrAccessor("sim_long_view_tag_cnt_3d");
  auto* sim_long_view_tag_cnt_7d_accessor = context->GetItemAttrAccessor("sim_long_view_tag_cnt_7d");
  auto* sim_long_view_tag_cnt_14d_accessor = context->GetItemAttrAccessor("sim_long_view_tag_cnt_14d");
  auto* sim_long_view_tag_cnt_30d_accessor = context->GetItemAttrAccessor("sim_long_view_tag_cnt_30d");
  auto* sim_long_view_tag_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_tag_last_lag_30d");
  auto* sim_negative_tag_cnt_1d_accessor = context->GetItemAttrAccessor("sim_negative_tag_cnt_1d");
  auto* sim_negative_tag_cnt_3d_accessor = context->GetItemAttrAccessor("sim_negative_tag_cnt_3d");
  auto* sim_negative_tag_cnt_7d_accessor = context->GetItemAttrAccessor("sim_negative_tag_cnt_7d");
  auto* sim_negative_tag_cnt_14d_accessor = context->GetItemAttrAccessor("sim_negative_tag_cnt_14d");
  auto* sim_negative_tag_cnt_30d_accessor = context->GetItemAttrAccessor("sim_negative_tag_cnt_30d");
  auto* sim_negative_tag_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_negative_tag_last_lag_30d");
  auto* sim_interact_author_cnt_1d_accessor = context->GetItemAttrAccessor("sim_interact_author_cnt_1d");
  auto* sim_interact_author_cnt_3d_accessor = context->GetItemAttrAccessor("sim_interact_author_cnt_3d");
  auto* sim_interact_author_cnt_7d_accessor = context->GetItemAttrAccessor("sim_interact_author_cnt_7d");
  auto* sim_interact_author_cnt_14d_accessor = context->GetItemAttrAccessor("sim_interact_author_cnt_14d");
  auto* sim_interact_author_cnt_30d_accessor = context->GetItemAttrAccessor("sim_interact_author_cnt_30d");
  auto* sim_interact_author_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_interact_author_last_lag_30d");
  auto* sim_short_view_author_cnt_1d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_cnt_1d");
  auto* sim_short_view_author_cnt_3d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_cnt_3d");
  auto* sim_short_view_author_cnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_cnt_7d");
  auto* sim_short_view_author_cnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_cnt_14d");
  auto* sim_short_view_author_cnt_30d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_cnt_30d");
  auto* sim_short_view_author_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_short_view_author_last_lag_30d");
  auto* sim_effective_view_author_cnt_1d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_cnt_1d");
  auto* sim_effective_view_author_play_1d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_play_1d");
  auto* sim_effective_view_author_cnt_3d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_cnt_3d");
  auto* sim_effective_view_author_play_3d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_play_3d");
  auto* sim_effective_view_author_cnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_cnt_7d");
  auto* sim_effective_view_author_play_7d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_play_7d");
  auto* sim_effective_view_author_cnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_cnt_14d");
  auto* sim_effective_view_author_play_14d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_play_14d");
  auto* sim_effective_view_author_cnt_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_cnt_30d");
  auto* sim_effective_view_author_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_last_lag_30d");
  auto* sim_effective_view_author_play_30d_accessor = context->GetItemAttrAccessor(
    "sim_effective_view_author_play_30d");
  auto* sim_long_view_author_cnt_1d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_cnt_1d");
  auto* sim_long_view_author_cnt_3d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_cnt_3d");
  auto* sim_long_view_author_cnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_cnt_7d");
  auto* sim_long_view_author_cnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_cnt_14d");
  auto* sim_long_view_author_cnt_30d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_cnt_30d");
  auto* sim_long_view_author_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_long_view_author_last_lag_30d");
  auto* sim_negative_author_cnt_1d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_cnt_1d");
  auto* sim_negative_author_cnt_3d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_cnt_3d");
  auto* sim_negative_author_cnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_cnt_7d");
  auto* sim_negative_author_cnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_cnt_14d");
  auto* sim_negative_author_cnt_30d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_cnt_30d");
  auto* sim_negative_author_last_lag_30d_accessor = context->GetItemAttrAccessor(
    "sim_negative_author_last_lag_30d");
  auto* sim_user_author_playcnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playcnt_14d");
  auto* sim_user_author_playcnt_14d_v2_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playcnt_14d_v2");
  auto* sim_user_author_playtime_14d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playtime_14d");
  auto* sim_user_author_playtime_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playtime_7d");
  auto* sim_user_author_playtime_7d_v2_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playtime_7d_v2");
  auto* sim_user_author_playcnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playcnt_7d");
  auto* sim_user_author_effectcnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_effectcnt_14d");
  auto* sim_user_author_effectcnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_effectcnt_7d");
  auto* sim_user_author_likecnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_likecnt_14d");
  auto* sim_user_author_likecnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_likecnt_7d");
  auto* sim_user_author_commentcnt_14d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_commentcnt_14d");
  auto* sim_user_author_commentcnt_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_commentcnt_7d");
  auto* sim_user_author_playtime_list_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_playtime_list_7d");
  auto* sim_user_author_duration_list_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_duration_list_7d");
  auto* sim_user_author_follow_playtime_list_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_follow_playtime_list_7d");
  auto* sim_user_author_follow_duration_list_7d_accessor = context->GetItemAttrAccessor(
    "sim_user_author_follow_duration_list_7d");

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto author_id = context->GetIntItemAttr(result, author_id_accessor).value_or(0);
    int64 hetu_tag = 0;
    auto hetu_tag_ptr = context->GetIntListItemAttr(result, hetu_tag_accessor);
    if (hetu_tag_ptr && hetu_tag_ptr->size() > 0) {
      hetu_tag = (*hetu_tag_ptr)[0];
    }
    int sim_interact_tag_cnt_1d = 0;
    if (sim_interact_tag_info_map_1d_.find(hetu_tag) != sim_interact_tag_info_map_1d_.end()) {
      sim_interact_tag_cnt_1d = sim_interact_tag_info_map_1d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_tag_cnt_1d_accessor, sim_interact_tag_cnt_1d);

    int sim_interact_tag_cnt_3d = 0;
    if (sim_interact_tag_info_map_3d_.find(hetu_tag) != sim_interact_tag_info_map_3d_.end()) {
      sim_interact_tag_cnt_3d = sim_interact_tag_info_map_3d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_tag_cnt_3d_accessor, sim_interact_tag_cnt_3d);

    int sim_interact_tag_cnt_7d = 0;
    if (sim_interact_tag_info_map_7d_.find(hetu_tag) != sim_interact_tag_info_map_7d_.end()) {
      sim_interact_tag_cnt_7d = sim_interact_tag_info_map_7d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_tag_cnt_7d_accessor, sim_interact_tag_cnt_7d);

    int sim_interact_tag_cnt_14d = 0;
    if (sim_interact_tag_info_map_14d_.find(hetu_tag) != sim_interact_tag_info_map_14d_.end()) {
      sim_interact_tag_info_map_14d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_tag_cnt_14d_accessor, sim_interact_tag_cnt_14d);

    int sim_interact_tag_cnt_30d = 0;
    int sim_interact_tag_last_lag_30d = -1;
    if (sim_interact_tag_info_map_30d_.find(hetu_tag) != sim_interact_tag_info_map_30d_.end()) {
      sim_interact_tag_cnt_30d = sim_interact_tag_info_map_30d_[hetu_tag]["cnt"];
      sim_interact_tag_last_lag_30d = sim_interact_tag_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_interact_tag_cnt_30d_accessor, sim_interact_tag_cnt_30d);
    context->SetIntItemAttr(result, sim_interact_tag_last_lag_30d_accessor, sim_interact_tag_last_lag_30d);

    int sim_short_view_tag_cnt_1d = 0;
    if (sim_short_view_tag_info_map_1d_.find(hetu_tag) != sim_short_view_tag_info_map_1d_.end()) {
      sim_short_view_tag_cnt_1d = sim_short_view_tag_info_map_1d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_tag_cnt_1d_accessor, sim_short_view_tag_cnt_1d);

    int sim_short_view_tag_cnt_3d = 0;
    if (sim_short_view_tag_info_map_3d_.find(hetu_tag) != sim_short_view_tag_info_map_3d_.end()) {
      sim_short_view_tag_cnt_3d = sim_short_view_tag_info_map_3d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_tag_cnt_3d_accessor, sim_short_view_tag_cnt_3d);

    int sim_short_view_tag_cnt_7d = 0;
    if (sim_short_view_tag_info_map_7d_.find(hetu_tag) != sim_short_view_tag_info_map_7d_.end()) {
      sim_short_view_tag_cnt_7d = sim_short_view_tag_info_map_7d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_tag_cnt_7d_accessor, sim_short_view_tag_cnt_7d);

    int sim_short_view_tag_cnt_14d = 0;
    if (sim_short_view_tag_info_map_14d_.find(hetu_tag) != sim_short_view_tag_info_map_14d_.end()) {
      sim_short_view_tag_cnt_14d = sim_short_view_tag_info_map_14d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_tag_cnt_14d_accessor, sim_short_view_tag_cnt_14d);

    int sim_short_view_tag_cnt_30d = 0;
    int sim_short_view_tag_last_lag_30d = -1;
    if (sim_short_view_tag_info_map_30d_.find(hetu_tag) != sim_short_view_tag_info_map_30d_.end()) {
      sim_short_view_tag_cnt_30d = sim_short_view_tag_info_map_30d_[hetu_tag]["cnt"];
      sim_short_view_tag_last_lag_30d = sim_short_view_tag_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_short_view_tag_cnt_30d_accessor, sim_short_view_tag_cnt_30d);
    context->SetIntItemAttr(result, sim_short_view_tag_last_lag_30d_accessor,
      sim_short_view_tag_last_lag_30d);

    int sim_effective_view_tag_cnt_1d = 0;
    int sim_effective_view_tag_play_1d = 0;
    if (sim_effective_view_tag_info_map_1d_.find(hetu_tag) != sim_effective_view_tag_info_map_1d_.end()) {
      sim_effective_view_tag_cnt_1d = sim_effective_view_tag_info_map_1d_[hetu_tag]["cnt"];
      sim_effective_view_tag_play_1d = sim_effective_view_tag_info_map_1d_[hetu_tag]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_tag_cnt_1d_accessor, sim_effective_view_tag_cnt_1d);
    context->SetIntItemAttr(result, sim_effective_view_tag_play_1d_accessor, sim_effective_view_tag_play_1d);

    int sim_effective_view_tag_cnt_3d = 0;
    int sim_effective_view_tag_play_3d = 0;
    if (sim_effective_view_tag_info_map_3d_.find(hetu_tag) != sim_effective_view_tag_info_map_3d_.end()) {
      sim_effective_view_tag_cnt_3d = sim_effective_view_tag_info_map_3d_[hetu_tag]["cnt"];
      sim_effective_view_tag_play_3d = sim_effective_view_tag_info_map_3d_[hetu_tag]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_tag_cnt_3d_accessor, sim_effective_view_tag_cnt_3d);
    context->SetIntItemAttr(result, sim_effective_view_tag_play_3d_accessor, sim_effective_view_tag_play_3d);

    int sim_effective_view_tag_cnt_7d = 0;
    int sim_effective_view_tag_play_7d = 0;
    if (sim_effective_view_tag_info_map_7d_.find(hetu_tag) != sim_effective_view_tag_info_map_7d_.end()) {
      sim_effective_view_tag_cnt_7d = sim_effective_view_tag_info_map_7d_[hetu_tag]["cnt"];
      sim_effective_view_tag_play_7d = sim_effective_view_tag_info_map_7d_[hetu_tag]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_tag_cnt_7d_accessor, sim_effective_view_tag_cnt_7d);
    context->SetIntItemAttr(result, sim_effective_view_tag_play_7d_accessor, sim_effective_view_tag_play_7d);

    int sim_effective_view_tag_cnt_14d = 0;
    int sim_effective_view_tag_play_14d = 0;
    if (sim_effective_view_tag_info_map_14d_.find(hetu_tag) != sim_effective_view_tag_info_map_14d_.end()) {
      sim_effective_view_tag_cnt_14d = sim_effective_view_tag_info_map_14d_[hetu_tag]["cnt"];
      sim_effective_view_tag_play_14d = sim_effective_view_tag_info_map_14d_[hetu_tag]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_tag_cnt_14d_accessor,
      sim_effective_view_tag_cnt_14d);
    context->SetIntItemAttr(result, sim_effective_view_tag_play_14d_accessor,
      sim_effective_view_tag_play_14d);

    int sim_effective_view_tag_cnt_30d = 0;
    int sim_effective_view_tag_last_lag_30d = -1;
    int sim_effective_view_tag_play_30d = 0;
    if (sim_effective_view_tag_info_map_30d_.find(hetu_tag) != sim_effective_view_tag_info_map_30d_.end()) {
      sim_effective_view_tag_cnt_30d = sim_effective_view_tag_info_map_30d_[hetu_tag]["cnt"];
      sim_effective_view_tag_last_lag_30d = sim_effective_view_tag_info_map_30d_[hetu_tag]["lag"];
      sim_effective_view_tag_play_30d = sim_effective_view_tag_info_map_30d_[hetu_tag]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_tag_cnt_30d_accessor,
      sim_effective_view_tag_cnt_30d);
    context->SetIntItemAttr(result, sim_effective_view_tag_last_lag_30d_accessor,
      sim_effective_view_tag_last_lag_30d);
    context->SetIntItemAttr(result, sim_effective_view_tag_play_30d_accessor,
      sim_effective_view_tag_play_30d);

    int sim_long_view_tag_cnt_1d = 0;
    if (sim_long_view_tag_info_map_1d_.find(hetu_tag) != sim_long_view_tag_info_map_1d_.end()) {
      sim_long_view_tag_cnt_1d = sim_long_view_tag_info_map_1d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_tag_cnt_1d_accessor, sim_long_view_tag_cnt_1d);

    int sim_long_view_tag_cnt_3d = 0;
    if (sim_long_view_tag_info_map_3d_.find(hetu_tag) != sim_long_view_tag_info_map_3d_.end()) {
      sim_long_view_tag_cnt_3d = sim_long_view_tag_info_map_3d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_tag_cnt_3d_accessor, sim_long_view_tag_cnt_3d);

    int sim_long_view_tag_cnt_7d = 0;
    if (sim_long_view_tag_info_map_7d_.find(hetu_tag) != sim_long_view_tag_info_map_7d_.end()) {
      sim_long_view_tag_cnt_7d = sim_long_view_tag_info_map_7d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_tag_cnt_7d_accessor, sim_long_view_tag_cnt_7d);

    int sim_long_view_tag_cnt_14d = 0;
    if (sim_long_view_tag_info_map_14d_.find(hetu_tag) != sim_long_view_tag_info_map_14d_.end()) {
      sim_long_view_tag_cnt_14d = sim_long_view_tag_info_map_14d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_tag_cnt_14d_accessor, sim_long_view_tag_cnt_14d);

    int sim_long_view_tag_cnt_30d = 0;
    int sim_long_view_tag_last_lag_30d = -1;
    if (sim_long_view_tag_info_map_30d_.find(hetu_tag) != sim_long_view_tag_info_map_30d_.end()) {
      sim_long_view_tag_cnt_30d = sim_long_view_tag_info_map_30d_[hetu_tag]["cnt"];
      sim_long_view_tag_last_lag_30d = sim_long_view_tag_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_long_view_tag_cnt_30d_accessor, sim_long_view_tag_cnt_30d);
    context->SetIntItemAttr(result, sim_long_view_tag_last_lag_30d_accessor, sim_long_view_tag_last_lag_30d);

    int sim_negative_tag_cnt_1d = 0;
    if (sim_negative_tag_info_map_1d_.find(hetu_tag) != sim_negative_tag_info_map_1d_.end()) {
      sim_negative_tag_cnt_1d = sim_negative_tag_info_map_1d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_tag_cnt_1d_accessor, sim_negative_tag_cnt_1d);

    int sim_negative_tag_cnt_3d = 0;
    if (sim_negative_tag_info_map_3d_.find(hetu_tag) != sim_negative_tag_info_map_3d_.end()) {
      sim_negative_tag_cnt_3d = sim_negative_tag_info_map_3d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_tag_cnt_3d_accessor, sim_negative_tag_cnt_3d);

    int sim_negative_tag_cnt_7d = 0;
    if (sim_negative_tag_info_map_7d_.find(hetu_tag) != sim_negative_tag_info_map_7d_.end()) {
      sim_negative_tag_cnt_7d = sim_negative_tag_info_map_7d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_tag_cnt_7d_accessor, sim_negative_tag_cnt_7d);

    int sim_negative_tag_cnt_14d = 0;
    if (sim_negative_tag_info_map_14d_.find(hetu_tag) != sim_negative_tag_info_map_14d_.end()) {
      sim_negative_tag_cnt_14d = sim_negative_tag_info_map_14d_[hetu_tag]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_tag_cnt_14d_accessor, sim_negative_tag_cnt_14d);

    int sim_negative_tag_cnt_30d = 0;
    int sim_negative_tag_last_lag_30d = -1;
    if (sim_negative_tag_info_map_30d_.find(hetu_tag) != sim_negative_tag_info_map_30d_.end()) {
      sim_negative_tag_cnt_30d = sim_negative_tag_info_map_30d_[hetu_tag]["cnt"];
      sim_negative_tag_last_lag_30d = sim_negative_tag_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_negative_tag_cnt_30d_accessor, sim_negative_tag_cnt_30d);
    context->SetIntItemAttr(result, sim_negative_tag_last_lag_30d_accessor, sim_negative_tag_last_lag_30d);

    int sim_interact_author_cnt_1d = 0;
    if (sim_interact_author_info_map_1d_.find(author_id) != sim_interact_author_info_map_1d_.end()) {
      sim_interact_author_cnt_1d = sim_interact_author_info_map_1d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_author_cnt_1d_accessor, sim_interact_author_cnt_1d);

    int sim_interact_author_cnt_3d = 0;
    if (sim_interact_author_info_map_3d_.find(author_id) != sim_interact_author_info_map_3d_.end()) {
      sim_interact_author_cnt_3d = sim_interact_author_info_map_3d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_author_cnt_3d_accessor, sim_interact_author_cnt_3d);

    int sim_interact_author_cnt_7d = 0;
    if (sim_interact_author_info_map_7d_.find(author_id) != sim_interact_author_info_map_7d_.end()) {
      sim_interact_author_cnt_7d = sim_interact_author_info_map_7d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_author_cnt_7d_accessor, sim_interact_author_cnt_7d);

    int sim_interact_author_cnt_14d = 0;
    if (sim_interact_author_info_map_14d_.find(author_id) != sim_interact_author_info_map_14d_.end()) {
      sim_interact_author_cnt_14d = sim_interact_author_info_map_14d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_interact_author_cnt_14d_accessor, sim_interact_author_cnt_14d);

    int sim_interact_author_cnt_30d = 0;
    int sim_interact_author_last_lag_30d = -1;
    if (sim_interact_author_info_map_30d_.find(author_id) != sim_interact_author_info_map_30d_.end()) {
      sim_interact_author_cnt_30d = sim_interact_author_info_map_30d_[author_id]["cnt"];
      sim_interact_author_last_lag_30d = sim_interact_author_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_interact_author_cnt_30d_accessor, sim_interact_author_cnt_30d);
    context->SetIntItemAttr(result, sim_interact_author_last_lag_30d_accessor,
      sim_interact_author_last_lag_30d);

    int sim_short_view_author_cnt_1d = 0;
    if (sim_short_view_author_info_map_1d_.find(author_id) != sim_short_view_author_info_map_1d_.end()) {
      sim_short_view_author_cnt_1d = sim_short_view_author_info_map_1d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_author_cnt_1d_accessor, sim_short_view_author_cnt_1d);

    int sim_short_view_author_cnt_3d = 0;
    if (sim_short_view_author_info_map_3d_.find(author_id) != sim_short_view_author_info_map_3d_.end()) {
      sim_short_view_author_cnt_3d = sim_short_view_author_info_map_3d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_author_cnt_3d_accessor, sim_short_view_author_cnt_3d);

    int sim_short_view_author_cnt_7d = 0;
    if (sim_short_view_author_info_map_7d_.find(author_id) != sim_short_view_author_info_map_7d_.end()) {
      sim_short_view_author_cnt_7d = sim_short_view_author_info_map_7d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_author_cnt_7d_accessor, sim_short_view_author_cnt_7d);

    int sim_short_view_author_cnt_14d = 0;
    if (sim_short_view_author_info_map_14d_.find(author_id) != sim_short_view_author_info_map_14d_.end()) {
      sim_short_view_author_cnt_14d = sim_short_view_author_info_map_14d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_short_view_author_cnt_14d_accessor, sim_short_view_author_cnt_14d);

    int sim_short_view_author_cnt_30d = 0;
    int sim_short_view_author_last_lag_30d = -1;
    if (sim_short_view_author_info_map_30d_.find(author_id) != sim_short_view_author_info_map_30d_.end()) {
      sim_short_view_author_cnt_30d = sim_short_view_author_info_map_30d_[author_id]["cnt"];
      sim_short_view_author_last_lag_30d = sim_short_view_author_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_short_view_author_cnt_30d_accessor,
      sim_short_view_author_cnt_30d);
    context->SetIntItemAttr(result, sim_short_view_author_last_lag_30d_accessor,
      sim_short_view_author_last_lag_30d);

    int sim_effective_view_author_cnt_1d = 0;
    int sim_effective_view_author_play_1d = 0;
    if (sim_effective_view_author_info_map_1d_.find(author_id)
      != sim_effective_view_author_info_map_1d_.end()) {
      sim_effective_view_author_cnt_1d = sim_effective_view_author_info_map_1d_[author_id]["cnt"];
      sim_effective_view_author_play_1d = sim_effective_view_author_info_map_1d_[author_id]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_author_cnt_1d_accessor,
      sim_effective_view_author_cnt_1d);
    context->SetIntItemAttr(result, sim_effective_view_author_play_1d_accessor,
      sim_effective_view_author_play_1d);

    int sim_effective_view_author_cnt_3d = 0;
    int sim_effective_view_author_play_3d = 0;
    if (sim_effective_view_author_info_map_3d_.find(author_id)
        != sim_effective_view_author_info_map_3d_.end()) {
      sim_effective_view_author_cnt_3d = sim_effective_view_author_info_map_3d_[author_id]["cnt"];
      sim_effective_view_author_play_3d = sim_effective_view_author_info_map_3d_[author_id]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_author_cnt_3d_accessor,
      sim_effective_view_author_cnt_3d);
    context->SetIntItemAttr(result, sim_effective_view_author_play_3d_accessor,
      sim_effective_view_author_play_3d);

    int sim_effective_view_author_cnt_7d = 0;
    int sim_effective_view_author_play_7d = 0;
    if (sim_effective_view_author_info_map_7d_.find(author_id)
       != sim_effective_view_author_info_map_7d_.end()) {
      sim_effective_view_author_cnt_7d = sim_effective_view_author_info_map_7d_[author_id]["cnt"];
      sim_effective_view_author_play_7d = sim_effective_view_author_info_map_7d_[author_id]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_author_cnt_7d_accessor,
      sim_effective_view_author_cnt_7d);
    context->SetIntItemAttr(result, sim_effective_view_author_play_7d_accessor,
      sim_effective_view_author_play_7d);

    int sim_effective_view_author_cnt_14d = 0;
    int sim_effective_view_author_play_14d = 0;
    if (sim_effective_view_author_info_map_14d_.find(author_id)
        != sim_effective_view_author_info_map_14d_.end()) {
      sim_effective_view_author_cnt_14d = sim_effective_view_author_info_map_14d_[author_id]["cnt"];
      sim_effective_view_author_play_14d = sim_effective_view_author_info_map_14d_[author_id]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_author_cnt_14d_accessor,
      sim_effective_view_author_cnt_14d);
    context->SetIntItemAttr(result, sim_effective_view_author_play_14d_accessor,
      sim_effective_view_author_play_14d);

    int sim_effective_view_author_cnt_30d = 0;
    int sim_effective_view_author_last_lag_30d = -1;
    int sim_effective_view_author_play_30d = 0;
    if (sim_effective_view_author_info_map_30d_.find(author_id)
        != sim_effective_view_author_info_map_30d_.end()) {
      sim_effective_view_author_cnt_30d = sim_effective_view_author_info_map_30d_[author_id]["cnt"];
      sim_effective_view_author_last_lag_30d = sim_effective_view_author_info_map_30d_[hetu_tag]["lag"];
      sim_effective_view_author_play_30d = sim_effective_view_author_info_map_30d_[author_id]["play"];
    }
    context->SetIntItemAttr(result, sim_effective_view_author_cnt_30d_accessor,
      sim_effective_view_author_cnt_30d);
    context->SetIntItemAttr(result, sim_effective_view_author_last_lag_30d_accessor,
      sim_effective_view_author_last_lag_30d);
    context->SetIntItemAttr(result, sim_effective_view_author_play_30d_accessor,
      sim_effective_view_author_play_30d);

    int sim_long_view_author_cnt_1d = 0;
    if (sim_long_view_author_info_map_1d_.find(author_id) != sim_long_view_author_info_map_1d_.end()) {
      sim_long_view_author_cnt_1d = sim_long_view_author_info_map_1d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_author_cnt_1d_accessor, sim_long_view_author_cnt_1d);

    int sim_long_view_author_cnt_3d = 0;
    if (sim_long_view_author_info_map_3d_.find(author_id) != sim_long_view_author_info_map_3d_.end()) {
      sim_long_view_author_cnt_3d = sim_long_view_author_info_map_3d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_author_cnt_3d_accessor, sim_long_view_author_cnt_3d);

    int sim_long_view_author_cnt_7d = 0;
    if (sim_long_view_author_info_map_7d_.find(author_id) != sim_long_view_author_info_map_7d_.end()) {
      sim_long_view_author_cnt_7d = sim_long_view_author_info_map_7d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_author_cnt_7d_accessor, sim_long_view_author_cnt_7d);

    int sim_long_view_author_cnt_14d = 0;
    if (sim_long_view_author_info_map_14d_.find(author_id) != sim_long_view_author_info_map_14d_.end()) {
      sim_long_view_author_cnt_14d = sim_long_view_author_info_map_14d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_long_view_author_cnt_14d_accessor, sim_long_view_author_cnt_14d);

    int sim_long_view_author_cnt_30d = 0;
    int sim_long_view_author_last_lag_30d = -1;
    if (sim_long_view_author_info_map_30d_.find(author_id) != sim_long_view_author_info_map_30d_.end()) {
      sim_long_view_author_cnt_30d = sim_long_view_author_info_map_30d_[author_id]["cnt"];
      sim_long_view_author_last_lag_30d = sim_long_view_author_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_long_view_author_cnt_30d_accessor, sim_long_view_author_cnt_30d);
    context->SetIntItemAttr(result, sim_long_view_author_last_lag_30d_accessor,
      sim_long_view_author_last_lag_30d);

    int sim_negative_author_cnt_1d = 0;
    if (sim_negative_author_info_map_1d_.find(author_id) != sim_negative_author_info_map_1d_.end()) {
      sim_negative_author_cnt_1d = sim_negative_author_info_map_1d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_author_cnt_1d_accessor, sim_negative_author_cnt_1d);

    int sim_negative_author_cnt_3d = 0;
    if (sim_negative_author_info_map_3d_.find(author_id) != sim_negative_author_info_map_3d_.end()) {
      sim_negative_author_cnt_3d = sim_negative_author_info_map_3d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_author_cnt_3d_accessor, sim_negative_author_cnt_3d);

    int sim_negative_author_cnt_7d = 0;
    if (sim_negative_author_info_map_7d_.find(author_id) != sim_negative_author_info_map_7d_.end()) {
      sim_negative_author_cnt_7d = sim_negative_author_info_map_7d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_author_cnt_7d_accessor, sim_negative_author_cnt_7d);

    int sim_negative_author_cnt_14d = 0;
    if (sim_negative_author_info_map_14d_.find(author_id) != sim_negative_author_info_map_14d_.end()) {
      sim_negative_author_cnt_14d = sim_negative_author_info_map_14d_[author_id]["cnt"];
    }
    context->SetIntItemAttr(result, sim_negative_author_cnt_14d_accessor, sim_negative_author_cnt_14d);

    int sim_negative_author_cnt_30d = 0;
    int sim_negative_author_last_lag_30d = -1;
    if (sim_negative_author_info_map_30d_.find(author_id) != sim_negative_author_info_map_30d_.end()) {
      sim_negative_author_cnt_30d = sim_negative_author_info_map_30d_[author_id]["cnt"];
      sim_negative_author_last_lag_30d = sim_negative_author_info_map_30d_[hetu_tag]["lag"];
    }
    context->SetIntItemAttr(result, sim_negative_author_cnt_30d_accessor, sim_negative_author_cnt_30d);
    context->SetIntItemAttr(result, sim_negative_author_last_lag_30d_accessor,
      sim_negative_author_last_lag_30d);

    int sim_user_author_playcnt_14d = 0;
    int sim_user_author_playcnt_14d_v2 = 0;
    int sim_user_author_playtime_14d = 0;
    int sim_user_author_playcnt_7d = 0;
    int sim_user_author_playcnt_7d_v2 = 0;
    int sim_user_author_playtime_7d = 0;
    int sim_user_author_effectcnt_14d = 0;
    int sim_user_author_effectcnt_7d = 0;
    int sim_user_author_likecnt_14d = 0;
    int sim_user_author_likecnt_7d = 0;
    int sim_user_author_commentcnt_14d = 0;
    int sim_user_author_commentcnt_7d = 0;
    if (user_recent_14D_playcnt_follow_author_map_.find(author_id)
      != user_recent_14D_playcnt_follow_author_map_.end()) {
      sim_user_author_playcnt_14d = user_recent_14D_playcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playcnt_14d_accessor, sim_user_author_playcnt_14d);
    }
    if (user_recent_14D_playcnt_v2_follow_author_map_.find(author_id)
      != user_recent_14D_playcnt_v2_follow_author_map_.end()) {
      sim_user_author_playcnt_14d_v2 = user_recent_14D_playcnt_v2_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playcnt_14d_v2_accessor,
        sim_user_author_playcnt_14d_v2);
    }
    if (user_recent_14D_playtime_follow_author_map_.find(author_id)
      != user_recent_14D_playtime_follow_author_map_.end()) {
      sim_user_author_playtime_14d = user_recent_14D_playtime_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playtime_14d_accessor,
        sim_user_author_playtime_14d);
    }
    if (user_recent_7D_playcnt_follow_author_map_.find(author_id)
      != user_recent_7D_playcnt_follow_author_map_.end()) {
      sim_user_author_playcnt_7d = user_recent_7D_playcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playtime_7d_accessor,
        sim_user_author_playcnt_7d);
      // 由于统计逻辑有误，这里统计的是播放次数的一种 boost 累加值，真正播放时长逻辑看 v2
    }
    if (user_recent_7D_playtime_follow_author_map_.find(author_id)
      != user_recent_7D_playtime_follow_author_map_.end()) {
      sim_user_author_playtime_7d = user_recent_7D_playtime_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playtime_7d_v2_accessor,
        sim_user_author_playtime_7d);
      // 正确的播放时长逻辑
    }
    if (user_recent_7D_playcnt_follow_author_map_v2_.find(author_id)
      != user_recent_7D_playcnt_follow_author_map_v2_.end()) {
      sim_user_author_playcnt_7d_v2 = user_recent_7D_playcnt_follow_author_map_v2_[author_id];
      context->SetIntItemAttr(result, sim_user_author_playcnt_7d_accessor,
        sim_user_author_playcnt_7d_v2);
      // 正确的 vv 统计逻辑
    }
    if (user_recent_14D_effectcnt_follow_author_map_.find(author_id)
      != user_recent_14D_effectcnt_follow_author_map_.end()) {
      sim_user_author_effectcnt_14d = user_recent_14D_effectcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_effectcnt_14d_accessor,
        sim_user_author_effectcnt_14d);
    }
    if (user_recent_7D_effectcnt_follow_author_map_.find(author_id)
      != user_recent_7D_effectcnt_follow_author_map_.end()) {
      sim_user_author_effectcnt_7d = user_recent_7D_effectcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_effectcnt_7d_accessor,
        sim_user_author_effectcnt_7d);
    }
    if (user_recent_14D_likecnt_follow_author_map_.find(author_id)
      != user_recent_14D_likecnt_follow_author_map_.end()) {
      sim_user_author_likecnt_14d = user_recent_14D_likecnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_likecnt_14d_accessor,
        sim_user_author_likecnt_14d);
    }
    if (user_recent_7D_likecnt_follow_author_map_.find(author_id)
      != user_recent_7D_likecnt_follow_author_map_.end()) {
      sim_user_author_likecnt_7d = user_recent_7D_likecnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_likecnt_7d_accessor,
        sim_user_author_likecnt_7d);
    }
    if (user_recent_14D_commentcnt_follow_author_map_.find(author_id)
      != user_recent_14D_commentcnt_follow_author_map_.end()) {
      sim_user_author_commentcnt_14d = user_recent_14D_commentcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_commentcnt_14d_accessor,
        sim_user_author_commentcnt_14d);
    }
    if (user_recent_7D_commentcnt_follow_author_map_.find(author_id)
      != user_recent_7D_commentcnt_follow_author_map_.end()) {
      sim_user_author_commentcnt_7d = user_recent_7D_commentcnt_follow_author_map_[author_id];
      context->SetIntItemAttr(result, sim_user_author_commentcnt_7d_accessor,
        sim_user_author_commentcnt_7d);
    }

    // UA 粒度历史序列聚合，落盘用作训练 label，无需传给打分请求
    // UA 序列截断到 50，item 侧序列特征 注意耗时问题
    if (sim_user_author_playtime_list_7d_map_.find(author_id)
        != sim_user_author_playtime_list_7d_map_.end()) {
      std::vector<int64> sim_user_author_playtime_list_7d = {};
      context->SetIntListItemAttr(result, sim_user_author_playtime_list_7d_accessor,
        std::move(sim_user_author_playtime_list_7d));
    }
    if (sim_user_author_duration_list_7d_map_.find(author_id)
        != sim_user_author_duration_list_7d_map_.end()) {
      std::vector<int64> sim_user_author_duration_list_7d = {};
      context->SetIntListItemAttr(result, sim_user_author_duration_list_7d_accessor,
        std::move(sim_user_author_duration_list_7d));
    }
    if (sim_user_author_follow_playtime_list_7d_map_.find(author_id)
        != sim_user_author_follow_playtime_list_7d_map_.end()) {
      std::vector<int64> sim_user_author_follow_playtime_list_7d = {};
      context->SetIntListItemAttr(result, sim_user_author_follow_playtime_list_7d_accessor,
      std::move(sim_user_author_follow_playtime_list_7d));
    }
    if (sim_user_author_follow_duration_list_7d_map_.find(author_id)
      != sim_user_author_follow_duration_list_7d_map_.end()) {
      std::vector<int64> sim_user_author_follow_duration_list_7d = {};
      context->SetIntListItemAttr(result, sim_user_author_follow_duration_list_7d_accessor,
      std::move(sim_user_author_follow_duration_list_7d));
    }
  });  // NOLINT
}  // NOLINT

void FollowParsePhotoColossusEnricher::FillUserAttrSimInfo(MutableRecoContextInterface *context) {
  context->SetIntCommonAttr("uInteractPhotoCnt1D", sim_user_static_info_map_["interact"]["1d"]);
  context->SetIntCommonAttr("uInteractPhotoCnt3D", sim_user_static_info_map_["interact"]["3d"]);
  context->SetIntCommonAttr("uInteractPhotoCnt7D", sim_user_static_info_map_["interact"]["7d"]);
  context->SetIntCommonAttr("uInteractPhotoCnt14D", sim_user_static_info_map_["interact"]["14d"]);
  context->SetIntCommonAttr("uInteractPhotoCnt30D", sim_user_static_info_map_["interact"]["30d"]);

  context->SetIntCommonAttr("uShortViewPhotoCnt1D", sim_user_static_info_map_["short_view"]["1d"]);
  context->SetIntCommonAttr("uShortViewPhotoCnt3D", sim_user_static_info_map_["short_view"]["3d"]);
  context->SetIntCommonAttr("uShortViewPhotoCnt7D", sim_user_static_info_map_["short_view"]["7d"]);
  context->SetIntCommonAttr("uShortViewPhotoCnt14D", sim_user_static_info_map_["short_view"]["14d"]);
  context->SetIntCommonAttr("uShortViewPhotoCnt30D", sim_user_static_info_map_["short_view"]["30d"]);

  context->SetIntCommonAttr("uEffectiveViewPhotoCnt1D", sim_user_static_info_map_["effective_view"]["1d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoCnt3D", sim_user_static_info_map_["effective_view"]["3d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoCnt7D", sim_user_static_info_map_["effective_view"]["7d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoCnt14D", sim_user_static_info_map_["effective_view"]["14d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoCnt30D", sim_user_static_info_map_["effective_view"]["30d"]);

  context->SetIntCommonAttr("uEffectiveViewPhotoPlaytime1D",
    sim_user_static_info_map_["effective_play"]["1d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoPlaytime3D",
    sim_user_static_info_map_["effective_play"]["3d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoPlaytime7D",
    sim_user_static_info_map_["effective_play"]["7d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoPlaytime14D",
    sim_user_static_info_map_["effective_play"]["14d"]);
  context->SetIntCommonAttr("uEffectiveViewPhotoPlaytime30D",
    sim_user_static_info_map_["effective_play"]["30d"]);

  context->SetIntCommonAttr("uLongViewPhotoCnt1D", sim_user_static_info_map_["long_view"]["1d"]);
  context->SetIntCommonAttr("uLongViewPhotoCnt3D", sim_user_static_info_map_["long_view"]["3d"]);
  context->SetIntCommonAttr("uLongViewPhotoCnt7D", sim_user_static_info_map_["long_view"]["7d"]);
  context->SetIntCommonAttr("uLongViewPhotoCnt14D", sim_user_static_info_map_["long_view"]["14d"]);
  context->SetIntCommonAttr("uLongViewPhotoCnt30D", sim_user_static_info_map_["long_view"]["30d"]);

  context->SetIntCommonAttr("uNegativePhotoCnt1D", sim_user_static_info_map_["negative"]["1d"]);
  context->SetIntCommonAttr("uNegativePhotoCnt3D", sim_user_static_info_map_["negative"]["3d"]);
  context->SetIntCommonAttr("uNegativePhotoCnt7D", sim_user_static_info_map_["negative"]["7d"]);
  context->SetIntCommonAttr("uNegativePhotoCnt14D", sim_user_static_info_map_["negative"]["14d"]);
  context->SetIntCommonAttr("uNegativePhotoCnt30D", sim_user_static_info_map_["negative"]["30d"]);
  context->SetIntCommonAttr("follow_photo_play_time_28d", sim_user_follow_photo_play_sum_28d_);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowParsePhotoColossusEnricher, FollowParsePhotoColossusEnricher);

}  // namespace platform
}  // namespace ks
