#include "dragon/src/processor/ext/follow_leaf/enricher/follow_pasre_user_info_slide_enricher.h"

#include <algorithm>
#include <map>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {
void FollowParseUserInfoSlideEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  Clear();
  current_ts_ = base::GetTimestamp();
  const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(user_info_pb_);
  if (!message) {
    CL_LOG_ERROR("FollowParseUserInfoSlideEnricher", "attr_not_found:" + user_info_pb_)
        << "FollowParseUserInfoSlideEnricher user_info is null";
    return;
  }
  user_info_ = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
  if (!user_info_) {
    CL_LOG_ERROR("FollowParseUserInfoSlideEnricher", "attr_cast_fail:" + user_info_pb_)
        << "FollowParseUserInfoSlideEnricher user_info dynamic_cast failed";
    return;
  }
  if (context->GetIntCommonAttr("gen_kuiba_attr_in_common_leaf").value_or(0)) {
    // feasury from rpc
    ParseSamplelistUserAttr(context);
  }

  FillCommonAttr(context);
  if (context->GetIntCommonAttr("gen_kuiba_attr_in_common_leaf").value_or(0)) {
    FillCommonAttrInCommonLeaf(context);
    FillRelationColossusList(context);
    FillRelationActionList(context);
  }
}

void FollowParseUserInfoSlideEnricher::FillCommonAttr(MutableRecoContextInterface *context) {
    const ks::reco::RealTimeFollowRecoUserInfo &user_info = *user_info_;
  // 填充 llsid
  std::string llsid_string = context->GetRequestId();
  context->SetStringCommonAttr("llsid_str", llsid_string);

  int cursor = 0;
  if (user_info.has_slide_mode_context_info()) {
    cursor = user_info.slide_mode_context_info().cursor_index();
  }
  context->SetIntCommonAttr("cursor_index", cursor);
  int user_activity = user_info.has_user_activity() ? user_info.user_activity() : 0;
  context->SetIntCommonAttr("userActivity", user_activity);

  if (user_info.has_api_request_info()) {
    const auto& api_request_info = user_info.api_request_info();
    context->SetIntCommonAttr("refresh_type", static_cast<int>(api_request_info.refresh_type()));
    context->SetIntCommonAttr("uIsTopFresh", api_request_info.is_down_fresh() ? 0 : 1);
    context->SetIntCommonAttr("is_down_fresh", api_request_info.is_down_fresh() ? 1 : 0);
  }
  std::string merchant_user_type(context->GetStringCommonAttr("uBuyerEffectiveType").value_or(""));
  if (merchant_user_type == "") merchant_user_type = "U0-null";
  context->SetStringCommonAttr("merchant_user_type", merchant_user_type);
  std::map<std::string, double> merchant_user_type_2_index_map = {
      {"U0-null", 0}, {"U0-sleep", 0.5}, {"U1", 1}, {"U2", 2}, {"U3", 3}, {"U4", 4}, {"U4+", 4.5}};
  context->SetDoubleCommonAttr("ubuyEffectiveTypeValue", merchant_user_type_2_index_map[merchant_user_type]);
  ks::reco::RealTimeFollowRecoUserInfo::FollowPageProductType product_type =
      ks::reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX;
  if (user_info.has_follow_page_product_type()) {
    product_type = user_info.follow_page_product_type();
  }
  if (product_type == ks::reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_VIDEO_ENTRY_MIX
  || product_type == ks::reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_INNER_SLIDE_LIVE_ENTRY_MIX) {
    context->SetIntCommonAttr("channel_id", 2);
  } else if (product_type == ks::reco::RealTimeFollowRecoUserInfo::SINGLE_COLUMN_NEBULA) {
    context->SetIntCommonAttr("channel_id", 3);
  }
  context->SetIntCommonAttr("product_type", product_type);
  context->SetIntCommonAttr("uFollowPageProductType", product_type);
  request_type_ = static_cast<int>(product_type);
  context->SetIntCommonAttr("uIsBigUser", user_info.is_big_user());
  int64 paying_type = 0;
  bool get_paying_type_success = false;
  if (product_type == ks::reco::RealTimeFollowRecoUserInfo::SINGLE_COLUMN_NEBULA) {
    auto product_type_ptr = context->GetIntCommonAttr("uUserClassLivePayingTypeNebula");
    if (product_type_ptr) {
      paying_type = *product_type_ptr;
      get_paying_type_success = true;
    }
  } else {
    auto product_type_ptr = context->GetIntCommonAttr("uUserClassLivePayingTypeMainApp");
    if (product_type_ptr) {
      paying_type = *product_type_ptr;
      get_paying_type_success = true;
    }
  }
  bool is_important_gift_user = user_info.is_big_user() || (paying_type >= 3 && paying_type <= 6);
  context->SetIntCommonAttr("uIsImportantGiftUser", is_important_gift_user);
  context->SetIntCommonAttr("uIsGiftUser", (get_paying_type_success && paying_type <= 10) ? 1 : 0);
  folly::F14FastSet<uint64> browsed_photo_ids_in_session;
  folly::F14FastSet<uint64> browsed_live_ids_in_session;
  for (int i = 0; i < user_info.browsed_photo_ids_in_session_size(); ++i) {
    browsed_photo_ids_in_session.insert(user_info.browsed_photo_ids_in_session(i));
  }
  for (int i = 0; i < user_info.browsed_live_ids_in_session_size(); ++i) {
    browsed_live_ids_in_session.insert(user_info.browsed_live_ids_in_session(i));
  }
  uint64 slide_mode_entry_feed_id = 0;
  if (user_info.has_slide_mode_context_info()) {
    slide_mode_entry_feed_id = user_info.slide_mode_context_info().entry_feed_id();
  }
  int64 current_ts = current_ts_;
  std::vector<int64> recent_feed_list;
  for (int i = 0; i < user_info.recent_inner_session_follow_reco_result_size(); i++) {
    auto &reco_result = user_info.recent_inner_session_follow_reco_result(i);
    uint64 cur_entry_feed_id = 0;
    if (reco_result.has_slide_mode_context_info()) {
      cur_entry_feed_id = reco_result.slide_mode_context_info().entry_feed_id();
    }
    if (reco_result.create_time() <= 0 || cur_entry_feed_id != slide_mode_entry_feed_id ||
        (current_ts - reco_result.create_time() * 1000L) / base::Time::kMicrosecondsPerHour > 24) {
      continue;
    }
    for (int j = 0; j < reco_result.myfollow_reco_feed_size(); j++) {
      auto &feed = reco_result.myfollow_reco_feed(j);
      if (feed.type() == reco::RecoEnum::ITEM_TYPE_PHOTO &&
          browsed_photo_ids_in_session.find(feed.id()) != browsed_photo_ids_in_session.end()) {
        int64 item_key = Util::GenKeysign(2, feed.id());
        recent_feed_list.emplace_back(item_key);
      }
      if (feed.type() == reco::RecoEnum::ITEM_TYPE_LIVESTREAM &&
          browsed_live_ids_in_session.find(feed.id()) != browsed_live_ids_in_session.end()) {
        int64 item_key = Util::GenKeysign(3, feed.id());
        recent_feed_list.emplace_back(item_key);
      }
    }
  }
  std::vector<int64> recent_inner_session_feed_list;
  size_t max_recent_count = 60;
  if (recent_feed_list.size() <= max_recent_count) {
    recent_inner_session_feed_list.insert(recent_inner_session_feed_list.begin(), recent_feed_list.begin(),
                                          recent_feed_list.end());
  } else {
    recent_inner_session_feed_list.insert(recent_inner_session_feed_list.begin(),
                                          recent_feed_list.end() - max_recent_count, recent_feed_list.end());
  }
  std::vector<int64> session_pre_item_list;
  size_t diverse_session_author_window_size = 16;
  if (recent_inner_session_feed_list.size() <= diverse_session_author_window_size) {
    session_pre_item_list.insert(session_pre_item_list.begin(), recent_inner_session_feed_list.begin(),
                                 recent_inner_session_feed_list.end());
  } else {
    session_pre_item_list.insert(session_pre_item_list.begin(),
                                 recent_inner_session_feed_list.end() - diverse_session_author_window_size,
                                 recent_inner_session_feed_list.end());
  }
  context->SetIntListCommonAttr("session_pre_items", std::move(session_pre_item_list));
}

void FollowParseUserInfoSlideEnricher::ParseSamplelistUserAttr(MutableRecoContextInterface *context) {
  // samplelist 统一提供了一些通用用户属性
  kuiba::PredictItem kuiba_user_attrs;
  if (user_info_->has_samplelist_user_attr()) {
    bool user_attr_valid = kuiba_user_attrs.ParseFromString(user_info_->samplelist_user_attr());
    if (!user_attr_valid) {
      return;
    }
  }

  for (const auto &attr : kuiba_user_attrs.attr()) {
    interop::SaveSampleAttrToCommonAttr(context, attr, true);
  }
}

void FollowParseUserInfoSlideEnricher::FillCommonAttrInCommonLeaf(MutableRecoContextInterface *context) {
  const ks::reco::RealTimeFollowRecoUserInfo &user_info = *user_info_;
  uint64 llsid = 0L;
  if (user_info.has_api_request_info()) {
    llsid = user_info.api_request_info().llsid();
  }
  context->SetIntCommonAttr("llsid", llsid);
  // 网络模式
  auto& reco_user_info = user_info.user_info();
  context->SetStringCommonAttr("dNet", reco_user_info.visit_net());
  context->SetIntCommonAttr("dWifi",
    ((reco_user_info.visit_net() == "WIFI" || reco_user_info.visit_net() == "wifi") ? 1 : 0));
  // 手机机型
  context->SetStringCommonAttr("dMod", reco_user_info.visit_mod());
  // 新样本下发时间戳
  int64 leaf_time_ms = current_ts_ / 1000;
  context->SetIntCommonAttr("leaf_time_ms", leaf_time_ms);

  context->SetIntCommonAttr("uFollowCount", user_info.follow_list().size());
  context->SetIntCommonAttr("uIsFollowSlideNewSample", 1);
  context->SetIntCommonAttr("uIsEshopUser",
    context->GetIntCommonAttr("uMerchantUserQuantileScore").value_or(0) > 0 ? 1 : 0);
  context->SetIntCommonAttr("uIsEshopPlay",
    context->GetIntCommonAttr("uMerchantUserQuantileScore").value_or(0) > 0 ? 1 : 0);
  // 设置打赏用户特征
  context->SetIntCommonAttr("uIsGiftSilence",
    context->GetIntCommonAttr("uIsLiveGiftSilence").value_or(0) > 0 ? 1 : 0);
  context->SetIntCommonAttr("uIsHasCoin",
    context->GetIntCommonAttr("uKsCoin").value_or(0) > 0 ? 1 : 0);
  context->SetIntCommonAttr("uHasPgtr", 0);
  auto follow_user_revenue_cost = user_info.follow_user_revenue_cost();
  context->SetIntCommonAttr("uCostAmt1d", follow_user_revenue_cost.total_cost_amt_1d());
  context->SetIntCommonAttr("uFollowCostAmt1d", follow_user_revenue_cost.follow_page_total_cost_amt_1d());
  context->SetIntCommonAttr("uCostAmt7d", follow_user_revenue_cost.total_cost_amt_7d());
  context->SetIntCommonAttr("uFollowCostAmt7d", follow_user_revenue_cost.follow_page_total_cost_amt_7d());
  context->SetIntCommonAttr("uCostAmt30d", follow_user_revenue_cost.total_cost_amt_30d());
  context->SetIntCommonAttr("uFollowCostAmt30d", follow_user_revenue_cost.follow_page_total_cost_amt_30d());
  context->SetIntCommonAttr("uCostAmtAccu", follow_user_revenue_cost.accu_total_cost_amt());
  context->SetIntCommonAttr("uCostCnt1d", follow_user_revenue_cost.total_cost_cnt_1d());
  context->SetIntCommonAttr("uFollowCostCnt1d", follow_user_revenue_cost.follow_page_total_cost_cnt_1d());
  context->SetIntCommonAttr("uCostCnt7d", follow_user_revenue_cost.total_cost_cnt_7d());
  context->SetIntCommonAttr("uFollowCostCnt7d", follow_user_revenue_cost.follow_page_total_cost_cnt_7d());
  context->SetIntCommonAttr("uCostCnt30d", follow_user_revenue_cost.total_cost_cnt_30d());
  context->SetIntCommonAttr("uFollowCostCnt30d", follow_user_revenue_cost.follow_page_total_cost_cnt_30d());
  context->SetIntCommonAttr("uCostCntAccu", follow_user_revenue_cost.accu_total_cost_cnt());
  context->SetIntCommonAttr("uInvertedCandidateNum", 0);
  context->SetIntCommonAttr("uForwardCandidateNum", 0);
  context->SetIntCommonAttr("uFinalArPhotoCnt", 0);

  if (user_info.has_slide_mode_context_info()) {
    const auto& slide_mode_context_info = user_info.slide_mode_context_info();
    context->SetIntCommonAttr("uSlideModeEntryFeedPId", slide_mode_context_info.entry_feed_id());
    context->SetIntCommonAttr("uSlideModeEntryFeedType", slide_mode_context_info.entry_feed_type());
    context->SetIntCommonAttr("uSlideModeEntryFeedAId", slide_mode_context_info.entry_feed_author_id());
    context->SetIntCommonAttr("uSlideModeEntryFeedPReason", slide_mode_context_info.entry_feed_reason());
    context->SetIntCommonAttr("uSlideModeCusorIndex", slide_mode_context_info.cursor_index());
    context->SetIntCommonAttr("uSlideModeIsSimpleLiveEntry",
      slide_mode_context_info.slide_type() == reco::SlideModeContextInfo::SIMPLE_LIVE_ENTRY_SLIDE);
    uint64 entry_feed_id = slide_mode_context_info.entry_feed_id();
    reco::RecoEnum::ItemType entry_feed_type = slide_mode_context_info.entry_feed_type();
    if (entry_feed_type == reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
      context->SetIntCommonAttr("entry_live_feed_item_key",
        ks::platform::Util::GenKeysign(3, entry_feed_id));
    } else {
      context->SetIntCommonAttr("entry_photo_feed_item_key",
        ks::platform::Util::GenKeysign(2, entry_feed_id));
    }
    FillRecentSessionFeedbackFeatures(context);
  }
  // 红点特征
  const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(notify_response_path_);
  const ks::reco::MyFollowTabNotifyResponse * notify_response = nullptr;
  if (message) {
    notify_response = dynamic_cast<const ks::reco::MyFollowTabNotifyResponse *>(message);
  }
  if (notify_response) {
    context->SetIntCommonAttr("uFollowTabNotifyType", notify_response->notify_type());
  } else {
    context->SetIntCommonAttr("uFollowTabNotifyType", ks::reco::MyFollowTabNotifyResponse::UNKNOWN);
  }
  std::vector<int64> friend_list;
  for (int i = 0; i < user_info.follow_list_size(); i++) {
    auto &follower = user_info.follow_list(i);
    int64 a_id = follower.id();
    if (follower.is_friend()) {
      friend_list.push_back(a_id);
    }
  }
  context->SetIntListCommonAttr("friend_author_list", std::move(friend_list));
}

void FollowParseUserInfoSlideEnricher::FillRelationColossusList(MutableRecoContextInterface *context) {
  const auto &relation_colossus_list = user_info_->relation_colossus_list();
  if (relation_colossus_list.empty()) {
    return;
  }
  std::vector<int64> colossus_author_id_list;
  std::vector<int64> colossus_photo_id_list;
  std::vector<int64> colossus_relation_type_list;
  std::vector<int64> colossus_play_time_list;
  std::vector<int64> colossus_label_list;
  std::vector<int64> colossus_last_show_minites_list;

  int friend_tab_max_colossus_list_size = 400;
  int cur_label = 0;
  int last_show_diff_in_min = 180 * 24 * 60;

  for (auto it = relation_colossus_list.rbegin(); it != relation_colossus_list.rend(); ++it) {
    if (friend_tab_max_colossus_list_size <= colossus_author_id_list.size()) {
      break;
    }
    const auto& item = *it;
    colossus_author_id_list.push_back(item.author_id());
    colossus_photo_id_list.push_back(item.photo_id());
    colossus_relation_type_list.push_back(item.relation_type());
    colossus_play_time_list.push_back(item.play_time());
    if (item.has_timestamp()) {
      last_show_diff_in_min = (int)(current_ts_ / 1000000 - item.timestamp()) / 60;
    }
    colossus_last_show_minites_list.push_back(last_show_diff_in_min);

    cur_label = (int64) item.is_like()           * (1)
              + (int64) item.is_follow()         * (1 << 1)
              + (int64) item.is_forward()        * (1 << 2)
              + (int64) item.is_report()         * (1 << 3)
              + (int64) item.is_comment()        * (1 << 4)
              + (int64) item.is_profile_stay()   * (1 << 6)
              + (int64) item.is_comment_stay()   * (1 << 8)
              + (int64) item.is_negetive()       * (1 << 13)
              + (int64) item.is_search()         * (1 << 17)
              + (int64) item.is_download()       * (1 << 18)
              + (int64) item.is_collect()        * (1 << 19)
              + (int64) item.is_comment_reply()  * (1 << 27);
    colossus_label_list.push_back(cur_label);
  }

  folly::F14FastSet<int32> arrival_dates_in_30d;
  int last_arrival_date_diff = 180;
  int day_diff = 0;
  for (auto it = relation_colossus_list.rbegin(); it != relation_colossus_list.rend(); ++it) {
    const auto& item = *it;
    if (item.has_timestamp()) {
      day_diff = (int)(current_ts_ / 1000000 - item.timestamp()) / 86400;
      if (day_diff > 30) {
        break;
      }
      last_arrival_date_diff = last_arrival_date_diff < day_diff ? last_arrival_date_diff : day_diff;
      arrival_dates_in_30d.insert(day_diff);
    }
  }
  context->SetIntCommonAttr("colossus_arrival_days_in_30d", arrival_dates_in_30d.size());
  context->SetIntCommonAttr("colossus_last_arrival_date_diff", last_arrival_date_diff);

  context->SetIntListCommonAttr("colossus_author_id_list", std::move(colossus_author_id_list));
  context->SetIntListCommonAttr("colossus_photo_id_list", std::move(colossus_photo_id_list));
  context->SetIntListCommonAttr("colossus_relation_type_list", std::move(colossus_relation_type_list));
  context->SetIntListCommonAttr("colossus_play_time_list", std::move(colossus_play_time_list));
  context->SetIntListCommonAttr("colossus_label_list", std::move(colossus_label_list));
  context->SetIntListCommonAttr("colossus_last_show_minites_list",
    std::move(colossus_last_show_minites_list));
}

void FollowParseUserInfoSlideEnricher::FillRelationActionList(MutableRecoContextInterface *context) {
  std::shared_ptr<std::vector<int>> default_val = std::make_shared<std::vector<int>>();
  static auto kconf = ks::infra::KConf().GetList("reco.follow.validProduct4RealtionFeatures", default_val);
  auto kconf_valids =  kconf->Get();
  bool is_valid = std::find(kconf_valids->begin(), kconf_valids->end(), request_type_)
    != kconf_valids->end();
  const auto &relation_action_list = user_info_->relation_action_list();
  if (!is_valid || relation_action_list.empty()) {
    return;
  }
  std::vector<int64> relation_im_aid_list;
  std::vector<int64> relation_im_cnt_list;
  std::vector<int64> relation_at_aid_list;
  std::vector<int64> relation_at_cnt_list;
  std::vector<int64> relation_enter_profile_aid_list;
  std::vector<int64> relation_enter_profile_cnt_list;

  int friend_tab_max_relation_list_size = 400;

  int action_cnt = 0;
  for (auto it = relation_action_list.rbegin(); it != relation_action_list.rend(); ++it) {
    if (friend_tab_max_relation_list_size <= action_cnt) {
      break;
    }
    action_cnt++;
    const auto& item = *it;
    if (item.im_cnt() > 0) {
      relation_im_aid_list.push_back(item.author_id());
      relation_im_cnt_list.push_back(item.im_cnt());
    }
    int at_cnt = item.publish_photo_at_cnt() + item.comment_photo_at_cnt();
    if (at_cnt > 0) {
      relation_at_aid_list.push_back(item.author_id());
      relation_at_cnt_list.push_back(at_cnt);
    }
    if (item.enter_other_profile_page_cnt() > 0) {
      relation_enter_profile_aid_list.push_back(item.author_id());
      relation_enter_profile_cnt_list.push_back(item.enter_other_profile_page_cnt());
    }
  }

  context->SetIntListCommonAttr("relation_im_aid_list", std::move(relation_im_aid_list));
  context->SetIntListCommonAttr("relation_im_cnt_list", std::move(relation_im_cnt_list));
  context->SetIntListCommonAttr("relation_at_aid_list", std::move(relation_at_aid_list));
  context->SetIntListCommonAttr("relation_at_cnt_list", std::move(relation_at_cnt_list));
  context->SetIntListCommonAttr("relation_enter_profile_aid_list",
    std::move(relation_enter_profile_aid_list));
  context->SetIntListCommonAttr("relation_enter_profile_cnt_list",
    std::move(relation_enter_profile_cnt_list));
}

void FollowParseUserInfoSlideEnricher::FillRecentSessionFeedbackFeatures(
    MutableRecoContextInterface *context) {
  const ks::reco::RealTimeFollowRecoUserInfo &user_info = *user_info_;
  if (user_info.slide_mode_context_info().cursor_index() <= 0) {
    return;
  }
  folly::F14FastSet<uint64> browsed_photo_ids_in_session;
  folly::F14FastSet<uint64> browsed_live_ids_in_session;
  size_t max_recent_count = 20;
  for (int i = 0; i < user_info.browsed_photo_ids_in_session_size(); ++i) {
    browsed_photo_ids_in_session.insert(user_info.browsed_photo_ids_in_session(i));
  }
  for (int i = 0; i < user_info.browsed_live_ids_in_session_size(); ++i) {
    browsed_live_ids_in_session.insert(user_info.browsed_live_ids_in_session(i));
  }
  std::vector<int64> recent_feedback_author_id_list;
  recent_feedback_author_id_list.clear();
  std::vector<int64> recent_feedback_play_length_list;
  recent_feedback_play_length_list.clear();
  std::vector<int64> recent_feedback_like_list;
  recent_feedback_like_list.clear();
  std::vector<int64> recent_feedback_comment_list;
  recent_feedback_comment_list.clear();
  // pos feedback include share / collect / profile / click
  std::vector<int64> recent_pos_feedback_list;
  recent_pos_feedback_list.clear();
  std::vector<int64> recent_neg_feedback_list;
  recent_neg_feedback_list.clear();
  std::vector<double> recent_feedback_fr_pwt_list;
  recent_feedback_fr_pwt_list.clear();
  std::vector<double> recent_feedback_fr_plvtr_list;
  recent_feedback_fr_plvtr_list.clear();
  std::vector<double> recent_feedback_fr_pltr_list;
  recent_feedback_fr_pltr_list.clear();
  std::vector<double> recent_feedback_fr_pcmtr_list;
  recent_feedback_fr_pcmtr_list.clear();
  std::vector<double> recent_feedback_fr_pvtr_list;
  recent_feedback_fr_pvtr_list.clear();
  std::vector<double> recent_feedback_fr_psvr_list;
  recent_feedback_fr_psvr_list.clear();
  std::vector<double> recent_feedback_fr_pftr_list;
  recent_feedback_fr_pftr_list.clear();
  int feedback_nums = 0;
  std::vector<int64> recent_inner_session_feedback_feed_id_list;
  std::vector<int64> recent_inner_session_feedback_feed_is_live_list;
  std::vector<int64> live_feedback_feed_item_key_list;
  std::vector<int64> photo_feedback_feed_item_key_list;
  auto slide_mode_entry_feed_id = user_info.slide_mode_context_info().entry_feed_id();
  for (int i = user_info.recent_inner_session_follow_reco_result_size() - 1; i >= 0; --i) {
    auto &reco_result = user_info.recent_inner_session_follow_reco_result(i);
    uint64 cur_entry_feed_id = 0;
    if (reco_result.has_slide_mode_context_info()) {
      cur_entry_feed_id = reco_result.slide_mode_context_info().entry_feed_id();
    }
    if (reco_result.create_time() <= 0
        || cur_entry_feed_id != slide_mode_entry_feed_id
        || (current_ts_ - reco_result.create_time() * 1000L) / base::Time::kMicrosecondsPerHour > 24) {
      continue;
    }
    for (int j = reco_result.myfollow_reco_feed_size() - 1; j >= 0; --j) {
      auto& feed = reco_result.myfollow_reco_feed(j);
      if (((feed.type() == reco::RecoEnum::ITEM_TYPE_PHOTO)
        && browsed_photo_ids_in_session.find(feed.id()) != browsed_photo_ids_in_session.end())
        || ((feed.type() == reco::RecoEnum::ITEM_TYPE_LIVESTREAM)
        && browsed_live_ids_in_session.find(feed.id()) != browsed_live_ids_in_session.end())) {
        if (recent_inner_session_feedback_feed_id_list.size() < max_recent_count
            && feed.has_last_feedback() && feed.last_feedback().play_length() > 0) {
          ++feedback_nums;
          recent_inner_session_feedback_feed_id_list.emplace_back(feed.id());
          recent_inner_session_feedback_feed_is_live_list.emplace_back(
                feed.type() == reco::RecoEnum::ITEM_TYPE_LIVESTREAM);
          if (feed.type() == reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
            live_feedback_feed_item_key_list.push_back(
              ks::platform::Util::GenKeysign(3, feed.id()));
          } else {
            photo_feedback_feed_item_key_list.push_back(
              ks::platform::Util::GenKeysign(2, feed.id()));
          }
          recent_feedback_author_id_list.emplace_back(feed.author_id());
          recent_feedback_play_length_list.emplace_back(feed.last_feedback().play_length());
          recent_feedback_like_list.emplace_back(feed.last_feedback().like());
          recent_feedback_comment_list.emplace_back(feed.last_feedback().comment());
          recent_pos_feedback_list.emplace_back(feed.last_feedback().share() ||
              feed.last_feedback().collect() || feed.last_feedback().entry_profile_feed()
              || feed.last_feedback().click());
          recent_neg_feedback_list.emplace_back(feed.last_feedback().negative());
          recent_feedback_fr_pwt_list.emplace_back(feed.pxtr_p_wt());
          recent_feedback_fr_plvtr_list.emplace_back(feed.pxtr_p_lvtr());
          recent_feedback_fr_pltr_list.emplace_back(feed.pxtr_p_ltr());
          recent_feedback_fr_pcmtr_list.emplace_back(feed.pxtr_p_cmtr());
          recent_feedback_fr_pvtr_list.emplace_back(feed.pxtr_p_vtr());
          recent_feedback_fr_psvr_list.emplace_back(feed.pxtr_p_svr());
          recent_feedback_fr_pftr_list.emplace_back(feed.pxtr_p_ftr());
        }
      }
    }
  }

  std::reverse(recent_inner_session_feedback_feed_id_list.begin(),
    recent_inner_session_feedback_feed_id_list.end());
  std::reverse(recent_inner_session_feedback_feed_is_live_list.begin(),
    recent_inner_session_feedback_feed_is_live_list.end());
  std::reverse(recent_feedback_author_id_list.begin(), recent_feedback_author_id_list.end());
  std::reverse(recent_feedback_fr_pwt_list.begin(), recent_feedback_fr_pwt_list.end());
  std::reverse(recent_feedback_fr_plvtr_list.begin(), recent_feedback_fr_plvtr_list.end());
  std::reverse(recent_feedback_fr_pltr_list.begin(), recent_feedback_fr_pltr_list.end());
  std::reverse(recent_feedback_fr_pcmtr_list.begin(), recent_feedback_fr_pcmtr_list.end());
  std::reverse(recent_feedback_fr_pvtr_list.begin(), recent_feedback_fr_pvtr_list.end());
  std::reverse(recent_feedback_fr_psvr_list.begin(), recent_feedback_fr_psvr_list.end());
  std::reverse(recent_feedback_fr_pftr_list.begin(), recent_feedback_fr_pftr_list.end());
  std::reverse(recent_feedback_play_length_list.begin(), recent_feedback_play_length_list.end());
  std::reverse(recent_feedback_like_list.begin(), recent_feedback_like_list.end());
  std::reverse(recent_feedback_comment_list.begin(), recent_feedback_comment_list.end());
  std::reverse(recent_pos_feedback_list.begin(), recent_pos_feedback_list.end());
  std::reverse(recent_neg_feedback_list.begin(), recent_neg_feedback_list.end());
  context->SetIntListCommonAttr("uFeedbackPhotoIds",
    std::move(recent_inner_session_feedback_feed_id_list));
  context->SetIntListCommonAttr("uFeedbackIsLive",
    std::move(recent_inner_session_feedback_feed_is_live_list));
  context->SetIntListCommonAttr("uFeedbackAuthorIds", std::move(recent_feedback_author_id_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPwt", std::move(recent_feedback_fr_pwt_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPlvtr", std::move(recent_feedback_fr_plvtr_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPltr", std::move(recent_feedback_fr_pltr_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPcmtr", std::move(recent_feedback_fr_pcmtr_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPvtr", std::move(recent_feedback_fr_pvtr_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPsvr", std::move(recent_feedback_fr_psvr_list));
  context->SetDoubleListCommonAttr("uFeedbackFrPftr", std::move(recent_feedback_fr_pftr_list));
  context->SetIntListCommonAttr("uFeedbackWatchTime", std::move(recent_feedback_play_length_list));
  context->SetIntListCommonAttr("uFeedbackLike", std::move(recent_feedback_like_list));
  context->SetIntListCommonAttr("uFeedbackComment", std::move(recent_feedback_comment_list));
  context->SetIntListCommonAttr("uPosFeedback", std::move(recent_pos_feedback_list));
  context->SetIntListCommonAttr("uNegFeedback", std::move(recent_neg_feedback_list));
  context->SetIntListCommonAttr("live_feedback_feed_item_key_list",
    std::move(live_feedback_feed_item_key_list));
  context->SetIntListCommonAttr("photo_feedback_feed_item_key_list",
    std::move(photo_feedback_feed_item_key_list));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowParseUserInfoSlideEnricher, FollowParseUserInfoSlideEnricher);

}  // namespace platform
}  // namespace ks
