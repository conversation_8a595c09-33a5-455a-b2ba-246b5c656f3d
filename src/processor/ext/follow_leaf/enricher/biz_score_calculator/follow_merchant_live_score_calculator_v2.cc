#include "dragon/src/processor/ext/follow_leaf/enricher/biz_score_calculator/follow_merchant_live_score_calculator_v2.h"

#include <algorithm>
#include <vector>

#include "dragon/src/processor/ext/follow_leaf/context/util.h"

namespace ks {
namespace platform {
void FollowMerchantLiveScoreCalculatorV2Enricher::Enrich(MutableRecoContextInterface *context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  InitAbParam(context);
  InitContextInternal(context);

  auto *score_accessor = context->GetItemAttrAccessor(score_name_attr_);
  auto *biz_index_accessor = context->GetItemAttrAccessor(biz_index_attr_);
  auto *merchant_cpm_accessor = context->GetItemAttrAccessor("merchant_cpm");
  auto *merchant_bonus_accessor = context->GetItemAttrAccessor("merchant_bonus");
  auto *merchant_cart_score_accessor = context->GetItemAttrAccessor("merchant_cart_score");
  auto *merchant_cart_cpm_accessor = context->GetItemAttrAccessor("merchant_cart_cpm");
  merchant_bonus_toB_accessor_ = context->GetItemAttrAccessor("merchant_bonus_toB");
  merchant_bonus_toB_new_goods_accessor_ = context->GetItemAttrAccessor("merchant_bonus_toB_new_goods");
  // 获取电商精排相关打分和 bonus 项计算电商价值
  auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
  auto *is_merchant_live_accessor = context->GetItemAttrAccessor("is_merchant_live");
  auto *is_merchant_cart_accessor = context->GetItemAttrAccessor("is_merchant_cart");
  auto *is_merchant_living_photo_accessor = context->GetItemAttrAccessor("is_merchant_living_photo");
  // auto *eshop_cart_cvr_accessor = context->GetItemAttrAccessor("eshop_cart_cvr");
  // auto *eshop_live_ctr_accessor = context->GetItemAttrAccessor("eshop_live_ctr");
  // auto *eshop_live_price_accessor = context->GetItemAttrAccessor("eshop_live_price");
  eshop_live_bonus_accessor_ = context->GetItemAttrAccessor("eshop_live_bonus");
  // 挂车短视频或者 living photo
  auto *merchant_cart_pctr_accessor = context->GetItemAttrAccessor("merchant_cart_pctr");
  auto *merchant_cart_pcvr_accessor = context->GetItemAttrAccessor("merchant_cart_pcvr");
  auto *merchant_cart_pgmv_accessor = context->GetItemAttrAccessor("merchant_cart_pgmv");
  auto *merchant_living_photo_pctr_accessor = context->GetItemAttrAccessor("merchant_living_photo_pctr");
  auto *merchant_living_photo_pcvr_accessor = context->GetItemAttrAccessor("merchant_living_photo_pcvr");
  auto *merchant_living_photo_pgmv_accessor = context->GetItemAttrAccessor("merchant_living_photo_pgmv");
  // bonus
  auto *show_cnt_accessor = context->GetItemAttrAccessor("uIsShowCnt");
  auto *author_id_accessor = context->GetItemAttrAccessor("author_id");
  auto *follow_day_accessor = context->GetItemAttrAccessor("uFollowAuthorTimeDay");

  std::vector<CommonRecoResult> result_vec;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, is_merchant_live_accessor).value_or(0) == 1) {
      // double ctr = context->GetDoubleItemAttr(result, eshop_live_ctr_accessor).value_or(0.0);
      // double cvr = context->GetDoubleItemAttr(result, eshop_cart_cvr_accessor).value_or(0.0);
      // double price = context->GetDoubleItemAttr(result, eshop_live_price_accessor).value_or(0.0);
      // double gpm = ctr * cvr * price;

      // 直接取数，计算过程在消费链路模块 follow_cal_mix_score_module
      double gpm = context->GetDoubleItemAttr(result, merchant_cpm_accessor).value_or(0.0);

      // bonus
      // uint64 show_cnt = context->GetIntItemAttr(result, show_cnt_accessor).value_or(0);
      // uint64 author_id = context->GetIntItemAttr(result, author_id_accessor).value_or(0);
      // uint64 follow_day = context->GetIntItemAttr(result, follow_day_accessor).value_or(0);
      // double bonus = CalMerchantBonus(context, result, show_cnt, author_id, gpm, follow_day);
      double bonus = context->GetDoubleItemAttr(result, merchant_bonus_accessor).value_or(0.0);

      double score = gpm + bonus;
      context->SetDoubleItemAttr(result, score_accessor, score);
      // context->SetDoubleItemAttr(result, merchant_cpm_accessor,
      //                            gpm < merchant_cpm_score_threshold_ ? 0.0 : gpm);
      // context->SetDoubleItemAttr(result, merchant_bonus_accessor, bonus);
      result_vec.emplace_back(result);
      result_vec.back().score = score;
    }
  });

  std::sort(result_vec.begin(), result_vec.end(),
            [&](const CommonRecoResult &a, const CommonRecoResult &b) { return a.score > b.score; });
  for (int i = 0; i < result_vec.size(); i++) {
    context->SetIntItemAttr(result_vec[i], biz_index_accessor, i);
  }
}

void FollowMerchantLiveScoreCalculatorV2Enricher::InitAbParam(MutableRecoContextInterface *context) {
  enable_merchant_bonus_ = context->GetIntCommonAttr("enable_merchant_bonus").value_or(0) == 1;
  enable_recover_high_level_merchant_seller_ = context->GetIntCommonAttr(
    "enable_recover_high_level_merchant_seller").value_or(0) == 1;
  enable_recover_high_value_merchant_seller_ = context->GetIntCommonAttr(
    "enable_recover_high_value_merchant_seller").value_or(0) == 1;
  enable_live_showcase_add_bonus_ = context->GetIntCommonAttr(
    "enable_live_showcase_add_bonus").value_or(0) == 1;
  enable_live_new_goods_add_bonus_ = context->GetIntCommonAttr(
    "enable_live_new_goods_add_bonus").value_or(0) == 1;
  skip_merchant_strategy_toB_new_goods_follow_ =
      context->GetIntCommonAttr("skip_merchant_strategy_toB_new_goods_follow").value_or(0) == 1;
  live_showcase_bonus_show_cnt_thres_ = context->GetIntCommonAttr(
    "live_showcase_bonus_show_cnt_thres").value_or(5);
  live_showcase_days_thres_follow_not_buy_ = context->GetDoubleCommonAttr(
    "live_showcase_days_thres_follow_not_buy").value_or(3.0);
  live_showcase_bonus_weight_redirect_ = context->GetDoubleCommonAttr(
    "live_showcase_bonus_weight_redirect").value_or(10.0);
  live_showcase_bonus_weight_common_ = context->GetDoubleCommonAttr(
    "live_showcase_bonus_weight_common").value_or(5.0);
  live_showcase_bonus_weight_recent_follow_not_buy_ = context->GetDoubleCommonAttr(
    "live_showcase_bonus_weight_recent_follow_not_buy").value_or(10.0);
  live_showcase_bonus_weight_decay_factor_ = context->GetDoubleCommonAttr(
    "live_showcase_bonus_weight_decay_factor").value_or(0.6);
  new_goods_bonus_adjust_ = context->GetDoubleCommonAttr(
    "new_goods_bonus_adjust").value_or(0.8);
  enable_adjust_new_goods_bonus_by_u_type_ = context->GetIntCommonAttr(
    "enable_adjust_new_goods_bonus_by_u_type").value_or(0) == 1;
  new_goods_bonus_weight_u2_ = context->GetDoubleCommonAttr(
    "new_goods_bonus_weight_u2").value_or(1.0);
  new_goods_bonus_weight_u3_ = context->GetDoubleCommonAttr(
    "new_goods_bonus_weight_u3").value_or(1.0);
  new_goods_bonus_weight_u4_ = context->GetDoubleCommonAttr(
    "new_goods_bonus_weight_u4").value_or(1.0);
  merchant_cpm_score_threshold_ = context->GetDoubleCommonAttr("merchant_cpm_score_threshold").value_or(0.0);
  enable_live_new_goods_bonus_self_weight_ =
      context->GetIntCommonAttr("enable_live_new_goods_bonus_self_weight").value_or(0) == 1;
}

void FollowMerchantLiveScoreCalculatorV2Enricher::InitContextInternal(MutableRecoContextInterface *context) {
  merchant_user_type_ = std::string(context->GetStringCommonAttr("uBuyerEffectiveType").value_or("U0-null"));
  merchant_coldstart_showcase_seller_set_ =
      context->GetMutablePtrCommonAttr<folly::F14FastSet<uint64>>("merchant_coldstart_showcase_seller_set");
  merchant_new_goods_aid_info_ =
      context->GetMutablePtrCommonAttr<folly::F14FastMap<uint64, double>>("merchant_new_goods_aid_info");

  auto eshop_buy_seller_id_list_ptr = context->GetIntListCommonAttr("uEshopBuyItemSellerIdList");
  if (eshop_buy_seller_id_list_ptr) {
    for (auto& id : *eshop_buy_seller_id_list_ptr) {
      eshop_buy_seller_id_set_.insert(id);
    }
  }
  auto merchant_buy_seller_id_list_ptr = context->GetIntListCommonAttr("uMerchantBuyItemSellerIdList");
  if (merchant_buy_seller_id_list_ptr) {
    for (auto& id : *merchant_buy_seller_id_list_ptr) {
      eshop_buy_seller_id_set_.insert(id);
    }
  }
  auto photo_buy_seller_id_list_ptr = context->GetIntListCommonAttr("uPhotoBuyItemSellerIdList");
  if (photo_buy_seller_id_list_ptr) {
    for (auto& id : *photo_buy_seller_id_list_ptr) {
      eshop_buy_seller_id_set_.insert(id);
    }
  }
  auto eshop_search_seller_id_list_ptr = context->GetIntListCommonAttr("uEshopClickSearchSellerIdList");
  if (eshop_search_seller_id_list_ptr) {
    for (auto& id : *eshop_search_seller_id_list_ptr) {
      eshop_search_seller_id_set_.insert(id);
    }
  }
  auto eshop_addcart_seller_id_list_ptr = context->GetIntListCommonAttr("uMerchantClickCartSellerIdList");
  if (eshop_addcart_seller_id_list_ptr) {
    for (auto& id : *eshop_addcart_seller_id_list_ptr) {
      eshop_addcart_seller_id_set_.insert(id);
    }
  }
}

double FollowMerchantLiveScoreCalculatorV2Enricher::CalMerchantBonus(MutableRecoContextInterface *context,
                                                                   const CommonRecoResult &result,
                                                                   uint64 show_cnt, uint64 author_id,
                                                                   double pgpm, uint64 follow_day) {
  double bonus = context->GetDoubleItemAttr(result, eshop_live_bonus_accessor_).value_or(0.0);
  if (!enable_live_showcase_add_bonus_) {
    return bonus;
  }
  double merchant_bonus_toB = 0.0;
  double merchant_bonus_toB_new_goods = 0.0;

  bool is_direct  = false;    // 判断是否是重定向
  if (eshop_buy_seller_id_set_.count(author_id)
      || eshop_search_seller_id_set_.count(author_id)
      || eshop_addcart_seller_id_set_.count(author_id)) {
    is_direct = true;
  }

  bool is_recent_follow_not_buy = false;    // 判断是否是最近已关但未购买
  if (follow_day <= live_showcase_days_thres_follow_not_buy_
      && !eshop_buy_seller_id_set_.count(author_id)) {
    is_recent_follow_not_buy = true;
  }

  double bonus_weight = 0.0;
  if (show_cnt < live_showcase_bonus_show_cnt_thres_) {
    double weight = is_direct ? live_showcase_bonus_weight_redirect_ : live_showcase_bonus_weight_common_;
    weight = is_recent_follow_not_buy ? live_showcase_bonus_weight_recent_follow_not_buy_ : weight;
    bonus_weight = weight * std::pow(live_showcase_bonus_weight_decay_factor_, show_cnt);
  }

  double adjust_ratio_by_emp_gpm = 1.0;
  double adjust_ratio_by_explosive_coeff = 1.0;
  if (merchant_coldstart_showcase_seller_set_->count(author_id)) {  // 不在 showcase 名单不计算
    merchant_bonus_toB = bonus_weight * pgpm * adjust_ratio_by_emp_gpm * adjust_ratio_by_explosive_coeff;
  }
  if (merchant_bonus_toB > 0) {
    context->SetDoubleItemAttr(result, merchant_bonus_toB_accessor_, merchant_bonus_toB);
  }

  auto new_goods_iter = merchant_new_goods_aid_info_->find(author_id);  // 不在新品目标商家名单 跳过
  double new_goods_ratio = 0.0;
  double u_type_bonus_adjust = 1.0;
  if (enable_adjust_new_goods_bonus_by_u_type_) {
    if (merchant_user_type_ == "U0-sleep" || merchant_user_type_ == "U1") {
      u_type_bonus_adjust = 0.0;
    } else if (merchant_user_type_ == "U2") {
      u_type_bonus_adjust = new_goods_bonus_weight_u2_;
    } else if (merchant_user_type_ == "U3") {
      u_type_bonus_adjust = new_goods_bonus_weight_u3_;
    } else if (merchant_user_type_ == "U4" || merchant_user_type_ == "U4+") {
      u_type_bonus_adjust = new_goods_bonus_weight_u4_;
    }
  }
  if (enable_live_new_goods_add_bonus_ && !skip_merchant_strategy_toB_new_goods_follow_ &&
      new_goods_iter != merchant_new_goods_aid_info_->end() && new_goods_iter->second > 0) {
    if (enable_live_new_goods_bonus_self_weight_) {
      new_goods_ratio = new_goods_iter->second;
    }
    merchant_bonus_toB_new_goods =
        bonus_weight * pgpm * (1 + new_goods_ratio) * new_goods_bonus_adjust_ * u_type_bonus_adjust;
    if (merchant_bonus_toB_new_goods < 0) {
      merchant_bonus_toB_new_goods = 0;
    }
  }
  if (merchant_bonus_toB_new_goods > 0) {
    context->SetDoubleItemAttr(result, merchant_bonus_toB_new_goods_accessor_, merchant_bonus_toB_new_goods);
  }

  bonus = bonus + merchant_bonus_toB + merchant_bonus_toB_new_goods;
  return bonus;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMerchantLiveScoreCalculatorV2Enricher,
  FollowMerchantLiveScoreCalculatorV2Enricher);

}  // namespace platform
}  // namespace ks
