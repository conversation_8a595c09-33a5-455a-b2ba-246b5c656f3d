#pragma once

#include <memory>
#include <string>
#include <vector>

#include "ks/reco_proto/proto/realtime_reco.pb.h"

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class FollowParseLiveColossusEnricher : public CommonRecoBaseEnricher {
 public:
  FollowParseLiveColossusEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    colossus_info_path_ = config()->GetString("colossus_info_path", "");
    if (colossus_info_path_.empty()) {
      LOG(ERROR) << "FollowLeafCascadingEnricher colossus_info_path is empty";
        return false;
    }
    return true;
  }
  bool ParseLiveColossus(MutableRecoContextInterface *context);
  void FillCommonAttr(MutableRecoContextInterface *context);
  void Clear() {
    sim_user_follow_live_play_sum_3d_ = 0;  // 单位秒
    sim_user_follow_live_play_cnt_3d_ = 0;
    sim_user_follow_live_pos_action_3d_ = 0;
    sim_user_follow_live_top10_author_aid_list_3d_.clear();
    sim_user_follow_live_top10_author_play_sum_3d_.clear();  // 单位秒
    sim_user_follow_live_top10_author_play_cnt_3d_.clear();
    sim_user_follow_live_play_sum_28d_ = 0;
    sim_user_follow_live_play_cnt_28d_ = 0;
    sim_user_follow_live_pos_action_aid_list_7d_.clear();
    sim_user_follow_live_pos_action_cnt_list_7d_.clear();

    sim_user_follow_live_play_list_7d_.clear();
    sim_user_follow_live_play_all_list_7d_.clear();
    sim_user_follow_live_id_list_7d_.clear();
    sim_user_follow_live_aid_list_7d_.clear();
    sim_user_follow_live_timestamp_list_7d_.clear();
    sim_user_follow_live_hetu_tag_list_7d_.clear();

    sim_user_follow_live_like_list_7d_.clear();
    sim_user_follow_live_comment_list_7d_.clear();
    sim_user_follow_live_forward_list_7d_.clear();
    sim_user_follow_live_shop_list_7d_.clear();
    sim_user_follow_live_gift_price_list_7d_.clear();
    sim_user_follow_live_gift_click_list_7d_.clear();
  }

 private:
  std::string colossus_info_path_;
  // 用于筛选 头部直播
  int64 sim_user_follow_live_play_sum_3d_ = 0;  // 单位秒
  int64 sim_user_follow_live_play_cnt_3d_ = 0;
  int64 sim_user_follow_live_pos_action_3d_ = 0;
  int64 sim_user_follow_live_play_sum_28d_ = 0;
  int64 sim_user_follow_live_play_cnt_28d_ = 0;
  std::vector<int64> sim_user_follow_live_top10_author_aid_list_3d_;
  std::vector<int64> sim_user_follow_live_top10_author_play_sum_3d_;  // 单位秒
  std::vector<int64> sim_user_follow_live_top10_author_play_cnt_3d_;
  std::vector<int64> sim_user_follow_live_pos_action_aid_list_7d_;
  std::vector<int64> sim_user_follow_live_pos_action_cnt_list_7d_;
  std::vector<int64> sim_user_follow_live_play_list_7d_;
  std::vector<int64> sim_user_follow_live_play_all_list_7d_;
  std::vector<int64> sim_user_follow_live_id_list_7d_;
  std::vector<int64> sim_user_follow_live_aid_list_7d_;
  std::vector<int64> sim_user_follow_live_timestamp_list_7d_;
  std::vector<int64> sim_user_follow_live_hetu_tag_list_7d_;

  std::vector<int64> sim_user_follow_live_like_list_7d_;
  std::vector<int64> sim_user_follow_live_comment_list_7d_;
  std::vector<int64> sim_user_follow_live_forward_list_7d_;
  std::vector<int64> sim_user_follow_live_shop_list_7d_;
  std::vector<int64> sim_user_follow_live_gift_price_list_7d_;
  std::vector<int64> sim_user_follow_live_gift_click_list_7d_;

 private:
  DISALLOW_COPY_AND_ASSIGN(FollowParseLiveColossusEnricher);
};
}  // namespace platform
}  // namespace ks
