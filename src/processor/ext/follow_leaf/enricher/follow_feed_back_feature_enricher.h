#pragma once

#include <memory>
#include <string>
#include <algorithm>
#include <vector>
#include <utility>

#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"
#include "ks/common_reco/util/key_sign_util.h"

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class FollowFeedbackFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  FollowFeedbackFeatureEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    return true;
  }

  DISALLOW_COPY_AND_ASSIGN(FollowFeedbackFeatureEnricher);
};
}  // namespace platform
}  // namespace ks
