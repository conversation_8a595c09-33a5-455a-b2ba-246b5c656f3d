#include "dragon/src/processor/ext/follow_leaf/enricher/follow_parse_live_colossus_enricher.h"

#include <algorithm>
#include <vector>
#include <utility>
#include <map>

#include "dragon/src/processor/ext/follow_leaf/context/util.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

using google::protobuf::Message;

namespace ks {
namespace platform {
void FollowParseLiveColossusEnricher::Enrich(
  MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  Clear();
  if (!ParseLiveColossus(context)) {
    return;
  }
  FillCommonAttr(context);
}

bool FollowParseLiveColossusEnricher::ParseLiveColossus(MutableRecoContextInterface *context) {
  const auto* colossus_sim_v2_live_items = context->GetPtrCommonAttr<std::vector<const Message *>>(
      colossus_info_path_);
  if (colossus_sim_v2_live_items == nullptr || colossus_sim_v2_live_items->empty()) {
    return false;
  }

  uint64 cur_time = base::GetTimestamp() / 1000000;

  std::map<int64, int64> sim_user_follow_live_top10_author_play_sum_3d_map;
  std::map<int64, int64> sim_user_follow_live_top10_author_play_cnt_3d_map;

  std::map<int64, uint64> sim_ua_pos_action_map;

  for (const Message * msg : *colossus_sim_v2_live_items) {
    const colossus::LiveItemV4* sim_v2_live = static_cast<const colossus::LiveItemV4*>(msg);
    if (sim_v2_live == nullptr) {
      continue;
    }
    const colossus::LiveItemV4& item = *sim_v2_live;
    if (item.live_id() <= 0) {
      continue;
    }
    if (item.author_id() <= 0) {
      continue;
    }
    // LiveItemV4 定义：https://docs.corp.kuaishou.com/d/home/<USER>
    // teams/reco-arch/colossus/proto/common_item.proto
    // int64 live_id = item.live_id();
    int64 author_id = item.author_id();
    uint64 timestamp = item.timestamp();
    uint32 play_time = item.play_time();   // 单位 second，这个是播放时长
    uint32 auto_play_time = item.auto_play_time();

    //  高 24bits 存 hetu_tag， 低 8bits 存 channel
    uint32 hetu_tag_channel = item.hetu_tag_channel();
    // uint32 hetu_tag = (hetu_tag_channel >> 8) & 0xffffff;
    uint32 channel_type = hetu_tag_channel & 0xff;

    // 偏移位: 0 点赞，1 关注页进入，2 评论，3 送礼金额，转发 5，12 点击送礼按钮，9 点击小黄车
    uint32 live_label = item.label();
    // int follow_enter = (live_label >> 1) & 1 ;
    int live_like = (live_label >> 0) & 1;
    int live_comment = (live_label >> 2) & 1;
    int live_forward = (live_label >> 5) & 1;
    int live_shop = (live_label >> 9) & 1;
    int live_gift_price = (live_label >> 3) & 1;
    int live_gift_click = (live_label >> 12) & 1;

    // bool is_friend = IsFriend(author_id);
    // 距今天时间天数

    int play_time_gap_day = static_cast<int>((cur_time - timestamp) / (24 * 3600));

    bool is_follow = channel_type == 3 || channel_type == 32
      || channel_type == 33 || channel_type == 86 || channel_type == 87;

    // U 粒度历史播放时长序列
    if (play_time_gap_day <= 7) {
      if (is_follow) {
        int64 play_time_all = play_time + auto_play_time;
        // sim_user_follow_live_play_list_7d_.push_back(play_time);
        sim_user_follow_live_play_all_list_7d_.push_back(play_time_all);
        // sim_user_follow_live_id_list_7d_.push_back(live_id);
        // sim_user_follow_live_aid_list_7d_.push_back(author_id);
        // sim_user_follow_live_timestamp_list_7d_.push_back(timestamp);
        // sim_user_follow_live_hetu_tag_list_7d_.push_back(hetu_tag);

        // sim_user_follow_live_like_list_7d_.push_back(live_like);
        // sim_user_follow_live_comment_list_7d_.push_back(live_comment);
        // sim_user_follow_live_forward_list_7d_.push_back(live_forward);
        // sim_user_follow_live_shop_list_7d_.push_back(live_shop);
        sim_user_follow_live_gift_price_list_7d_.push_back(live_gift_price);
        // sim_user_follow_live_gift_click_list_7d_.push_back(live_gift_click);

        int pos_action_cnt = live_like + live_comment + live_forward + live_gift_click
          + live_gift_price + live_shop;
        if (pos_action_cnt > 0) {
          if (sim_ua_pos_action_map.find(author_id) == sim_ua_pos_action_map.end()) {
            sim_ua_pos_action_map.insert(std::pair<int64, int64>(author_id, pos_action_cnt));
          } else {
            sim_ua_pos_action_map[author_id] += pos_action_cnt;
          }
        }
      }
    }

    // 用于筛选 头部直播
    if (play_time_gap_day <= 3 && is_follow) {
      int64 play_time_all = play_time + auto_play_time;  // 单位是秒
      if (play_time_all > 0) {
        // U 粒度，统计特征
        sim_user_follow_live_play_sum_3d_ += play_time_all;
        sim_user_follow_live_play_cnt_3d_++;
        // 正向互动行为次数，不同行为累积
        if (live_like > 0) {sim_user_follow_live_pos_action_3d_++;}
        if (live_comment > 0) {sim_user_follow_live_pos_action_3d_++;}
        if (live_forward > 0) {sim_user_follow_live_pos_action_3d_++;}
        if (live_shop > 0) {sim_user_follow_live_pos_action_3d_++;}
        if (live_gift_price > 0) {sim_user_follow_live_pos_action_3d_++;}
        if (live_gift_click > 0) {sim_user_follow_live_pos_action_3d_++;}

        // UA 粒度, top 序列
        if (sim_user_follow_live_top10_author_play_sum_3d_map.find(author_id)
          ==  sim_user_follow_live_top10_author_play_sum_3d_map.end()) {
          sim_user_follow_live_top10_author_play_sum_3d_map.insert(std::pair<int64, int64>(author_id, 0));
        } else {
          sim_user_follow_live_top10_author_play_sum_3d_map[author_id] += play_time_all;
        }
        if (sim_user_follow_live_top10_author_play_cnt_3d_map.find(author_id)
          == sim_user_follow_live_top10_author_play_cnt_3d_map.end()) {
          sim_user_follow_live_top10_author_play_cnt_3d_map.insert(std::pair<int64, int64>(author_id, 0));
        } else {
          sim_user_follow_live_top10_author_play_cnt_3d_map[author_id]++;
        }
      }
    }
    // 用于筛选 头部直播
    if (play_time_gap_day <= 28 && is_follow) {
      int64 play_time_all = play_time + auto_play_time;  // 单位是秒
      if (play_time_all > 0) {
        // U 粒度，统计特征
        sim_user_follow_live_play_sum_28d_ += play_time_all;
        sim_user_follow_live_play_cnt_28d_++;
      }
    }
  }  // -- end for

  // UA pos action
  for (auto& iter : sim_ua_pos_action_map) {
    sim_user_follow_live_pos_action_aid_list_7d_.push_back(iter.first);
    sim_user_follow_live_pos_action_cnt_list_7d_.push_back(iter.second);
  }

  // UA top10
  std::vector<std::pair<int64, int64>> index_play(
    sim_user_follow_live_top10_author_play_sum_3d_map.begin(),
    sim_user_follow_live_top10_author_play_sum_3d_map.end());

  std::sort(index_play.begin(), index_play.end(),
        [](const std::pair<int64, int64> &x, const std::pair<int64, int64> &y) -> bool {
        return x.second > y.second;
  });

  int top_limit = 0;
  for (auto it = index_play.begin(); it != index_play.end(); ++it) {
    int64 aid = it->first;
    sim_user_follow_live_top10_author_aid_list_3d_.emplace_back(aid);
    sim_user_follow_live_top10_author_play_sum_3d_.emplace_back(
      sim_user_follow_live_top10_author_play_sum_3d_map[aid]);
    sim_user_follow_live_top10_author_play_cnt_3d_.emplace_back(
      sim_user_follow_live_top10_author_play_cnt_3d_map[aid]);
    top_limit++;
    if (top_limit > 9) {
      break;
    }
  }
  return true;
}

void FollowParseLiveColossusEnricher::FillCommonAttr(MutableRecoContextInterface *context) {
  context->SetIntListCommonAttr("sim_user_follow_live_play_list_7d",
    std::move(sim_user_follow_live_play_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_play_all_list_7d",
    std::move(sim_user_follow_live_play_all_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_id_list_7d",
    std::move(sim_user_follow_live_id_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_aid_list_7d",
    std::move(sim_user_follow_live_aid_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_timestamp_list_7d",
    std::move(sim_user_follow_live_timestamp_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_hetu_tag_list_7d",
    std::move(sim_user_follow_live_hetu_tag_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_top10_author_aid_list_3d",
    std::move(sim_user_follow_live_top10_author_aid_list_3d_));
  context->SetIntListCommonAttr("sim_user_follow_live_top10_author_play_sum_3d",
    std::move(sim_user_follow_live_top10_author_play_sum_3d_));
  context->SetIntListCommonAttr("sim_user_follow_live_top10_author_play_cnt_3d",
    std::move(sim_user_follow_live_top10_author_play_cnt_3d_));
  context->SetIntListCommonAttr("sim_user_follow_live_pos_action_aid_list_7d",
    std::move(sim_user_follow_live_pos_action_aid_list_7d_));
  context->SetIntListCommonAttr("sim_user_follow_live_pos_action_cnt_list_7d",
    std::move(sim_user_follow_live_pos_action_cnt_list_7d_));
  context->SetIntCommonAttr("follow_watch_live_times_28d", sim_user_follow_live_play_sum_28d_);
  context->SetIntCommonAttr("sim_user_follow_live_play_cnt_28d", sim_user_follow_live_play_cnt_28d_);
  context->SetIntCommonAttr("sim_user_follow_live_play_sum_3d", sim_user_follow_live_play_sum_3d_);
  context->SetIntCommonAttr("sim_user_follow_live_play_cnt_3d", sim_user_follow_live_play_cnt_3d_);
  context->SetIntCommonAttr("sim_user_follow_live_pos_action_3d", sim_user_follow_live_pos_action_3d_);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowParseLiveColossusEnricher, FollowParseLiveColossusEnricher);

}  // namespace platform
}  // namespace ks
