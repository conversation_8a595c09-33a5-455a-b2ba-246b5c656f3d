#pragma once
#include <memory>
#include <vector>
#include <string>
#include <map>
#include <set>
#include <queue>
#include <chrono>
#include <algorithm>
#include <utility>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_ad_gap_checker.h"
#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_rank_type.h"

namespace ks {
namespace platform {
class FollowMixEnsembleSortArranger: public CommonRecoBaseArranger {
 public:
  struct ScoreInfo {
    double weight = 1.0;
    double a = 0.0;
    double b = 0.0;
    ItemAttr* score_accessor = nullptr;
  };
  FollowMixEnsembleSortArranger() {}
  ~FollowMixEnsembleSortArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  struct ScoreInfoAttr {
    FollowMix::BizType type = FollowMix::BizType::UNKNOWN;
    std::string weight_attr;
    std::string a_attr;
    std::string b_attr;
    std::string score_attr;
  };
  void Init(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end);
  void InitAbParam(MutableRecoContextInterface *context);
  void InitScoreInfos(MutableRecoContextInterface *context);
  void InitGapchecker(MutableRecoContextInterface *context);
  RecoResultIter SelectBestOne(MutableRecoContextInterface *context, int cur_index,
    RecoResultIter begin, RecoResultIter end);
  double UpdateScore(MutableRecoContextInterface *context, int cur_index,
    const CommonRecoResult &item_result, bool ad_flag);
  double CalcMixScore(MutableRecoContextInterface *context,
    const CommonRecoResult &item_result,
    const std::unordered_set<FollowMix::BizType>& black_type_set,
    const std::unordered_map<FollowMix::BizType, double>& type_weight_discount_map);
  void GetAdaptiveAdGapDetailByUserType(
    const std::string& user_type, const std::string& raw_param,
    std::vector<int>* frac_detail, std::vector<int>* gap_detail, int* ad_gap_default);
  FollowMix::BizType Identify(MutableRecoContextInterface *context,
   int current_index, const CommonRecoResult &result);
  bool JudgeAdType(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index);
  bool JudgeStoreWide(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index);
  bool JudgeLocalLife(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index);
  void Perf(MutableRecoContextInterface *context, const std::vector<CommonRecoResult>& result_vec);

  bool InitProcessor() override {
    const base::Json *score_infos_list_config = config()->Get("score_infos");
    if (!score_infos_list_config || !score_infos_list_config->IsArray()) {
      LOG(ERROR) << "FollowMixEnsembleSortArranger init failed! Missing \"score_infos\" config!";
      return false;
    }
    score_infos_attrs_.reserve(score_infos_list_config->array().size());
    for (const auto *score_infos_config : score_infos_list_config->array()) {
      if (!score_infos_config || !score_infos_config->IsObject()) {
        LOG(ERROR) << "score_infos_config is not object";
        return false;
      }
      ScoreInfoAttr score_infos_attr;
      std::string type_str = score_infos_config->GetString("type");
      score_infos_attr.type = StrToType(type_str);
      if (score_infos_attr.type == FollowMix::BizType::UNKNOWN) {
        LOG(ERROR) << "type is unknown_type:" << type_str;
        return false;
      }
      score_infos_attr.weight_attr = score_infos_config->GetString("weight_attr");
      if (score_infos_attr.weight_attr.empty()) {
        LOG(ERROR) << "weight_attr is empty";
        return false;
      }
      score_infos_attr.a_attr = score_infos_config->GetString("a_attr");
      if (score_infos_attr.a_attr.empty()) {
        LOG(ERROR) << "a_attr is empty";
        return false;
      }
      score_infos_attr.b_attr = score_infos_config->GetString("b_attr");
      if (score_infos_attr.b_attr.empty()) {
        LOG(ERROR) << "b_attr is empty";
        return false;
      }
      score_infos_attr.score_attr = score_infos_config->GetString("score_attr");
      if (score_infos_attr.score_attr.empty()) {
        LOG(ERROR) << "score_attr is empty";
        return false;
      }
      score_infos_attrs_.push_back(score_infos_attr);
    }

    es_name_ = config()->GetString("es_name", "");
    exp_tag_attr_ = config()->GetString("exp_tag_attr", "");
    is_perf_attr_ = config()->GetString("is_perf_attr", "");
    perf_discount_attr_ = config()->GetString("perf_discount_attr", "");
    close_identify_ad_flag_attr_ = config()->GetString("close_identify_ad_flag_attr", "");
    enable_clamp_score_attr_ = config()->GetString("enable_clamp_score_attr", "");
    ue_avoid_business_weight_discount_attr_ = config()->GetString(
      "ue_avoid_business_weight_discount_attr", "");
    enable_equal_index_is_ad_attr_ = config()->GetString("enable_equal_index_is_ad_attr", "");
    return true;
  }
  void Clear() {
    result_size_ = 0;
    new_result_vec_.clear();
    origin_score_infos_.clear();
    ad_gap_checker_.reset();
    is_one_size_ = false;
  }

  FollowMix::BizType StrToType(const std::string& type_str) {
    if (type_str == "natural") {
      return FollowMix::BizType::NATURAL;
    } else if (type_str == "ad") {
      return FollowMix::BizType::AD_DSP;
    } else if (type_str == "store_wide") {
      return FollowMix::BizType::STORE_WIDE;
    } else if (type_str == "merchant") {
      return FollowMix::BizType::MERCHANT;
    } else if (type_str == "merchant_cart") {
      return FollowMix::BizType::MERCHANT_CART;
    } else if (type_str == "gift") {
      return FollowMix::BizType::GIFT;
    } else if (type_str == "local_life") {
      return FollowMix::BizType::LOCAL_LIFE;
    } else if (type_str == "recruit_photo") {
      return FollowMix::BizType::RECRUIT_PHOTO;
    } else if (type_str == "recruit_live") {
      return FollowMix::BizType::RECRUIT_LIVE;
    } else if (type_str == "merchant_livehead") {
      return FollowMix::BizType::RECRUIT_LIVE;
    } else if (type_str == "merchant_livehead") {
      return FollowMix::BizType::MERCHANT_LIVEHEAD;
    }
    return FollowMix::BizType::UNKNOWN;
  }

 private:
  // 结果
  std::vector<CommonRecoResult> new_result_vec_;
  std::vector<bool> is_selected_vec_;
  std::unordered_map<FollowMix::BizType, ScoreInfo> origin_score_infos_;
  std::vector<ScoreInfoAttr> score_infos_attrs_;
  int result_size_ = 12;

  ItemAttr* mix_score_accessor_ = nullptr;
  ItemAttr* next_feed_mix_score_accessor_ = nullptr;
  ItemAttr* ad_score_accessor_ = nullptr;
  ItemAttr* ad_flag_accessor_ = nullptr;
  ItemAttr* natural_index_accessor_ = nullptr;
  ItemAttr* biz_type_list_accessor_ = nullptr;

  int last_ad_pos_ = -1000;

  double merchant_avoid_business_weight_discount_ = 1.0;
  double gift_avoid_business_weight_discount_ = 1.0;
  double ue_avoid_business_weight_discount_ = 1.0;
  bool color_ab_disable_store_wide_photo_ = false;
  bool enable_identify_close_ad_flag_ = false;
  bool enable_clamp_score_ = false;
  bool is_one_size_ = false;
  bool enable_equal_index_is_ad_ = false;

  bool is_perf_ = false;
  double perf_discount_ = 1.0;
  std::string exp_tag_;
  std::string es_name_;
  std::string exp_tag_attr_;
  std::string is_perf_attr_;
  std::string close_identify_ad_flag_attr_;
  std::string enable_clamp_score_attr_;
  std::string ue_avoid_business_weight_discount_attr_;
  std::string perf_discount_attr_;
  std::string enable_equal_index_is_ad_attr_;

  std::shared_ptr<FollowMixAdGapChecker> ad_gap_checker_;
  DISALLOW_COPY_AND_ASSIGN(FollowMixEnsembleSortArranger);
};

}  // namespace platform
}  // namespace ks
