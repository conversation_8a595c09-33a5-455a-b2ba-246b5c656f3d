#include "dragon/src/processor/ext/follow_leaf/arranger/follow_mix_ensemble_sort_arranger.h"

#include <algorithm>
#include <functional>
#include <numeric>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/ext/follow_leaf/context/util.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {
RecoResultIter FollowMixEnsembleSortArranger::Arrange(
  MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) {
  Clear();
  Init(context, begin, end);
  if (end - begin == 1) {
    is_one_size_ = true;
  }
  if (ad_gap_checker_ == nullptr) {
    return end;
  }
  auto * biz_type_accessor = context->GetItemAttrAccessor("biz_type");
  for (size_t i = 0; i < result_size_; ++i) {
    // 判断当前位置是否可以是广告
    // ad_flag = ad_gap_checker.check(i, nullptr);
    RecoResultIter select_it = SelectBestOne(context, i, begin, end);
    if (select_it == end) {
      break;
    }
    int biz_type = static_cast<int>(Identify(context, i, *select_it));
    context->SetIntItemAttr(*select_it, biz_type_accessor, biz_type);
    ad_gap_checker_->Update(i, biz_type);
    new_result_vec_.push_back(*select_it);
  }
  Perf(context, new_result_vec_);
  return std::move(new_result_vec_.begin(), new_result_vec_.end(), begin);
}

void FollowMixEnsembleSortArranger::Perf(MutableRecoContextInterface *context,
    const std::vector<CommonRecoResult>& result_vec) {
  auto *biz_type_accessor = context->GetItemAttrAccessor("biz_type");
  auto *biz_type_list_accessor = context->GetItemAttrAccessor("biz_type_list");
  auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
  auto *cpm_accessor = context->GetItemAttrAccessor("ad_cpm");

  const std::string& tag_name = exp_tag_;
  int index = 0;
  int ad_live_count = 0;

  std::unordered_map<int, uint32> biz_type_count;
  std::unordered_map<int, uint32> biz_type_list_count;
  std::unordered_map<int, uint32> biz_type_list_top4_count;
  int type_size = static_cast<int>(FollowMix::BizType::TYPE_SIZE);
  for (int i = 1; i < type_size; i++) {
    biz_type_count[i] = 0;
    biz_type_list_count[i] = 0;
    biz_type_list_top4_count[i] = 0;
  }
  for (auto it = result_vec.begin(); it != result_vec.end(); ++it, ++index) {
    auto biz_type = context->GetIntItemAttr(*it, biz_type_accessor).value_or(0);
    auto item_type = context->GetIntItemAttr(*it, item_type_accessor).value_or(0);
    biz_type_count[biz_type]++;
    auto biz_type_list = context->GetIntListItemAttr(*it, biz_type_list_accessor);
    if (biz_type_list) {
      for (auto biz_type : *biz_type_list) {
        biz_type_list_count[biz_type]++;
        // perf top 4
        if (index < 4) {
          biz_type_list_top4_count[biz_type]++;
        }
      }
    }
    // perf top 6
    if (index < 6) {
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow", "follow.after.es.result",
        FollowMix::BizTypeToString(biz_type), std::to_string(index), std::to_string(item_type),
        tag_name, es_name_);
    }

    if (biz_type == static_cast<int>(FollowMix::BizType::AD_DSP)) {
      if (item_type == follow::LIVE_TYPE) {
        ad_live_count++;
      }
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1e+4 * context->GetDoubleItemAttr(
        *it, cpm_accessor).value_or(0.0), "reco.follow", "follow.after.es.ad_cpm", tag_name, es_name_);
    }
  }
  for (const auto& pair : biz_type_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      pair.second, "reco.follow", "follow.after.es.biz.type.counter",
      FollowMix::BizTypeToString(pair.first), tag_name, es_name_);
  }
  for (const auto& pair : biz_type_list_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      pair.second, "reco.follow", "follow.after.es.biz.type.list.counter",
      FollowMix::BizTypeToString(pair.first), tag_name, es_name_);
  }
  for (const auto& pair : biz_type_list_top4_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      pair.second, "reco.follow", "follow.after.es.biz.type.list.top4.counter",
      FollowMix::BizTypeToString(pair.first), tag_name, es_name_);
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      ad_live_count, "reco.follow", "follow.after.es.biz.type.counter", "ad_live", tag_name, es_name_);
}

void FollowMixEnsembleSortArranger::Init(MutableRecoContextInterface *context,
    RecoResultIter begin, RecoResultIter end) {
  is_perf_ = context->GetIntCommonAttr(is_perf_attr_).value_or(0) == 1;
  exp_tag_ = std::string(context->GetStringCommonAttr(exp_tag_attr_).value_or(""));
  perf_discount_ = context->GetDoubleCommonAttr(perf_discount_attr_).value_or(1.0);
  is_selected_vec_.clear();
  is_selected_vec_.resize(end - begin, false);
  InitAbParam(context);
  InitScoreInfos(context);
  InitGapchecker(context);

  mix_score_accessor_ = context->GetItemAttrAccessor("mix_score");
  next_feed_mix_score_accessor_ = context->GetItemAttrAccessor("next_feed_mix_score");
  ad_score_accessor_ = context->GetItemAttrAccessor("ad_score");
  ad_flag_accessor_ = context->GetItemAttrAccessor("ad_flag");
  natural_index_accessor_ = context->GetItemAttrAccessor("natural_index");
  biz_type_list_accessor_ = context->GetItemAttrAccessor("biz_type_list");
}

void FollowMixEnsembleSortArranger::InitGapchecker(MutableRecoContextInterface *context) {
  int adaptive_ad_gap_default = 7;
  std::vector<int> ad_gap_detail;
  std::vector<int> ad_fractile_detail;
  auto realtime_fractile_ad = context->GetPtrCommonAttr<const std::vector<double>>(
    "realtime_fractile_ad");
  bool enable_ad_gap_backup = context->GetIntCommonAttr(
    "enable_follow_mix_ad_gap_backup").value_or(0) == 1;
  bool enable_adaptive_ad_gap_detail =
    context->GetIntCommonAttr("enable_adaptive_ad_gap_detail").value_or(0) == 1;
  if (enable_adaptive_ad_gap_detail) {
    std::string merchant_user_type(context->GetStringCommonAttr(
      "merchant_user_type").value_or("U0-null"));
    auto user_type_adaptive_ad_gap_detail_ptr =
      context->GetStringCommonAttr("user_type_adaptive_ad_gap_detail_str");
    if (user_type_adaptive_ad_gap_detail_ptr) {
      std::string user_type_adaptive_ad_gap_detail_str = std::string(*user_type_adaptive_ad_gap_detail_ptr);
      GetAdaptiveAdGapDetailByUserType(merchant_user_type, user_type_adaptive_ad_gap_detail_str,
        &ad_fractile_detail, &ad_gap_detail, &adaptive_ad_gap_default);
    }
  }
  ad_gap_checker_ = std::make_shared<FollowMixAdGapChecker>(last_ad_pos_);
  ad_gap_checker_->SetDetail(adaptive_ad_gap_default,
      ad_fractile_detail, ad_gap_detail, enable_ad_gap_backup,
      realtime_fractile_ad);
}

void FollowMixEnsembleSortArranger::GetAdaptiveAdGapDetailByUserType(
    const std::string& user_type, const std::string& raw_param,
    std::vector<int>* frac_detail, std::vector<int>* gap_detail, int* ad_gap_default) {
  std::vector<std::string> param_type;
  base::SplitStringWithOptions(raw_param, ";", true, true, &param_type);
  for (const auto &type_value_str : param_type) {
    std::vector<std::string> type_value;
    base::SplitStringWithOptions(type_value_str, ",", true, true, &type_value);
    if (type_value.size() == 1 && type_value[0].size() > 0) {
      int default_gap = 0;
      if (absl::SimpleAtoi(type_value[0], &default_gap)) {
        *ad_gap_default = default_gap;
      }
    }
    if (type_value.size() > 1 && type_value[0] == user_type) {
      for (int idx = 1; idx < type_value.size(); idx++) {
        std::vector<std::string> detail;
        base::SplitStringWithOptions(type_value[idx], ":", true, true, &detail);
        int frac = 0, gap = 0;
        if (detail.size() == 2 && absl::SimpleAtoi(detail[0], &frac) && absl::SimpleAtoi(detail[1], &gap)) {
          frac_detail->push_back(frac);
          gap_detail->push_back(gap);
        }
      }
    }
  }
}

void FollowMixEnsembleSortArranger::InitAbParam(MutableRecoContextInterface *context) {
  merchant_avoid_business_weight_discount_ = context->GetDoubleCommonAttr(
      "follow_mix_ensemble_sort_merchant_avoid_business_weight_discount").value_or(1.0);
  gift_avoid_business_weight_discount_ = context->GetDoubleCommonAttr(
      "follow_mix_ensemble_sort_gift_avoid_business_weight_discount").value_or(1.0);
  ue_avoid_business_weight_discount_ = context->GetDoubleCommonAttr(
      ue_avoid_business_weight_discount_attr_).value_or(1.0);
  result_size_ = GetIntProcessorParameter(context, "result_size", 12);
  enable_identify_close_ad_flag_ = context->GetIntCommonAttr(
    close_identify_ad_flag_attr_).value_or(0) == 1;
  enable_clamp_score_ = context->GetIntCommonAttr(
    enable_clamp_score_attr_).value_or(0) == 1;
  enable_equal_index_is_ad_ = context->GetIntCommonAttr(
    enable_equal_index_is_ad_attr_).value_or(0) == 1;
}

void FollowMixEnsembleSortArranger::InitScoreInfos(MutableRecoContextInterface *context) {
  origin_score_infos_.clear();
  for (const auto& infos_attrs : score_infos_attrs_) {
    ScoreInfo score_info;
    score_info.score_accessor = context->GetItemAttrAccessor(infos_attrs.score_attr);
    score_info.weight = context->GetDoubleCommonAttr(infos_attrs.weight_attr).value_or(1.0);
    score_info.a = context->GetDoubleCommonAttr(infos_attrs.a_attr).value_or(0.0);
    score_info.b = context->GetDoubleCommonAttr(infos_attrs.b_attr).value_or(0.0);
    origin_score_infos_.emplace(infos_attrs.type, std::move(score_info));
  }
}

RecoResultIter FollowMixEnsembleSortArranger::SelectBestOne(MutableRecoContextInterface *context,
    int cur_index, RecoResultIter begin, RecoResultIter end) {
  int index = 0;
  double best_score = -1.0;
  double second_score = -1.0;
  RecoResultIter best_item = end;
  int best_index = -1;
  bool select_is_ad_flag = false;
  for (auto it = begin; it != end; it++, index++) {
    if (is_selected_vec_[index]) {
      // 已经被选走了
      continue;
    }
    double ad_score = context->GetDoubleItemAttr(*it, ad_score_accessor_).value_or(0.0);
    bool ad_flag = ad_gap_checker_->Check(cur_index, ad_score);
    // 更新最新分数
    double score = UpdateScore(context, cur_index, *it, ad_flag);
    if (best_item == end || score > best_score) {
      second_score = best_score;  // 原最大值降级为第二大值
      best_score = score;         // 更新最大值
      best_item = it;
      best_index = index;
      select_is_ad_flag = ad_flag;
    } else if (score <= best_score && score > second_score) {
      second_score = score;  // 更新第二大值
    }
  }
  if (best_index >= 0 && best_index < is_selected_vec_.size()) {
    is_selected_vec_[best_index] = true;
    context->SetDoubleItemAttr(*best_item, mix_score_accessor_, best_score);
    context->SetDoubleItemAttr(*best_item, next_feed_mix_score_accessor_, second_score);  // 第二位的分数
    if (select_is_ad_flag) {
      context->SetIntItemAttr(*best_item, ad_flag_accessor_, 1);
    }
  }
  return best_item;
}

double FollowMixEnsembleSortArranger::UpdateScore(MutableRecoContextInterface *context,
     int cur_index, const CommonRecoResult &item_result, bool ad_flag) {
  std::unordered_set<FollowMix::BizType> black_type_set;
  if (!ad_flag) {
    black_type_set.insert(FollowMix::BizType::AD_DSP);
  }

  // 电商避让商业化
  std::unordered_map<FollowMix::BizType, double> type_weight_discount_map;
  if (merchant_avoid_business_weight_discount_ != 1.0 && ad_flag) {
    type_weight_discount_map.emplace(FollowMix::BizType::MERCHANT,
      merchant_avoid_business_weight_discount_);
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad_avoid", "merchant", exp_tag_, es_name_);
  }
  // 秀场避让商业化
  if (gift_avoid_business_weight_discount_ != 1.0 && ad_flag) {
    type_weight_discount_map.emplace(FollowMix::BizType::GIFT,
      gift_avoid_business_weight_discount_);
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad_avoid", "gift", exp_tag_, es_name_);
  }
  // uescore 避让商业化 for 实验
  if (ue_avoid_business_weight_discount_ != 1.0 && ad_flag) {
    type_weight_discount_map.emplace(FollowMix::BizType::NATURAL,
      ue_avoid_business_weight_discount_);
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad_avoid", "natural", exp_tag_, es_name_);
  }
  return CalcMixScore(context, item_result, black_type_set, type_weight_discount_map);
}

double FollowMixEnsembleSortArranger::CalcMixScore(MutableRecoContextInterface *context,
    const CommonRecoResult &item_result,
    const std::unordered_set<FollowMix::BizType>& black_type_set,
    const std::unordered_map<FollowMix::BizType, double>& type_weight_discount_map) {
  double mix_score = 1.0;
  for (const auto& pair : origin_score_infos_) {
    if (black_type_set.find(pair.first) != black_type_set.end()) {
      // 在黑名单里的不计算
      continue;
    }
    const auto& score_info = pair.second;
    double score = context->GetDoubleItemAttr(item_result, score_info.score_accessor).value_or(0.0);
    auto it = type_weight_discount_map.find(pair.first);
    double weight = score_info.weight;
    if (it != type_weight_discount_map.end()) {
       weight *= it->second;
    }
    double buss_score = std::pow(weight * score + score_info.a, score_info.b);
    // 命中商品全站染色物料的加权分的指数项置 0
    // if (color_ab_disable_store_wide_photo_ && item->is_store_wide_ && !item->is_live_
    //     && index == FollowMix::BizType::STORE_WIDE) {
    //   buss_score = std::pow(score_info.weight * score + score_info.a, 0);
    // }
    buss_score =  enable_clamp_score_ ? std::max(buss_score, 1.0) : Clamp(buss_score, 0.0, 1e10);
    if (buss_score > 0) {
      mix_score *= buss_score;
    }
    if (is_perf_) {
      auto biz_type_ptr = context->GetIntListItemAttr(item_result, biz_type_list_accessor_);
      std::unordered_set<FollowMix::BizType> biz_type_set;
      if (biz_type_ptr) {
        for (auto biz_type : *biz_type_ptr) {
          biz_type_set.insert(FollowMix::BizType(biz_type));
        }
      }
      if (biz_type_set.find(pair.first) != biz_type_set.end()) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
          buss_score * 1e4, "reco.follow", "follow.biz.es.score",
          FollowMix::BizTypeToString(pair.first), es_name_, exp_tag_);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
          score * 1e5, "reco.follow", "follow.biz.es.origin.score",
          FollowMix::BizTypeToString(pair.first), es_name_, exp_tag_);
      }
    }
  }
  return mix_score;
}

FollowMix::BizType FollowMixEnsembleSortArranger::Identify(MutableRecoContextInterface *context,
   int current_index, const CommonRecoResult &result) {
  // 每一个 item 可能属于多个 biz
  auto biz_type_ptr = context->GetIntListItemAttr(result, biz_type_list_accessor_);
  std::unordered_set<FollowMix::BizType> biz_type_set;
  if (biz_type_ptr) {
    for (auto biz_type : *biz_type_ptr) {
      biz_type_set.insert(FollowMix::BizType(biz_type));
    }
  }

  int natural_index = context->GetIntItemAttr(result, natural_index_accessor_).value_or(10000);
  bool ad_flag = enable_identify_close_ad_flag_
    ||  context->GetIntItemAttr(result, ad_flag_accessor_).value_or(0) == 1;
  if (biz_type_set.find(FollowMix::BizType::AD_DSP) != biz_type_set.end()) {
    if (ad_flag) {
      if (JudgeAdType(context, result, current_index, natural_index)) {
        return FollowMix::BizType::AD_DSP;
      }
    } else {
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "filter.gap", exp_tag_, es_name_);
    }
  }
  if (biz_type_set.find(FollowMix::BizType::STORE_WIDE) != biz_type_set.end() &&
     JudgeStoreWide(context, result, current_index, natural_index)) {
    return FollowMix::BizType::STORE_WIDE;
  }
  if (biz_type_set.find(FollowMix::BizType::LOCAL_LIFE) != biz_type_set.end() &&
      JudgeLocalLife(context, result, current_index, natural_index)) {
    return FollowMix::BizType::LOCAL_LIFE;
  }
  if (biz_type_set.find(FollowMix::BizType::NATURAL) != biz_type_set.end()) {
    return FollowMix::BizType::NATURAL;
  }
  // if (biz_type_set.find(FollowMix::BizType::MERCHANT) != biz_type_set.end()) {
  //   return FollowMixRankResult::MERCHANT;
  // }
  // if (biz_type_set.find(FollowMix::BizType::MERCHANT_CART) != biz_type_set.end()) {
  //   return FollowMixRankResult::MERCHANT_CART;
  // }
  // if (biz_type_set.find(FollowMix::BizType::GIFT) != biz_type_set.end()) {
  //   return FollowMixRankResult::GIFT;
  // }
  // if (biz_type_set.find(FollowMix::BizType::RECRUIT_PHOTO) != biz_type_set.end()) {
  //   return FollowMixRankResult::RECRUIT_PHOTO;
  // }
  // if (biz_type_set.find(FollowMix::BizType::RECRUIT_LIVE) != biz_type_set.end()) {
  //   return FollowMixRankResult::RECRUIT_LIVE;
  // }
  return FollowMix::BizType::NATURAL;
}

bool FollowMixEnsembleSortArranger::JudgeAdType(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index) {
  static std::unordered_set<FollowMix::BizType> no_ad_type_set = {FollowMix::BizType::AD_DSP};
  static std::unordered_map<FollowMix::BizType, double> type_weight_discount_map;
  double ad_score = context->GetDoubleItemAttr(result, ad_score_accessor_).value_or(0.0);
  if (natural_index > cur_index) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "up.index", exp_tag_, es_name_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(ad_score * 1e4, "reco.follow",
        "follow.mix.ad.judge.ad_score", "up.index", es_name_, exp_tag_);
    return true;
  } else if (natural_index == cur_index) {
    if (enable_equal_index_is_ad_) {
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "equal.up", exp_tag_, es_name_);
      base::perfutil::PerfUtilWrapper::IntervalLogStash(ad_score * 1e4, "reco.follow",
        "follow.mix.ad.judge.ad_score", "equal.up", es_name_, exp_tag_);
      return true;
    }
    double mix_score_no_ad = CalcMixScore(context, result, no_ad_type_set, type_weight_discount_map);
    double next_feed_score = context->GetDoubleItemAttr(
      result, next_feed_mix_score_accessor_).value_or(0.0);
    double mix_score = context->GetDoubleItemAttr(result, mix_score_accessor_).value_or(0.0);
    if (next_feed_score == -1) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(is_one_size_, "reco.follow",
        "follow.mix.ad.judge.index_equal", "is_one_size", es_name_, exp_tag_);
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(mix_score_no_ad * perf_discount_, "reco.follow",
        "follow.mix.ad.judge.index_equal", "no_ad_score", es_name_, exp_tag_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(next_feed_score * perf_discount_, "reco.follow",
        "follow.mix.ad.judge.index_equal", "next_score_gap", es_name_, exp_tag_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((mix_score - mix_score_no_ad) * perf_discount_,
        "reco.follow", "follow.mix.ad.judge.index_equal", "mix-no_ad", es_name_, exp_tag_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((mix_score - next_feed_score) * perf_discount_,
        "reco.follow", "follow.mix.ad.judge.index_equal", "mix-next", es_name_, exp_tag_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((mix_score_no_ad - next_feed_score) * perf_discount_,
        "reco.follow", "follow.mix.ad.judge.index_equal", "no_ad-next", es_name_, exp_tag_);
    if (mix_score_no_ad < next_feed_score || is_one_size_) {  // 冲突但保序部分
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "up.score", exp_tag_, es_name_);
      base::perfutil::PerfUtilWrapper::IntervalLogStash(ad_score * 1e4, "reco.follow",
        "follow.mix.ad.judge.ad_score", "up.score", es_name_, exp_tag_);
      return true;
    }
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "filter.score", exp_tag_, es_name_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(ad_score * 1e4, "reco.follow",
        "follow.mix.ad.judge.ad_score", "filter.score", es_name_, exp_tag_);
  } else {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow",
        "follow.mix.ad.judge", "filter.index", exp_tag_, es_name_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(ad_score * 1e4, "reco.follow",
        "follow.mix.ad.judge.ad_score", "filter.index", es_name_, exp_tag_);
  }
  return false;
}

bool FollowMixEnsembleSortArranger::JudgeStoreWide(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index) {
  if (natural_index > cur_index) {
    return true;
  }
  return false;
}

bool FollowMixEnsembleSortArranger::JudgeLocalLife(MutableRecoContextInterface *context,
    const CommonRecoResult &result, int cur_index, int natural_index) {
  if (natural_index > cur_index) {
    return true;
  }
  return false;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMixEnsembleSortArranger,
                 FollowMixEnsembleSortArranger)
}  // namespace platform
}  // namespace ks
