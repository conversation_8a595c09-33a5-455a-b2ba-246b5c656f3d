#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class NrCopyCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  struct AttrConfig {
    std::string from_common;
    std::string from_item;
    std::string to_common;
    std::string to_item;
    bool overwrite = true;
  };

  NrCopyCommonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    to_common_ = config()->GetString("to_common_attr");
    return true;
  }


 private:
  std::string to_common_;

  DISALLOW_COPY_AND_ASSIGN(NrCopyCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
