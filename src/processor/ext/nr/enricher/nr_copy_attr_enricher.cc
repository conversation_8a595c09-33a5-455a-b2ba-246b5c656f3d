#include "dragon/src/processor/ext/nr/enricher/nr_copy_attr_enricher.h"

namespace ks {
namespace platform {

void NrCopyCommonAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  auto from_common_attr = GetStringProcessorParameter(context, "from_common_attr", "");
  auto *from_common_acc = context->GetCommonAttrAccessor(from_common_attr);
  switch (from_common_acc->value_type) {
    case AttrType::INT: {
      if (auto int_val = from_common_acc->GetIntValue()) {
        if (!to_common_.empty()) {
          context->SetIntCommonAttr(to_common_, *int_val);
        }
      }
      break;
    }
    case AttrType::FLOAT: {
      if (auto double_val = from_common_acc->GetDoubleValue()) {
        if (!to_common_.empty()) {
          context->SetDoubleCommonAttr(to_common_, *double_val);
        }
      }
      break;
    }
    case AttrType::STRING: {
      if (auto str_val = from_common_acc->GetStringValue()) {
        if (!to_common_.empty()) {
          context->SetStringCommonAttr(to_common_, {str_val->data(), str_val->size()});
        }
      }
      break;
    }
    case AttrType::INT_LIST: {
      if (auto int_list = from_common_acc->GetIntListValue()) {
        if (!to_common_.empty()) {
          context->SetIntListCommonAttr(to_common_, {int_list->begin(), int_list->end()});
        }
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      if (auto double_list = from_common_acc->GetDoubleListValue()) {
        if (!to_common_.empty()) {
          context->SetDoubleListCommonAttr(to_common_, {double_list->begin(), double_list->end()});
        }
      }
      break;
    }
    case AttrType::STRING_LIST: {
      if (auto str_list = from_common_acc->GetStringListValue()) {
        if (!to_common_.empty()) {
          std::vector<std::string> vec;
          vec.reserve(str_list->size());
          for (const auto &str : *str_list) {
            vec.emplace_back(str.data(), str.size());
          }
          context->SetStringListCommonAttr(to_common_, std::move(vec));
        }
      }
      break;
    }
    default:
      CL_LOG(INFO) << "processor: " << GetName()
                   << " copy common_attr skipped as common_attr is missing: " << from_common_attr;
      break;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NrCopyCommonAttrEnricher, NrCopyCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
