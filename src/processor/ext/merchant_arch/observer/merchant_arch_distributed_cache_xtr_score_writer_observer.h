#pragma once

#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_observer.h"
#include "dynamic_kafka_client/dynamic_kafka_client.h"
#include "ks/action/client/sample_join_client.h"

namespace ks {
namespace platform {

// 计算 kafka 分片 id, 计算逻辑与 cache part_id 一致
class CachePartitionerCb : public RdKafka::PartitionerCb {
 public:
  CachePartitionerCb() {}
  ~CachePartitionerCb() override {}

  int32_t partitioner_cb(const RdKafka::Topic *topic, const std::string *key, int32_t part_num,
                         void *msg_opaque) override {
    uintptr_t intPtr = reinterpret_cast<uintptr_t>(msg_opaque);

    int32_t part_id = 0;
    if (intPtr > static_cast<uintptr_t>(INT32_MAX) || intPtr < static_cast<uintptr_t>(INT32_MIN)) {
      part_id = static_cast<int32_t>(intPtr % (INT32_MAX));
      if (part_id < 0) {
        part_id += (INT32_MAX);
      }
    } else {
      part_id = static_cast<int32_t>(intPtr);
    }
    return part_id;
  }

  static std::shared_ptr<CachePartitionerCb> &GetInstance() {
    static std::shared_ptr<CachePartitionerCb> cache_partitioner_cb = std::make_shared<CachePartitionerCb>();
    return cache_partitioner_cb;
  }
};

class MerchantArchDistributedCacheXtrScoreWriterObserver : public CommonRecoBaseObserver {
 public:
  MerchantArchDistributedCacheXtrScoreWriterObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

  void BuildScoreAtttrs(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);

  void ClearInit() {
    kconf_path_.clear();
    table_name_.clear();
    kafka_topic_.clear();
    attr_prefix_.clear();
    expire_seconds_attr_.clear();
    origin_attr_name_map_.clear();
    photo_store_kconf_key_.clear();
    part_num_ = 1000;
    get_table_name_from_kconf_ = false;
    use_request_type_ = true;
    partition_with_uid_ = false;
  }

  void ClearEnrich() {
    item_attr_pb_strings_.clear();
    for (auto &pair : batch_common_index_doc_map_) {
      delete pair.second;  // 释放指针指向的对象
    }
    batch_common_index_doc_map_.clear();
    request_id_.clear();
  }

 private:
  int part_num_ = 1000;
  std::string kconf_path_;
  std::string photo_store_kconf_key_;
  std::string kafka_topic_;
  std::string table_name_;
  std::string attr_prefix_;
  std::vector<std::string> item_attr_pb_strings_;
  std::shared_ptr<ks::infra::kfk::Producer> kafka_producer_;
  folly::F14FastMap<int, BatchCommonIndexDoc *> batch_common_index_doc_map_;
  // <leaf_attr_name, kconf_origin_name>
  folly::F14FastMap<std::string, std::string> origin_attr_name_map_;
  std::string request_id_;
  std::string expire_seconds_attr_;
  bool get_table_name_from_kconf_ = false;
  bool use_request_type_ = true;
  bool partition_with_uid_ = false;

  bool InitProcessor() override;
  bool ParseKconfConfig();
  bool ParseTableNameFromPhotoStoreKconf();
  bool InitKafkaClient();
  bool SendToKafka();
  uint64 HashStringKey(const std::string &key);
  bool HasPrefix(const std::string &str, const std::string &prefix);
  void RemovePrefix(const std::vector<std::string> &input, const std::string &prefix);

  DISALLOW_COPY_AND_ASSIGN(MerchantArchDistributedCacheXtrScoreWriterObserver);
};

}  // namespace platform
}  // namespace ks
