#include "dragon/src/processor/ext/merchant_arch/observer/merchant_arch_distributed_cache_xtr_score_writer_observer.h"
#include <sys/types.h>
#include <memory>
#include <vector>
#include <unordered_set>
#include <ctime>
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kenv/service_meta.h"


namespace ks {
namespace platform {
bool MerchantArchDistributedCacheXtrScoreWriterObserver::InitProcessor() {
  ClearInit();

  kconf_path_ = config()->GetString("kconf_path");
  if (kconf_path_.empty()) {
    LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreWriterObserver init failed! "
                  "kconf_path config need set";
    return false;
  }
  table_name_ = config()->GetString("table_name");
  if (table_name_.empty()) {
    LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreWriterObserver init failed! "
                  "table_name config need set";
    return false;
  }
  // 从 kconf 中获取 table_name，基于 kconf 实现一套代码，不同集群配置不同的 table_name
  get_table_name_from_kconf_ = config()->GetBoolean("get_table_name_from_kconf", false);
  if (get_table_name_from_kconf_) {
    photo_store_kconf_key_ = config()->GetString("photo_store_kconf_key");
    if (photo_store_kconf_key_.empty()) {
      CL_LOG_ERROR("distributed_cache", "empty_photo_store_kconf_key")
          << "MerchantDistributedCacheXtrScoreReaderEnricher init failed! photo_store_kconf_key is empty";
      return false;
    }
    ParseTableNameFromPhotoStoreKconf();
  }
  // 超时时间 item attr
  expire_seconds_attr_ = config()->GetString("expire_seconds_attr");

  use_request_type_ = config()->GetBoolean("use_request_type", true);
  partition_with_uid_ = config()->GetBoolean("partition_with_uid", false);

  // 解析 kconf 配置，获取对应业务配置
  if (!ParseKconfConfig()) {
    LOG(ERROR) << "ParseKconfConfig failed";
    return false;
  }
  // 初始化 kafka client
  if (!InitKafkaClient()) {
    LOG(ERROR) << "InitKafkaClient failed";
    return false;
  }

  return true;
}

bool MerchantArchDistributedCacheXtrScoreWriterObserver::HasPrefix(const std::string& str,
          const std::string& prefix) {
    return str.rfind(prefix, 0) == 0;      // 检查字符串是否以 prefix 开头
}

void MerchantArchDistributedCacheXtrScoreWriterObserver::RemovePrefix(const std::vector<std::string>& input,
        const std::string& prefix) {
    origin_attr_name_map_.reserve(input.size());
    for (const auto& str : input) {
        if (HasPrefix(str, prefix)) {
          origin_attr_name_map_.insert({str.substr(prefix.length()), str});
        } else {
            LOG_EVERY_N(ERROR, 5000) << "attr_name " << str <<" does not has biz name perfix";
            continue;
        }
    }
}

bool MerchantArchDistributedCacheXtrScoreWriterObserver::InitKafkaClient() {
  if (!kafka_topic_.empty()) {
    kafka_producer_ = ks::infra::kfk::DynamicKafkaClient::GetProducerByLogicalTopicId(kafka_topic_, "",
      CachePartitionerCb::GetInstance());
    if (!kafka_producer_) {
      LOG(ERROR) << "request_id_: " << request_id_ << " kafka_client_fail: " + kafka_topic_
      << "failed to get kafka producer client for topic: " << kafka_topic_;
      return false;
    }
  } else {
    LOG(ERROR) << "request_id_: " << request_id_ << " kafka_client_fail: need to set kafka topic";
    return false;
  }
  return true;
}

bool MerchantArchDistributedCacheXtrScoreWriterObserver::ParseKconfConfig() {
  auto default_val = std::make_shared<::Json::Value>();
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> cache_configs;
  cache_configs = ks::infra::KConf().Get(kconf_path_, default_val);

  if (!cache_configs || !cache_configs->Get()) {
    LOG(ERROR) << "request_id_: " << request_id_ << " init kconf data config failed! kconf name is " \
        << kconf_path_;
    return false;
  }

  std::shared_ptr<::Json::Value> json_config = cache_configs->Get();

  if (json_config->isObject()) {
    try {
        const ::Json::Value& configValue = *json_config;

        auto cache_config = configValue[table_name_];
        std::vector<std::string> origin_attr_names;
        for (const auto& attr : cache_config["active_attrs"]) {
            origin_attr_names.push_back(attr.asString());
        }

        kafka_topic_ = cache_config["kafka_topic"].asString();

        attr_prefix_ = cache_config["attr_prefix"].asString();
        attr_prefix_ += '_';

        RemovePrefix(origin_attr_names, attr_prefix_);
    } catch(const std::exception& e) {
        LOG(ERROR) << "request_id_: " << request_id_ << ", catch exception:" << e.what();
        return false;
    } catch (...) {  // 捕获所有其他类型的异常
        LOG(ERROR) << "request_id_: " << request_id_ << ", catch unknown exception";
        return false;
    }
  } else {
    LOG(ERROR) << "request_id_: " << request_id_
          << " json_config expected to be a json but got something else.";
    return false;
  }

  return true;
}

bool MerchantArchDistributedCacheXtrScoreWriterObserver::ParseTableNameFromPhotoStoreKconf() {
  auto default_val = std::make_shared<::Json::Value>();
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> cache_configs;
  cache_configs = ks::infra::KConf().Get(photo_store_kconf_key_, default_val);

  if (!cache_configs || !cache_configs->Get()) {
    CL_LOG_ERROR("distributed_cache", "init_kconf_data_config_failed")
        << "MerchantDistributedCacheXtrScoreReaderEnricher init failed! kconf name is "
        << photo_store_kconf_key_;
    return false;
  }

  std::shared_ptr<::Json::Value> json_config = cache_configs->Get();

  if (json_config->isObject()) {
    try {
        const ::Json::Value &configValue = *json_config;

        auto table_name = configValue["ps_client_table_name"];
        table_name_ = table_name.asString();
    } catch (const std::exception &e) {
        CL_LOG_ERROR("distributed_cache", "parse_table_name_from_photo_store_kconf_failed")
            << "MerchantDistributedCacheXtrScoreReaderEnricher init failed! parse table_name from "
               "photo_store_kconf failed"
            << "request_id_: " << request_id_ << ", catch exception:" << e.what();
        return false;
    } catch (...) {  // 捕获所有其他类型的异常
        CL_LOG_ERROR("distributed_cache", "parse_table_name_from_photo_store_kconf_failed")
            << "MerchantDistributedCacheXtrScoreReaderEnricher init failed! parse table_name from "
               "photo_store_kconf failed"
            << "request_id_: " << request_id_ << ", catch unknown exception";
        return false;
    }
  } else {
    CL_LOG_ERROR("distributed_cache", "parse_table_name_from_photo_store_kconf_failed")
        << "MerchantDistributedCacheXtrScoreReaderEnricher init failed! parse table_name from "
           "photo_store_kconf failed"
        << "request_id_: " << request_id_ << ", json_config expected to be a json but got something else.";
    return false;
  }

  return true;
}

uint64 MerchantArchDistributedCacheXtrScoreWriterObserver::HashStringKey(const std::string& key) {
  std::hash<std::string> hash_fn;
  size_t hash_value = hash_fn(key);
  if (hash_value > INT64_MAX) {
    // 如果哈希值超过 INT64_MAX，取模
    hash_value = hash_value % INT64_MAX;
  }
  return static_cast<uint64>(hash_value);
}

void MerchantArchDistributedCacheXtrScoreWriterObserver::BuildScoreAtttrs
        (ReadableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  // generate map <origin_attr_name, attr_accessor>
  folly::F14FastMap<std::string, ItemAttr *> attr_name_accessors_map(origin_attr_name_map_.size());
  for (const auto& [leaf_attr_name, kconf_origin_name] : origin_attr_name_map_) {
    attr_name_accessors_map.insert({kconf_origin_name, context->GetItemAttrAccessor(leaf_attr_name)});
  }
  auto expire_seconds_accessors =
      expire_seconds_attr_.empty() ? nullptr : context->GetItemAttrAccessor(expire_seconds_attr_);

  uint64 user_id = context->GetUserId();
  if (!user_id || user_id == 0) {
    LOG(ERROR) << "invalid user id!";
    return;
  }
  // generate key_prefix, key = request_type + user_id + item_id
  std::string request_type = context->GetRequestType();
  std::string key_pefix;
  if (use_request_type_) {
    key_pefix = request_type + base::Uint64ToString(user_id);
  } else {
    key_pefix = base::Uint64ToString(user_id);
  }
  uint64 hash_key = HashStringKey(key_pefix);
  uint64 key;

  for (auto it = begin; it != end; ++it) {
    CommonIndexDoc* doc;
    doc = nullptr;
    auto &result = *it;

    if (partition_with_uid_) {
        // 保证同一个 user_id 的 item 一定会落在同一个 partition
        key = result.GetId() * 1000 + hash_key;
    } else {
        std::string string_key = key_pefix + base::Uint64ToString(result.GetId());
        key = HashStringKey(string_key);
    }

    int part_id = part_num_ == 0 ? key % 1000 : key % part_num_;
    auto iter = batch_common_index_doc_map_.find(part_id);
    BatchCommonIndexDoc* batch_doc;
    batch_doc = nullptr;

    if (iter != batch_common_index_doc_map_.end()) {
      batch_doc = iter->second;
    } else {
      batch_doc = new BatchCommonIndexDoc();
      batch_common_index_doc_map_[part_id] = batch_doc;
    }
    doc = batch_doc->add_doc();

    if (doc != nullptr) {
      doc->set_key(key);

      if (expire_seconds_accessors != nullptr) {
            auto expire_seconds = result.GetIntAttr(expire_seconds_accessors);
            if (expire_seconds && *expire_seconds > 0) {
              doc->set_expire_seconds(*expire_seconds);
            }
      }

      for (const auto& [attr_name, attr_accessor] : attr_name_accessors_map) {
        bool is_attr_valid = true;
        auto attr = doc->add_attr();
        if (attr != nullptr && attr_accessor != nullptr) {
          if (attr_accessor->value_type == AttrType::FLOAT) {
            auto attr_value = result.GetDoubleAttr(attr_accessor);
            if (!attr_value) {
              is_attr_valid = false;
            } else {
              attr->set_key(attr_name);
              attr->set_type(CommonIndexEnum_AttrType_FLOAT_ATTR);
              attr->set_float_value(*attr_value);
            }
          } else if (attr_accessor->value_type == AttrType::INT) {
            auto attr_value = result.GetIntAttr(attr_accessor);
            if (!attr_value) {
              is_attr_valid = false;
            } else {
              attr->set_key(attr_name);
              attr->set_type(CommonIndexEnum_AttrType_INT_ATTR);
              attr->set_int_value(*attr_value);
            }
          } else if (attr_accessor->value_type == AttrType::STRING) {
            auto attr_value = result.GetStringAttr(attr_accessor);
            if (!attr_value) {
              is_attr_valid = false;
            } else {
              attr->set_key(attr_name);
              attr->set_type(CommonIndexEnum_AttrType_STRING_ATTR);
              attr->set_string_value((*attr_value).data());
            }
          } else {
            LOG_EVERY_N(ERROR, 5000) << "request_id_: " << request_id_
                << "attr type invalid or attr doesnt exist! invalid attr_name: " << attr_name;
            is_attr_valid = false;
          }
        } else {
          LOG_EVERY_N(ERROR, 5000) << "request_id_: " << request_id_ << " attr is nullptr!";
          is_attr_valid = false;
        }
        if (!is_attr_valid) {
          doc->mutable_attr()->RemoveLast();
          LOG_EVERY_N(ERROR, 5000) << "request_id_: " << request_id_ << "attr invalid, abandon this attr.";
        }
      }
    } else {
      batch_doc->mutable_doc()->RemoveLast();
      LOG_EVERY_N(ERROR, 5000) << "request_id_: " << request_id_ << " doc is nullptr! "
            << " part_id: " << part_id << " key: " << key << " part_num: " << part_num_;
    }
  }
}

bool MerchantArchDistributedCacheXtrScoreWriterObserver::SendToKafka() {
  int send_value_count = 0;
  int zero_count = 0;
  for (const auto& [part_id, batch_doc] : batch_common_index_doc_map_) {
    thread_local std::string value;
    value.clear();
    try {
        if (batch_doc == nullptr) {
          LOG_EVERY_N(ERROR, 5000) << "batch_doc is null.";
          continue;
        }
        value = batch_doc->SerializeAsString();
    } catch (const std::exception& e) {
        LOG_EVERY_N(ERROR, 5000) << "Serialization failed, catch exception: " << e.what();
    }

    if (value.size() == 0) {
      zero_count++;
    }
    send_value_count += value.size();

    if (kafka_producer_) {
      RdKafka::ErrorCode err =
          kafka_producer_->Produce(value, reinterpret_cast<void*>(static_cast<uintptr_t>(part_id)));
      if (err != RdKafka::ERR_NO_ERROR) {
        std::string err_str = RdKafka::err2str(err);
        LOG_EVERY_N(ERROR, 5000) << "request_id_: " << request_id_ << " send kafka part_id "
              << part_id << " value failed! : err_str" << err_str;
      }
    } else {
      LOG(ERROR) << "kafka_producer_ is nullptr";
      return false;
    }
  }
  return true;
}

void MerchantArchDistributedCacheXtrScoreWriterObserver::Observe(ReadableRecoContextInterface *context,
                                                          RecoResultConstIter begin,
                                                          RecoResultConstIter end) {
  ClearEnrich();
  request_id_ = context->GetRequestId();

  BuildScoreAtttrs(context, begin, end);

  SendToKafka();
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantArchDistributedCacheXtrScoreWriterObserver,
                 MerchantArchDistributedCacheXtrScoreWriterObserver)
}  // namespace platform
}  // namespace ks
