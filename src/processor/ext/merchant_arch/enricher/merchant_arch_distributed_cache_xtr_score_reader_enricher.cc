#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_arch_distributed_cache_xtr_score_reader_enricher.h"
#include <cstdint>
#include <string>

#include "kconf/kconf.h"
#include "kess/rpc/rpc_facade.h"
#include "ks/action/kuiba_predict_photo_store_item.h"
#include "ks/reco_proto/distributed_photo_info/photo_info_service.kess.grpc.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_defaults.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"
#include "ks/serving_util/kess_grpc_client.h"
#include "third_party/abseil/absl/hash/internal/city.h"

namespace ks {
namespace platform {
DEFINE_bool(
    slide_new_cache_photo_store_fetch_score_attr_only, false,
    "MerchantArchDistributedCacheXtrScoreReaderEnricher use adaptive "
    "attr list instead all attrs calling index");

template <>
std::function<void()>
MerchantArchDistributedCacheXtrScoreReaderEnricher<ks::reco::protoutil::AttrKVItem>::Purge() {
  return [this]() {
    DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAttrKVItemFactory>::Singleton()->Stop();
  };
}

template <>
ks::photo_store::DynamicPhotoStore *
MerchantArchDistributedCacheXtrScoreReaderEnricher<ks::reco::protoutil::AttrKVItem>::GetDynamicPhotoStore() {
  auto attr_meta_data = FlatCacheAttrMetaDataHolder::Singleton()->GetAttrMetaData();
  // 初始化 photo store 需要传入构造好的 factory 类
  auto factory_ptr = std::make_shared<reco::protoutil::DynamicAttrKVItemFactory>(attr_meta_data);
  if (FLAGS_slide_new_cache_photo_store_fetch_score_attr_only) {
    std::set<std::string> attrs;
    for (const auto &pair : *attr_meta_data) {
      attrs.insert(pair.first);
    }
    return DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAttrKVItemFactory>::Singleton()
        ->GetDynamicPhotoStore(photo_store_kconf_key_, factory_ptr, &attrs);
  } else {
    return DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAttrKVItemFactory>::Singleton()
        ->GetDynamicPhotoStore(photo_store_kconf_key_, factory_ptr);
  }
}

template <>
void MerchantArchDistributedCacheXtrScoreReaderEnricher<ks::reco::protoutil::AttrKVItem>::
                              SpecializedEnrichInit(MutableRecoContextInterface *context) {
  for (auto &data : attr_kv_special_data_) {
    data.accessor = context->GetItemAttrAccessor(data.name);
  }
}

template <>
bool MerchantArchDistributedCacheXtrScoreReaderEnricher<
    ks::reco::protoutil::AttrKVItem>::SpecializedInitProcess() {
  for (int retry_times = 0; retry_times < 10; ++retry_times) {
    auto *attr_name_types = config()->Get("attr_name_types");
    bool attr_name_types_exists = attr_name_types && attr_name_types->IsArray();
    if (!attr_name_types_exists) {
      LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
                    "need to set attr_name_types";
      return false;
    }

    for (const auto *attr : attr_name_types->array()) {
      std::string attr_name;
      std::string attr_type_str;
      if (attr->IsObject()) {
        attr_name = attr->GetString("name");
        attr_type_str = attr->GetString("type");
        if (attr_name.empty() || attr_type_str.empty()) {
          LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
                        "Invalid 'attrs' config. "
                        "must as {\"name\": \"attr_name\", \"type\":\"int\" } format";
          return false;
        }
      } else {
        LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
                      "Invalid 'attrs' config. "
                      "must as {\"name\": \"attr_name\", \"type\":\"int\" } format";
        return false;
      }
      if (item_score_attrs_names_.find(attr_prefix_ + attr_name) == item_score_attrs_names_.end()) {
        LOG_EVERY_N(ERROR, 5000) << "attr = " << attr_name << " does not exist in kconf, please add "
                                 << attr_prefix_ + attr_name << " to kconf active_attrs!";
        continue;
      }
      if (attr_type_str != "string" && attr_type_str != "double" && attr_type_str != "int") {
        LOG_EVERY_N(ERROR, 5000) << "attr type invalid, plesase choose string double or int. invalid type: "
                                 << attr_type_str;
        continue;
      }

      uint32 attr_key = 0;
      if (!CalculateAttrKey(attr_prefix_ + attr_name, attr_type_str, &attr_key)) {
        LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher: CalculateAttrKey failed! ";
        return false;
      }
      attr_kv_special_data_.emplace_back(
          AttrKVSpecialData{.name = attr_name, .key = attr_key, .accessor = nullptr});
    }

    attr_path_config_.clear();
    attr_path_config_.reserve(attr_kv_special_data_.size());
    for (auto &data : attr_kv_special_data_) {
      attr_path_config_.emplace_back(
          PhotoInfoAttrConfig{.name = data.name, .path = std::vector<int>(), .accessor = nullptr});
    }

    return true;
  }
  LOG(ERROR) << "SpecializedInitProcess failed";
  return false;
}

template <>
void MerchantArchDistributedCacheXtrScoreReaderEnricher<ks::reco::protoutil::AttrKVItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item_ptr) {
  auto *item = dynamic_cast<ks::reco::protoutil::AttrKVItem *>(item_ptr);
  if (!item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  auto item_data = item->GetItemData();
  if (!item_data || !item_data->reader) {
    CL_LOG_EVERY_N(WARNING, 1000) << "AttrKV item no reader, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  int64 int_val = 0;
  float float_val = 0.0;
  std::string str_val;

  int32 cache_hit_flag = 1;
  for (int i = 0; i < attr_kv_special_data_.size(); ++i) {
    const auto &data = attr_kv_special_data_[i];
    uint32 attr_key = data.key;
    auto attr_type = ks::reco::protoutil::GetAttrTypeFromAttrKey(attr_key);
    auto accessor = data.accessor;
    if (!accessor) {
      LOG_EVERY_N(ERROR, 5000) << "get nullptr accessor attr_name = " << data.name;
      continue;
    }
    auto reader = item_data->reader;
    auto message_it = item_data->message_map.find(attr_key);

    switch (attr_type) {
      case ks::reco::protoutil::AttrType::kInt:
        if (reader->GetIntValue(attr_key, &int_val)) {
          result.SetIntAttr(accessor, int_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else {
          cache_hit_flag = 0;
        }
        break;

      case ks::reco::protoutil::AttrType::kFloat:
        if (reader->GetFloatValue(attr_key, &float_val)) {
          result.SetDoubleAttr(accessor, float_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else {
          cache_hit_flag = 0;
        }
        break;

      case ks::reco::protoutil::AttrType::kString:
        str_val.clear();
        if (message_it != item_data->message_map.end() && message_it->second) {
          result.SetPtrAttr(accessor, message_it->second);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else if (reader->GetStringValue(attr_key, &str_val)) {
          result.SetStringAttr(accessor, std::move(str_val), no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else {
          cache_hit_flag = 0;
        }
        break;

      default:
        cache_hit_flag = 0;
        break;
    }
  }

  if (cache_flag_accessor_ != nullptr) {
    result.SetIntAttr(cache_flag_accessor_, 1, no_overwrite_);
  } else {
    CL_LOG_EVERY_N(WARNING, 1000) << "MerchantArchDistributedCacheXtrScoreReaderEnricher "
                                     "cache_flag_accessor nullptr";
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantArchDistributedCacheXtrScoreReaderEnricher,
                 MerchantArchDistributedCacheXtrScoreReaderEnricher<ks::reco::protoutil::AttrKVItem>)
}  // namespace platform
}  // namespace ks
