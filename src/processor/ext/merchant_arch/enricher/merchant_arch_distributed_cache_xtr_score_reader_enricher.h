#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <utility>
#include <vector>
#include <unordered_set>

#include "serving_base/util/scope_exit.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/module/photo_store_manager.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/common/enricher/common_reco_distributed_index_item_attr_enricher.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/photo_store/dynamic_photo_store_fetcher.h"
#include "ks/photo_store/photo_store_fetcher.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/merchant_living_photo_store_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/merchant_photo_store_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/new_photo_info_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/photo_info_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"
#include "redis_proxy_client/redis_proxy_client.h"
// #include "kenv/service_meta.h"

namespace ks {
namespace platform {
DECLARE_bool(new_cache_photo_store_fetch_score_attr_only);

class FlatCacheAttrMetaDataHolder : public FlatIndexAttrMetaDataHolder {
 public:
  SINGLETON(FlatCacheAttrMetaDataHolder);
};

template <typename PhotoItemType>
class MerchantArchDistributedCacheXtrScoreReaderEnricher : public CommonRecoBaseEnricher {
 protected:
  template <typename T>
  struct static_false : std::false_type {};

 public:
  MerchantArchDistributedCacheXtrScoreReaderEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  std::function<void()> Purge() override {
    static_assert(static_false<PhotoItemType>::value, "Purge must be specialized");
    return []() {};
  }

  void ClearInit() {
    attr_kv_special_data_.clear();
    attr_path_config_.clear();
    is_cache_hit_attr_name_.clear();
    photo_store_kconf_key_.clear();
    table_kconf_path_.clear();
    request_info_.clear();
    attr_prefix_.clear();
    item_score_attrs_names_.clear();
    table_name_.clear();
    no_overwrite_ = false;
    photo_store_rpc_req_cache_rate_ = 100.0;
    get_table_name_from_kconf_ = false;
    use_request_type_ = true;
    partition_with_uid_ = false;
  }

  void ClearEnrich() {
    request_id_.clear();
    index_item_keys_.clear();
    target_items_.clear();
    attrs_cnt_.clear();
    attrs_total_size_.clear();
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    ClearEnrich();

    SpecializedEnrichInit(context);
    std::string request_type = context->GetRequestType();
    request_id_ = context->GetRequestId();
    if (is_cache_hit_attr_name_.empty()) {
      is_cache_hit_attr_name_ = request_type+"_default_hit_cache";
    }

    if (!is_cache_hit_attr_name_.empty()) {
      cache_flag_accessor_ = context->GetItemAttrAccessor(is_cache_hit_attr_name_);
    }

    attrs_cnt_.resize(attr_path_config_.size(), 0);
    attrs_total_size_.resize(attr_path_config_.size(), 0);

    photo_store_rpc_req_cache_rate_ =
        GetDoubleProcessorParameter(context, "photo_store_rpc_req_cache_rate", 100.0);

    uint64 user_id = context->GetUserId();
    if (!user_id || user_id == 0) {
      LOG(ERROR) << "Invalid user ID";
      return;
    }
    // generate key_prefix, key = request_type + user_id + item_id
    std::string key_pefix;
    if (use_request_type_) {
      key_pefix = request_type + base::Uint64ToString(user_id);
    } else {
      key_pefix = base::Uint64ToString(user_id);
    }
    uint64 hash_key = HashStringKey(key_pefix);
    uint64 key;

    for (auto it = begin; it != end; ++it) {
      auto &result = *it;

      if (partition_with_uid_) {
        // 保证同一个 user_id 的 item 一定会落在同一个 partition
        key = result.GetId() * 1000 + hash_key;
      } else {
        std::string string_key = key_pefix + base::Uint64ToString(result.GetId());
        key = HashStringKey(string_key);
      }

      index_item_keys_.push_back(key);
      target_items_.push_back(result);
    }
    if (index_item_keys_.empty()) {
      LOG(ERROR) << "distributed index request "
                      "cancelled with empty item list: "
                   << request_info_;
      return;
    }
    QueryDynamicPhotoStore(context);
  }

 private:
  bool ParseTableNameFromPhotoStoreKconf() {
    auto default_val = std::make_shared<::Json::Value>();
    std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> cache_configs;
    cache_configs = ks::infra::KConf().Get(photo_store_kconf_key_, default_val);

    if (!cache_configs || !cache_configs->Get()) {
      LOG(ERROR) << "request_id_: " << request_id_ << " init kconf data config failed! kconf name is "
                 << photo_store_kconf_key_;
      return false;
    }

    std::shared_ptr<::Json::Value> json_config = cache_configs->Get();

    if (json_config->isObject()) {
      try {
        const ::Json::Value &configValue = *json_config;

        auto table_name = configValue["ps_client_table_name"];
        table_name_ = table_name.asString();
      } catch (const std::exception &e) {
        LOG(ERROR) << "request_id_: " << request_id_ << ", catch exception:" << e.what();
        return false;
      } catch (...) {  // 捕获所有其他类型的异常
        LOG(ERROR) << "request_id_: " << request_id_ << ", catch unknown exception";
        return false;
      }
    } else {
      LOG(ERROR) << "request_id_: " << request_id_
                 << " json_config expected to be a json but got something else.";
      return false;
    }

    return true;
  }

  bool InitProcessor() override {
    ClearInit();
    is_cache_hit_attr_name_ = config()->GetString("is_cache_hit");

    photo_store_kconf_key_ = config()->GetString("photo_store_kconf_key", "");
    if (photo_store_kconf_key_.empty()) {
      LOG(ERROR) << "photo_store_kconf_key cannot be empty.";
      return false;
    }

    table_kconf_path_ = config()->GetString("table_kconf_path", "");
    if (table_kconf_path_.empty()) {
      LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
                    "kconf_path config need set";
      return false;
    }

    table_name_ = config()->GetString("table_name");
    if (table_name_.empty()) {
      LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
                    "table_name config need set";
      return false;
    }

    // 从 kconf 中获取 table_name，基于 kconf 实现一套代码，不同集群配置不同的 table_name
    get_table_name_from_kconf_ = config()->GetBoolean("get_table_name_from_kconf", false);
    if (get_table_name_from_kconf_) {
      photo_store_kconf_key_ = config()->GetString("photo_store_kconf_key");
      if (photo_store_kconf_key_.empty()) {
        CL_LOG_ERROR("distributed_cache", "empty_photo_store_kconf_key")
            << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! "
               "photo_store_kconf_key is empty";
        return false;
      }
      ParseTableNameFromPhotoStoreKconf();
    }

    use_request_type_ = config()->GetBoolean("use_request_type", true);
    partition_with_uid_ = config()->GetBoolean("partition_with_uid", false);

    no_overwrite_ = config()->GetBoolean("no_overwrite", false);

    auto *dynamic_photo_store = GetDynamicPhotoStore();
    if (!dynamic_photo_store) {
    LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed!"
              << " failed to initialize photo store";
    return false;
    }
    dynamic_photo_store_fetcher_.Init(dynamic_photo_store);
    request_info_ = "kess_service: " + dynamic_photo_store->GetConfig().service_name + ", timeout_ms: " +
                  std::to_string(dynamic_photo_store->GetConfig().grpc_timeout.count()) + "ms";

    if (!ParseKconfConfig()) {
      LOG(ERROR) << "ParseKconfConfig failed";
      return false;
    }

    return SpecializedInitProcess();
  }

  bool CalculateAttrKey(const std::string &attr_name, const std::string &attr_type_str, uint32 *attr_key) {
    using ks::reco::protoutil::ConvertAttrNameAndTypeToAttrKey;
    if (attr_type_str == "int") {
      *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kInt);
    } else if (attr_type_str == "double") {
      *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kFloat);
    } else if (attr_type_str == "string") {
      *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kString);
    } else {
      LOG(ERROR) << "MerchantArchDistributedCacheXtrScoreReaderEnricher init failed! Invalid attr type str, "
         "must be type string, please check config. name:"
      << attr_name << " str:" << attr_type_str
      << " which should be one of 'int', 'double', 'string'";
      return false;
    }
    return true;
  }

  bool ParseKconfConfig() {
    auto default_val = std::make_shared<::Json::Value>();
    std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> cache_configs;
    cache_configs = ks::infra::KConf().Get(table_kconf_path_, default_val);

    if (!cache_configs || !cache_configs->Get()) {
      LOG(ERROR) << "request_id_: " << request_id_ << " init kconf data config failed! kconf name is " \
          << table_kconf_path_;
      return false;
    }

    std::shared_ptr<::Json::Value> json_config = cache_configs->Get();

    if (json_config->isObject()) {
      try {
          const ::Json::Value& configValue = *json_config;

          auto cache_config = configValue[table_name_];
          for (const auto& attr : cache_config["active_attrs"]) {
              item_score_attrs_names_.insert(attr.asString());
          }

          attr_prefix_ = cache_config["attr_prefix"].asString();
          attr_prefix_ += '_';
      } catch(const std::exception& e) {
          LOG(ERROR) << "request_id_: " << request_id_ << ", catch exception:" << e.what();
          return false;
      } catch (...) {  // 捕获所有其他类型的异常
          LOG(ERROR) << "request_id_: " << request_id_ << ", catch unknown exception";
          return false;
      }
    } else {
      LOG(ERROR) << "request_id_: " << request_id_
          << " json_config expected to be a json but got something else.";
      return false;
    }

    return true;
  }

  bool HasPrefix(const std::string& str, const std::string& prefix) {
    return str.rfind(prefix, 0) == 0;      // 检查字符串是否以 prefix 开头
  }

  std::string RemovePrefix(const std::string& input) {
    if (HasPrefix(input, attr_prefix_)) {
      return input.substr(attr_prefix_.length());
    } else {
      LOG(ERROR) << "attr_name " << input <<" does not has biz name perfix: " << attr_prefix_;
      return "";
    }
  }

  uint64 HashStringKey(const std::string& key) {
    std::hash<std::string> hash_fn;
    size_t hash_value = hash_fn(key);
    if (hash_value > INT64_MAX) {
      // 如果哈希值超过 INT64_MAX，取模
      hash_value = hash_value % INT64_MAX;
    }
    return static_cast<uint64>(hash_value);
  }

 private:
  struct PhotoInfoAttrConfig {
    std::string name;
    std::vector<int> path;
    ItemAttr *accessor = nullptr;
  };

  struct AttrKVSpecialData {
    std::string name;
    uint32 key = 0;
    ItemAttr *accessor = nullptr;
  };

  ks::photo_store::DynamicPhotoStore *GetDynamicPhotoStore() {
    static_assert(static_false<PhotoItemType>::value, "GetPhotoStore must be specialized");
    return nullptr;
  }

  void QueryDynamicPhotoStore(MutableRecoContextInterface *context) {
    auto dynamic_item_vector = std::make_shared<ks::photo_store::DynamicPhotoStoreFetcher::ItemVector>();
    RegisterAsyncCallback(
      context,
      dynamic_photo_store_fetcher_.AsyncMultiGet(index_item_keys_, dynamic_item_vector.get(), false,
                                              "distributed_index", nullptr, photo_store_rpc_req_cache_rate_,
                                              ks::photo_store::DefaultDataSetTags(), nullptr),
      [this, context, dynamic_item_vector, target_items = std::move(target_items_)](
          const ks::photo_store::DynamicPhotoStoreFetcher::ItemVector *item_infos) {
        if (!item_infos) {
          CL_LOG_ERROR("distributed_cache", "null_response")
              << "fail to get response from distributed index server, " << request_info_;
          return;
        }

        if (item_infos->size() != target_items.size()) {
          CL_LOG_ERROR("distributed_cache", "response_size_mismatch")
              << "item list size mismatch, request num: " << target_items.size()
              << ", response num: " << item_infos->size() << ", requst_info: " << request_info_;
          return;
        }

        std::fill(attrs_total_size_.begin(), attrs_total_size_.end(), 0);
        std::fill(attrs_cnt_.begin(), attrs_cnt_.end(), 0);

        int item_hit = 0;
        for (int i = 0; i < item_infos->size(); ++i) {
          const auto &item = item_infos->at(i);
          const auto &result = target_items[i];
          if (!item) {
            CL_LOG_EVERY_N(WARNING, 1000) << "item miss in photo_store, key: " << result.item_key
                                          << ", id: " << result.GetId() << ", type: " << result.GetType();
            continue;
          }
          ++item_hit;
          SavePhotoStoreItem(result, const_cast<photo_store::Item *>(item));
        }

        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            kPerfBase * item_hit / target_items.size(), kPerfNs, "cache.item_hit",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
        base::perfutil::PerfUtilWrapper::IntervalLogStash(target_items.size(), kPerfNs, "cache.item_total",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName());
        for (int i = 0; i < attr_path_config_.size(); ++i) {
          const auto &attr_name = attr_path_config_[i].name;
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              kPerfBase * attrs_cnt_[i] / std::max(item_hit, 1), kPerfNs, "cache.attr_hit",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              kPerfBase * attrs_total_size_[i] / std::max(attrs_cnt_[i], 1), kPerfNs, "cache.attr_size",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
        }

        CL_LOG(INFO) << "received response from distributed cache server, "
                        "request num: "
                      << target_items.size() << ", response num: " << item_infos->size();
      },
      request_info_);
  }

  bool SpecializedInitProcess() {
    return true;
  }

  void SavePhotoStoreItem(const CommonRecoResult &result, ks::photo_store::Item *item) {
    static_assert(static_false<PhotoItemType>::value, "SavePhotoStoreItem must be specialized");
  }

  void SpecializedEnrichInit(MutableRecoContextInterface *context) {}

  std::string is_cache_hit_attr_name_;
  std::string photo_store_kconf_key_;
  std::string table_kconf_path_;
  bool get_table_name_from_kconf_ = false;
  bool use_request_type_ = true;
  bool partition_with_uid_ = false;
  bool no_overwrite_ = false;
  std::string request_info_;
  std::string attr_prefix_;
  std::unordered_set<std::string> item_score_attrs_names_;
  ks::photo_store::DynamicPhotoStoreFetcher dynamic_photo_store_fetcher_;
  std::string request_id_;
  std::string table_name_;
  std::vector<AttrKVSpecialData> attr_kv_special_data_;
  std::vector<PhotoInfoAttrConfig> attr_path_config_;
  std::vector<uint64> index_item_keys_;
  std::vector<CommonRecoResult> target_items_;
  double photo_store_rpc_req_cache_rate_ = 100.0;
  std::vector<int> attrs_cnt_, attrs_total_size_;
  ItemAttr *cache_flag_accessor_ = nullptr;
  static constexpr float kPerfBase = 1000.0;

  DISALLOW_COPY_AND_ASSIGN(MerchantArchDistributedCacheXtrScoreReaderEnricher);
};

}  // namespace platform
}  // namespace ks
