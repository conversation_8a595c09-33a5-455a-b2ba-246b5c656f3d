#include "dragon/src/processor/ext/ad/predict/enricher/ad_infer_result_v2_enricher.h"

#include <cmath>

#include <iterator>
#include <string>
#include <utility>
#include <vector>

#include "serving_base/util/factory.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/utils/common.h"
#include "teams/ad/ad_nn/utils/const.h"
#include "teams/ad/ad_nn/utils/falcon_statistic.h"
#include "teams/ad/ad_nn/utils/rpc_name_tools.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
namespace ks {
namespace platform {

using kuaishou::ad::algorithm::UniversePredictResponse;

namespace {
bool is_valid_value(float value, bool is_negative_valid) {
  if (std::isnan(value)) {
    falcon::Inc("dnn_predict_server.predict_output_isnan", 1);
    return false;
  }
  if (std::isinf(value)) {
    falcon::Inc("dnn_predict_server.predict_output_isinf", 1);
    return false;
  }
  if (value < 0 && !is_negative_valid) {
    falcon::Inc("dnn_predict_server.predict_output_negative", 1);
    return false;
  }
  return true;
}
}  // namespace

#define CHECK_RETURN(cond, msg) \
  do {                          \
    if ((cond)) {               \
      LOG(ERROR) << (msg);      \
      return false;             \
    }                           \
  } while (0)

void AdInferResultV2Enricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
  auto infer_stats_attr = context->GetIntCommonAttr(ad_nn::constant::DragonInferStatus);
  if (infer_stats_attr && *infer_stats_attr != ad_nn::InferStatus::OK) {
    falcon::Inc("dnn_predict_server.predict_fail_count", 1);
    CL_LOG(ERROR) << "dragon infer status is not ok, status: " << *infer_stats_attr;
    return;
  }

  auto num_items = std::distance(begin, end);
  std::vector<bool> item_miss(num_items, false);

  std::vector<std::vector<float>> probs(num_items);
  if (!FillOutputs(context, begin, end, &probs, &item_miss)) {
    CL_LOG(ERROR) << "FillOutputs failed.";
    return;
  }

  std::vector<std::vector<float>> vector_output_values(num_items);
  if (!FillVectorOutputs(context, begin, end, &vector_output_values, &item_miss)) {
    CL_LOG(ERROR) << "FillVectorOutputs failed.";
    return;
  }

  LogResult(context, probs, vector_output_values, item_miss);

  if (context->IsDebugRequest()) {
    std::vector<uint64_t> item_ids;
    for (auto iter = begin; iter != end; ++iter) {
      item_ids.emplace_back(iter->GetId());
    }
    auto infer_req_ctx = context->GetPtrCommonAttr<ks::ad_nn::InferRequestContext>(
        ks::ad_nn::constant::DragonInputInferRequest);
    ks::ad_nn::debug::CollectOutputProbs(item_ids, probs, infer_req_ctx->debug_kv_collector);
    auto resp = context->GetPtrCommonAttr<UniversePredictResponse>("ad_debug_fake_res");
    std::vector<std::string> debug_info;
    if (resp != nullptr) {
      CL_LOG(INFO) << "debug reponse:" << resp << " debug_info size:" << resp->debug_info_size();
      for (auto &info : resp->debug_info()) {
        std::string info_str;
        info.SerializeToString(&info_str);
        debug_info.emplace_back(info_str);
      }
    } else {
      CL_LOG(ERROR) << "no debug response found";
    }
    context->SetStringListCommonAttr("debug_info", std::move(debug_info));
  }
}

/// private method impl
bool AdInferResultV2Enricher::InitProcessor() {
  neg_sample_rate_ = config()->GetFloat("neg_sample_rate", 1.0);
  is_negative_valid_ = config()->GetBoolean("is_negative_valid", false);
  LOG(INFO) << "neg_sample_rate: " << neg_sample_rate_ << ", is_negative_valid: " << is_negative_valid_;

  /// init optputs and vector_outputs
  /// outputs, optional
  auto outputs_config = config()->Get("outputs");
  CHECK_RETURN(!ParseOutputConfig(outputs_config, &outputs_, false), "Parse `outputs` failed.");

  /// vector_outputs, optional
  auto vector_outputs_config = config()->Get("vector_outputs");
  CHECK_RETURN(!ParseOutputConfig(vector_outputs_config, &vector_outputs_, true),
               "Parse `vector_outputs` failed.");

  if (outputs_.empty() && vector_outputs_.empty()) {
    LOG(ERROR) << "config `outputs` and `vector_outputs` can not be empty both.";
    return false;
  }

  /// init perf
  std::string model_cmd = ad_nn::GetKcsModelCmd();
  std::string model_category, model_name;
  CHECK_RETURN(!ad_nn::GetFalconConfig(model_cmd, &model_name, &model_category), "Get falcon config failed.");
  GetPredictResultFalconKeys(model_category, model_name, &result_falcon_keys_);
  auto kess_suffix = std::getenv("KESS_SUFFIX");
  kess_suffix_ = kess_suffix != nullptr ? std::string(kess_suffix) : "";

  /// init output attr
  output_prob_attr_ = config()->GetString("output_prob_attr", "predict_value");
  output_vector_attr_ = config()->GetString("output_vector_attr", "vector_predict_value");
  output_result_stat_attr_ = config()->GetString("output_result_stat_attr", "result_stat");

  /// init model validator: 先不支持 model validator 吧
  model_key_ = config()->GetString("key");
  CHECK_RETURN(model_key_.empty(), "`key` is empty.");
  return true;
}

bool AdInferResultV2Enricher::FillOutputs(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end, std::vector<std::vector<float>> *p_probs,
                                          std::vector<bool> *p_item_miss) const {
  /// 输出格式说明, 这里以 output_layer_dim = 8, output_value_width = 2, predict_value_num = 4 为例进行说明:
  ///
  /// output_value_width = 2 表示将输出层相邻两个元素为一组, output_layer_dim = 8 的情况下共 4 个分组, 取每个
  /// 分组的后一个元素, 恰好与 predict_value_num 相等. 因此下面的数组将取下标为[1, 3, 5, 7] 处的值作为输出.
  /// -------------------------
  /// |  |  |  |  |  |  |  |  |
  /// -------------------------
  /// 0  1     3     5     7
  /// 实际 output_layer_dim 通常为 2, output_value_width = 2, predict_value_num = 1
  /// 如果 output_layer_dim = 1 时, 则直接将 output 作为输出

  if (outputs_.empty()) {
    return true;
  }

  auto num_items = std::distance(begin, end);
  auto &outputs_value = *p_probs;
  std::vector<int> abnormal_idx(num_items, 0);

  for (const auto &elem : outputs_) {
    const auto &tensor_name = elem.first;
    const auto &output_configs = elem.second;
    for (const auto &output_config : output_configs) {
      int output_value_width = output_config.output_value_width;
      int predict_value_num = output_config.predict_value_num;
      const float *addr = context->GetPtrCommonAttr<float>(output_config.attr_name);
      if (!addr) {
        CL_LOG(ERROR) << "infer output addr is nullptr, attr_name: " << output_config.attr_name
                      << ", tensor_name: " << tensor_name;
        return false;
      }
      int64_t output_layer_dim = context->GetIntCommonAttr(output_config.attr_name + "_layer_len").value();
      if (output_layer_dim > 1) {
        if (output_layer_dim % output_value_width != 0) {
          CL_LOG(ERROR) << "output_layer_dim % output_value_width != 0, output_layer_dim: "
                        << output_layer_dim << ", output_value_width: " << output_value_width
                        << ", tensor: " << tensor_name;
          return false;
        }
        if (output_layer_dim / output_value_width != predict_value_num) {
          CL_LOG(ERROR) << "output_layer_dim / output_value_width != predict_value_num, output_layer_dim: "
                        << output_layer_dim << ", output_value_width: " << output_value_width
                        << ", predict_value_num: " << predict_value_num << ", tensor: " << tensor_name;
          return false;
        }
      } else {
        if (output_layer_dim != 1 || predict_value_num != 1) {
          CL_LOG(ERROR) << "output_layer_dim invalid (should >= 1): " << output_layer_dim
                        << ", or predict_value_num != 1 when output_layer_dim = 1, predict_value_num: "
                        << predict_value_num << ", tensor: " << tensor_name;
          return false;
        }
      }
      std::vector<float> tmp_output(predict_value_num, 0.);
      for (size_t i = 0; i < num_items; ++i) {
        if (output_layer_dim == 1) {
          tmp_output[0] = addr[i * output_layer_dim];
        } else {
          for (int index = output_value_width - 1, num = 0; num < predict_value_num; ++num) {
            tmp_output[num] = addr[i * output_layer_dim + num * output_value_width + index];
          }
        }
        // 结果后处理
        for (size_t j = 0; j < tmp_output.size(); ++j) {
          if (neg_sample_rate_ > 0.f) {
            tmp_output[j] = tmp_output[j] / (neg_sample_rate_ - (neg_sample_rate_ - 1) * tmp_output[j]);
          }
          if (!is_valid_value(tmp_output[j], is_negative_valid_)) {
            falcon::Inc("dnn_predict_server.tf_predict_output_is_abnormal", 1);
            CL_LOG(WARNING) << "model: " << model_key_ << " predict output is abnormal:" << tmp_output[j];
            abnormal_idx[i] = 1;
          }
        }
        outputs_value[i].insert(outputs_value[i].end(), tmp_output.begin(), tmp_output.end());
      }
    }
  }

  auto val_accessor = context->GetItemAttrAccessor(output_prob_attr_);
  auto stat_accessor = context->GetItemAttrAccessor(output_result_stat_attr_);
  auto miss_accessor = context->GetItemAttrAccessor("item_miss");
  auto &item_miss = *p_item_miss;
  size_t idx = 0;
  for (auto iter = begin; iter != end; ++iter, ++idx) {
    std::vector<double> attr_prob;
    auto miss_attr = context->GetIntItemAttr(*iter, miss_accessor);
    if (miss_attr && miss_attr.value() == 1) {
      item_miss[idx] = true;
      attr_prob.resize(outputs_value[idx].size(), 0.);
      context->SetIntItemAttr(*iter, stat_accessor, static_cast<int64_t>(kuaishou::ad::algorithm::ITEM_MISS));
    } else {
      attr_prob.assign(outputs_value[idx].begin(), outputs_value[idx].end());
      context->SetIntItemAttr(*iter, stat_accessor,
                              static_cast<int64_t>(kuaishou::ad::algorithm::RESULT_VALID));
    }
    context->SetDoubleListItemAttr(*iter, val_accessor, std::move(attr_prob));
    if (abnormal_idx[idx]) {
      context->SetIntItemAttr(*iter, stat_accessor,
                              static_cast<int64_t>(kuaishou::ad::algorithm::RESULT_NAN));
    }
  }
  return true;
}

bool AdInferResultV2Enricher::FillVectorOutputs(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end,
                                                std::vector<std::vector<float>> *p_vector_output_values,
                                                std::vector<bool> *p_item_miss) const {
  if (vector_outputs_.empty()) {
    return true;
  }
  auto num_items = std::distance(begin, end);
  auto &vector_outputs = *p_vector_output_values;
  for (const auto &elem : vector_outputs_) {
    const auto &tensor_name = elem.first;
    const auto &output_configs = elem.second;
    for (const auto &output_config : output_configs) {
      const float *addr = context->GetPtrCommonAttr<float>(output_config.attr_name);
      if (!addr) {
        CL_LOG(ERROR) << "infer output addr is nullptr, attr_name: " << output_config.attr_name
                      << ", tensor_name: " << tensor_name;
        return false;
      }
      int64_t output_layer_dim = context->GetIntCommonAttr(output_config.attr_name + "_layer_len").value();
      if (output_layer_dim != output_config.predict_value_num) {
        CL_LOG(ERROR) << "vector_output_layer_dim != config_predict_value_num, " << output_layer_dim << " vs "
                      << output_config.predict_value_num;
        return false;
      }
      for (int i = 0; i < num_items; ++i) {
        const float *tmp_addr = addr + i * output_layer_dim;
        vector_outputs[i].insert(vector_outputs[i].end(), tmp_addr, tmp_addr + output_layer_dim);
      }
    }
  }

  auto vec_val_accessor = context->GetItemAttrAccessor(output_vector_attr_);
  auto stat_accessor = context->GetItemAttrAccessor(output_result_stat_attr_);
  auto miss_accessor = context->GetItemAttrAccessor("item_miss");
  auto &item_miss = *p_item_miss;
  int idx = 0;
  for (auto iter = begin; iter != end; ++iter, ++idx) {
    std::vector<double> vector_val;
    auto miss_attr = context->GetIntItemAttr(*iter, miss_accessor);
    if (miss_attr && miss_attr.value() == 1) {
      item_miss[idx] = true;
      vector_val.resize(vector_outputs[idx].size(), 0.);
      context->SetIntItemAttr(*iter, stat_accessor, static_cast<int64_t>(kuaishou::ad::algorithm::ITEM_MISS));
    } else {
      vector_val.assign(vector_outputs[idx].begin(), vector_outputs[idx].end());
      context->SetIntItemAttr(*iter, stat_accessor,
                              static_cast<int64_t>(kuaishou::ad::algorithm::RESULT_VALID));
    }
    context->SetDoubleListItemAttr(*iter, vec_val_accessor, std::move(vector_val));
  }
  return true;
}

void AdInferResultV2Enricher::PerfOutputs(
    const absl::Span<const int64> &cmdkey_end_pos, const std::vector<const char *> &tags,
    const std::vector<std::pair<std::string, std::vector<OutputConfig>>> &output_configs,
    const std::vector<std::vector<float>> &output_values, const std::vector<bool> &item_miss,
    bool is_vector_output) const {
  int32_t log_step = 10;
  int output_index = 0;
  for (const auto &it : output_configs) {
    const auto &outputs = it.second;
    for (auto &output : outputs) {
      int64_t xvalue = 0, xcount = 0;
      std::vector<int64_t> xvalue_list;
      std::vector<int64_t> xcount_list;
      if (tags.size() > 0) {
        xvalue_list.resize(tags.size(), 0);
        xcount_list.resize(tags.size(), 0);
      }
      size_t cmdkey_index = 0;
      for (uint32_t iidx = 0; iidx < output_values.size(); ++iidx) {
        if (cmdkey_index < cmdkey_end_pos.size() && iidx == cmdkey_end_pos[cmdkey_index]) {
          ++cmdkey_index;
        }
        if (item_miss[iidx]) {
          continue;
        }
        // 统计时每个 output 只算了第一个值, 对齐 ad infer
        int64_t value = ad_nn::FalconPredictValue(output_values[iidx][output_index]);
        xvalue += value;
        xcount += 1;
        if (cmdkey_index < xvalue_list.size()) {
          xvalue_list[cmdkey_index] += value;
          xcount_list[cmdkey_index] += 1;
        }
        try {
          if (iidx % log_step == 0) {
            ad_nn::LogPredictResult(value, result_falcon_keys_, kess_suffix_.c_str());
          }
        } catch (const std::exception &e) {
          falcon::Inc("dnn_predict_server.predict_log_result_fail", 1);
          CL_LOG(ERROR) << "Exception: " << e.what() << " item size: " << output_values.size()
                        << " log step: " << log_step;
        }
      }
      output_index += output.predict_value_num;
      // 打点
      bool enable_validate = is_vector_output ? output.enable_validate : false;
      ad_nn::PredictResultFalcon::Instance().Stat(output.attr_name, xvalue, xcount, is_vector_output,
                                                  enable_validate);
      // cmdkey 预估值打点
      for (size_t i = 0; i < tags.size(); ++i) {
        ad_nn::PredictResultFalcon::Instance().StatWithCmdKey(
            tags[i], output.attr_name, xvalue_list[i], xcount_list[i], is_vector_output, enable_validate);
      }
    }
  }
}

void AdInferResultV2Enricher::LogResult(MutableRecoContextInterface *context,
                                        const std::vector<std::vector<float>> &probs,
                                        const std::vector<std::vector<float>> &vector_output_values,
                                        const std::vector<bool> &item_miss) const {
  std::vector<const char *> tags;
  auto cmdkey_list = context->GetStringListCommonAttr("cmdkey_list");
  auto cmdkey_end_pos = context->GetIntListCommonAttr("cmdkey_end_pos");
  if (!cmdkey_list || !cmdkey_end_pos || cmdkey_list->size() <= 0 ||
      cmdkey_list->size() != cmdkey_end_pos->size()) {
    LOG_EVERY_N(WARNING, 1000) << "invalid cmd key got";
    return;
  }
  bool is_offline_traffic = ad_nn::IsOfflineTraffic();
  if (is_offline_traffic) {
    tags.emplace_back("offline");
  } else if (cmdkey_list->size() > 0) {
    for (const auto &cmdkey : *cmdkey_list) {
      tags.emplace_back(cmdkey.data());
    }
  }

  int32_t log_step = 10;
  if (cmdkey_list->size() > 0) {
    uint64_t last_end_pos = 0;
    for (size_t i = 0; i < cmdkey_list->size(); ++i) {
      std::string falcon_key =
          base::StringPrintf("dnn_predict_server.request_item_id_count_cmdkey_%s", (*cmdkey_list)[i].data());
      uint64_t ck_item_size = (*cmdkey_end_pos)[i] - last_end_pos;
      last_end_pos = (*cmdkey_end_pos)[i];
      falcon::Inc(falcon_key.c_str(), ck_item_size);
    }
  } else if (tags.size() == 1) {
    falcon::Stat(base::StringPrintf("dnn_predict_server.request_item_id_count_cmdkey_%s", tags[0]).c_str(),
                 probs.size());
  }
  falcon::Stat("dnn_predict_server.request_item_id_count", probs.size());

  // 进行预估值打点，falcon 打点和 perf 打点
  if (!is_offline_traffic) {
    PerfOutputs(cmdkey_end_pos.value(), tags, outputs_, probs, item_miss, false);
    PerfOutputs(cmdkey_end_pos.value(), tags, vector_outputs_, vector_output_values, item_miss, true);
  }
}

bool AdInferResultV2Enricher::ParseOutputConfig(
    const base::Json *configs, std::vector<std::pair<std::string, std::vector<OutputConfig>>> *outputs,
    bool is_vector_output) const {
  if (!configs || !configs->IsArray()) {
    return true;
  }
  for (const auto &config : configs->array()) {
    OutputConfig output;
    output.attr_name = config->GetString("attr_name");
    output.tensor_name = config->GetString("tensor_name");
    output.predict_value_num = config->GetInt("predict_value_num", 1);
    output.is_vector_output = is_vector_output;
    if (!is_vector_output) {
      output.output_value_width = config->GetInt("output_value_width", 2);
    } else {
      output.enable_validate = config->GetBoolean("enable_validate", true);
    }
    CHECK_RETURN(output.tensor_name.empty(), "`tensor_name` of outputs is empty.");
    CHECK_RETURN(output.attr_name.empty(), "`attr_name` of outputs is empty.");

    LOG(INFO) << "output: " << output.DebugString();

    auto iter = std::find_if(outputs->begin(), outputs->end(),
                             [output](const std::pair<std::string, std::vector<OutputConfig>> &item) {
                               return output.tensor_name == item.first;
                             });
    if (iter != outputs->end()) {
      iter->second.push_back(output);
    } else {
      std::vector<OutputConfig> tmp_out_config;
      tmp_out_config.push_back(output);
      outputs->push_back(std::make_pair(output.tensor_name, tmp_out_config));
    }
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdInferResultV2Enricher, AdInferResultV2Enricher)
}  // namespace platform
}  // namespace ks
