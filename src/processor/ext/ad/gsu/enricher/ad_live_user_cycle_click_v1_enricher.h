#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/utility/timer.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {

class AdLiveUserCycleClickV1Enricher : public CommonRecoBaseEnricher {
 public:
  AdLiveUserCycleClickV1Enricher() = default;
  ~AdLiveUserCycleClickV1Enricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string &key, std::vector<int> *vec);
  int64_t log_x_plus_1(int64_t x);
  int CalculateHourDistance(int h1, int h2);
  int64_t min_int64(int64_t x, int64_t y);

 private:
  // item id
  std::string colossus_goods_id_attr_ = "item_id_list";
  // time info
  std::string colossus_timestamp_attr_ = "click_timestamp_list";
  std::string detail_page_view_time_attr_ = "detail_page_view_time_list";
  // side info
  std::string colossus_category_attr_ = "category_a_list";            // x3
  std::string colossus_host_attr_ = "seller_id_list";                 // 1
  std::string colossus_shop_attr_ = "real_seller_id_list";            // 1
  std::string colossus_real_price_attr_ = "real_price_list";          // 1
  std::string colossus_label_attr_ = "label_list";                    // 1
  std::string colossus_uniform_spu_id_attr_ = "uniform_spu_id_list";  // 1
  // position info
  std::string click_flow_type_attr_ = "click_flow_type_list";
  std::string click_from_attr_ = "click_from_list";

  // seq config
  int limit_user_num_ = 1000;
  int min_seconds_ago_ = 60;
  int max_seconds_ago_ = 31 * 24 * 3600;  // 小时级特征只筛选最近一个月

  // output config
  bool use_ad_slot_size_ = true;
  int parameter_sign_bits_ = 52;
  uint64_t SIGN_MASK_ = ((1ul << 52) - 1);
  std::vector<int> mio_slots_ids_ = {2001, 2002, 2003, 2004, 2005, 2006, 2007,
                                     2008, 2009, 2010, 2011, 2012, 2013, 2014};
  std::vector<int> slots_ids_ = {2001, 2002, 2003, 2004, 2005, 2006, 2007,
                                 2008, 2009, 2010, 2011, 2012, 2013, 2014};
  std::string output_slot_attr_ = "ad_live_user_cycle_click_v1_slot";
  std::string output_sign_attr_ = "ad_live_user_cycle_click_v1_sign";
  serving_base::Timer timer_;
  SlotSignRemapUtil remap_util_;
  bool slot_as_attr_name_ = false;
  std::string slot_as_attr_name_prefix_ = "";

  DISALLOW_COPY_AND_ASSIGN(AdLiveUserCycleClickV1Enricher);
};

}  // namespace platform
}  // namespace ks
