#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

#ifndef DISALLOW_COPY_AND_ASSIGN
#define DISALLOW_COPY_AND_ASSIGN(TypeName) \
 private:                                  \
  TypeName(const TypeName &);              \
  void operator=(const TypeName &)
#endif

namespace ks {
namespace platform {

class GoodsClickGsuWithIdxListEnricher : public CommonRecoBaseEnricher {
 public:
  GoodsClickGsuWithIdxListEnricher() {}
  ~GoodsClickGsuWithIdxListEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string timestamp_attr_;
  std::string commodity_id_attr_;
  std::string seller_id_attr_;
  std::string carry_type_attr_;
  std::string price_attr_;
  std::string item_count_attr_;

  std::string topk_indices_attr_;

  std::string output_timestamp_attr_;
  std::string output_commodity_id_attr_;
  std::string output_seller_id_attr_;
  std::string output_carry_type_attr_;
  std::string output_price_attr_;
  std::string output_item_count_attr_;

  int limit_num_ = 100;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(GoodsClickGsuWithIdxListEnricher);
};

}  // namespace platform
}  // namespace ks
