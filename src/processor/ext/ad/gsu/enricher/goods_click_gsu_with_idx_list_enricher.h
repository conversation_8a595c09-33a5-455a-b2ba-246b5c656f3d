#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"

namespace ks {
namespace platform {

class GoodsClickGsuWithIdxListEnricher : public CommonRecoBaseEnricher {
 public:
  GoodsClickGsuWithIdxListEnricher() {}
  ~GoodsClickGsuWithIdxListEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string& key, std::vector<int> *vec);

 private:
  std::string timestamp_attr_;
  std::string commodity_id_attr_;
  std::string pagecode_id_attr_;
  std::string uniform_spu_id_attr_;
  std::string exposure_ratio_attr_;
  std::string exposure_time_attr_;
  std::string detail_content_stay_time_attr_;
  std::string category_attr_;
  std::string leaf_category_attr_;
  std::string seller_id_attr_;
  std::string input_idx_list_attr_ = "";
  std::string input_bias_attr_ = "";
  std::string sideinfo_cache_map_ = "";
  std::string soft_score_list_attr_ = "";

  int limit_num_ = 100;
  bool fused_slot_sign_remap_ = false;
  bool use_sideinfo_cache_ = false;

  std::string output_sign_attr_;
  std::string output_slot_attr_;
  std::vector<int> slots_ids_;
  std::vector<int> mio_slots_ids_;
  SlotSignRemapUtil remap_util_;
  int slot_cnt_ = 0;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(GoodsClickGsuWithIdxListEnricher);
};

}  // namespace platform
}  // namespace ks
