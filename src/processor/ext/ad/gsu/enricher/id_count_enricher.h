#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "serving_base/utility/timer.h"

#ifndef DISALLOW_COPY_AND_ASSIGN
#define DISALLOW_COPY_AND_ASSIGN(TypeName) \
 private:                                  \
  TypeName(const TypeName &);              \
  void operator=(const TypeName &)
#endif

namespace ks {
namespace platform {

class IdCountEnricher : public CommonRecoBaseEnricher {
 public:
  IdCountEnricher() {}
  ~IdCountEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string item_id_list_attr_;
  std::string output_item_count_list_attr_;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(IdCountEnricher);
};

}  // namespace platform
}  // namespace ks
