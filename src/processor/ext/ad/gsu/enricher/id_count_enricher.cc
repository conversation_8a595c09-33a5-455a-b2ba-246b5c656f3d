#include "dragon/src/processor/ext/ad/gsu/enricher/id_count_enricher.h"

#include <algorithm>
#include <iostream>
#include <unordered_map>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "serving_base/util/factory.h"

namespace ks {
namespace platform {

bool IdCountEnricher::InitProcessor() {
  item_id_list_attr_ = config()->GetString("item_id_list_attr", "");
  output_item_count_list_attr_ = config()->GetString("output_item_count_list_attr", "");

  if (item_id_list_attr_.empty()) {
    CL_LOG(ERROR) << "miss item_id_list_attr";
    return false;
  }

  if (output_item_count_list_attr_.empty()) {
    CL_LOG(ERROR) << "miss output_item_count_list_attr";
    return false;
  }

  return true;
}

void IdCountEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  auto item_id_list = context->GetIntListCommonAttr(item_id_list_attr_);
  uint64_t uid = context->GetUserId();

  if (!item_id_list || item_id_list->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "item_id_list is empty" << ", user_id: " << uid;
    return;
  }

  int64 length = item_id_list->size();
  FB_LOG_EVERY_MS(INFO, 3000) << "user_id: " << uid << ", item_id_list size: " << length;
  timer_.AppendCostMs("get_item_id_list");

  // 统计每个 item_id 的出现次数
  std::unordered_map<int64, int64> count_map;
  for (int64 i = 0; i < length; ++i) {
    int64 item_id = (*item_id_list)[i];
    count_map[item_id]++;
  }
  timer_.AppendCostMs("count_occurrences");

  // 为每个位置生成对应的计数
  std::vector<int64> output_item_count_list;
  output_item_count_list.reserve(length);

  for (int64 i = 0; i < length; ++i) {
    int64 item_id = (*item_id_list)[i];
    output_item_count_list.push_back(count_map[item_id]);
  }
  timer_.AppendCostMs("generate_output");

  // 设置输出属性
  context->SetIntListCommonAttr(output_item_count_list_attr_, std::move(output_item_count_list));

  timer_.AppendCostMs("set_output_attr");
  FB_LOG_EVERY_MS(INFO, 3000) << "user_id: " << uid
                              << ", item_id_list length: " << length
                              << ", unique_items: " << count_map.size()
                              << ", timer: " << timer_.display()
                              << ", total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, IdCountEnricher, IdCountEnricher);

}  // namespace platform
}  // namespace ks
