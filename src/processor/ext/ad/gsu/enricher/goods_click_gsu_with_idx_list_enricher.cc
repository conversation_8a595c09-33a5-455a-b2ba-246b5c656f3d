#include "dragon/src/processor/ext/ad/gsu/enricher/goods_click_gsu_with_idx_list_enricher.h"

#include <algorithm>
#include <iostream>
#include <unordered_map>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "serving_base/util/factory.h"

namespace ks {
namespace platform {

bool GoodsClickGsuWithIdxListEnricher::InitProcessor() {
  timestamp_attr_ = config()->GetString("timestamp_attr", "");
  commodity_id_attr_ = config()->GetString("commodity_id_attr", "");
  seller_id_attr_ = config()->GetString("seller_id_attr", "");
  carry_type_attr_ = config()->GetString("carry_type_attr", "");
  price_attr_ = config()->GetString("price_attr", "");
  item_count_attr_ = config()->GetString("item_count_attr", "");

  topk_indices_attr_ = config()->GetString("topk_indices_attr", "");
  output_suffix_ = config()->GetString("output_suffix", "");
  sideinfo_time_gap_attr_ = config()->GetString("sideinfo_time_gap_attr", "sideinfo_time_gap");

  limit_num_ = config()->GetInt("limit_num", 100);

  if (topk_indices_attr_.empty()) {
    CL_LOG(ERROR) << "miss topk_indices_attr";
    return false;
  }

  if (timestamp_attr_.empty()) {
    CL_LOG(ERROR) << "miss timestamp_attr";
    return false;
  }

  if (commodity_id_attr_.empty()) {
    CL_LOG(ERROR) << "miss commodity_id_attr";
    return false;
  }

  if (seller_id_attr_.empty()) {
    CL_LOG(ERROR) << "miss seller_id_attr";
    return false;
  }

  if (carry_type_attr_.empty()) {
    CL_LOG(ERROR) << "miss carry_type_attr";
    return false;
  }

  if (price_attr_.empty()) {
    CL_LOG(ERROR) << "miss price_attr";
    return false;
  }

  if (item_count_attr_.empty()) {
    CL_LOG(ERROR) << "miss item_count_attr";
    return false;
  }

  if (output_suffix_.empty()) {
    CL_LOG(ERROR) << "miss output_suffix";
    return false;
  }

  return true;
}

void GoodsClickGsuWithIdxListEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  auto timestamp_list = context->GetIntListCommonAttr(timestamp_attr_);
  auto commodity_id_list = context->GetIntListCommonAttr(commodity_id_attr_);
  auto seller_id_list = context->GetIntListCommonAttr(seller_id_attr_);
  auto carry_type_list = context->GetIntListCommonAttr(carry_type_attr_);
  auto price_list = context->GetIntListCommonAttr(price_attr_);
  auto item_count_list = context->GetIntListCommonAttr(item_count_attr_);
  uint64_t uid = context->GetUserId();

  if (!timestamp_list || !commodity_id_list || !seller_id_list ||
      !carry_type_list || !price_list || !item_count_list || timestamp_list->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus no data" << ", user_id: " << uid;
    return;
  }

  int64 length = timestamp_list->size();
  if (commodity_id_list->size() != length ||
      seller_id_list->size() != length ||
      carry_type_list->size() != length ||
      price_list->size() != length ||
      item_count_list->size() != length) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus length invalid: " << length
                                   << ", user_id: " << uid
                                   << ", timestamp_list->size()=" << timestamp_list->size()
                                   << ", commodity_id_list->size()=" << commodity_id_list->size()
                                   << ", seller_id_list->size()=" << seller_id_list->size()
                                   << ", carry_type_list->size()=" << carry_type_list->size()
                                   << ", price_list->size()=" << price_list->size()
                                   << ", item_count_list->size()=" << item_count_list->size();
    return;
  }

  FB_LOG_EVERY_MS(INFO, 3000) << "user_id: " << uid << ", history items size: " << length;
  timer_.AppendCostMs("get_resp");

  // 获取当前请求时间（秒）
  int64 request_time_sec = context->GetRequestTime() / 1000;

  for (auto it = begin; it != end; ++it) {
    const auto& topk_indices_list = context->GetIntListItemAttr(it->item_key, topk_indices_attr_);

    if (!topk_indices_list || topk_indices_list->size() == 0) {
      FB_LOG_EVERY_MS(WARNING, 1000) << "topk_indices is empty for item:" << it->item_key;
      continue;
    }

    // 取前 limit_num_ 个 index
    int64 actual_limit = std::min(static_cast<int64>(topk_indices_list->size()), static_cast<int64>(limit_num_));

    std::vector<int64> output_timestamp;
    std::vector<int64> output_commodity_id;
    std::vector<int64> output_seller_id;
    std::vector<int64> output_carry_type;
    std::vector<int64> output_price;
    std::vector<int64> output_item_count;
    std::vector<int64> output_sideinfo_time_gap;

    output_timestamp.reserve(actual_limit);
    output_commodity_id.reserve(actual_limit);
    output_seller_id.reserve(actual_limit);
    output_carry_type.reserve(actual_limit);
    output_price.reserve(actual_limit);
    output_item_count.reserve(actual_limit);
    output_sideinfo_time_gap.reserve(actual_limit);

    for (int64 j = 0; j < actual_limit; ++j) {
      int64 idx = (*topk_indices_list)[j];

      // 检查索引是否在有效范围内
      if (idx < 0 || idx >= length) {
        FB_LOG_EVERY_MS(WARNING, 1000) << "invalid index: " << idx << ", length: " << length;
        continue;
      }

      // 按 index 获取对应元素
      int64 good_click_timestamp = (*timestamp_list)[idx];
      output_timestamp.push_back(good_click_timestamp);
      output_commodity_id.push_back((*commodity_id_list)[idx]);
      output_seller_id.push_back((*seller_id_list)[idx]);
      output_carry_type.push_back((*carry_type_list)[idx]);
      output_price.push_back((*price_list)[idx]);
      output_item_count.push_back((*item_count_list)[idx]);

      // 计算时间差（天数）
      int64 time_gap = (request_time_sec - good_click_timestamp) / (3600 * 24);
      output_sideinfo_time_gap.push_back(time_gap);
    }

    // 输出结果
    if (!output_timestamp.empty()) {
      context->SetIntListItemAttr(it->item_key, timestamp_attr_ + output_suffix_, std::move(output_timestamp));
    }
    if (!output_commodity_id.empty()) {
      context->SetIntListItemAttr(it->item_key, commodity_id_attr_ + output_suffix_, std::move(output_commodity_id));
    }
    if (!output_seller_id.empty()) {
      context->SetIntListItemAttr(it->item_key, seller_id_attr_ + output_suffix_, std::move(output_seller_id));
    }
    if (!output_carry_type.empty()) {
      context->SetIntListItemAttr(it->item_key, carry_type_attr_ + output_suffix_, std::move(output_carry_type));
    }
    if (!output_price.empty()) {
      context->SetIntListItemAttr(it->item_key, price_attr_ + output_suffix_, std::move(output_price));
    }
    if (!output_item_count.empty()) {
      context->SetIntListItemAttr(it->item_key, item_count_attr_ + output_suffix_, std::move(output_item_count));
    }
    if (!output_sideinfo_time_gap.empty()) {
      context->SetIntListItemAttr(it->item_key, sideinfo_time_gap_attr_ + output_suffix_, std::move(output_sideinfo_time_gap));
    }
  }

  timer_.AppendCostMs("extract_and_fill_attr");
  FB_LOG_EVERY_MS(INFO, 3000) << "user_id: " << uid
                              << ", colossus length: " << length
                              << ", candidate_num: " << end - begin
                              << ", timer: " << timer_.display()
                              << ", total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GoodsClickGsuWithIdxListEnricher, GoodsClickGsuWithIdxListEnricher);

}  // namespace platform
}  // namespace ks
