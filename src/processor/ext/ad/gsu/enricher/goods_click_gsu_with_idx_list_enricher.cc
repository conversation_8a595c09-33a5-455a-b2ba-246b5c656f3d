#include "dragon/src/processor/ext/ad/gsu/enricher/goods_click_gsu_with_idx_list_enricher.h"

#include <algorithm>
#include <iostream>
#include <unordered_map>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {
bool GoodsClickGsuWithIdxListEnricher::GetIntList(const std::string &key, std::vector<int> *vec) {
  if (!vec) {
    CL_LOG(ERROR) << "vec is nullptr!";
    return false;
  }
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    FB_LOG_EVERY_MS(WARNING, 30000) << "key not find, the default slots config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    CL_LOG(ERROR) << "object is not a list, key = " << key;
    return false;
  }
  for (auto *item : json_obj->array()) {
    int val = 0;
    vec->push_back(item->IntValue(val));
  }
  return true;
}

bool GoodsClickGsuWithIdxListEnricher::InitProcessor() {
  timestamp_attr_ = config()->GetString("timestamp_attr", "");
  commodity_id_attr_ = config()->GetString("commodity_id_attr", "");
  seller_id_attr_ = config()->GetString("seller_id_attr", "");
  carry_type_attr_ = config()->GetString("carry_type_attr", "");
  price_attr_ = config()->GetString("price_attr", "");
  item_count_attr_ = config()->GetString("item_count_attr", "");

  topk_indices_attr_ = config()->GetString("topk_indices_attr", "");
  topk_values_attr_ = config()->GetString("topk_values_attr", "");

  limit_num_ = config()->GetInt("limit_num", 100);

  if (topk_indices_attr_.empty()) {
    CL_LOG(ERROR) << "miss topk_indices_attr_";
    return false;
  }

  if (commodity_id_attr_.empty()) {
    CL_LOG(ERROR) << "miss commodity_id_attr";
    return false;
  }
  return true;
}

void GoodsClickGsuWithIdxListEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  auto timestamp_list = context->GetIntListCommonAttr(timestamp_attr_);
  auto commodity_id_list = context->GetIntListCommonAttr(commodity_id_attr_);
  auto pagecode_id_list = context->GetIntListCommonAttr(pagecode_id_attr_);
  auto uniform_spu_id_list = context->GetIntListCommonAttr(uniform_spu_id_attr_);
  auto exposure_ratio_list = context->GetIntListCommonAttr(exposure_ratio_attr_);
  auto exposure_time_list = context->GetIntListCommonAttr(exposure_time_attr_);
  auto detail_content_stay_time_list = context->GetIntListCommonAttr(detail_content_stay_time_attr_);
  auto category_list = context->GetIntListCommonAttr(category_attr_);
  auto leaf_category_list = context->GetIntListCommonAttr(leaf_category_attr_);
  auto seller_id_list = context->GetIntListCommonAttr(seller_id_attr_);
  uint64_t uid = context->GetUserId();

  if (!timestamp_list || !commodity_id_list || !pagecode_id_list || !uniform_spu_id_list ||
      !exposure_ratio_list || !exposure_time_list || !detail_content_stay_time_list ||
      !category_list || !leaf_category_list || !seller_id_list || timestamp_list->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus no data" << ", user_id: " << uid;
    return;
  }
  int length = timestamp_list->size();
  if (timestamp_list->size() != length ||
      commodity_id_list->size() != length ||
      pagecode_id_list->size() != length ||
      uniform_spu_id_list->size() != length ||
      exposure_ratio_list->size() != length ||
      exposure_time_list->size() != length ||
      detail_content_stay_time_list->size() != length ||
      category_list->size() != length ||
      leaf_category_list->size() != length ||
      seller_id_list->size() != length) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus length invalid: " << length
                                   << ", user_id: " << uid
                                   << ", timestamp_list->size()=" << timestamp_list->size();
    return;
  }
  int idx_bias = context->GetIntCommonAttr(input_bias_attr_).value_or(0);
  FB_LOG_EVERY_MS(INFO, 3000) << "user_id: " << uid << ", history items size: " << length
                              << " idx_bias: " << idx_bias;
  timer_.AppendCostMs("get_resp");

#define SIDEINFO_ADD_SIGN(i, expr)                                                           \
  do {                                                                                       \
    if (!fused_slot_sign_remap_) {                                                           \
      sideinfo.slots[i] = mio_slots_ids_[i];                                                 \
      sideinfo.signs[i] = kuiba::Parameter(slots_ids_[i], expr).GetParameterSign();          \
    } else {                                                                                 \
      uint16_t slot_new = 0;                                                                 \
      uint64_t sign_new = 0;                                                                 \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i],                                       \
          kuiba::Parameter(slots_ids_[i], expr).GetParameterSign(), &slot_new, &sign_new)) { \
        sideinfo.signs[i] = sign_new;                                                        \
        sideinfo.slots[i] = slot_new;                                                        \
      }                                                                                      \
    }                                                                                        \
  } while (0)

  int64_t timestamp;
  uint64_t category;
  int64_t cid;
  int64 filter_time_end = context->GetRequestTime() / 1000 - 60;
  auto new_sideinfo_cache = std::make_shared<std::unordered_map<uint64, SideInfo>>();
  int output_cnt = 0;
  int hit_cache_cnt1 = 0;
  int hit_cache_cnt2 = 0;
  int sideinfo_save_cnt = 0;

  std::vector<int64> output_sign;
  std::vector<int64> output_slot;

  for (auto it = begin; it != end; ++it) {
    int result_cnt = 0;
    const auto& soft_idx_list = context->GetIntListItemAttr(it->item_key, input_idx_list_attr_);
    const auto& soft_score_list = context->GetDoubleListItemAttr(it->item_key, soft_score_list_attr_);
    if (!soft_idx_list || soft_idx_list->size() == 0 || !soft_score_list || soft_score_list->size() == 0) {
      FB_LOG_EVERY_MS(WARNING, 30000) << "soft_idx_list " << input_idx_list_attr_
                                      << " soft_score_list:" << soft_score_list_attr_
                                      << " empty for item:" << it->item_key;
      continue;
    }
    if (soft_idx_list->size() != soft_score_list->size()) {
      FB_LOG_EVERY_MS(WARNING, 30000) << "size not match: soft_idx_list:" << soft_idx_list->size()
                                      << " soft_score_list:" << soft_score_list->size();
      return;
    }

    output_sign.reserve(soft_idx_list->size() * slot_cnt_);  // 每个 slot 输出的 slots
    output_slot.reserve(soft_idx_list->size() * slot_cnt_);  // 每个 slot 输出的 signs
    output_sign.clear();
    output_slot.clear();

    for (size_t j = 0; j < soft_idx_list->size(); ++j) {
      const auto& soft_score_value = (*soft_score_list)[j];
      if (soft_score_value == 0.0) {
        continue;  // skip invalid item
      }
      const auto& idx_raw = (*soft_idx_list)[j];
      int i = idx_raw + idx_bias;
      if (i < 0 || i >= length) {
        FB_LOG_EVERY_MS(WARNING, 30000) << "idx value err:" << i;
        continue;
      }
      cid = (*commodity_id_list)[i];
      timestamp = (*timestamp_list)[i];
      if (timestamp > filter_time_end) {
        FB_LOG_EVERY_MS(INFO, 30000) << "filter future item:" << cid << " ts:" << timestamp;
        continue;
      }

      bool hit_flag = false;
      if (use_sideinfo_cache_) {
        auto sideinfo_iter = sideinfo_cache->find(cid);
        if (sideinfo_iter != sideinfo_cache->end()) {
          const SideInfo& sideinfo = sideinfo_iter->second;
          output_sign.insert(output_sign.end(), sideinfo.signs.begin(), sideinfo.signs.end());
          output_slot.insert(output_slot.end(), sideinfo.slots.begin(), sideinfo.slots.end());
          hit_cache_cnt1++;
          hit_flag = true;
        }
      }
      auto new_sideinfo_iter = new_sideinfo_cache->find(cid);
      if (new_sideinfo_iter != new_sideinfo_cache->end()) {
        const SideInfo& sideinfo = new_sideinfo_iter->second;
        output_sign.insert(output_sign.end(), sideinfo.signs.begin(), sideinfo.signs.end());
        output_slot.insert(output_slot.end(), sideinfo.slots.begin(), sideinfo.slots.end());
        hit_cache_cnt2++;
        hit_flag = true;
      }
      if (!hit_flag) {
        SideInfo sideinfo(slot_cnt_);
        SIDEINFO_ADD_SIGN(0, cid);
        SIDEINFO_ADD_SIGN(1, (*pagecode_id_list)[i]);
        SIDEINFO_ADD_SIGN(2, (*uniform_spu_id_list)[i]);
        SIDEINFO_ADD_SIGN(3, (*exposure_ratio_list)[i]);
        SIDEINFO_ADD_SIGN(4, (*exposure_time_list)[i]);
        SIDEINFO_ADD_SIGN(5, (*detail_content_stay_time_list)[i]);
        SIDEINFO_ADD_SIGN(6, (*seller_id_list)[i]);
        SIDEINFO_ADD_SIGN(7, timestamp / 3600 % 24);             // hour_of_day
        SIDEINFO_ADD_SIGN(8, (filter_time_end - timestamp) / 3600);  // 时间 gap 小时
        category = (*category_list)[i];
        SIDEINFO_ADD_SIGN(9, category & 0xFFFF);           // category1
        SIDEINFO_ADD_SIGN(10, (category >> 16) & 0xFFFF);  // category2
        SIDEINFO_ADD_SIGN(11, (category >> 32) & 0xFFFF);  // category3
        SIDEINFO_ADD_SIGN(12, (category >> 48) & 0xFFFF);  // category4
        SIDEINFO_ADD_SIGN(13, (*leaf_category_list)[i]);
        output_sign.insert(output_sign.end(), sideinfo.signs.begin(), sideinfo.signs.end());
        output_slot.insert(output_slot.end(), sideinfo.slots.begin(), sideinfo.slots.end());
        new_sideinfo_cache->emplace(cid, sideinfo);
        sideinfo_save_cnt++;
      }
#undef SIDEINFO_ADD_SIGN
      ++result_cnt;
      ++output_cnt;
      if (result_cnt >= limit_num_) {
        break;
      }
    }
    context->SetIntListItemAttr(it->item_key, output_sign_attr_, std::vector<int64>(output_sign));
    context->SetIntListItemAttr(it->item_key, output_slot_attr_, std::vector<int64>(output_slot));
  }
  auto unique_item_size = commodity_id_list->size();
  timer_.AppendCostMs("extract_signs_and_fill_attr");
  FB_LOG_EVERY_MS(INFO, 30000) << " output_sign_name: " << output_sign_attr_
                               << ", filter_time_end: " << filter_time_end
                               << ", user_id: " << uid
                               << ", colossus length: " << length
                               << ", colossus unique_item_size: " << unique_item_size
                               << ", candidate_num: " << end - begin
                               << ", output_cnt: " << output_cnt
                               << ", hit_cache_cnt1: " << hit_cache_cnt1
                               << ", hit_cache_cnt2: " << hit_cache_cnt2
                               << ", sideinfo_save_cnt: " << sideinfo_save_cnt
                               << ", timer: " << timer_.display()
                               << ", total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GoodsClickGsuWithIdxListEnricher, GoodsClickGsuWithIdxListEnricher);

}  // namespace platform
}  // namespace ks
