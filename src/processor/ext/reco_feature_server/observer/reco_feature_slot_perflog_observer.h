#pragma once

#include <vector>
#include <string>

#include "base/random/pseudo_random.h"
#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks {
namespace platform {

class RecoFeatureSlotPerflogObserver : public CommonRecoBaseObserver {
 public:
  RecoFeatureSlotPerflogObserver() : random_(base::GetTimestamp()) {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    const auto *common_slot_params = config()->Get("common_slots");
    const auto *common_parameter_params = config()->Get("common_signs");
    if (common_slot_params && common_parameter_params) {
      if (!RecoUtil::ExtractStringListFromJsonConfig(common_slot_params, &common_slot_names_)) {
        LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'common_slots' "
                   << "should be a string array!";
        return false;
      }
      if (!RecoUtil::ExtractStringListFromJsonConfig(common_parameter_params, &common_parameter_names_)) {
        LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'common_signs' "
                   << "should be a string array!";
        return false;
      }
    } else if (common_slot_params || common_parameter_params) {
      LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'common_slots' or "
                 << "'common_signs' is empty!";
      return false;
    }

    const auto *item_slot_params = config()->Get("item_slots");
    const auto *item_parameter_params = config()->Get("item_signs");
    if (item_slot_params && item_parameter_params) {
      if (!RecoUtil::ExtractStringListFromJsonConfig(item_slot_params, &item_slot_names_)) {
        LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'item_slots' should be a string array!";
        return false;
      }
      if (!RecoUtil::ExtractStringListFromJsonConfig(item_parameter_params, &item_parameter_names_)) {
        LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'item_signs' "
                   << "should be a string array!";
        return false;
      }
    } else if (item_slot_params || item_parameter_params) {
      LOG(ERROR) << "RecoFeatureSlotPerflogObserver init failed! 'item_slots' or 'item_signs' is empty!";
      return false;
    }
    return true;
  }

 private:
  std::vector<std::string> common_slot_names_;
  std::vector<std::string> common_parameter_names_;
  std::vector<std::string> item_slot_names_;
  std::vector<std::string> item_parameter_names_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(RecoFeatureSlotPerflogObserver);
};

}  // namespace platform
}  // namespace ks
