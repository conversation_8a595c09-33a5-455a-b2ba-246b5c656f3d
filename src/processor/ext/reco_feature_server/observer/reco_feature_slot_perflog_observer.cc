#include <chrono>

#include "dragon/src/processor/ext/reco_feature_server/observer/reco_feature_slot_perflog_observer.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"


namespace ks {
namespace platform {

void RecoFeatureSlotPerflogObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  std::string name_space = "common.leaf";
  std::string service_name = GlobalHolder::GetServiceIdentifier();
  std::string request_type = context->GetRequestType();
  int perf_base = 1000;
  if (!common_slot_names_.empty()) {
    // common 侧
    folly::F14FastMap<int64, folly::F14FastSet<int64>> common_slot_uniq_parameters;
    folly::F14FastMap<int64, int64> common_slot_parameters_count;
    int64 common_parameters_count = 0;
    for (int i = 0; i < common_slot_names_.size(); ++i) {
      auto common_slot_accessor = context->GetCommonAttrAccessor(common_slot_names_[i]);
      auto common_slots = context->GetIntListCommonAttr(common_slot_accessor);
      if (!common_slots.has_value()) {
        continue;
      }
      auto common_parameter_accessor = context->GetCommonAttrAccessor(common_parameter_names_[i]);
      auto common_parameters = context->GetIntListCommonAttr(common_parameter_accessor);
      if (!common_parameters.has_value()) {
        continue;
      }
      if (common_slots->size() != common_parameters->size()) {
        CL_LOG_ERROR("size_mismatch", "common_attr error") << "common_slots size != common_signs size";
        return;
      }
      common_parameters_count += common_parameters->size();
      for (int j = 0; j < common_slots->size(); ++j) {
        common_slot_uniq_parameters[(*common_slots)[j]].insert((*common_parameters)[j]);
        ++common_slot_parameters_count[(*common_slots)[j]];
      }
    }

    for (const auto& it : common_slot_uniq_parameters) {
      auto num = it.second.size();
      if (num == 0) {
        continue;
      }
      double repetition = static_cast<double>(common_slot_parameters_count[it.first]) / num;
      std::string name = std::to_string(it.first);
      CL_PERF_INTERVAL(common_slot_parameters_count[it.first], name_space, "common_slot_extraction_count",
                       service_name, request_type, "", name);
      CL_PERF_INTERVAL(repetition * perf_base, name_space, "common_slot_extraction_repetition",
                       service_name, request_type, "", name);
    }
    CL_PERF_INTERVAL(common_slot_uniq_parameters.size() * perf_base, name_space, "feature", service_name,
                     request_type, "", "common_slots_count");
    CL_PERF_INTERVAL(common_parameters_count * perf_base, name_space, "feature", service_name, request_type,
                     "", "common_signs_count");
  }
  if (item_slot_names_.empty()) {
    return;
  }
  // item 侧
  double prob = GetDoubleProcessorParameter(context, "prob", 0.1);
  if (prob <= 0.0 || prob > 1.0) {
    CL_LOG_ERROR("probility out_of_range", "parameter error") << "prob should be in range (0.0, 1.0]";
    return;
  }
  folly::F14FastMap<int64, int> item_slot_counts;
  folly::F14FastMap<int64, double> item_slot_repetitions;
  folly::F14FastMap<int64, int> slot_item_count;
  int item_size = 0;
  for (auto result_iter = begin; result_iter != end; ++result_iter) {
    double random_double = random_.GetDouble();
    if (random_double > prob) {
      continue;
    }
    const CommonRecoResult &result = *result_iter;
    ++item_size;
    folly::F14FastMap<int64, folly::F14FastSet<int64>> item_slot_uniq_parameters;
    folly::F14FastMap<int64, int64> item_slot_parameters_count;
    for (int i = 0; i < item_slot_names_.size(); ++i) {
      auto item_slot_accessor = context->GetItemAttrAccessor(item_slot_names_[i]);
      auto item_slots = context->GetIntListItemAttr(result, item_slot_accessor);
      if (!item_slots.has_value()) {
        continue;
      }
      auto item_parameter_accessor = context->GetItemAttrAccessor(item_parameter_names_[i]);
      auto item_parameters = context->GetIntListItemAttr(result, item_parameter_accessor);
      if (!item_parameters.has_value()) {
        continue;
      }
      if (item_slots->size() != item_parameters->size()) {
        CL_LOG_ERROR("size_mismatch", "item_attr error") << "item_slots_size != item_signs_size";
        return;
      }
      for (int j = 0; j < item_slots->size(); ++j) {
        item_slot_uniq_parameters[(*item_slots)[j]].insert((*item_parameters)[j]);
        ++item_slot_parameters_count[(*item_slots)[j]];
      }
    }
    for (const auto& it : item_slot_uniq_parameters) {
      auto num = it.second.size();
      if (num == 0) {
        continue;
      }
      double repetition = static_cast<double>(item_slot_parameters_count[it.first]) / num;
      item_slot_counts[it.first] += item_slot_parameters_count[it.first];
      item_slot_repetitions[it.first] += repetition;
      ++slot_item_count[it.first];
    }
  }
  if (item_size == 0) {
    return;
  }
  int64 signsCount = 0;
  for (const auto& it : item_slot_counts) {
    if (slot_item_count[it.first] == 0) {
      continue;
    }
    signsCount += it.second;
    std::string name = std::to_string(it.first);
    double numbers = static_cast<double>(it.second) / slot_item_count[it.first];
    double repetition = item_slot_repetitions[it.first] / slot_item_count[it.first];
    CL_PERF_INTERVAL(numbers * perf_base, name_space, "item_slot_avg_extraction_count", service_name,
                     request_type, "", name);
    CL_PERF_INTERVAL(repetition * perf_base, name_space, "item_slot_avg_extraction_repetition",
                     service_name, request_type, "", name);
  }
  double avg_signs_count = static_cast<double>(signsCount) / item_size;
  CL_PERF_INTERVAL(item_slot_counts.size() * perf_base, name_space, "feature", service_name, request_type,
                   "", "item_slots_count");
  CL_PERF_INTERVAL(avg_signs_count * perf_base, name_space, "feature", service_name, request_type, "",
                   "item_signs_count");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, RecoFeatureSlotPerflogObserver, RecoFeatureSlotPerflogObserver)

}  // namespace platform
}  // namespace ks
