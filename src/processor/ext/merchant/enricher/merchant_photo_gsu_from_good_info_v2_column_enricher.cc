#include "dragon/src/processor/ext/merchant/enricher/merchant_photo_gsu_from_good_info_v2_column_enricher.h"

#include <iostream>
#include <algorithm>
#include <unordered_map>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/client/rpc_client.h"
#include "teams/reco-arch/colossus/client/snack_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool MerchantPhotoGsuFromGoodInfoV2ColumnEnricher::InitProcessor() {
  target_cluster_attr_ = config()->GetString("target_cluster_attr", "");
  if (target_cluster_attr_.empty()) {
    LOG(ERROR) << "miss target_cluster_attr";
    return false;
  }
  limit_num_ = config()->GetInt("limit_num_attr", 50);
  filter_future_attr_ = config()->GetBoolean("filter_future_attr", true);
  entity_match_attr_ = config()->GetBoolean("entity_match_attr", true);
  cate1_match_attr_ = config()->GetBoolean("cate1_match_attr", true);
  cate2_match_attr_ = config()->GetBoolean("cate2_match_attr", true);
  cate3_match_attr_ = config()->GetBoolean("cate3_match_attr", true);

  // input attr
  input_photo_id_attr_ = config()->GetString("input_photo_id_attr", "");
  input_author_id_attr_ = config()->GetString("input_author_id_attr", "");
  input_duration_attr_ = config()->GetString("input_duration_attr", "");
  input_play_time_attr_ = config()->GetString("input_play_time_attr", "");
  input_tag_attr_ = config()->GetString("input_tag_attr", "");
  input_channel_attr_ = config()->GetString("input_channel_attr", "");
  input_label_attr_ = config()->GetString("input_label_attr", "");
  input_timestamp_attr_ = config()->GetString("input_timestamp_attr", "");
  if (input_photo_id_attr_.empty() || input_author_id_attr_.empty() || input_duration_attr_.empty() ||
      input_play_time_attr_.empty() || input_tag_attr_.empty() || input_channel_attr_.empty() ||
      input_label_attr_.empty() || input_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss input colossus attr";
    return false;
  }

  photo_id_list_attr_ = config()->GetString("photo_id_list_attr", "cart_photo_gsu_pid_list");
  author_id_list_attr_ = config()->GetString("author_id_list_attr", "cart_photo_gsu_aid_list");
  duration_list_attr_ = config()->GetString("duration_list_attr", "cart_photo_gsu_duration_list");
  play_time_list_attr_ = config()->GetString("play_time_list_attr", "cart_photo_gsu_play_time_list");
  play_lag_list_attr_ = config()->GetString("play_lag_list_attr", "cart_photo_gsu_lag_list");
  tag_list_attr_ = config()->GetString("tag_list_attr", "cart_photo_gsu_tag_list");
  channel_list_attr_ = config()->GetString("channel_list_attr", "cart_photo_gsu_channel_list");
  label_list_attr_ = config()->GetString("label_list_attr", "cart_photo_gsu_label_list");
  index_list_attr_ = config()->GetString("index_list_attr", "cart_photo_gsu_index_list");
  cate1_list_attr_ = config()->GetString("cate1_list_attr", "cart_photo_gsu_cate1_list");
  cate2_list_attr_ = config()->GetString("cate2_list_attr", "cart_photo_gsu_cate2_list");
  cate3_list_attr_ = config()->GetString("cate3_list_attr", "cart_photo_gsu_cate3_list");
  entity_list_attr_ = config()->GetString("entity_list_attr", "cart_photo_gsu_entity_list");

  timeout_ms_ = config()->GetInt("timeout_ms", 10);

  kess_service_ = config()->GetString("kess_service");
  if (kess_service_.empty()) {
    LOG(ERROR) << "MerchantPhotoGsuFromGoodInfoV2ColumnEnricher init failed!"
               << "Config of kess_service is empty.";
    return false;
  }
  shards_ = config()->GetInt("shards", -1);
  if (shards_ <= 0) {
    LOG(ERROR) << "MerchantPhotoGsuFromGoodInfoV2ColumnEnricher init failed!"
               << "the config of shards is invalid";
    return false;
  }
  max_pids_per_request_ = config()->GetInt("max_pids_per_request", 0);

  kess_cluster_ = config()->GetString("kess_cluster", "PRODUCTION");
  kess_client_ = ks::kess::rpc::grpc::GrpcClientBuilder(kess_service_)
                     .SetAsyncThreadNum(config()->GetInt("thread_num", 1))
                     .SetCluster(kess_cluster_)
                     .Build();
  return true;
}

void MerchantPhotoGsuFromGoodInfoV2ColumnEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();

  timer_.AppendCostMs("get_colossus_resp");

  // unique candidate photo ids
  thread_local std::unordered_set<uint64> photo_set;
  photo_set.clear();
  // unique candidate photo indexes
  thread_local std::vector<int> photo_idx;
  photo_idx.clear();
    // for cluster id request
  thread_local std::vector<uint64> request_pids;
  request_pids.clear();
  // for cluster id response
  thread_local std::vector<uint64> response_cluster_ids;
  response_cluster_ids.clear();
  thread_local std::unordered_map<uint64, uint64> pid_cluster_id_map;
  pid_cluster_id_map.clear();
  // for candidate photos, cate1 id -> unique candidate photo indexes, keys do not contain kInvalidClusterId
  thread_local std::unordered_map<uint16, std::vector<int>> cate1_index;
  cate1_index.clear();
  // for candidate photos, cate2 id -> unique candidate photo indexes, keys do not contain kInvalidClusterId
  thread_local std::unordered_map<uint16, std::vector<int>> cate2_index;
  cate2_index.clear();
  // for candidate photos, cate3 id -> unique candidate photo indexes, keys do not contain kInvalidClusterId
  thread_local std::unordered_map<uint16, std::vector<int>> cate3_index;
  cate3_index.clear();
  // for candidate photos, entity id -> unique candidate photo indexes, keys do not contain kInvalidClusterId
  thread_local std::unordered_map<uint32, std::vector<int>> entity_index;
  entity_index.clear();
  // all target cate1 id
  thread_local std::unordered_set<uint16> target_cate1_ids;
  target_cate1_ids.clear();
  // all target cate2 id
  thread_local std::unordered_set<uint16> target_cate2_ids;
  target_cate2_ids.clear();
  // all target cate1 id
  thread_local std::unordered_set<uint16> target_cate3_ids;
  target_cate3_ids.clear();
  // all target entity id
  thread_local std::unordered_set<uint32> target_entity_ids;
  target_entity_ids.clear();

  auto input_photo_id_list = context->GetIntListCommonAttr(input_photo_id_attr_);
  auto input_author_id_list = context->GetIntListCommonAttr(input_author_id_attr_);
  auto input_duration_list = context->GetIntListCommonAttr(input_duration_attr_);
  auto input_play_time_list = context->GetIntListCommonAttr(input_play_time_attr_);
  auto input_tag_list = context->GetIntListCommonAttr(input_tag_attr_);
  auto input_channel_list = context->GetIntListCommonAttr(input_channel_attr_);
  auto input_label_list = context->GetIntListCommonAttr(input_label_attr_);
  auto input_timestamp_list = context->GetIntListCommonAttr(input_timestamp_attr_);

  auto get_input_list_len = [&](const auto &list) { return list ? list->size() : 0; };
  int align_colossus_size =
      std::min({get_input_list_len(input_photo_id_list), get_input_list_len(input_author_id_list),
                get_input_list_len(input_duration_list), get_input_list_len(input_play_time_list),
                get_input_list_len(input_tag_list), get_input_list_len(input_channel_list),
                get_input_list_len(input_label_list), get_input_list_len(input_timestamp_list)});
  if (align_colossus_size == 0) {
    CL_LOG(INFO) << "align_colossus_size == 0";
    return;
  }

  int64 filter_time = context->GetRequestTime() / 1000 - 60;
  for (int i = align_colossus_size - 1; i >= 0; --i) {
    uint64 pid = input_photo_id_list->at(i);
    if (filter_future_attr_) {
      if (input_timestamp_list->at(i) > filter_time) {
        continue;
      }
    }
    if ((input_label_list->at(i) & (1 << 14)) == 0) {
      continue;
    }
    if (photo_set.find(pid) != photo_set.end()) {
      continue;
    }
    photo_set.insert(pid);
    photo_idx.emplace_back(i);
    request_pids.emplace_back(pid);
  }
  auto unique_item_size = photo_idx.size();
  timer_.AppendCostMs("unique_candidate_photos");

  auto item_attr_accessor = context->GetItemAttrAccessor(target_cluster_attr_);
  for (auto it = begin; it != end; ++it) {
    uint64 target_cluster_id = 0;
    if (context->HasItemAttr(*it, item_attr_accessor)) {
      auto cluster_id = context->GetIntItemAttr(*it, item_attr_accessor);
      target_cluster_id = (uint64_t)(*cluster_id);
    }
    if (target_cluster_id <= 0) {
      CL_LOG(WARNING) << "invalid target_cluster for item: " << it->item_key;
      continue;
    }
    uint16 target_cate1 = (target_cluster_id >> 47) & 0x7fff;
    uint16 target_cate2 = (target_cluster_id >> 32) & 0x7fff;
    uint16 target_cate3 = (target_cluster_id >> 17) & 0x7fff;
    uint32 target_entity = target_cluster_id & 0x1ffff;
    if (target_cate1 > 0) target_cate1_ids.insert(target_cate1);
    if (target_cate2 > 0) target_cate2_ids.insert(target_cate2);
    if (target_cate3 > 0) target_cate3_ids.insert(target_cate3);
    if (target_entity > 0) target_entity_ids.insert(target_entity);
    CL_LOG_EVERY_N(INFO, 10000) << "item key:" << it->item_key
                          << ", item key cluster id:" << target_cluster_id;
  }
  if (target_cate1_ids.size() <= 0 && target_cate2_ids.size() <= 0 && target_cate3_ids.size() <= 0 &&
      target_entity_ids.size() <=0) {
      CL_LOG(WARNING) << "request no cluster id: ";
      return;
  }

  if (!FetchClusterIdsFromEmbeddingServer(request_pids, &response_cluster_ids, &pid_cluster_id_map)) {
    return;
  }
  timer_.AppendCostMs("fetch_cluster_id");

  for (int i = 0; i < unique_item_size; i++) {
    if (response_cluster_ids[i] > 0) {
      uint64 cluster_id = response_cluster_ids[i];
      uint16 cate1 = (cluster_id >> 47) & 0x7fff;
      uint16 cate2 = (cluster_id >> 32) & 0x7fff;
      uint16 cate3 = (cluster_id >> 17) & 0x7fff;
      uint32 entity = cluster_id & 0x1ffff;
      if (entity > 0 && target_entity_ids.find(entity) != target_entity_ids.end()) {
        entity_index[entity].emplace_back(photo_idx[i]);
      }
      if (cate3 > 0 && target_cate3_ids.find(cate3) != target_cate3_ids.end()) {
        cate3_index[cate3].emplace_back(photo_idx[i]);
      }
      if (cate2 > 0 && target_cate2_ids.find(cate2) != target_cate2_ids.end()) {
        cate2_index[cate2].emplace_back(photo_idx[i]);
      }
      if (cate1 > 0 && target_cate1_ids.find(cate1) != target_cate1_ids.end()) {
        cate1_index[cate1].emplace_back(photo_idx[i]);
      }
    }
  }

  timer_.AppendCostMs("build_cluster_index");

  thread_local std::unordered_set<uint64> photo_id_set;
  photo_id_set.clear();
  thread_local std::vector<int64> photo_id_list;
  photo_id_list.clear();
  thread_local std::vector<int64> author_id_list;
  author_id_list.clear();
  thread_local std::vector<int64> duration_list;
  duration_list.clear();
  thread_local std::vector<int64> play_time_list;
  play_time_list.clear();
  thread_local std::vector<int64> play_lag_list;
  play_lag_list.clear();
  thread_local std::vector<int64> tag_list;
  tag_list.clear();
  thread_local std::vector<int64> channel_list;
  channel_list.clear();
  thread_local std::vector<int64> label_list;
  label_list.clear();
  thread_local std::vector<int64> cate1_list;
  cate1_list.clear();
  thread_local std::vector<int64> cate2_list;
  cate2_list.clear();
  thread_local std::vector<int64> cate3_list;
  cate3_list.clear();
  thread_local std::vector<int64> entity_list;
  entity_list.clear();
  thread_local std::vector<int64> index_list;
  index_list.clear();

  for (auto it = begin; it != end; ++it) {
    uint64 item_key = it->item_key;
    uint64 target_cluster_id = 0;
    if (context->HasItemAttr(*it, item_attr_accessor)) {
      auto cluster_id_iter = context->GetIntItemAttr(*it, item_attr_accessor);
      target_cluster_id = (uint64_t)(*cluster_id_iter);
    }
    if (target_cluster_id <= 0) {
      continue;
    }
    photo_id_set.clear();
    photo_id_list.clear();
    author_id_list.clear();
    duration_list.clear();
    play_time_list.clear();
    play_lag_list.clear();
    tag_list.clear();
    channel_list.clear();
    label_list.clear();
    index_list.clear();
    cate1_list.clear();
    cate2_list.clear();
    cate3_list.clear();
    entity_list.clear();
    uint16 target_cate1 = (target_cluster_id >> 47) & 0x7fff;
    uint16 target_cate2 = (target_cluster_id >> 32) & 0x7fff;
    uint16 target_cate3 = (target_cluster_id >> 17) & 0x7fff;
    uint32 target_entity = target_cluster_id & 0x1ffff;
    int cnt = 0;
    // Note: 使用这部分 entity_match_attr_ = true 请自行验证一下逻辑正确性
    if (target_entity > 0 && entity_match_attr_) {
      for (auto offset : entity_index[target_entity]) {
        uint64 cluster_id = pid_cluster_id_map[input_photo_id_list->at(offset)];
        uint16 cate1 = (cluster_id >> 47) & 0x7fff;
        uint16 cate2 = (cluster_id >> 32) & 0x7fff;
        uint16 cate3 = (cluster_id >> 17) & 0x7fff;
        uint32 entity = cluster_id & 0x1ffff;
        photo_id_set.insert(input_photo_id_list->at(offset));
        photo_id_list.emplace_back(input_photo_id_list->at(offset));
        author_id_list.emplace_back(input_author_id_list->at(offset));
        duration_list.emplace_back(input_duration_list->at(offset));
        play_time_list.emplace_back(input_play_time_list->at(offset));
        play_lag_list.emplace_back((filter_time - input_timestamp_list->at(offset)) / (3600 * 24));
        tag_list.emplace_back(input_tag_list->at(offset));
        channel_list.emplace_back(input_channel_list->at(offset));
        label_list.emplace_back(input_label_list->at(offset));
        cate1_list.emplace_back(cate1);
        cate2_list.emplace_back(cate2);
        cate3_list.emplace_back(cate3);
        entity_list.emplace_back(entity);
        index_list.emplace_back(cnt);
        ++cnt;
        if (cnt >= limit_num_) break;
      }
    }
    if (cnt < limit_num_ && target_cate3 > 0 && cate3_match_attr_) {
      for (auto offset : cate3_index[target_cate3]) {
        if (photo_id_set.find(input_photo_id_list->at(offset)) != photo_id_set.end()) continue;
        uint64 cluster_id = pid_cluster_id_map[input_photo_id_list->at(offset)];
        uint16 cate1 = (cluster_id >> 47) & 0x7fff;
        uint16 cate2 = (cluster_id >> 32) & 0x7fff;
        uint16 cate3 = (cluster_id >> 17) & 0x7fff;
        uint32 entity = cluster_id & 0x1ffff;
        photo_id_set.insert(input_photo_id_list->at(offset));
        photo_id_list.emplace_back(input_photo_id_list->at(offset));
        author_id_list.emplace_back(input_author_id_list->at(offset));
        duration_list.emplace_back(input_duration_list->at(offset));
        play_time_list.emplace_back(input_play_time_list->at(offset));
        play_lag_list.emplace_back((filter_time - input_timestamp_list->at(offset)) / (3600 * 24));
        tag_list.emplace_back(input_tag_list->at(offset));
        channel_list.emplace_back(input_channel_list->at(offset));
        label_list.emplace_back(input_label_list->at(offset));
        cate1_list.emplace_back(cate1);
        cate2_list.emplace_back(cate2);
        cate3_list.emplace_back(cate3);
        entity_list.emplace_back(entity);
        index_list.emplace_back(cnt);
        ++cnt;
        if (cnt >= limit_num_) break;
      }
    }

    if (cnt < limit_num_ && target_cate2 > 0 && cate2_match_attr_) {
      for (auto offset : cate2_index[target_cate2]) {
        if (photo_id_set.find(input_photo_id_list->at(offset)) != photo_id_set.end()) continue;
        uint64 cluster_id = pid_cluster_id_map[input_photo_id_list->at(offset)];
        uint16 cate1 = (cluster_id >> 47) & 0x7fff;
        uint16 cate2 = (cluster_id >> 32) & 0x7fff;
        uint16 cate3 = (cluster_id >> 17) & 0x7fff;
        uint32 entity = cluster_id & 0x1ffff;
        photo_id_set.insert(input_photo_id_list->at(offset));
        photo_id_list.emplace_back(input_photo_id_list->at(offset));
        author_id_list.emplace_back(input_author_id_list->at(offset));
        duration_list.emplace_back(input_duration_list->at(offset));
        play_time_list.emplace_back(input_play_time_list->at(offset));
        play_lag_list.emplace_back((filter_time - input_timestamp_list->at(offset)) / (3600 * 24));
        tag_list.emplace_back(input_tag_list->at(offset));
        channel_list.emplace_back(input_channel_list->at(offset));
        label_list.emplace_back(input_label_list->at(offset));
        cate1_list.emplace_back(cate1);
        cate2_list.emplace_back(cate2);
        cate3_list.emplace_back(cate3);
        entity_list.emplace_back(entity);
        index_list.emplace_back(cnt);
        ++cnt;
        if (cnt >= limit_num_) break;
      }
    }
    if (cnt < limit_num_ && target_cate1 > 0 && cate1_match_attr_) {
      for (auto offset : cate1_index[target_cate1]) {
        if (photo_id_set.find(input_photo_id_list->at(offset)) != photo_id_set.end()) continue;
        uint64 cluster_id = pid_cluster_id_map[input_photo_id_list->at(offset)];
        uint16 cate1 = (cluster_id >> 47) & 0x7fff;
        uint16 cate2 = (cluster_id >> 32) & 0x7fff;
        uint16 cate3 = (cluster_id >> 17) & 0x7fff;
        uint32 entity = cluster_id & 0x1ffff;
        photo_id_set.insert(input_photo_id_list->at(offset));
        photo_id_list.emplace_back(input_photo_id_list->at(offset));
        author_id_list.emplace_back(input_author_id_list->at(offset));
        duration_list.emplace_back(input_duration_list->at(offset));
        play_time_list.emplace_back(input_play_time_list->at(offset));
        play_lag_list.emplace_back((filter_time - input_timestamp_list->at(offset)) / (3600 * 24));
        tag_list.emplace_back(input_tag_list->at(offset));
        channel_list.emplace_back(input_channel_list->at(offset));
        label_list.emplace_back(input_label_list->at(offset));
        cate1_list.emplace_back(cate1);
        cate2_list.emplace_back(cate2);
        cate3_list.emplace_back(cate3);
        entity_list.emplace_back(entity);
        index_list.emplace_back(cnt);
        ++cnt;
        if (cnt >= limit_num_) break;
      }
    }
    if (photo_id_list.size() > 0) {
      context->SetIntListItemAttr(item_key, photo_id_list_attr_, std::move(photo_id_list));
      context->SetIntListItemAttr(item_key, author_id_list_attr_, std::move(author_id_list));
      context->SetIntListItemAttr(item_key, duration_list_attr_, std::move(duration_list));
      context->SetIntListItemAttr(item_key, play_time_list_attr_, std::move(play_time_list));
      context->SetIntListItemAttr(item_key, play_lag_list_attr_, std::move(play_lag_list));
      context->SetIntListItemAttr(item_key, tag_list_attr_, std::move(tag_list));
      context->SetIntListItemAttr(item_key, channel_list_attr_, std::move(channel_list));
      context->SetIntListItemAttr(item_key, label_list_attr_, std::move(label_list));
      context->SetIntListItemAttr(item_key, cate1_list_attr_, std::move(cate1_list));
      context->SetIntListItemAttr(item_key, cate2_list_attr_, std::move(cate2_list));
      context->SetIntListItemAttr(item_key, cate3_list_attr_, std::move(cate3_list));
      context->SetIntListItemAttr(item_key, entity_list_attr_, std::move(entity_list));
      context->SetIntListItemAttr(item_key, index_list_attr_, std::move(index_list));
    }
    LOG_EVERY_N(INFO, 10000) << ",target_cluster_id:" << target_cluster_id
                            << ", target_entity:" << target_entity
                            << ", target_cate3:" << target_cate3
                            << ", target_cate2:" << target_cate2
                            << ", target_cate1:" << target_cate1
                             << ", cnt:" << cnt;
  }

  timer_.AppendCostMs("cluster_search");

  CL_LOG(INFO) << ", limit_num: " << limit_num_ << ", filter_time: " << filter_time
               << ", timer: " << timer_.display() << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

bool MerchantPhotoGsuFromGoodInfoV2ColumnEnricher::FetchClusterIdsFromEmbeddingServer(
    const std::vector<uint64> &pids, std::vector<uint64> *cluster_ids,
    std::unordered_map<uint64, uint64> *pid_cluster_id_map) {
  if (pids.empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "fetch cluster ids from embedding server cancelled: empty pids";
    return false;
  }
  ks::kess::rpc::BatchWaiter batch_waiter;
  thread_local std::mutex map_mutex;
  auto send_request_to_specific_shard = [&, this](
                                            int shard, const ks::reco::GetKuibaEmbeddingRequest &request) {
    ks::kess::rpc::grpc::Options options;
    options.SetTimeout(std::chrono::milliseconds(timeout_ms_));
    std::string shard_name = "s" + std::to_string(shard);
    auto *response = response_pool_.Acquire();
    response->Clear();
    batch_waiter.Add(kess_client_->ByShard(shard_name)
                         ->SelectOne()
                         ->Stub<ks::reco::kess::PredictKessService::Stub>()
                         ->AsyncGetKuibaEmbedding(options, request, response),
                     [&, this, shard_name](const ::grpc::Status &status,
                                                        ks::reco::GetKuibaEmbeddingResponse *response) {
                       if (status.ok()) {
                         std::lock_guard<std::mutex> lock(map_mutex);
                         for (int i = 0; i < response->signs_size(); i++) {
                           uint64 id = 0;
                           if (response->values(i).size() == sizeof(uint64)) {
                             id = *reinterpret_cast<const uint64 *>(response->values(i).data());
                             LOG_EVERY_N(INFO, 10000)
                                 << "valid cluster id size for pid: " << pids[i] << ",id:" << id;
                           } else {
                             LOG_EVERY_N(WARNING, 10000) << "invalid cluster id size for pid: " << pids[i];
                           }
                           (*pid_cluster_id_map)[response->signs(i)] = id;
                         }
                       }
                       response_pool_.Recycle(response);
                     });
  };

  std::vector<ks::reco::GetKuibaEmbeddingRequest> requests(shards_);
  for (uint64 pid : pids) {
    int shard = pid % shards_;
    auto &request = requests[shard];
    request.add_signs(pid);
    if (max_pids_per_request_ > 0 && request.signs_size() >= max_pids_per_request_) {
      send_request_to_specific_shard(shard, request);
      request.clear_signs();
    }
  }
  for (int i = 0; i < shards_; i++) {
    const auto &request = requests[i];
    if (request.signs_size() > 0) {
      send_request_to_specific_shard(i, request);
    }
  }

  batch_waiter.Wait();
  if (pid_cluster_id_map->empty()) {
    return false;
  }
  cluster_ids->clear();
  cluster_ids->reserve(pids.size());
  for (auto pid : pids) {
    auto it = pid_cluster_id_map->find(pid);
    if (it != pid_cluster_id_map->end()) {
      cluster_ids->push_back(it->second);
    } else {
      cluster_ids->push_back(0);
    }
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantPhotoGsuFromGoodInfoV2ColumnEnricher,
  MerchantPhotoGsuFromGoodInfoV2ColumnEnricher);

}  // namespace platform
}  // namespace ks
