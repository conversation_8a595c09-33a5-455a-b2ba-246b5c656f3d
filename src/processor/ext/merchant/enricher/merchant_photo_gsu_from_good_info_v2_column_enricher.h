#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

class MerchantPhotoGsuFromGoodInfoV2ColumnEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantPhotoGsuFromGoodInfoV2ColumnEnricher() {}
  ~MerchantPhotoGsuFromGoodInfoV2ColumnEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool FetchClusterIdsFromEmbeddingServer(
    const std::vector<uint64> &pids, std::vector<uint64> *cluster_ids,
    std::unordered_map<uint64, uint64> *pid_cluster_id_map);

 private:
  std::string colossus_resp_attr_;
  bool filter_future_attr_ = true;
  int limit_num_ = 50;
  std::string target_cluster_attr_;
  bool entity_match_attr_ = true;
  bool cate1_match_attr_ = true;
  bool cate2_match_attr_ = true;
  bool cate3_match_attr_ = true;

  std::string kess_service_, kess_cluster_;
  int shards_ = 1;
  std::shared_ptr<ks::kess::rpc::grpc::Client2> kess_client_;
  int max_pids_per_request_ = 1;
  CommonRecoObjectPool<ks::reco::GetKuibaEmbeddingResponse> response_pool_;

  int timeout_ms_ = 10;

  std::string photo_id_list_attr_;
  std::string author_id_list_attr_;
  std::string duration_list_attr_;
  std::string play_time_list_attr_;
  std::string play_lag_list_attr_;
  std::string tag_list_attr_;
  std::string channel_list_attr_;
  std::string label_list_attr_;
  std::string index_list_attr_;
  std::string cate1_list_attr_;
  std::string cate2_list_attr_;
  std::string cate3_list_attr_;
  std::string entity_list_attr_;
  // input
  std::string input_photo_id_attr_;
  std::string input_author_id_attr_;
  std::string input_duration_attr_;
  std::string input_play_time_attr_;
  std::string input_tag_attr_;
  std::string input_channel_attr_;
  std::string input_label_attr_;
  std::string input_timestamp_attr_;

  serving_base::Timer timer_;


  DISALLOW_COPY_AND_ASSIGN(MerchantPhotoGsuFromGoodInfoV2ColumnEnricher);
};

}  // namespace platform
}  // namespace ks
