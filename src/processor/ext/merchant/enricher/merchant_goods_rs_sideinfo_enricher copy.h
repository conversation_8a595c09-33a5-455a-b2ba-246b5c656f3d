#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <cmath>
#include <numeric>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

class MerchantGoodsRsSideInfoEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantGoodsRsSideInfoEnricher() {}
  ~MerchantGoodsRsSideInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  int GetMinutesLag(int minutes_diff);
  void GetCategoryLeval(const std::vector<int64> &cate_list,
                       std::vector<int64> *cate1_list,
                       std::vector<int64> *cate2_list,
                       std::vector<int64> *cate3_list);
  void GetLagList(const std::vector<int64> &ts_list,
                  int64 request_time,
                  std::vector<int64> *lagV1_list,
                  std::vector<int64> *lagV2_list);
  void GetIsActionList(const std::vector<int64> &item_id_list,
                       const absl::optional<absl::Span<const int64>> &action_item_id_list,
                       std::vector<int64> *is_action_list);
  void GetCountIndexList(const std::vector<int64> &item_id_list,
                         const std::vector<int64> &ts_list,
                         std::vector<int64> *count_index_list);
  void GetTsCountList(const std::vector<int64> &ts_list,
                      std::vector<int64> *ts_count_list);
  void GetIsActionV2List(const std::vector<int64> &realshow_id_list,
                         const std::vector<int64> &realshow_ts_list,
                         const absl::optional<absl::Span<const int64>> &click_id_list,
                         const absl::optional<absl::Span<const int64>> &click_ts_list,
                         std::vector<int64> *is_actionV2_list);
  void GetActionCrossList(const std::vector<int64> &is_action_list,
                          const std::vector<int64> &is_actionV2_list,
                          std::vector<int64> *is_actionCross_list);
  void GetCate2RsIndexMap(const std::vector<int64> &cate_list,
                          std::unordered_map<int64, std::vector<int>> *cate_2_rsIndex);
  void Add2RsIndexSet(std::unordered_set<int> *rsIndex_set,
                      std::vector<int> *rsIndex_list,
                      std::unordered_map<int64, int> *rsItemId2Count_map,
                      const int max_length,
                      const std::vector<int64> &item_id_list,
                      const int64 cate,
                      std::unordered_map<int64, std::vector<int>> *cate_2_rsIndex);
  void AssignCommonAttr(MutableRecoContextInterface *context,
                        const std::vector<int64> &item_id_list,
                        const std::string &attr_name,
                        int limit);
  bool IsValidRsList(MutableRecoContextInterface *context,
                   const absl::optional<absl::Span<const int64>> &input_rs_item_id_list,
                   const absl::optional<absl::Span<const int64>> &input_rs_pageCode_list,
                   const absl::optional<absl::Span<const int64>> &input_rs_timestamp_list,
                   const absl::optional<absl::Span<const int64>> &input_rs_spu_id_list,
                   const absl::optional<absl::Span<const int64>> &input_rs_exposure_ratio_list,
                   const absl::optional<absl::Span<const int64>> &nput_rs_seller_id_list,
                   const absl::optional<absl::Span<const int64>> &input_rs_category_list);

void FilterByTimestamp(MutableRecoContextInterface *context,
                 int64 filter_time,
                 const absl::optional<absl::Span<const int64>> &input_rs_item_id_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_pageCode_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_timestamp_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_spu_id_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_exposure_ratio_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_seller_id_list,
                 const absl::optional<absl::Span<const int64>> &input_rs_category_list,
                 std::vector<int64> *item_id_vec,
                 std::vector<int64> *pageCode_vec,
                 std::vector<int64> *ts_vec,
                 std::vector<int64> *spuId_vec,
                 std::vector<int64> *exposureRatio_vec,
                 std::vector<int64> *sellerId_vec,
                 std::vector<int64> *category_vec);

 private:
  std::string clk_item_id_list_attr_;
  std::string clk_timestamp_list_attr_;
  std::string buy_item_id_list_attr_;
  std::string buy_timestamp_list_attr_;
  std::string cart_item_id_list_attr_;
  std::string input_prefix_attr_;
  std::string gsu_prefix_attr_;
  std::string item_id_list_attr_;
  std::string pageCode_list_attr_;
  std::string timestamp_list_attr_;
  std::string spu_id_list_attr_;
  std::string exposure_ratio_list_attr_;
  std::string seller_id_list_attr_;
  std::string category_list_attr_;
  std::string cate1_list_attr_;
  std::string cate2_list_attr_;
  std::string cate3_list_attr_;
  std::string lagV1_list_attr_;
  std::string is_click_list_attr_;
  std::string is_cart_list_attr_;
  std::string is_buy_list_attr_;
  std::string count_index_list_attr_;
  std::string lagV2_list_attr_;
  std::string ts_count_list_attr_;
  std::string is_click_cross_list_attr_;
  std::string is_buy_cross_list_attr_;
  std::string id_count_list_attr_;

  int seconds_to_lookback_ = 0;

  int limit_num_ = 500;
  int gsu_limit_num_ = 100;
  bool cate1_match_attr_ = true;
  bool cate2_match_attr_ = true;
  bool cate3_match_attr_ = true;
  std::string cate1_attr_;
  std::string cate2_attr_;
  std::string cate3_attr_;

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(MerchantGoodsRsSideInfoEnricher);
};

}  // namespace platform
}  // namespace ks
