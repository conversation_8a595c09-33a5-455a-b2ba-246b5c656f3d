#pragma once

#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
namespace ks {
namespace platform {

class MerchantColossusClickSliceFeatureV2Enricher : public CommonRecoBaseEnricher {
  enum AttrName {
    ATTR_NAME_MIN = 0,
    ATTR_NAME_AUTHOR_ID = 1,
    ATTR_NAME_CATE1 = 2,
    ATTR_NAME_CATE2 = 3,
    ATTR_NAME_CATE3 = 4,
    ATTR_NAME_LEAF = 5,
    ATTR_NAME_MAX = 6
  };

  enum ActionListType { ACTION_LIST_TYPE_MIN = 0, ACTION_LIST_TYPE_CLICK = 1, ACTION_LIST_TYPE_MAX = 2 };
  enum CountType {
    COUNT_TYPE_MIN = 0,
    COUNT_TYPE_LAST_1 = 1,
    COUNT_TYPE_LAST_5 = 2,
    COUNT_TYPE_LAST_10 = 3,
    COUNT_TYPE_LAST_20 = 4,
    COUNT_TYPE_LAST_100 = 5,
    COUNT_TYPE_LAST_200 = 6,
    COUNT_TYPE_1H = 7,
    COUNT_TYPE_3H = 8,
    COUNT_TYPE_6H = 9,
    COUNT_TYPE_12H = 10,
    COUNT_TYPE_1D = 11,
    COUNT_TYPE_2D = 12,
    COUNT_TYPE_3D = 13,
    COUNT_TYPE_7D = 14,
    COUNT_TYPE_14D = 15,
    COUNT_TYPE_28D = 16,
    COUNT_TYPE_30D = 17,
    COUNT_TYPE_60D = 18,
    COUNT_TYPE_90D = 19,
    COUNT_TYPE_10MIN = 20,
    COUNT_TYPE_30MIN = 21,
    COUNT_TYPE_MAX = 22
  };
  struct AggregationInfo {
    folly::F14FastMap<
        ActionListType,
        folly::F14FastMap<AttrName, folly::F14FastMap<int64, folly::F14FastMap<CountType, int>>>>
        aggregation;
    folly::F14FastMap<
        ActionListType,
        folly::F14FastMap<AttrName, folly::F14FastMap<int64, std::vector<std::pair<int, int64>>>>>
        pre_aggregation;
    folly::F14FastMap<CountType, int> result;
  };

 public:
  MerchantColossusClickSliceFeatureV2Enricher() {}
  ~MerchantColossusClickSliceFeatureV2Enricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    // init cur processor need column attr
    print_debug_log_ = config()->GetBoolean("print_debug_log", false);
    click_colossus_item_id_attr_ = config()->GetString("item_id_list_attr");
    click_colossus_click_timestamp_attr_ = config()->GetString("click_timestamp_list_attr");
    click_colossus_category_attr_ = config()->GetString("category_list_attr");
    click_colossus_leaf_category_attr_ = config()->GetString("leaf_category_list_attr");
    click_colossus_seller_id_attr_ = config()->GetString("seller_id_list_attr");
    target_aid_ = config()->GetString("target_aid");
    target_cate1_ = config()->GetString("target_cate1");
    target_cate2_ = config()->GetString("target_cate2");
    target_cate3_ = config()->GetString("target_cate3");
    target_leaf_ = config()->GetString("target_leaf");
    cart_truncat_num_ = config()->GetInt("cart_truncat_num", 10);
    filter_time_ = config()->GetInt("filter_time", 1);
    click_colossus_latest_timestamp_ =
        config()->GetString("click_colossus_latest_timestamp");
    CL_LOG(INFO) << GetName() << ", click_colossus_latest_timestamp: "
              << click_colossus_latest_timestamp_ << std::endl;
    vec_attr_name_.clear();
    for (int i = (int)ATTR_NAME_MIN + 1; i < (int)ATTR_NAME_MAX; i++) {
      vec_attr_name_.push_back(AttrName(i));
    }
    vec_action_list_type_.clear();
    for (int i = (int)ACTION_LIST_TYPE_MIN + 1; i < (int)ACTION_LIST_TYPE_MAX; i++) {
      vec_action_list_type_.push_back(ActionListType(i));
    }
    vec_count_type_.clear();
    for (int i = (int)COUNT_TYPE_MIN + 1; i < (int)COUNT_TYPE_MAX; i++) {
      vec_count_type_.push_back(CountType(i));
    }
    InitFeatureNames();
    std::reverse(last_size_table_.begin(), last_size_table_.end());
    std::reverse(time_gap_table_.begin(), time_gap_table_.end());
    return true;
  }

 private:
  bool print_debug_log_ = false;
  std::string target_aid_, target_cate1_, target_cate2_, target_cate3_, target_leaf_;
  std::string click_colossus_item_id_attr_;
  std::string click_colossus_click_timestamp_attr_;
  std::string click_colossus_category_attr_;
  std::string click_colossus_leaf_category_attr_;
  std::string click_colossus_seller_id_attr_;
  std::string click_colossus_latest_timestamp_;
  int cart_truncat_num_ = 100;
  int filter_time_ = 0;
  AggregationInfo aggregation_info_;
  serving_base::Timer timer_;
  std::vector<ActionListType> vec_action_list_type_;
  std::vector<AttrName> vec_attr_name_;
  std::vector<CountType> vec_count_type_;
  std::vector<std::string> feature_attr_names_;
  int64 max_feature_index_ = ACTION_LIST_TYPE_MAX * ATTR_NAME_MAX * COUNT_TYPE_MAX;
  std::vector<ItemAttr *> feature_attr_accessors_;
  std::map<AttrName, std::string> map_attr_name_str_ = {
      {ATTR_NAME_AUTHOR_ID, "authorid"}, {ATTR_NAME_CATE1, "cate1"}, {ATTR_NAME_CATE2, "cate2"},
      {ATTR_NAME_CATE3, "cate3"},        {ATTR_NAME_LEAF, "leaf"},
  };

  std::map<ActionListType, std::string> map_action_list_type_str_ = {
      {ACTION_LIST_TYPE_CLICK, "click"},
  };

  std::map<CountType, std::string> map_count_type_str_ = {{COUNT_TYPE_LAST_1, "last1"},
                                                          {COUNT_TYPE_LAST_5, "last5"},
                                                          {COUNT_TYPE_LAST_10, "last10"},
                                                          {COUNT_TYPE_LAST_20, "last20"},
                                                          {COUNT_TYPE_LAST_100, "last100"},
                                                          {COUNT_TYPE_LAST_200, "last200"},
                                                          {COUNT_TYPE_10MIN, "10min"},
                                                          {COUNT_TYPE_30MIN, "30min"},
                                                          {COUNT_TYPE_1H, "1h"},
                                                          {COUNT_TYPE_3H, "3h"},
                                                          {COUNT_TYPE_6H, "6h"},
                                                          {COUNT_TYPE_12H, "12h"},
                                                          {COUNT_TYPE_1D, "1d"},
                                                          {COUNT_TYPE_2D, "2d"},
                                                          {COUNT_TYPE_3D, "3d"},
                                                          {COUNT_TYPE_7D, "7d"},
                                                          {COUNT_TYPE_14D, "14d"},
                                                          {COUNT_TYPE_28D, "28d"},
                                                          {COUNT_TYPE_30D, "30d"},
                                                          {COUNT_TYPE_60D, "60d"},
                                                          {COUNT_TYPE_90D, "90d"}};
  std::vector<std::pair<CountType, int>> last_size_table_ = {
      {COUNT_TYPE_LAST_1, 1},   {COUNT_TYPE_LAST_5, 5},     {COUNT_TYPE_LAST_10, 10},
      {COUNT_TYPE_LAST_20, 20}, {COUNT_TYPE_LAST_100, 100}, {COUNT_TYPE_LAST_200, 200},
  };
  std::vector<std::pair<CountType, int64>> time_gap_table_ = {
      {COUNT_TYPE_10MIN, 10 * 60},         {COUNT_TYPE_30MIN, 30 * 60},
      {COUNT_TYPE_1H, 1 * 60 * 60},        {COUNT_TYPE_3H, 3 * 60 * 60},
      {COUNT_TYPE_6H, 6 * 60 * 60},        {COUNT_TYPE_12H, 12 * 60 * 60},
      {COUNT_TYPE_1D, 1 * 24 * 60 * 60},   {COUNT_TYPE_2D, 2 * 24 * 60 * 60},
      {COUNT_TYPE_3D, 3 * 24 * 60 * 60},   {COUNT_TYPE_7D, 7 * 24 * 60 * 60},
      {COUNT_TYPE_14D, 14 * 24 * 60 * 60}, {COUNT_TYPE_28D, 28 * 24 * 60 * 60},
      {COUNT_TYPE_30D, 30 * 24 * 60 * 60}, {COUNT_TYPE_60D, 60 * 24 * 60 * 60},
      {COUNT_TYPE_90D, 90 * 24 * 60 * 60}};

  inline std::string GetAttrNameStr(AttrName attr_name) {
    return map_attr_name_str_[attr_name];
  }

  inline std::string GetActionListTypeStr(ActionListType action_list_type) {
    return map_action_list_type_str_[action_list_type];
  }

  inline std::string GetCountTypeStr(CountType count_type) {
    return map_count_type_str_[count_type];
  }

  inline int64 GetIndex(ActionListType action, AttrName attr, CountType count) {
    return action * ATTR_NAME_MAX * COUNT_TYPE_MAX + attr * COUNT_TYPE_MAX + count;
  }
  inline void InitFeatureNames() {
    feature_attr_names_.clear();
    feature_attr_names_.resize(max_feature_index_);
    for (auto action_list_type : vec_action_list_type_) {
      for (auto attr_name : vec_attr_name_) {
        for (auto count_type : vec_count_type_) {
          std::string new_feature_key = "merchant_colossus_" + GetActionListTypeStr(action_list_type) + "_" +
                                        GetAttrNameStr(attr_name) + "_" + GetCountTypeStr(count_type);
          LOG(INFO) << "merchant colossus feature_attr_names:"
                    << "\"" << new_feature_key << "\"," << std::endl;
          feature_attr_names_[GetIndex(action_list_type, attr_name, count_type)] = new_feature_key;
        }
      }
    }
    return;
  }
  void PreAggregation(MutableRecoContextInterface *context,
                      absl::optional<absl::Span<const int64>> item_id_column,
                      absl::optional<absl::Span<const int64>> click_timestamp_column,
                      absl::optional<absl::Span<const int64>> category_column,
                      absl::optional<absl::Span<const int64>> leaf_category_column,
                      absl::optional<absl::Span<const int64>> seller_column,
                      AggregationInfo *aggregation_info, int64 colossus_filter_time);
  void PreAggregationActionListProcess(MutableRecoContextInterface *context, ActionListType action_list_type,
                                       absl::optional<absl::Span<const int64>> item_id_column,
                                       absl::optional<absl::Span<const int64>> click_timestamp_column,
                                       absl::optional<absl::Span<const int64>> category_column,
                                       absl::optional<absl::Span<const int64>> leaf_category_column,
                                       absl::optional<absl::Span<const int64>> seller_column,
                                       AggregationInfo *aggregation_info, int64 colossus_filter_time);

  void InitFeatureAttrAccessors(MutableRecoContextInterface *context);
  void ExtractAndSetPhotoStatisticDenseFeature(MutableRecoContextInterface *context,
                                               const CommonRecoResult &result,
                                               AggregationInfo *aggregation_info);
  DISALLOW_COPY_AND_ASSIGN(MerchantColossusClickSliceFeatureV2Enricher);
};

}  // namespace platform
}  // namespace ks
