#include "dragon/src/processor/ext/merchant/enricher/merchant_colossus_click_slice_feature_v2.h"
#include <algorithm>
#include <csignal>
#include <iostream>
#include <ostream>
#include <set>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"

#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {

void MerchantColossusClickSliceFeatureV2Enricher::Enrich(MutableRecoContextInterface *context,
                                                         RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  const int num_items = std::distance(begin, end);
  if (num_items <= 0) {
    return;
  }
  int64 colossus_filter_time = context->GetRequestTime() / 1000 - filter_time_ * 60;
  InitFeatureAttrAccessors(context);
  aggregation_info_.aggregation.clear();
  aggregation_info_.pre_aggregation.clear();
  auto item_id_column = context->GetIntListCommonAttr(click_colossus_item_id_attr_);
  auto click_timestamp_column = context->GetIntListCommonAttr(click_colossus_click_timestamp_attr_);
  auto category_column = context->GetIntListCommonAttr(click_colossus_category_attr_);
  auto leaf_category_column = context->GetIntListCommonAttr(click_colossus_leaf_category_attr_);
  auto seller_column = context->GetIntListCommonAttr(click_colossus_seller_id_attr_);
  auto is_valid_and_aligned = [&](const auto &column) { return column.has_value() && column->size() > 0; };
  if (!(is_valid_and_aligned(item_id_column) && is_valid_and_aligned(click_timestamp_column) &&
        is_valid_and_aligned(category_column) && is_valid_and_aligned(leaf_category_column) &&
        is_valid_and_aligned(seller_column))) {
    CL_LOG(WARNING) << "merchant click colossus read " << click_colossus_item_id_attr_
                    << " exist feature not valid or aligned";
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (auto action_list_type : vec_action_list_type_) {
        for (auto attr_name : vec_attr_name_) {
          for (auto count_type : vec_count_type_) {
            double val = 0.0;
            auto attr_accessor = feature_attr_accessors_[GetIndex(action_list_type, attr_name, count_type)];
            context->SetDoubleItemAttr(result, attr_accessor, val);
          }
        }
      }
    });
    return;
  }
  PreAggregation(context, item_id_column, click_timestamp_column, category_column, leaf_category_column,
                 seller_column, &aggregation_info_, colossus_filter_time);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto item_key = result.item_key;
    int64 start_ts = base::GetTimestamp();
    ExtractAndSetPhotoStatisticDenseFeature(context, result, &aggregation_info_);
    int64 duration = base::GetTimestamp() - start_ts;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        duration, kPerfNs, "ExtractAndSetPhotoStatisticDenseFeature", GlobalHolder::GetServiceIdentifier(),
        context->GetRequestType());
  });
}

void MerchantColossusClickSliceFeatureV2Enricher::InitFeatureAttrAccessors(
    MutableRecoContextInterface *context) {
  feature_attr_accessors_.clear();
  feature_attr_accessors_.resize(max_feature_index_);
  for (auto action_list_type : vec_action_list_type_) {
    for (auto attr_name : vec_attr_name_) {
      for (auto count_type : vec_count_type_) {
        auto index = GetIndex(action_list_type, attr_name, count_type);
        feature_attr_accessors_[index] = context->GetItemAttrAccessor(feature_attr_names_[index]);
      }
    }
  }
}

void MerchantColossusClickSliceFeatureV2Enricher::ExtractAndSetPhotoStatisticDenseFeature(
    MutableRecoContextInterface *context, const CommonRecoResult &result, AggregationInfo *aggregation_info) {
  if (aggregation_info == nullptr) {
    return;
  }
  auto target_aid_accessor = context->GetItemAttrAccessor(target_aid_);
  absl::optional<int64> author_id;
  absl::optional<absl::Span<const int64>> target_cate1;
  absl::optional<absl::Span<const int64>> target_cate2;
  absl::optional<absl::Span<const int64>> target_cate3;
  absl::optional<absl::Span<const int64>> target_leaf;
  if (context->HasItemAttr(result, target_aid_accessor)) {
    author_id = context->GetIntItemAttr(result, target_aid_accessor);
  } else {
    LOG_EVERY_N(WARNING, 10000) << "no aid for item: " << result.item_key;
  }
  auto target_cate1_accessor = context->GetItemAttrAccessor(target_cate1_);
  if (context->HasItemAttr(result, target_cate1_accessor)) {
    target_cate1 = context->GetIntListItemAttr(result, target_cate1_accessor);
  } else {
    LOG_EVERY_N(WARNING, 10000) << "no target_cate1 for item: " << result.item_key;
  }
  auto target_cate2_accessor = context->GetItemAttrAccessor(target_cate2_);
  if (context->HasItemAttr(result, target_cate2_accessor)) {
    target_cate2 = context->GetIntListItemAttr(result, target_cate2_accessor);
  } else {
    LOG_EVERY_N(WARNING, 10000) << "no target_cate2 for item: " << result.item_key;
  }
  auto target_cate3_accessor = context->GetItemAttrAccessor(target_cate3_);
  if (context->HasItemAttr(result, target_cate3_accessor)) {
    target_cate3 = context->GetIntListItemAttr(result, target_cate3_accessor);
  } else {
    LOG_EVERY_N(WARNING, 10000) << "no target_cate3 for item: " << result.item_key;
  }
  auto target_leaf_accessor = context->GetItemAttrAccessor(target_leaf_);
  if (context->HasItemAttr(result, target_leaf_accessor)) {
    target_leaf = context->GetIntListItemAttr(result, target_leaf_accessor);
  } else {
    LOG_EVERY_N(WARNING, 10000) << "no target_leaf for item: " << result.item_key;
  }
  auto &aggregation = aggregation_info->aggregation;
  auto &pre_aggregation = aggregation_info->pre_aggregation;
  auto &aggregation_final_result = aggregation_info->result;
  if (print_debug_log_) {
    std::cout << "\n[v2]begin extract item_key:" << result.item_key
              << "| target_cate1:" << target_cate1->size() << ", target_cate2:" << target_cate2->size()
              << ", target_cate3:" << target_cate2->size() << ", target_leaf:" << target_leaf->size()
              << std::endl;
  }
  for (auto attr_name : vec_attr_name_) {
    std::set<int64> attr_val_list;
    bool has_valid_value = true;
    switch (attr_name) {
      case ATTR_NAME_AUTHOR_ID:
        // if (author_id.has_value()) { // TODO 对齐 base
        attr_val_list.insert(*author_id);
        if (print_debug_log_) {
          std::cout << "[v2]insert author_id: " << *author_id
                    << " author_id.has_value(): " << author_id.has_value() << std::endl;
        }
        // }
        break;

      case ATTR_NAME_CATE1:
        if (target_cate1.has_value()) {
          for (int64 val : *target_cate1) {
            if (attr_val_list.size() <= cart_truncat_num_) {
              if (val > 0) {
                attr_val_list.insert(val);
              }
            }
          }
        }
        break;

      case ATTR_NAME_CATE2:
        if (target_cate2.has_value()) {
          for (int64 val : *target_cate2) {
            if (attr_val_list.size() <= cart_truncat_num_) {
              if (val > -1) {
                attr_val_list.insert(val);
              }
            }
          }
        }
        break;

      case ATTR_NAME_CATE3:
        if (target_cate3.has_value()) {
          for (int64 val : *target_cate3) {
            if (attr_val_list.size() <= cart_truncat_num_) {
              if (val > 0) {
                attr_val_list.insert(val);
              }
            }
          }
        }
        break;
      case ATTR_NAME_LEAF:
        if (target_leaf.has_value()) {
          for (int64 val : *target_leaf) {
            if (attr_val_list.size() <= cart_truncat_num_) {
              if (val > 0) {
                attr_val_list.insert(val);
              }
            }
          }
        }
        break;
      default:
        has_valid_value = false;
        LOG(ERROR) << "not supported attr_name:" << attr_name;
    }

    if (!has_valid_value) {
      continue;
    }
    for (auto action_list_type : vec_action_list_type_) {
      for (int64 attr_val : attr_val_list) {
        auto &aggregation_result = aggregation[action_list_type][attr_name][attr_val];
        if (aggregation_result.size() <= 0) {
          if (pre_aggregation[action_list_type][attr_name][attr_val].size() > 0) {
            const std::vector<std::pair<int, int64>> &index_time_list =
                pre_aggregation[action_list_type][attr_name][attr_val];
            auto it_right = index_time_list.end();
            for (auto &bound : last_size_table_) {
              auto it_bound =
                  std::upper_bound(index_time_list.begin(), it_right, std::make_pair(bound.second, 0),
                                   [](const std::pair<int, int64> &target, const std::pair<int, int64> &mid) {
                                     return target.first < mid.first;
                                   });
              aggregation[action_list_type][attr_name][attr_val][bound.first] =
                  std::distance(index_time_list.begin(), it_bound);
              it_right = it_bound;
            }
            it_right = index_time_list.end();
            for (auto &bound : time_gap_table_) {
              auto it_bound =
                  std::upper_bound(index_time_list.begin(), it_right, std::make_pair(0, bound.second),
                                   [](const std::pair<int, int64> &target, const std::pair<int, int64> &mid) {
                                     return target.second < mid.second;
                                   });
              aggregation[action_list_type][attr_name][attr_val][bound.first] =
                  std::distance(index_time_list.begin(), it_bound);
              it_right = it_bound;
            }
          }
        }
      }
      auto &aggregation_attr_result = aggregation[action_list_type][attr_name];
      aggregation_final_result.clear();
      if (aggregation_attr_result.size() > 0) {
        for (int64 attr_val : attr_val_list) {
          if (aggregation_attr_result[attr_val].size() > 0) {
            for (auto pair : aggregation_attr_result[attr_val]) {
              if (aggregation_final_result.count(pair.first) > 0) {
                aggregation_final_result[pair.first] += pair.second;
              } else {
                aggregation_final_result[pair.first] = pair.second;
              }
            }
          }
        }
      }
      for (auto count_type : vec_count_type_) {
        double val = 0.0;
        if (aggregation_final_result.count(count_type) > 0) {
          val = static_cast<double>(aggregation_final_result[count_type]);
          auto attr_accessor = feature_attr_accessors_[GetIndex(action_list_type, attr_name, count_type)];
          context->SetDoubleItemAttr(result, attr_accessor, val);
        } else {
          auto attr_accessor = feature_attr_accessors_[GetIndex(action_list_type, attr_name, count_type)];
          context->SetDoubleItemAttr(result, attr_accessor, static_cast<double>(0));
        }
        if (print_debug_log_) {
          std::string feature_key = GetActionListTypeStr(action_list_type) + "_" + GetAttrNameStr(attr_name);
          std::string new_feature_key =
              "merchant_colossus_" + feature_key + "_" + GetCountTypeStr(count_type);
          std::cout << "[v2]dense key: " << new_feature_key << ", val:" << val
                    << " | action_list_type:" << GetActionListTypeStr(action_list_type)
                    << ", attr_name:" << GetAttrNameStr(attr_name)
                    << ", attr_val:" << absl::StrJoin(attr_val_list, ",")
                    << ", count_type:" << GetCountTypeStr(count_type) << std::endl;
        }
      }
    }
  }
}

void MerchantColossusClickSliceFeatureV2Enricher::PreAggregationActionListProcess(
    MutableRecoContextInterface *context, ActionListType action_list_type,
    absl::optional<absl::Span<const int64>> item_id_column,
    absl::optional<absl::Span<const int64>> click_timestamp_column,
    absl::optional<absl::Span<const int64>> category_column,
    absl::optional<absl::Span<const int64>> leaf_category_column,
    absl::optional<absl::Span<const int64>> seller_column, AggregationInfo *aggregation_info,
    int64 colossus_filter_time) {
  if (!aggregation_info) {
    return;
  }
  int64 click_colossus_latest_timestamp_in_leaf = -2;
  if (!click_colossus_latest_timestamp_.empty()) {
    auto click_colossus_latest_timestamp_accessor =
        context->GetCommonAttrAccessor(click_colossus_latest_timestamp_);
    if (click_colossus_latest_timestamp_accessor &&
        click_colossus_latest_timestamp_accessor->value_type == AttrType::INT) {
      auto latest_timestamp = context->GetIntCommonAttr(click_colossus_latest_timestamp_);
      if (latest_timestamp) {
        click_colossus_latest_timestamp_in_leaf = *latest_timestamp;
      }
    }
  }

  auto &pre_aggr = aggregation_info->pre_aggregation;
  int64 cur_time_s = context->GetRequestTime() / 1000;
  if (!item_id_column.has_value() || !click_timestamp_column.has_value()) {
    if (print_debug_log_) {
      std::cout << "[v2]item_id_column or click_timestamp_column not has value" << std::endl;
    }
    return;
  }
  int column_size = item_id_column->size();
  bool is_print_log = true;
  for (auto &attr_name : vec_attr_name_) {
    auto &aggr_attr_map = pre_aggr[action_list_type][attr_name];
    int keep_index = 1;
    bool is_filter = true;
    for (int index = column_size - 1; index >= 0; index--) {
      if (item_id_column->size() < index) {
        if (print_debug_log_) {
          std::cout << "[v2]item_id_column->size() < index " << std::endl;
        }
        continue;
      }
      if (index > click_timestamp_column->size()) {
        if (print_debug_log_) {
          std::cout << "[v2]pre aggregation click timestamp column index no valid index: " << index
                    << " total size" << click_timestamp_column->size() << std::endl;
        }
        continue;
      }
      int64 cur_item_id = item_id_column->at(index);
      int64 cur_click_ts = click_timestamp_column->at(index);
      if (click_colossus_latest_timestamp_in_leaf == -2) {
        if (cur_click_ts > colossus_filter_time) {
          if (print_debug_log_) {
            std::cout << "click_colossus_latest_ts == -2  [v2]======filter item_id: " << cur_item_id
                      << ", colossus click time :" << cur_click_ts
                      << ", colossus_filter_time :" << colossus_filter_time << std::endl;
          }
          continue;
        }
      } else {
        if (cur_click_ts != click_colossus_latest_timestamp_in_leaf && is_filter) {
          if (print_debug_log_) {
            std::cout << "\n[v2]======filter item_id: " << cur_item_id
                      << ", click_colossus_latest_timestamp_in_leaf: "
                      << click_colossus_latest_timestamp_in_leaf << ", coloss click time :" << cur_click_ts
                      << std::endl;
          }
          continue;
        } else {
          is_filter = false;
        }
      }
      int gap = cur_time_s - cur_click_ts;
      switch (attr_name) {
        case ATTR_NAME_AUTHOR_ID:
          if (seller_column.has_value() && seller_column->size() > index) {
            aggr_attr_map[seller_column->at(index)].push_back({keep_index, gap});
          }
          break;
        case ATTR_NAME_CATE1:
          if (category_column.has_value() && category_column->size() > index) {
            aggr_attr_map[(category_column->at(index) >> 48) & 0xffff].push_back({keep_index, gap});
          }
          break;
        case ATTR_NAME_CATE2:
          if (category_column.has_value() && category_column->size() > index) {
            aggr_attr_map[(category_column->at(index) >> 32) & 0xffff].push_back({keep_index, gap});
          }
          break;
        case ATTR_NAME_CATE3:
          if (category_column.has_value() && category_column->size() > index) {
            aggr_attr_map[(category_column->at(index) >> 16) & 0xffff].push_back({keep_index, gap});
          }
          break;
        case ATTR_NAME_LEAF:
          if (category_column.has_value() && category_column->size() > index) {
            aggr_attr_map[(leaf_category_column->at(index) >> 0) & 0xffff].push_back({keep_index, gap});
          }
          break;
        default:
          break;
      }
      if (print_debug_log_ && is_print_log) {
        std::cout << "[v2]MerchantColossusClickSliceFeatureEnricher_index: " << index + 1 << " ,"
                  << "attr_name: " << attr_name << " ,"
                  << ",item_id: " << cur_item_id << ", coloss seller id: " << seller_column->at(index)
                  << ", coloss cate1: " << ((category_column->at(index) >> 48) & 0xffff)
                  << ", coloss cate2: " << ((category_column->at(index) >> 32) & 0xffff)
                  << ", coloss cate3: " << ((category_column->at(index) >> 16) & 0xffff)
                  << ", coloss leaf: " << ((leaf_category_column->at(index) >> 0) & 0xffff)
                  << ", click_colossus_latest_timestamp_in_leaf :" << click_colossus_latest_timestamp_in_leaf
                  << ", coloss click time :" << cur_click_ts
                  << ", colossus_filter_time: " << colossus_filter_time << std::endl;
      }
      keep_index += 1;
    }
    is_print_log = false;
  }
  return;
}

void MerchantColossusClickSliceFeatureV2Enricher::PreAggregation(
    MutableRecoContextInterface *context, absl::optional<absl::Span<const int64>> item_id_column,
    absl::optional<absl::Span<const int64>> click_timestamp_column,
    absl::optional<absl::Span<const int64>> category_column,
    absl::optional<absl::Span<const int64>> leaf_category_column,
    absl::optional<absl::Span<const int64>> seller_column, AggregationInfo *aggregation_info,
    int64 colossus_filter_time) {
  for (auto action_list_type : vec_action_list_type_) {
    int64 start_ts = base::GetTimestamp();
    PreAggregationActionListProcess(context, action_list_type, item_id_column, click_timestamp_column,
                                    category_column, leaf_category_column, seller_column, aggregation_info,
                                    colossus_filter_time);
    int64 duration = base::GetTimestamp() - start_ts;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(duration, kPerfNs, "PreAggrActionListProcess",
                                                      map_action_list_type_str_[action_list_type]);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantColossusClickSliceFeatureV2Enricher,
                 MerchantColossusClickSliceFeatureV2Enricher)

}  // namespace platform
}  // namespace ks
