#pragma once

#include <sys/types.h>
#include <algorithm>
#include <cstddef>
#include <map>
#include <queue>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <random>
#include <sstream>

#include "dragon/src/processor/ext/merchant/util/merchant_util.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "ks/base/abtest/metrics/abtest_metric.h"


namespace ks {
namespace platform {

class MerchantLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  MerchantLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CalculateFollowTimeDay);
    REGISTER_LIGHT_FUNCTION(GetMcScoreSub);
    REGISTER_LIGHT_FUNCTION(GetFrScoreSub);
    REGISTER_LIGHT_FUNCTION(LiveInteractiveAuthorRetrvGenerateWeight);
    REGISTER_LIGHT_FUNCTION(ExtractLive2SellerList);
    REGISTER_LIGHT_FUNCTION(CalculateCurrentResultSize);
    REGISTER_LIGHT_FUNCTION(PackEffectivelyReasonItemList);
    REGISTER_LIGHT_FUNCTION(SaveReasonToTargetAttr);
    REGISTER_LIGHT_FUNCTION(CalculatePDI2IMergeScore);
    REGISTER_LIGHT_FUNCTION(TagForEffectivelyReason);
    REGISTER_LIGHT_FUNCTION(GetDistSellerLivingFilterSetStr);
    REGISTER_LIGHT_FUNCTION(GetShangdaSpuExpansionSellerIdList);
    REGISTER_LIGHT_FUNCTION(CalculateDedupAllMergeSellerTimeList);
    REGISTER_LIGHT_FUNCTION(LuaOpt2);
    REGISTER_LIGHT_FUNCTION(LuaOpt3);
    REGISTER_LIGHT_FUNCTION(LuaOptSwtn);
    REGISTER_LIGHT_FUNCTION(LuaOptSwtnV2);
    REGISTER_LIGHT_FUNCTION(LuaOptSwtnV3);
    REGISTER_LIGHT_FUNCTION(GetSellerQuality);
    REGISTER_LIGHT_FUNCTION(GetLiveidAndQueryIdxFromAid);
    REGISTER_LIGHT_FUNCTION(GetLiveidAndQueryIdxFromAidNoPrefixHasDedup);
    REGISTER_LIGHT_FUNCTION(GetExtraMerchantPictureSet);
    REGISTER_LIGHT_FUNCTION(GenDiversityCateLeafFilterTagV2);
    REGISTER_LIGHT_FUNCTION(GetDancingAuthorBrowsetFlag);
    REGISTER_LIGHT_FUNCTION(GetUnFollowFilterFlag);
    REGISTER_LIGHT_FUNCTION(GetUserSpecPackageFilterFlag);
    REGISTER_LIGHT_FUNCTION(GetAdDataAuthorScore);
    REGISTER_LIGHT_FUNCTION(GenAudienceIdList);
    REGISTER_LIGHT_FUNCTION(GetUserAuthorRealshowFrequencyFilter);
    REGISTER_LIGHT_FUNCTION(GetHighQualityUserFixGpm);
    REGISTER_LIGHT_FUNCTION(GetFollowBuyLongPlayDay);
    REGISTER_LIGHT_FUNCTION(ExtractRealtimeLiveGmvAndOrder);
    REGISTER_LIGHT_FUNCTION(GetFrScoreSubInRecallFrMerchants);
    REGISTER_LIGHT_FUNCTION(FilterByRiskPunishSignalOfGenerateFilterFlag);
    REGISTER_LIGHT_FUNCTION(WatchtimeRetargetingSetValue);
    REGISTER_LIGHT_FUNCTION(BigVAidForceInsert);
    REGISTER_LIGHT_FUNCTION(GenTelecomTag);
    REGISTER_LIGHT_FUNCTION(MidRankGenAdsMultiAttrs);
    REGISTER_LIGHT_FUNCTION(GetDedupTagSellerIdAttrs);
    REGISTER_LIGHT_FUNCTION(GetABSellerInfo);
    REGISTER_LIGHT_FUNCTION(ListSamplingProcessor);
    REGISTER_LIGHT_FUNCTION(GetABExpName);
    REGISTER_LIGHT_FUNCTION(ExtractIntKvValueList);
    REGISTER_LIGHT_FUNCTION(ExplorationUcbScore);
    REGISTER_LIGHT_FUNCTION(AddBasicBonus);
    REGISTER_LIGHT_FUNCTION(ItemListTopKInCommonList);
    REGISTER_LIGHT_FUNCTION(LiveTopKItemHasNoOrderCate2);
    REGISTER_LIGHT_FUNCTION(GetRoiBonus);
    REGISTER_LIGHT_FUNCTION(GetRoiBonusMtb);
    REGISTER_LIGHT_FUNCTION(GetRoiBonusMtb2);
    REGISTER_LIGHT_FUNCTION(GetRoiBonusMtbCom);
    REGISTER_LIGHT_FUNCTION(GetRoiBonusMtbCpm);
    REGISTER_LIGHT_FUNCTION(GetRoiBonusMtbCpm2);
    REGISTER_LIGHT_FUNCTION(CalcTrafficWorthScore);
    REGISTER_LIGHT_FUNCTION(TagIfBelowThreshold);
    REGISTER_LIGHT_FUNCTION(ColossusReverseTruncateV4Attr);
    REGISTER_LIGHT_FUNCTION(CalcGpmPrePostScore);
    REGISTER_LIGHT_FUNCTION(CalcUePrePostScore);
    REGISTER_LIGHT_FUNCTION(B2CRetrieveReverse);
    REGISTER_LIGHT_FUNCTION(B2CDataMerge);
    REGISTER_LIGHT_FUNCTION(FgCalculateTopkIndicesAndValues);
    REGISTER_LIGHT_FUNCTION(FgCalculateTopkIndicesAndValuesIav2);
  }

  static bool CalculateCurrentResultSize(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    int64 result_size = std::distance(begin, end);
    context.SetDoubleCommonAttr("result_size", result_size);
    return true;
  }

  static bool TagForEffectivelyReason(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    int64 result_size = std::distance(begin, end);
    auto redirect_reason_flag_func = context.SetIntItemAttr("redirect_reason_flag");
    auto effectively_reason_flag_func = context.SetIntItemAttr("effectively_reason_flag");
    for (auto r_it = begin; r_it != end; ++r_it) {
      const CommonRecoResult &reco_result = *r_it;
      if (300070 == reco_result.reason) {
        redirect_reason_flag_func(reco_result, 1);
      }
      if (300051 == reco_result.reason) {
        effectively_reason_flag_func(reco_result, 1);
      }
    }
    return true;
  }

  static bool PackEffectivelyReasonItemList(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> redirect_list;
    std::vector<int64> effectively_list;
    auto redirect_reason_flag_attr = context.GetIntItemAttr("redirect_reason_flag");
    auto effectively_reason_flag_attr = context.GetIntItemAttr("effectively_reason_flag");
    for (auto r_it = begin; r_it != end; ++r_it) {
      const CommonRecoResult &reco_result = *r_it;
      auto flag1 = redirect_reason_flag_attr(reco_result).value_or(0);
      if (flag1 == 1) {
        redirect_list.push_back(reco_result.GetId());
      }
      auto flag2 = effectively_reason_flag_attr(reco_result).value_or(0);
      if (1 == flag2) {
        effectively_list.push_back(reco_result.GetId());
      }
    }

    std::vector<int64> final_list;
    final_list.swap(redirect_list);
    auto addition_size = std::min(static_cast<int>(effectively_list.size()), 200);
    for (int i = 0; i < addition_size; ++i) {
      final_list.push_back(effectively_list[i]);
    }
    context.SetIntListCommonAttr("effectively_reason_item_list", std::move(final_list));
    return true;
  }

  static bool SaveReasonToTargetAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto target_reason_str = context.GetStringCommonAttr("save_reason_to_attr").value_or("");
    if (target_reason_str.empty()) {
      return true;
    }
    auto target_reason_func = context.SetIntItemAttr(target_reason_str);
    for (auto r_it = begin; r_it != end; ++r_it) {
      const CommonRecoResult &reco_result = *r_it;
      target_reason_func(reco_result, reco_result.reason);
    }
    return true;
  }

  static bool CalculatePDI2IMergeScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    int64 result_size = std::distance(begin, end);
    auto online_clk_item_list = context.GetIntListCommonAttr("online_clk_item_list");
    if (!online_clk_item_list) {
      return true;
    }
    auto trigger_item_attr = context.GetStringItemAttr("trigger_item");
    auto swing_score_attr = context.GetDoubleItemAttr("swing_score");
    auto trigger_score_func = context.SetDoubleItemAttr("trigger_score");
    auto pdi2i_merge_score_func = context.SetDoubleItemAttr("pdi2i_merge_score");
    for (auto r_it = begin; r_it != end; ++r_it) {
      const CommonRecoResult &reco_result = *r_it;
      auto trigger_item_str = trigger_item_attr(reco_result).value_or("");
      int64 trigger_item = 0;
      if (!absl::SimpleAtoi(trigger_item_str, &trigger_item)) {
        continue;
      }
      double trigger_score = 0.0;
      for (size_t i = 0; i < online_clk_item_list->size(); ++i) {
        if (trigger_item != (*online_clk_item_list)[i]) {
          continue;
        }
        if (online_clk_item_list->size() - i + 1 > 0) {
          trigger_score = 1.0 / (online_clk_item_list->size() - i + 1);
        }
      }
      double swing_score = swing_score_attr(reco_result).value_or(0.0);
      double pdi2i_merge_score = pow(trigger_score, 10.0) * pow(swing_score, 1.0);
      trigger_score_func(reco_result, swing_score);
      pdi2i_merge_score_func(reco_result, pdi2i_merge_score);
    }
    return true;
  }

  static bool LiveInteractiveAuthorRetrvGenerateWeight(const CommonRecoLightFunctionContext &context,
                                                       RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto live_interactive_author_ids =
        context.GetIntListCommonAttr("live_interactive_author_ids").value_or(default_list);
    auto merchant_live_interact_author_w =
        context.GetDoubleCommonAttr("merchant_live_interact_author_w").value_or(1.0);
    auto merchant_live_interact_author_w_max =
        context.GetDoubleCommonAttr("merchant_live_interact_author_w_max").value_or(1.05);
    auto merchant_live_interact_author_time_w =
        context.GetDoubleCommonAttr("merchant_live_interact_author_time_w").value_or(0.2);
    auto merchant_live_interact_author_action_w1 =
        context.GetIntCommonAttr("merchant_live_interact_author_action_w1").value_or(2);
    auto merchant_live_interact_author_action_w2 =
        context.GetIntCommonAttr("merchant_live_interact_author_action_w2").value_or(1);
    auto live_interactive_author_detail =
        context.GetStringCommonAttr("live_interactive_author_detail").value_or("");

    auto aId_attr = context.GetIntItemAttr("aId");
    auto set_live_interactive_score = context.SetDoubleItemAttr("live_interactive_score");
    auto set_is_live_interactive = context.SetIntItemAttr("is_live_interactive");

    std::vector<absl::string_view> detail_list = absl::StrSplit(live_interactive_author_detail, "|");
    std::unordered_map<uint64, double> boost_map;
    for (const auto &detail : detail_list) {
      std::vector<absl::string_view> info_list = absl::StrSplit(detail, "_");
      if (info_list.empty()) {
        continue;
      }
      uint64 aId;
      if (!absl::SimpleAtoi(info_list[0], &aId)) {
        continue;
      }
      double boost_weight = 1.0;
      double sum_score = 0.0;
      double score = 0.0;
      sum_score += info_list.size() > 2 && absl::SimpleAtod(info_list[1], &score)
                       ? score * merchant_live_interact_author_time_w
                       : 0.0;
      sum_score += info_list.size() > 3 && absl::SimpleAtod(info_list[2], &score)
                       ? score * merchant_live_interact_author_action_w1
                       : 0.0;
      sum_score += info_list.size() > 4 && absl::SimpleAtod(info_list[3], &score)
                       ? score * merchant_live_interact_author_action_w2
                       : 0.0;
      sum_score += info_list.size() > 5 && absl::SimpleAtod(info_list[4], &score)
                       ? score * merchant_live_interact_author_action_w1
                       : 0.0;
      sum_score += info_list.size() > 6 && absl::SimpleAtod(info_list[5], &score)
                       ? score * merchant_live_interact_author_action_w1
                       : 0.0;
      sum_score += info_list.size() > 7 && absl::SimpleAtod(info_list[6], &score)
                       ? score * merchant_live_interact_author_action_w2
                       : 0.0;
      boost_weight += sum_score / 100;
      boost_weight *= merchant_live_interact_author_w;
      if (boost_weight > merchant_live_interact_author_w_max) {
        boost_weight = merchant_live_interact_author_w_max;
      }
      boost_map.insert({aId, boost_weight});
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aId = aId_attr(result).value_or(-1);
      int is_interactive = 0;
      double interactive_score = 1.0;
      for (const auto interactive_aid : live_interactive_author_ids) {
        if (aId == interactive_aid) {
          is_interactive = 1;
          auto it = boost_map.find(aId);
          if (it != boost_map.end()) {
            interactive_score = it->second;
          }
          break;
        }
      }
      set_is_live_interactive(result, is_interactive);
      set_live_interactive_score(result, interactive_score);
    });
    return true;
  }

  static bool CalculateFollowTimeDay(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto follow_aid_list = context.GetIntListCommonAttr("follow_aid_list").value_or(default_list);
    auto follow_time_list = context.GetIntListCommonAttr("follow_time_list").value_or(default_list);
    auto current_timestamp = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1);
    auto aId = context.GetIntItemAttr("aId");
    auto follow_time_day = context.SetIntItemAttr("follow_time_day");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (int i = 0; i < follow_aid_list.length(); i++) {
        int follow_day = -1;
        if (aId(result) == follow_aid_list[i]) {
          follow_day = std::ceil((current_timestamp - follow_time_list[i]) * 1.0 / 3600 / 24 / 1000);
        }
        follow_time_day(result, follow_day);
      }
    });
    return true;
  }

  static bool GetMcScoreSub(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    static std::vector<absl::string_view> default_list;
    auto aid_mc_score_list = context.GetStringListCommonAttr("aid_mc_score_list").value_or(default_list);
    auto rey_merchant_match_aid_prerank_score_list =
        context.GetStringListCommonAttr("rey_merchant_match_aid_prerank_score_list").value_or(default_list);
    auto aId_attr = context.GetIntItemAttr("aId");
    auto set_mc_score_sub = context.SetDoubleItemAttr("mc_score_sub");
    auto set_is_filter_flag = context.SetIntItemAttr("is_filter_flag");
    auto set_prerank_ctr_sub = context.SetDoubleItemAttr("prerank_ctr_sub");
    auto set_prerank_cvr_sub = context.SetDoubleItemAttr("prerank_cvr_sub");
    auto set_prerank_wtr_sub = context.SetDoubleItemAttr("prerank_wtr_sub");
    auto set_prerank_wtime_sub = context.SetDoubleItemAttr("prerank_wtime_sub");
    auto set_realtime_price_sub = context.SetDoubleItemAttr("realtime_price_sub");

    thread_local std::unordered_map<uint64, double> aid_mc_score_map;
    for (const auto &aid_mc_score : aid_mc_score_list) {
      std::vector<absl::string_view> list = absl::StrSplit(aid_mc_score, ":");
      if (list.size() == 2) {
        uint64 aId = 0;
        if (!absl::SimpleAtoi(list[0], &aId)) {
          continue;
        }
        double score = 0.0;
        absl::SimpleAtod(list[1], &score) ? aid_mc_score_map.insert({aId, score})
                                          : aid_mc_score_map.insert({aId, 0.0});
      }
    }

    thread_local std::unordered_map<uint64, std::vector<double>> aid_prerank_score_map;
    for (const auto &aid_prerank_score : rey_merchant_match_aid_prerank_score_list) {
      std::vector<absl::string_view> list = absl::StrSplit(aid_prerank_score, ":");
      if (list.size() == 6) {
        uint64 aId = 0;
        if (!absl::SimpleAtoi(list[0], &aId)) {
          continue;
        }
        std::vector<double> score_list;
        for (int i = 1; i < 6; i++) {
          double score = 0.0;
          absl::SimpleAtod(list[i], &score) ? score_list.push_back(score) : score_list.push_back(0.0);
        }
        aid_prerank_score_map.insert({aId, std::move(score_list)});
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 aId = aId_attr(result).value_or(-1);
      int is_filter_flag = 1;
      double mc_score_sub = 0.0;
      double prerank_ctr_sub = 0.0;
      double prerank_cvr_sub = 0.0;
      double prerank_wtr_sub = 0.0;
      double prerank_wtime_sub = 0.0;
      double realtime_price_sub = 0.0;
      if (aId > 0) {
        auto it = aid_mc_score_map.find(aId);
        if (it != aid_mc_score_map.end()) {
          mc_score_sub = it->second;
          is_filter_flag = 0;
        }

        auto prerank_it = aid_prerank_score_map.find(aId);
        if (prerank_it != aid_prerank_score_map.end()) {
          const auto &list = prerank_it->second;
          prerank_ctr_sub = list[0];
          prerank_cvr_sub = list[1];
          prerank_wtr_sub = list[2];
          prerank_wtime_sub = list[3];
          realtime_price_sub = list[4];
        }
      }
      set_mc_score_sub(result, mc_score_sub);
      set_is_filter_flag(result, is_filter_flag);
      set_prerank_ctr_sub(result, prerank_ctr_sub);
      set_prerank_cvr_sub(result, prerank_cvr_sub);
      set_prerank_wtr_sub(result, prerank_wtr_sub);
      set_prerank_wtime_sub(result, prerank_wtime_sub);
      set_realtime_price_sub(result, realtime_price_sub);
    });
    return true;
  }

  static bool ExtractLive2SellerList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto live_spu_info_list = context.GetIntListCommonAttr("live_spu_info_list");
    auto timestamp_list = context.GetIntListCommonAttr("filter_timestamp_list");
    auto author_id_list = context.GetIntListCommonAttr("filter_author_id_list");
    auto max_result_num = context.GetIntCommonAttr("max_result_num").value_or(0);
    auto max_item_num = context.GetIntCommonAttr("max_item_num").value_or(0);

    // 返回
    std::vector<int64> live_item_id_list;
    std::vector<int64> live_seller_id_list;
    std::vector<int64> live_spu_id_list;
    std::vector<int64> live_brand_cate1_id_list;

    int try_item_num = 0;
    int target_item_match_num = 0;
    int for_try_num = 0;
    int live_spu_id_empty = 0;

    if (!live_spu_info_list || live_spu_info_list->size() == 0) {
      live_spu_id_empty = 1;
    } else {
      for (int k = 0; timestamp_list && k < timestamp_list->size(); k++) {
        int start_index = k * 1200;
        int64 author_id = author_id_list->at(k);
        if (live_spu_id_list.size() > max_result_num) {
          break;
        }
        try_item_num = try_item_num + 1;
        int64 select_max_timestamp = -1;
        int select_max_index = -1;
        int64 realshow_time = timestamp_list->at(k);

        // 遍历 live ( 正在讲解 / top3 )
        for (int j = 0; j < max_item_num; j++) {
          int i = start_index + j * 24;
          int64 tmp_item_id = live_spu_info_list->at(i);
          int64 tmp_spu_id = live_spu_info_list->at(i + 4);
          int64 tmp_item_start_timestamp_sec = live_spu_info_list->at(i + 1);
          if (tmp_item_start_timestamp_sec == 0) {
            break;
          }
          if (tmp_item_id > 0) {
            if (tmp_item_start_timestamp_sec < realshow_time &&
                tmp_item_start_timestamp_sec > select_max_index) {
              select_max_timestamp = tmp_item_start_timestamp_sec;
              select_max_index = i;
            }
          }
          for_try_num = for_try_num + 1;
        }

        if (select_max_index >= 0) {
          target_item_match_num = target_item_match_num + 1;
          int64 tmp_item_id = live_spu_info_list->at(select_max_index);
          int64 tmp_seller_id = live_spu_info_list->at(select_max_index + 2);
          if (author_id != tmp_seller_id) {
            int64 tmp_cate_id = live_spu_info_list->at(select_max_index + 3);
            int64 tmp_spu_id = live_spu_info_list->at(select_max_index + 4);
            int64 tmp_brand_id = live_spu_info_list->at(select_max_index + 5);

            int64 brand_cate1_id = ((tmp_cate_id >> 48) << 48) | tmp_brand_id;
            if (tmp_brand_id == 0) {
              brand_cate1_id = 0;
            }

            live_item_id_list.push_back(tmp_item_id);
            live_spu_id_list.push_back(tmp_spu_id);
            live_seller_id_list.push_back(tmp_seller_id);
            live_brand_cate1_id_list.push_back(brand_cate1_id);
          }
        }
      }
    }

    context.SetIntCommonAttr("live_item_id_list_len", live_item_id_list.size());
    context.SetIntListCommonAttr("live_item_id_list", std::move(live_item_id_list));
    context.SetIntListCommonAttr("live_seller_id_list", std::move(live_seller_id_list));
    context.SetIntListCommonAttr("live_spu_id_list", std::move(live_spu_id_list));
    context.SetIntListCommonAttr("live_brand_cate1_id_list", std::move(live_brand_cate1_id_list));
    context.SetIntCommonAttr("try_item_num", try_item_num);
    context.SetIntCommonAttr("target_item_match_num", target_item_match_num);
    context.SetIntCommonAttr("avg_slice_num", floor(for_try_num * 1.0 / (try_item_num + 1)));
    context.SetIntCommonAttr("live_spu_id_empty", live_spu_id_empty);
    return true;
  }

  static bool GetFrScoreSub(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    static std::vector<absl::string_view> default_list;
    auto aid_fr_score_list = context.GetStringListCommonAttr("aid_fr_score_list").value_or(default_list);
    auto all_1pp_support_fr_price_weight =
        context.GetDoubleCommonAttr("all_1pp_support_fr_price_weight").value_or(0.0);
    auto aId_attr = context.GetIntItemAttr("aId");

    auto sc_live_ctr_sub_set = context.SetDoubleItemAttr("sc_live_ctr_sub");
    auto sc_wtr_sub_set = context.SetDoubleItemAttr("sc_wtr_sub");
    auto sc_cvr_sub_set = context.SetDoubleItemAttr("sc_cvr_sub");
    auto sc_wtime_sub_set = context.SetDoubleItemAttr("sc_wtime_sub");
    auto realtime_price_sub_set = context.SetDoubleItemAttr("realtime_price_sub");
    auto sc_ctr_cvr_sub_set = context.SetDoubleItemAttr("sc_ctr_cvr_sub");
    auto fr_score_sub_set = context.SetDoubleItemAttr("fr_score_sub");
    auto fr_subflow_gpm_score_set = context.SetDoubleItemAttr("fr_subflow_gpm_score");
    auto set_is_filter_flag = context.SetIntItemAttr("is_filter_flag");

    std::unordered_map<uint64, std::vector<double>> aid_fr_score_map;
    for (const auto &aid_fr_score_str : aid_fr_score_list) {
      std::vector<absl::string_view> list = absl::StrSplit(aid_fr_score_str, ":");
      if (list.size() == 6) {
        uint64 aId;
        if (!absl::SimpleAtoi(list[0], &aId)) {
          continue;
        }
        std::vector<double> score_list;
        for (int i = 1; i < 6; i++) {
          double score = 0.0;
          absl::SimpleAtod(list[i], &score) ? score_list.push_back(score) : score_list.push_back(0.0);
        }
        aid_fr_score_map[aId] = std::move(score_list);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double sc_live_ctr_sub = 0.0, sc_wtr_sub = 0.0, sc_cvr_sub = 0.0, sc_wtime_sub = 0.0,
             realtime_price_sub = 0.0, sc_ctr_cvr_sub = 0.0, fr_score_sub = 0.0, gpm_score_sub = 0.0;
      int is_filter_flag = 1;

      auto aId = aId_attr(result).value_or(-1);
      auto it = aid_fr_score_map.find(aId);
      if (it != aid_fr_score_map.end()) {
        const auto &score_list = it->second;
        sc_live_ctr_sub = score_list[0];
        sc_wtr_sub = score_list[1];
        sc_cvr_sub = score_list[2];
        sc_wtime_sub = score_list[3];
        realtime_price_sub = score_list[4];

        sc_ctr_cvr_sub = (sc_live_ctr_sub + 0.001) * (sc_cvr_sub + 0.001) * 1000000;
        fr_score_sub =
            sc_live_ctr_sub * sc_cvr_sub * pow(realtime_price_sub, all_1pp_support_fr_price_weight);
        is_filter_flag = 0;

        // 如果 fr_score_sub 迭代成为 ensembleSort，则 gpm_score_sub 不可直接使用 fr_score_sub
        gpm_score_sub =
            sc_live_ctr_sub * sc_cvr_sub * pow(realtime_price_sub, all_1pp_support_fr_price_weight) * 10000;
      }

      sc_live_ctr_sub_set(result, sc_live_ctr_sub);
      sc_wtr_sub_set(result, sc_wtr_sub);
      sc_cvr_sub_set(result, sc_cvr_sub);
      sc_wtime_sub_set(result, sc_wtime_sub);
      realtime_price_sub_set(result, realtime_price_sub);
      fr_score_sub_set(result, fr_score_sub);
      fr_subflow_gpm_score_set(result, gpm_score_sub);
      set_is_filter_flag(result, is_filter_flag);
      sc_ctr_cvr_sub_set(result, sc_ctr_cvr_sub);
    });
    return true;
  }

  static bool GetDistSellerLivingFilterSetStr(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto dist_seller_living_filter_set_str_vector_ptr =
        context.GetPtrCommonAttr<std::vector<std::string>>("dist_seller_living_filter_set_str_ptr");
    if (nullptr == dist_seller_living_filter_set_str_vector_ptr) {
      LOG(INFO) << "dist_seller_living_filter_set_str_vector_ptr is a nullptr";
      return true;
    }
    std::string res = "";
    if (dist_seller_living_filter_set_str_vector_ptr->size() > 0) {
      std::for_each(dist_seller_living_filter_set_str_vector_ptr->begin(),
                    dist_seller_living_filter_set_str_vector_ptr->end(), [&](const auto &value) {
                      res = absl::StrJoin(*dist_seller_living_filter_set_str_vector_ptr, ",");
                    });
    }
    context.SetStringCommonAttr("dist_seller_living_filter_set_str", std::move(res));
    return true;
  }

  static bool GetShangdaSpuExpansionSellerIdList(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto shangda_spu_or_shangda_brand_cate1_memory_data_ptr =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<std::string>>>(
            "shangda_spu_or_shangda_brand_cate1_memory_data_ptr");

    if (nullptr == shangda_spu_or_shangda_brand_cate1_memory_data_ptr) {
      LOG(INFO) << "shangda_spu_or_shangda_brand_cate1_memory_data_ptr is a nullptr";
      return true;
    }
    std::vector<std::string> string_output_common_attr;
    if (shangda_spu_or_shangda_brand_cate1_memory_data_ptr->size() > 0) {
      auto &resMap = *shangda_spu_or_shangda_brand_cate1_memory_data_ptr;

      static std::vector<absl::string_view> default_list;
      auto shangda_spu_expansion_seller_id_list =
          context.GetStringListCommonAttr("shangda_spu_or_shangda_brand_cate1_redis_key")
              .value_or(default_list);
      std::for_each(shangda_spu_expansion_seller_id_list.begin(), shangda_spu_expansion_seller_id_list.end(),
                    [&](const auto &strKey) {
                      auto it = resMap.find(std::string(strKey));
                      std::string strResult = "";
                      if (it != resMap.end()) strResult = absl::StrJoin(it->second, ",");
                      string_output_common_attr.push_back(strResult);
                    });
    }
    context.SetStringListCommonAttr("shangda_spu_or_shangda_brand_cate1_result",
                                    std::move(string_output_common_attr));
    return true;
  }

  static bool CalculateDedupAllMergeSellerTimeList(const CommonRecoLightFunctionContext &context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_attr_list = {"item_seller_id_min_time",
                           "item_seller_id_max_time",
                           "video_seller_id_min_time",
                           "video_seller_id_max_time",
                           "live_seller_id_min_time",
                           "live_seller_id_max_time",
                           "lookalike_seller_id_min_time",
                           "lookalike_seller_id_max_time",
                           "item_spu_seller_id_min_time",
                           "item_spu_seller_id_max_time",
                           "item_brand_cate1_seller_id_min_time",
                           "item_brand_cate1_seller_id_max_time",
                           "video_spu_seller_id_min_time",
                           "video_spu_seller_id_max_time",
                           "video_brand_cate1_seller_id_min_time",
                           "video_brand_cate1_seller_id_max_time",
                           "live_spu_seller_id_min_time",
                           "live_spu_seller_id_max_time",
                           "live_brand_cate1_seller_id_min_time",
                           "live_brand_cate1_seller_id_max_time",
                           "live_cart_seller_id_min_time",
                           "live_cart_seller_id_max_time",
                           "offline_item_seller_id_min_time",
                           "offline_item_seller_id_max_time",
                           "offline_item_spu_seller_id_min_time",
                           "offline_item_spu_seller_id_max_time",
                           "offline_item_brand_cate1_seller_id_min_time",
                           "offline_item_brand_cate1_seller_id_max_time",
                           "bought_seller_id_min_time",
                           "bought_seller_id_max_time"};
    auto set_min_time = context.SetIntItemAttr("min_time");
    auto set_max_time = context.SetIntItemAttr("max_time");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 min_time = static_cast<int64>(1) << 60, max_time = 0;
      std::for_each(item_attr_list.begin(), item_attr_list.end(), [&](const auto &item_attr) {
        auto attr_func = context.GetIntItemAttr(item_attr);
        auto value = attr_func(result).value_or(0);  // 得到的每一个 item_attr 的值
        if (0 != value && value < min_time) min_time = value;
        if (value > max_time) max_time = value;
      });
      set_min_time(result, min_time);
      set_max_time(result, max_time);
    });

    return true;
  }

  static bool LuaOpt2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                      RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto video_spu_info = context.GetIntListCommonAttr("video_spu_info").value_or(default_list);
    auto video_author_id = context.GetIntListCommonAttr("video_author_id").value_or(default_list);
    auto video_playtime = context.GetIntListCommonAttr("video_playtime").value_or(default_list);
    auto video_timestamp = context.GetIntListCommonAttr("video_timestamp").value_or(default_list);
    auto all_1pp_all_enable_spu_brand_filter_time =
        context.GetIntCommonAttr("all_1pp_all_enable_spu_brand_filter_time").value_or(0);
    auto all_1pp_all_spu_brand_filter_video_time_threshold =
        context.GetIntCommonAttr("all_1pp_all_spu_brand_filter_video_time_threshold").value_or(-1);

    std::vector<int64> recall_item_id_list, recall_seller_id_list, recall_spu_id_list, recall_brand_id_list,
        recall_brand_cate1_id_list, recall_seller_id_min_time, recall_seller_id_max_time,
        recall_author_id_list, recall_author_item_id_list, recall_spu_id_min_time, recall_spu_id_max_time,
        recall_spu_author_id_list, recall_spu_author_item_id_list, recall_brand_cate1_id_min_time,
        recall_brand_cate1_id_max_time, recall_brand_cate1_author_id_list,
        recall_brand_cate1_author_item_id_list;
    folly::F14FastMap<int64, int64> seller_id_min_time, seller_id_max_time, spu_id_min_time, spu_id_max_time,
        brand_cate1_id_min_time, brand_cate1_id_max_time;
    using pii64 = std::pair<int64, int64>;
    folly::F14FastMap<int64, pii64> seller_to_info_id_map, spu_to_info_id_map, brand_to_info_id_map;

    int64 filter_count = 0;
    static constexpr int64 data_split_n = 5;
    const int64 video_spu_info_len = video_spu_info.size() / data_split_n;

    for (int64 i = 0; i < video_spu_info_len; i++) {
      int64 index = data_split_n * i;
      int64 author_id = (i < video_author_id.size()) ? video_author_id[i] : 0;
      int64 timestamp = (i < video_timestamp.size()) ? video_timestamp[i] : 0;
      int64 playtime = (i < video_playtime.size()) ? video_playtime[i] : 0;
      int64 spu_id = (index < video_spu_info.size()) ? video_spu_info[index] : 0;
      int64 brand_id = (index + 1 < video_spu_info.size()) ? video_spu_info[index + 1] : 0;
      int64 cate_id = (index + 2 < video_spu_info.size()) ? video_spu_info[index + 2] : 0;
      int64 item_id = (index + 3 < video_spu_info.size()) ? video_spu_info[index + 3] : 0;
      int64 seller_id = (index + 4 < video_spu_info.size()) ? video_spu_info[index + 4] : 0;
      int64 cate1_id = (cate_id >> 48);
      int64 brand_cate1_id = ((cate1_id << 48) | brand_id);

      if (author_id != seller_id) {
        if (item_id > 0 && seller_id > 0) {
          recall_item_id_list.push_back(item_id);
          if (seller_id_min_time.find(seller_id) == seller_id_min_time.end()) {
            recall_seller_id_list.push_back(seller_id);
            seller_id_min_time[seller_id] = (1LL << 60);
            seller_id_max_time[seller_id] = 0;
            seller_to_info_id_map[seller_id] = pii64{author_id, item_id};
          }
          seller_id_min_time[seller_id] = std::min(seller_id_min_time[seller_id], timestamp);
          if (timestamp > seller_id_max_time[seller_id]) {
            seller_id_max_time[seller_id] = timestamp;
            seller_to_info_id_map[seller_id] = {author_id, item_id};
          }
        }

        bool no_filter_condition = (all_1pp_all_enable_spu_brand_filter_time == 0) ||
                                   (all_1pp_all_enable_spu_brand_filter_time == 1 &&
                                    playtime >= all_1pp_all_spu_brand_filter_video_time_threshold);

        if (!no_filter_condition) {
          filter_count++;
        }

        // Deduplication
        if (spu_id > 0 && no_filter_condition) {
          if (spu_id_min_time.find(spu_id) == spu_id_min_time.end()) {
            spu_id_min_time[spu_id] = (1LL << 60);
            spu_id_max_time[spu_id] = 0;
            spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
            recall_spu_id_list.push_back(spu_id);
          }
          spu_id_min_time[spu_id] = std::min(spu_id_min_time[spu_id], timestamp);
          if (timestamp > spu_id_max_time[spu_id]) {
            spu_id_max_time[spu_id] = timestamp;
            spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
          }
        }

        if (brand_id > 0 && cate1_id > 0 && no_filter_condition) {
          recall_brand_id_list.push_back(brand_id);
          if (brand_cate1_id_min_time.find(brand_cate1_id) == brand_cate1_id_min_time.end()) {
            brand_cate1_id_min_time[brand_cate1_id] = (1LL << 60);
            brand_cate1_id_max_time[brand_cate1_id] = 0;
            brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
            recall_brand_cate1_id_list.push_back(brand_cate1_id);
          }
          brand_cate1_id_min_time[brand_cate1_id] =
              std::min(brand_cate1_id_min_time[brand_cate1_id], timestamp);
          if (timestamp > brand_cate1_id_max_time[brand_cate1_id]) {
            brand_cate1_id_max_time[brand_cate1_id] = timestamp;
            brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
          }
        }
      }
    }

    for (const auto seller_id : recall_seller_id_list) {
      auto min_time = seller_id_min_time[seller_id];
      auto max_time = seller_id_max_time[seller_id];
      auto author_id = seller_to_info_id_map[seller_id].first;
      auto item_id = seller_to_info_id_map[seller_id].second;
      recall_seller_id_min_time.push_back(min_time);
      recall_seller_id_max_time.push_back(max_time);
      recall_author_id_list.push_back(author_id);
      recall_author_item_id_list.push_back(item_id);
    }

    // Loop over recall_spu_id_list and insert data into vectors
    for (const auto &spu : recall_spu_id_list) {
      auto min_time = spu_id_min_time[spu];
      auto max_time = spu_id_max_time[spu];
      auto author_id = spu_to_info_id_map[spu].first;
      auto item_id = spu_to_info_id_map[spu].second;
      recall_spu_id_min_time.push_back(min_time);
      recall_spu_id_max_time.push_back(max_time);
      recall_spu_author_id_list.push_back(author_id);
      recall_spu_author_item_id_list.push_back(item_id);
    }

    // Loop over recall_brand_cate1_id_list and insert data into vectors
    for (const auto &brand_cate1_id : recall_brand_cate1_id_list) {
      auto min_time = brand_cate1_id_min_time[brand_cate1_id];
      auto max_time = brand_cate1_id_max_time[brand_cate1_id];
      auto author_id = brand_to_info_id_map[brand_cate1_id].first;
      auto item_id = brand_to_info_id_map[brand_cate1_id].second;
      recall_brand_cate1_id_min_time.push_back(min_time);
      recall_brand_cate1_id_max_time.push_back(max_time);
      recall_brand_cate1_author_id_list.push_back(author_id);
      recall_brand_cate1_author_item_id_list.push_back(item_id);
    }

    context.SetIntCommonAttr("recall_video_item_id_list_len", recall_item_id_list.size());
    context.SetIntCommonAttr("recall_video_seller_id_list_len", recall_seller_id_list.size());
    context.SetIntCommonAttr("recall_video_spu_id_list_len", recall_spu_id_list.size());
    context.SetIntCommonAttr("recall_video_brand_cate1_id_list_len", recall_brand_cate1_id_list.size());
    context.SetIntListCommonAttr("recall_video_item_id_list", std::move(recall_item_id_list));
    context.SetIntListCommonAttr("recall_video_seller_id_list", std::move(recall_seller_id_list));
    context.SetIntListCommonAttr("recall_video_spu_id_list", std::move(recall_spu_id_list));
    context.SetIntListCommonAttr("recall_video_brand_cate1_id_list", std::move(recall_brand_cate1_id_list));
    context.SetIntListCommonAttr("recall_video_brand_id_list", std::move(recall_brand_id_list));
    context.SetIntListCommonAttr("recall_video_seller_id_min_time", std::move(recall_seller_id_min_time));
    context.SetIntListCommonAttr("recall_video_seller_id_max_time", std::move(recall_seller_id_max_time));
    context.SetIntListCommonAttr("recall_video_author_id_list", std::move(recall_author_id_list));
    context.SetIntListCommonAttr("recall_video_author_item_id_list", std::move(recall_author_item_id_list));
    context.SetIntListCommonAttr("recall_video_spu_id_min_time", std::move(recall_spu_id_min_time));
    context.SetIntListCommonAttr("recall_video_spu_id_max_time", std::move(recall_spu_id_max_time));
    context.SetIntListCommonAttr("recall_video_spu_author_id_list", std::move(recall_spu_author_id_list));
    context.SetIntListCommonAttr("recall_video_spu_author_item_id_list",
                                 std::move(recall_spu_author_item_id_list));
    context.SetIntListCommonAttr("recall_video_brand_cate1_id_min_time",
                                 std::move(recall_brand_cate1_id_min_time));
    context.SetIntListCommonAttr("recall_video_brand_cate1_id_max_time",
                                 std::move(recall_brand_cate1_id_max_time));
    context.SetIntListCommonAttr("recall_video_brand_cate1_author_id_list",
                                 std::move(recall_brand_cate1_author_id_list));
    context.SetIntListCommonAttr("recall_video_brand_cate1_author_item_id_list",
                                 std::move(recall_brand_cate1_author_item_id_list));
    context.SetIntCommonAttr("video_time_filter_cnt", filter_count);

    return true;
  }

  static bool LuaOpt3(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                      RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto filter_click_item_id_list =
        context.GetIntListCommonAttr("filter_click_item_id_list").value_or(default_list);
    auto click_spu_info = context.GetIntListCommonAttr("click_spu_info").value_or(default_list);
    auto filter_click_timestamp_list =
        context.GetIntListCommonAttr("filter_click_timestamp_list").value_or(default_list);
    auto filter_real_seller_id_list =
        context.GetIntListCommonAttr("filter_real_seller_id_list").value_or(default_list);
    auto all_1pp_all_enable_spu_brand_filter_time =
        context.GetIntCommonAttr("all_1pp_all_enable_spu_brand_filter_time").value_or(0);
    auto all_1pp_all_spu_brand_filter_video_time_threshold =
        context.GetIntCommonAttr("all_1pp_all_spu_brand_filter_video_time_threshold").value_or(-1);

    std::vector<int64> recall_item_id_list, recall_seller_id_list, recall_spu_id_list, recall_brand_id_list,
        recall_brand_cate1_id_list, recall_seller_id_min_time, recall_seller_id_max_time,
        recall_author_id_list, recall_author_item_id_list, recall_spu_id_min_time, recall_spu_id_max_time,
        recall_spu_author_id_list, recall_spu_author_item_id_list, recall_brand_cate1_id_min_time,
        recall_brand_cate1_id_max_time, recall_brand_cate1_author_id_list,
        recall_brand_cate1_author_item_id_list;
    folly::F14FastMap<int64, int64> seller_id_min_time, seller_id_max_time, spu_id_min_time, spu_id_max_time,
        brand_cate1_id_min_time, brand_cate1_id_max_time;
    using pii64 = std::pair<int64, int64>;
    folly::F14FastMap<int64, pii64> seller_to_info_id_map, spu_to_info_id_map, brand_to_info_id_map;

    static constexpr int64 data_split_n = 4;
    const int64 click_spu_info_len = click_spu_info.size() / data_split_n;

    for (int64 i = 0; i < click_spu_info_len; i++) {
      int64 item_id = (i < filter_click_item_id_list.size()) ? filter_click_item_id_list[i] : 0;
      int64 index = data_split_n * i;
      int64 spu_id = (index < click_spu_info.size()) ? click_spu_info[index] : 0;
      int64 seller_id = (index + 1 < click_spu_info.size()) ? click_spu_info[index + 1] : 0;
      int64 brand_id = (index + 2 < click_spu_info.size()) ? click_spu_info[index + 2] : 0;
      int64 cate_id = (index + 3 < click_spu_info.size()) ? click_spu_info[index + 3] : 0;
      int64 cate1_id = (cate_id >> 48);
      int64 timestamp = (i < filter_click_timestamp_list.size()) ? filter_click_timestamp_list[i] : 0;
      int64 author_id = (i < filter_real_seller_id_list.size()) ? filter_real_seller_id_list[i] : 0;
      int64 brand_cate1_id = ((cate1_id << 48) | brand_id);

      if (item_id > 0 && seller_id > 0) {
        recall_item_id_list.push_back(item_id);
        if (seller_id_min_time.find(seller_id) == seller_id_min_time.end()) {
          recall_seller_id_list.push_back(seller_id);
          seller_id_min_time[seller_id] = (1LL << 60);
          seller_id_max_time[seller_id] = 0;
          seller_to_info_id_map[seller_id] = pii64{author_id, item_id};
        }
        seller_id_min_time[seller_id] = std::min(seller_id_min_time[seller_id], timestamp);
        if (timestamp > seller_id_max_time[seller_id]) {
          seller_id_max_time[seller_id] = timestamp;
          seller_to_info_id_map[seller_id] = {author_id, item_id};
        }
      }

      // Deduplication
      if (spu_id > 0) {
        if (spu_id_min_time.find(spu_id) == spu_id_min_time.end()) {
          spu_id_min_time[spu_id] = (1LL << 60);
          spu_id_max_time[spu_id] = 0;
          spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
          recall_spu_id_list.push_back(spu_id);
        }
        spu_id_min_time[spu_id] = std::min(spu_id_min_time[spu_id], timestamp);
        if (timestamp > spu_id_max_time[spu_id]) {
          spu_id_max_time[spu_id] = timestamp;
          spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
        }
      }

      if (brand_id > 0 && cate1_id > 0) {
        recall_brand_id_list.push_back(brand_id);
        if (brand_cate1_id_min_time.find(brand_cate1_id) == brand_cate1_id_min_time.end()) {
          brand_cate1_id_min_time[brand_cate1_id] = (1LL << 60);
          brand_cate1_id_max_time[brand_cate1_id] = 0;
          brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
          recall_brand_cate1_id_list.push_back(brand_cate1_id);
        }
        brand_cate1_id_min_time[brand_cate1_id] =
            std::min(brand_cate1_id_min_time[brand_cate1_id], timestamp);
        if (timestamp > brand_cate1_id_max_time[brand_cate1_id]) {
          brand_cate1_id_max_time[brand_cate1_id] = timestamp;
          brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
        }
      }
    }

    for (const auto seller_id : recall_seller_id_list) {
      auto min_time = seller_id_min_time[seller_id];
      auto max_time = seller_id_max_time[seller_id];
      auto author_id = seller_to_info_id_map[seller_id].first;
      auto item_id = seller_to_info_id_map[seller_id].second;
      recall_seller_id_min_time.push_back(min_time);
      recall_seller_id_max_time.push_back(max_time);
      recall_author_id_list.push_back(author_id);
      recall_author_item_id_list.push_back(item_id);
    }

    // Loop over recall_spu_id_list and insert data into vectors
    for (const auto &spu : recall_spu_id_list) {
      auto min_time = spu_id_min_time[spu];
      auto max_time = spu_id_max_time[spu];
      auto author_id = spu_to_info_id_map[spu].first;
      auto item_id = spu_to_info_id_map[spu].second;
      recall_spu_id_min_time.push_back(min_time);
      recall_spu_id_max_time.push_back(max_time);
      recall_spu_author_id_list.push_back(author_id);
      recall_spu_author_item_id_list.push_back(item_id);
    }

    // Loop over recall_brand_cate1_id_list and insert data into vectors
    for (const auto &brand_cate1_id : recall_brand_cate1_id_list) {
      auto min_time = brand_cate1_id_min_time[brand_cate1_id];
      auto max_time = brand_cate1_id_max_time[brand_cate1_id];
      auto author_id = brand_to_info_id_map[brand_cate1_id].first;
      auto item_id = brand_to_info_id_map[brand_cate1_id].second;
      recall_brand_cate1_id_min_time.push_back(min_time);
      recall_brand_cate1_id_max_time.push_back(max_time);
      recall_brand_cate1_author_id_list.push_back(author_id);
      recall_brand_cate1_author_item_id_list.push_back(item_id);
    }

    context.SetIntCommonAttr("recall_item_item_id_list_len", recall_item_id_list.size());
    context.SetIntCommonAttr("recall_item_seller_id_list_len", recall_seller_id_list.size());
    context.SetIntCommonAttr("recall_item_spu_id_list_len", recall_spu_id_list.size());
    context.SetIntCommonAttr("recall_item_brand_cate1_id_list_len", recall_brand_cate1_id_list.size());
    context.SetIntListCommonAttr("recall_item_item_id_list", std::move(recall_item_id_list));
    context.SetIntListCommonAttr("recall_item_seller_id_list", std::move(recall_seller_id_list));
    context.SetIntListCommonAttr("recall_item_spu_id_list", std::move(recall_spu_id_list));
    context.SetIntListCommonAttr("recall_item_brand_cate1_id_list", std::move(recall_brand_cate1_id_list));
    context.SetIntListCommonAttr("recall_item_brand_id_list", std::move(recall_brand_id_list));

    context.SetIntListCommonAttr("recall_item_seller_id_min_time", std::move(recall_seller_id_min_time));
    context.SetIntListCommonAttr("recall_item_seller_id_max_time", std::move(recall_seller_id_max_time));
    context.SetIntListCommonAttr("recall_item_author_id_list", std::move(recall_author_id_list));
    context.SetIntListCommonAttr("recall_item_author_item_id_list", std::move(recall_author_item_id_list));
    context.SetIntListCommonAttr("recall_item_spu_id_min_time", std::move(recall_spu_id_min_time));
    context.SetIntListCommonAttr("recall_item_spu_id_max_time", std::move(recall_spu_id_max_time));
    context.SetIntListCommonAttr("recall_item_spu_author_id_list", std::move(recall_spu_author_id_list));
    context.SetIntListCommonAttr("recall_item_spu_author_item_id_list",
                                 std::move(recall_spu_author_item_id_list));
    context.SetIntListCommonAttr("recall_item_brand_cate1_id_min_time",
                                 std::move(recall_brand_cate1_id_min_time));
    context.SetIntListCommonAttr("recall_item_brand_cate1_id_max_time",
                                 std::move(recall_brand_cate1_id_max_time));
    context.SetIntListCommonAttr("recall_item_brand_cate1_author_id_list",
                                 std::move(recall_brand_cate1_author_id_list));
    context.SetIntListCommonAttr("recall_item_brand_cate1_author_item_id_list",
                                 std::move(recall_brand_cate1_author_item_id_list));

    return true;
  }

  static std::vector<std::string> stringSplit(const std::string& str, char delim) {
      std::size_t previous = 0;
      std::size_t current = str.find(delim);
      std::vector<std::string> elems;
      while (current != std::string::npos) {
          if (current > previous) {
              elems.push_back(str.substr(previous, current - previous));
          }
          previous = current + 1;
          current = str.find(delim, previous);
      }
      if (previous != str.size()) {
          elems.push_back(str.substr(previous));
      }
      return elems;
  }

  static bool LuaOptSwtn(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                      RecoResultConstIter end) {
    static std::vector<int64> default_list;
    static std::vector<absl::string_view> string_default_list;
    // input  list
    auto source_id_list = context.GetIntListCommonAttr("source_id_list").value_or(default_list);
    auto item_id_list = context.GetIntListCommonAttr("item_id_list").value_or(default_list);
    auto seller_id_list = context.GetIntListCommonAttr("seller_id_list").value_or(default_list);
    auto spu_id_list = context.GetIntListCommonAttr("spu_id_list").value_or(default_list);
    auto brand_id_list = context.GetIntListCommonAttr("brand_id_list").value_or(default_list);
    auto category_pack_list = context.GetIntListCommonAttr("category_pack_list").value_or(default_list);
    auto real_seller_id_list = context.GetIntListCommonAttr("real_seller_id_list").value_or(default_list);
    auto timestamp_list = context.GetIntListCommonAttr("timestamp_list").value_or(default_list);
    auto target_vdeo_seller_replace_string_list =
      context.GetStringListCommonAttr("target_video_seller_replace_set").value_or(string_default_list);
    auto video_seller_replace_cnt = 0;
    folly::F14FastMap<int64, int64> target_video_seller_map;
    for (int i = 0; i < target_vdeo_seller_replace_string_list.size(); i++) {
      // split
      auto video_seller_string = std::string(target_vdeo_seller_replace_string_list[i]);
      auto kv_vector = stringSplit(video_seller_string, ':');
      if (kv_vector.size() != 2) {
        break;
      }
      auto sid = std::stoll(kv_vector[0]);
      auto video_vector = stringSplit(kv_vector[1], ',');

      // to map
      for (int j = 0; j < video_vector.size(); j++) {
        auto vid = video_vector[j];
        target_video_seller_map[std::stoll(vid)] = sid;
      }
    }

    // time gap
    auto time_gap = context.GetIntCommonAttr("all_1pp_all_swnt_us_max_days").value_or(360);
    auto current_timestamp = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1) / 1000;
    auto time_gap_ts = time_gap * 84600;
    // scene name for attr
    auto scene_name = context.GetStringCommonAttr("scene_name").value_or("");
    auto attr_prefix = std::string("recall_") + std::string(scene_name) + std::string("_");
    // redis key name
    auto spu_redis_key =
        context.GetStringCommonAttr("all_1pp_all_goods_seller_spuid_to_seller_redis_key").value_or("");
    auto brand_cate1_redis_key =
        context.GetStringCommonAttr("all_1pp_all_goods_seller_brand_cate1_to_seller_redis_key").value_or("");

    const int source_id_list_len = source_id_list.size();

    std::vector<int64>
        recall_item_id_list, recall_seller_id_list, recall_spu_id_list, recall_brand_id_list,
        recall_brand_cate1_id_list,
        recall_seller_id_min_time, recall_seller_id_max_time,
        recall_author_id_list, recall_author_item_id_list,
        recall_spu_id_min_time, recall_spu_id_max_time,
        recall_spu_author_id_list, recall_spu_author_item_id_list,
        recall_brand_cate1_id_min_time, recall_brand_cate1_id_max_time,
        recall_brand_cate1_author_id_list, recall_brand_cate1_author_item_id_list;
    std::vector<std::string> recall_spu_redis_key_list, recall_brand_cate1_redis_key_list;
    // reserve
    recall_item_id_list.reserve(source_id_list_len);
    recall_seller_id_list.reserve(source_id_list_len);
    recall_spu_id_list.reserve(source_id_list_len);
    recall_brand_id_list.reserve(source_id_list_len);
    recall_brand_cate1_id_list.reserve(source_id_list_len);
    recall_seller_id_min_time.reserve(source_id_list_len);
    recall_seller_id_max_time.reserve(source_id_list_len);
    recall_author_id_list.reserve(source_id_list_len);
    recall_author_item_id_list.reserve(source_id_list_len);
    recall_spu_id_min_time.reserve(source_id_list_len);
    recall_spu_id_max_time.reserve(source_id_list_len);
    recall_spu_author_id_list.reserve(source_id_list_len);
    recall_spu_author_item_id_list.reserve(source_id_list_len);
    recall_brand_cate1_id_min_time.reserve(source_id_list_len);
    recall_brand_cate1_id_max_time.reserve(source_id_list_len);
    recall_brand_cate1_author_id_list.reserve(source_id_list_len);
    recall_brand_cate1_author_item_id_list.reserve(source_id_list_len);
    recall_spu_redis_key_list.reserve(source_id_list_len);
    recall_brand_cate1_author_item_id_list.reserve(source_id_list_len);

    folly::F14FastMap<int64, int64>
        seller_id_min_time, seller_id_max_time, spu_id_min_time, spu_id_max_time,
        brand_cate1_id_min_time, brand_cate1_id_max_time;
    using pii64 = std::pair<int64, int64>;
    folly::F14FastMap<int64, pii64> seller_to_info_id_map, spu_to_info_id_map, brand_to_info_id_map;

    for (int i = 0; i < source_id_list_len; i++) {
      int64 source_id = source_id_list[i];
      int64 item_id = item_id_list[i];
      int64 spu_id = spu_id_list[i];
      int64 seller_id = seller_id_list[i];
      int64 brand_id = brand_id_list[i];
      int64 timestamp = timestamp_list[i];
      int64 author_id = real_seller_id_list[i];
      int64 cate_id = category_pack_list[i];
      int64 cate1_id = (cate_id >> 48);
      int64 brand_cate1_id = ((cate1_id << 48) | brand_id);

      if (target_video_seller_map.find(source_id) != target_video_seller_map.end()) {
        seller_id = target_video_seller_map[source_id];
        video_seller_replace_cnt += 1;
      }

      if ((seller_id == author_id) || (current_timestamp - timestamp > time_gap_ts)) {
        continue;
      }

      if (item_id > 0 && seller_id > 0) {
        recall_item_id_list.push_back(item_id);
        if (seller_id_min_time.find(seller_id) == seller_id_min_time.end()) {
          recall_seller_id_list.push_back(seller_id);
          seller_id_min_time[seller_id] = (1LL << 60);
          seller_id_max_time[seller_id] = 0;
          seller_to_info_id_map[seller_id] = pii64{author_id, item_id};
        }
        seller_id_min_time[seller_id] = std::min(seller_id_min_time[seller_id], timestamp);
        if (timestamp > seller_id_max_time[seller_id]) {
          seller_id_max_time[seller_id] = timestamp;
          seller_to_info_id_map[seller_id] = {author_id, item_id};
        }
      }

      // Deduplication
      if (spu_id > 0) {
        if (spu_id_min_time.find(spu_id) == spu_id_min_time.end()) {
          spu_id_min_time[spu_id] = (1LL << 60);
          spu_id_max_time[spu_id] = 0;
          spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
          recall_spu_id_list.push_back(spu_id);
        }
        spu_id_min_time[spu_id] = std::min(spu_id_min_time[spu_id], timestamp);
        if (timestamp > spu_id_max_time[spu_id]) {
          spu_id_max_time[spu_id] = timestamp;
          spu_to_info_id_map[spu_id] = pii64{author_id, item_id};
        }
      }

      if (brand_id > 0 && cate1_id > 0) {
        recall_brand_id_list.push_back(brand_id);
        if (brand_cate1_id_min_time.find(brand_cate1_id) == brand_cate1_id_min_time.end()) {
          brand_cate1_id_min_time[brand_cate1_id] = (1LL << 60);
          brand_cate1_id_max_time[brand_cate1_id] = 0;
          brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
          recall_brand_cate1_id_list.push_back(brand_cate1_id);
        }
        brand_cate1_id_min_time[brand_cate1_id] =
            std::min(brand_cate1_id_min_time[brand_cate1_id], timestamp);
        if (timestamp > brand_cate1_id_max_time[brand_cate1_id]) {
          brand_cate1_id_max_time[brand_cate1_id] = timestamp;
          brand_to_info_id_map[brand_cate1_id] = pii64{author_id, item_id};
        }
      }
    }

    for (const auto seller_id : recall_seller_id_list) {
      auto min_time = seller_id_min_time[seller_id];
      auto max_time = seller_id_max_time[seller_id];
      auto author_id = seller_to_info_id_map[seller_id].first;
      auto item_id = seller_to_info_id_map[seller_id].second;
      recall_seller_id_min_time.push_back(min_time);
      recall_seller_id_max_time.push_back(max_time);
      recall_author_id_list.push_back(author_id);
      recall_author_item_id_list.push_back(item_id);
    }

    // Loop over recall_spu_id_list and insert data into vectors
    for (const auto &spu : recall_spu_id_list) {
      auto min_time = spu_id_min_time[spu];
      auto max_time = spu_id_max_time[spu];
      auto author_id = spu_to_info_id_map[spu].first;
      auto item_id = spu_to_info_id_map[spu].second;
      recall_spu_id_min_time.push_back(min_time);
      recall_spu_id_max_time.push_back(max_time);
      recall_spu_author_id_list.push_back(author_id);
      recall_spu_author_item_id_list.push_back(item_id);
      recall_spu_redis_key_list.push_back(
        std::string(spu_redis_key) + std::string("_") + std::to_string(spu));
    }

    // Loop over recall_brand_cate1_id_list and insert data into vectors
    for (const auto &brand_cate1_id : recall_brand_cate1_id_list) {
      auto min_time = brand_cate1_id_min_time[brand_cate1_id];
      auto max_time = brand_cate1_id_max_time[brand_cate1_id];
      auto author_id = brand_to_info_id_map[brand_cate1_id].first;
      auto item_id = brand_to_info_id_map[brand_cate1_id].second;
      recall_brand_cate1_id_min_time.push_back(min_time);
      recall_brand_cate1_id_max_time.push_back(max_time);
      recall_brand_cate1_author_id_list.push_back(author_id);
      recall_brand_cate1_author_item_id_list.push_back(item_id);
      recall_brand_cate1_redis_key_list.push_back(
       std::string(brand_cate1_redis_key) + std::string("_") + std::to_string(brand_cate1_id));
    }

    context.SetIntCommonAttr(attr_prefix + "item_id_list_len", recall_item_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "seller_id_list_len", recall_seller_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "spu_id_list_len", recall_spu_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "brand_cate1_id_list_len", recall_brand_cate1_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "brand_id_list_len", recall_brand_id_list.size());
    context.SetIntListCommonAttr(attr_prefix + "item_id_list", std::move(recall_item_id_list));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_list", std::move(recall_seller_id_list));
    context.SetIntListCommonAttr(attr_prefix + "spu_id_list", std::move(recall_spu_id_list));
    context.SetIntListCommonAttr(attr_prefix + "brand_cate1_id_list", std::move(recall_brand_cate1_id_list));
    context.SetIntListCommonAttr(attr_prefix + "brand_id_list", std::move(recall_brand_id_list));

    context.SetIntListCommonAttr(attr_prefix + "seller_id_min_time", std::move(recall_seller_id_min_time));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_max_time", std::move(recall_seller_id_max_time));
    context.SetIntListCommonAttr(attr_prefix + "author_id_list", std::move(recall_author_id_list));
    context.SetIntListCommonAttr(attr_prefix + "author_item_id_list", std::move(recall_author_item_id_list));
    context.SetIntListCommonAttr(attr_prefix + "spu_id_min_time", std::move(recall_spu_id_min_time));
    context.SetIntListCommonAttr(attr_prefix + "spu_id_max_time", std::move(recall_spu_id_max_time));
    context.SetIntListCommonAttr(attr_prefix + "spu_author_id_list", std::move(recall_spu_author_id_list));
    context.SetIntListCommonAttr(attr_prefix + "spu_author_item_id_list",
                                 std::move(recall_spu_author_item_id_list));
    context.SetIntListCommonAttr(attr_prefix + "brand_cate1_id_min_time",
                                 std::move(recall_brand_cate1_id_min_time));
    context.SetIntListCommonAttr(attr_prefix + "brand_cate1_id_max_time",
                                 std::move(recall_brand_cate1_id_max_time));
    context.SetIntListCommonAttr(attr_prefix + "brand_cate1_author_id_list",
                                 std::move(recall_brand_cate1_author_id_list));
    context.SetIntListCommonAttr(attr_prefix + "brand_cate1_author_item_id_list",
                                 std::move(recall_brand_cate1_author_item_id_list));
    context.SetStringListCommonAttr(attr_prefix + "spu_redis_key_list",
                                 std::move(recall_spu_redis_key_list));
    context.SetStringListCommonAttr(attr_prefix + "brand_cate1_redis_key_list",
                                 std::move(recall_brand_cate1_redis_key_list));
    context.SetIntCommonAttr(attr_prefix + "target_seller_replace_cnt", video_seller_replace_cnt);

    return true;
  }

  static bool LuaOptSwtnV2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                      RecoResultConstIter end) {
    static std::vector<int64> default_list;
    // input  list
    auto item_id_list = context.GetIntListCommonAttr("item_id_list").value_or(default_list);
    auto seller_id_list = context.GetIntListCommonAttr("seller_id_list").value_or(default_list);
    auto real_seller_id_list = context.GetIntListCommonAttr("author_id_list").value_or(default_list);
    auto timestamp_list = context.GetIntListCommonAttr("timestamp_list").value_or(default_list);

    // scene name for attr
    auto scene_name = context.GetStringCommonAttr("scene_name").value_or("");
    auto attr_prefix = std::string("recall_") + std::string(scene_name) + std::string("_");

    std::vector<int64>
        recall_item_id_list, recall_seller_id_list,
        recall_seller_id_min_time, recall_seller_id_max_time,
        recall_author_id_list, recall_author_item_id_list;

    const int source_id_list_len = item_id_list.size();

    // reserve
    recall_item_id_list.reserve(source_id_list_len);
    recall_seller_id_list.reserve(source_id_list_len);
    recall_seller_id_min_time.reserve(source_id_list_len);
    recall_seller_id_max_time.reserve(source_id_list_len);
    recall_author_id_list.reserve(source_id_list_len);
    recall_author_item_id_list.reserve(source_id_list_len);

    folly::F14FastMap<int64, int64> seller_id_min_time, seller_id_max_time;
    using pii64 = std::pair<int64, int64>;
    folly::F14FastMap<int64, pii64> seller_to_info_id_map;

    for (int i = 0; i < source_id_list_len; i++) {
      int64 item_id = item_id_list[i];
      int64 seller_id = seller_id_list[i];
      int64 author_id = real_seller_id_list[i];
      int64 timestamp = timestamp_list[i];

      if (seller_id == author_id) {
        continue;
      }

      if (item_id > 0 && seller_id > 0) {
        recall_item_id_list.push_back(item_id);
        if (seller_id_min_time.find(seller_id) == seller_id_min_time.end()) {
          recall_seller_id_list.push_back(seller_id);
          seller_id_min_time[seller_id] = (1LL << 60);
          seller_id_max_time[seller_id] = 0;
          seller_to_info_id_map[seller_id] = pii64{author_id, item_id};
        }
        seller_id_min_time[seller_id] = std::min(seller_id_min_time[seller_id], timestamp);
        if (timestamp > seller_id_max_time[seller_id]) {
          seller_id_max_time[seller_id] = timestamp;
          seller_to_info_id_map[seller_id] = {author_id, item_id};
        }
      }
    }

    for (const auto seller_id : recall_seller_id_list) {
      auto min_time = seller_id_min_time[seller_id];
      auto max_time = seller_id_max_time[seller_id];
      auto author_id = seller_to_info_id_map[seller_id].first;
      auto item_id = seller_to_info_id_map[seller_id].second;
      recall_seller_id_min_time.push_back(min_time);
      recall_seller_id_max_time.push_back(max_time);
      recall_author_id_list.push_back(author_id);
      recall_author_item_id_list.push_back(item_id);
    }

    context.SetIntCommonAttr(attr_prefix + "item_id_list_len", recall_item_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "seller_id_list_len", recall_seller_id_list.size());

    context.SetIntListCommonAttr(attr_prefix + "item_id_list", std::move(recall_item_id_list));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_list", std::move(recall_seller_id_list));
    context.SetIntListCommonAttr(attr_prefix + "author_id_list", std::move(recall_author_id_list));
    context.SetIntListCommonAttr(attr_prefix + "author_item_id_list", std::move(recall_author_item_id_list));

    context.SetIntListCommonAttr(attr_prefix + "seller_id_min_time", std::move(recall_seller_id_min_time));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_max_time", std::move(recall_seller_id_max_time));

    return true;
  }

  static bool LuaOptSwtnV3(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                      RecoResultConstIter end) {
    static std::vector<int64> default_list;
    // input  list
    auto item_id_list = context.GetIntListCommonAttr("item_id_list").value_or(default_list);
    auto seller_id_list = context.GetIntListCommonAttr("seller_id_list").value_or(default_list);
    auto real_seller_id_list = context.GetIntListCommonAttr("author_id_list").value_or(default_list);
    auto timestamp_list = context.GetIntListCommonAttr("timestamp_list").value_or(default_list);

    auto living_seller_filter_set =
      context.GetIntListCommonAttr("living_seller_filter_set").value_or(default_list);
    folly::F14FastMap<int64, int64> in_seller_map;
    for (int i = 0; i < living_seller_filter_set.size(); i++) {
      in_seller_map[living_seller_filter_set[i]] = 1;
    }

    // scene name for attr
    auto scene_name = context.GetStringCommonAttr("scene_name").value_or("");
    auto attr_prefix = std::string("recall_") + std::string(scene_name) + std::string("_");

    std::vector<int64>
        recall_item_id_list, recall_seller_id_list,
        recall_seller_id_min_time, recall_seller_id_max_time,
        recall_author_id_list, recall_author_item_id_list;

    const int source_id_list_len = item_id_list.size();

    // reserve
    recall_item_id_list.reserve(source_id_list_len);
    recall_seller_id_list.reserve(source_id_list_len);
    recall_seller_id_min_time.reserve(source_id_list_len);
    recall_seller_id_max_time.reserve(source_id_list_len);
    recall_author_id_list.reserve(source_id_list_len);
    recall_author_item_id_list.reserve(source_id_list_len);

    folly::F14FastMap<int64, int64> seller_id_min_time, seller_id_max_time;
    using pii64 = std::pair<int64, int64>;
    folly::F14FastMap<int64, pii64> seller_to_info_id_map;

    for (int i = 0; i < source_id_list_len; i++) {
      int64 item_id = item_id_list[i];
      int64 seller_id = seller_id_list[i];
      int64 author_id = real_seller_id_list[i];
      int64 timestamp = timestamp_list[i];

      if (seller_id == author_id) {
        continue;
      }

      if (in_seller_map.find(seller_id) == in_seller_map.end()) {
        continue;
      }

      if (item_id > 0 && seller_id > 0) {
        recall_item_id_list.push_back(item_id);
        if (seller_id_min_time.find(seller_id) == seller_id_min_time.end()) {
          recall_seller_id_list.push_back(seller_id);
          seller_id_min_time[seller_id] = (1LL << 60);
          seller_id_max_time[seller_id] = 0;
          seller_to_info_id_map[seller_id] = pii64{author_id, item_id};
        }
        seller_id_min_time[seller_id] = std::min(seller_id_min_time[seller_id], timestamp);
        if (timestamp > seller_id_max_time[seller_id]) {
          seller_id_max_time[seller_id] = timestamp;
          seller_to_info_id_map[seller_id] = {author_id, item_id};
        }
      }
    }

    for (const auto seller_id : recall_seller_id_list) {
      auto min_time = seller_id_min_time[seller_id];
      auto max_time = seller_id_max_time[seller_id];
      auto author_id = seller_to_info_id_map[seller_id].first;
      auto item_id = seller_to_info_id_map[seller_id].second;
      recall_seller_id_min_time.push_back(min_time);
      recall_seller_id_max_time.push_back(max_time);
      recall_author_id_list.push_back(author_id);
      recall_author_item_id_list.push_back(item_id);
    }

    context.SetIntCommonAttr(attr_prefix + "item_id_list_len", recall_item_id_list.size());
    context.SetIntCommonAttr(attr_prefix + "seller_id_list_len", recall_seller_id_list.size());

    context.SetIntListCommonAttr(attr_prefix + "item_id_list", std::move(recall_item_id_list));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_list", std::move(recall_seller_id_list));
    context.SetIntListCommonAttr(attr_prefix + "author_id_list", std::move(recall_author_id_list));
    context.SetIntListCommonAttr(attr_prefix + "author_item_id_list", std::move(recall_author_item_id_list));

    context.SetIntListCommonAttr(attr_prefix + "seller_id_min_time", std::move(recall_seller_id_min_time));
    context.SetIntListCommonAttr(attr_prefix + "seller_id_max_time", std::move(recall_seller_id_max_time));

    return true;
  }

  static bool GetSellerQuality(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto seller_quality_ptr =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, int32>>("seller_quality_ptr");

    if (nullptr == seller_quality_ptr) {
      LOG(INFO) << "seller_quality_ptr is a nullptr";
      return true;
    }

    auto &resMap = *seller_quality_ptr;
    auto set_item_func = context.SetStringItemAttr("seller_quality");
    auto get_attr_func = context.GetStringItemAttr("seller_id_key");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      auto seller_id_key = get_attr_func(result).value_or("");
      int32 res = 0;
      auto it = resMap.find(std::string(seller_id_key));
      if (it != resMap.end()) {
        res = it->second;
        set_item_func(result, std::to_string(res));
      } else {
        set_item_func(result, "");
      }
    });

    return true;
  }

  static bool GetExtraMerchantPictureSet(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<int64> default_list;
    std::vector<int64> extra_picture_pid_list;
    auto picture_pid_set_ptr =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("picture_pid_set_ptr");
    auto extra_pid_list =
        context.GetIntListCommonAttr("extra_pid_list").value_or(default_list);
    int64 set_size = picture_pid_set_ptr->size();

    // output common attr
    if (picture_pid_set_ptr == nullptr || picture_pid_set_ptr->size() == 0) {
      LOG(ERROR) << "picture_pid_set_ptr is a nullptr or memory data core dump";
    } else {
      for (auto each_dedup : extra_pid_list) {
        if (picture_pid_set_ptr->count(each_dedup) > 0) {
          extra_picture_pid_list.push_back(each_dedup);
        }
      }
    }
    context.SetIntListCommonAttr("extra_picture_pid_list", std::move(extra_picture_pid_list));
    context.SetIntCommonAttr("picture_set_size", set_size);
    return true;
  }

  static bool GetLiveidAndQueryIdxFromAid(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<absl::string_view> default_list;
    std::vector<int64> liveid_list, query_ind_list;
    int64 is_service_ok = 1;  // 服务兜底
    auto aid_to_liveid_ptr =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, uint64>>("aid_to_liveid_ptr");
    auto prefix_dedup_all_merge_seller_list =
        context.GetStringListCommonAttr("prefix_dedup_all_merge_seller_list").value_or(default_list);

    // output common attr
    if (aid_to_liveid_ptr == nullptr || aid_to_liveid_ptr->size() == 0) {
      LOG_EVERY_N(ERROR, 20000) << "aid_to_liveid_ptr is a nullptr or size == 0";
      is_service_ok = 0;
      // return true;
    } else {
      auto &redisMap = *aid_to_liveid_ptr;
      uint64 i = 0;
      for (auto each_dedup : prefix_dedup_all_merge_seller_list) {
        auto it = redisMap.find(std::string(each_dedup));
        if (it != redisMap.end()) {
          liveid_list.push_back(it->second);
          query_ind_list.push_back(i);
        }
        i++;
      }
    }

    context.SetIntListCommonAttr("liveid_list", std::move(liveid_list));
    context.SetIntListCommonAttr("query_ind_list", std::move(query_ind_list));
    context.SetIntCommonAttr("is_service_ok", is_service_ok);
    return true;
  }

  static bool GetLiveidAndQueryIdxFromAidNoPrefixHasDedup(const CommonRecoLightFunctionContext &context,
                                                          RecoResultConstIter begin,
                                                          RecoResultConstIter end) {
    std::vector<int64> default_int_list;
    absl::Span<const int64> aid_list;
    std::vector<int64> liveid_list;
    int8 is_service_ok = 1;  // 服务兜底
    auto aid_to_liveid_ptr = context.GetPtrCommonAttr<folly::F14FastMap<uint64, uint64>>("aid_to_liveid_ptr");
    auto aid_list_optional = context.GetIntListCommonAttr("aid_list");
    if (likely(aid_list_optional != absl::nullopt)) {
      aid_list = aid_list_optional.value();
    } else {
      auto sv_aid_list_optional = context.GetStringListCommonAttr("aid_list");
      // sv_list 转换为 int_list
      if (sv_aid_list_optional != absl::nullopt) {
        int64 int_val;
        for (auto sv : sv_aid_list_optional.value()) {
          if (absl::SimpleAtoi(sv, &int_val)) {
            default_int_list.emplace_back(int_val);
          }
        }
        aid_list = default_int_list;
      }
    }
    folly::F14FastSet<uint64> seen;  // live id 去重
    if (unlikely(aid_to_liveid_ptr == nullptr)) {
      LOG_EVERY_N(ERROR, 20000) << "aid_to_liveid_ptr is a nullptr No Prefix";
      is_service_ok = 0;
    } else if (unlikely(aid_to_liveid_ptr->size() == 0)) {
      LOG_EVERY_N(ERROR, 20000) << "aid_to_liveid_ptr size == 0 No Prefix";
      is_service_ok = 0;
    } else {
      auto &redisMap = *aid_to_liveid_ptr;
      for (auto aid : aid_list) {
        if (aid > 0) {
          auto it = redisMap.find(aid);
          if (it != redisMap.end() && seen.find(it->second) == seen.end()) {
            liveid_list.push_back(it->second);
            seen.emplace(it->second);
          }
        }
      }
    }

    context.SetIntListCommonAttr("liveid_list", std::move(liveid_list));
    context.SetIntCommonAttr("is_service_ok", is_service_ok);
    return true;
  }

  static bool GenDiversityCateLeafFilterTagV2(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    // gen_diversity_cate_leaf_filter_tag2
    static std::vector<int64> default_list;
    auto before_real_need_filter_cate_leaf =
        context.GetIntListCommonAttr("before_real_need_filter_cate_leaf").value_or(default_list);
    auto category2_real_diversity_whitelist =
        context.GetIntListCommonAttr("category2_real_diversity_whitelist").value_or(default_list);

    auto get_scart_item_category_leafid_list_attr_func =
        context.GetIntListItemAttr("sCartItemCategoryLeafIdList");
    auto get_scart_item_cate2_id_list_attr_func = context.GetIntListItemAttr("sCartItemCate2IdList");
    auto get_seshopbigv_attr_func = context.GetIntItemAttr("sEshopIsBigV");

    auto set_item_func = context.SetIntItemAttr("diversity_real_cate_leaf_filter_tag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      uint64 diversity_real_cate_leaf_filter_tag = 0;
      auto cart_item_category_leafid_list =
          get_scart_item_category_leafid_list_attr_func(result).value_or(default_list);
      auto cart_item_cate2_id_list = get_scart_item_cate2_id_list_attr_func(result).value_or(default_list);
      auto seshopbigv = get_seshopbigv_attr_func(result).value_or(0);

      if (cart_item_category_leafid_list.size() == 0 || seshopbigv == 1) {
        set_item_func(result, diversity_real_cate_leaf_filter_tag);
        return;
      }

      if (cart_item_category_leafid_list.size() > 0) {
        auto first_cart_item_category_leafid = cart_item_category_leafid_list[0];
        for (const auto &v : before_real_need_filter_cate_leaf) {
          if (v == first_cart_item_category_leafid) {
            diversity_real_cate_leaf_filter_tag = 1;
            break;
          }
        }
      }

      if (cart_item_cate2_id_list.size() > 0) {
        auto first_cart_item_cate2_id = cart_item_cate2_id_list[0];
        for (const auto &v : category2_real_diversity_whitelist) {
          if (v == first_cart_item_cate2_id) {
            diversity_real_cate_leaf_filter_tag = 0;
            break;
          }
        }
      }

      set_item_func(result, diversity_real_cate_leaf_filter_tag);
    });

    return true;
  }

  static bool GetDancingAuthorBrowsetFlag(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    // get_dancing_author_browset_flag
    static std::vector<int64> default_list;
    static std::vector<std::string> default_string_list;
    auto disco_dancing_author_list =
        context.GetIntListCommonAttr("disco_dancing_author_list").value_or(default_list);
    auto browse_set_with_timestamp =
        context.GetPtrCommonAttr<std::vector<std::string>>("browse_set_with_timestamp");
    auto browse_set_time_interval_for_all =
        context.GetIntCommonAttr("browse_set_time_interval_for_all").value_or(600);
    auto browse_set_time_interval_for_single =
        context.GetIntCommonAttr("browse_set_time_interval_for_single").value_or(10800);
    auto watch_live_aid_list = context.GetIntListCommonAttr("watch_live_aid_list").value_or(default_list);
    auto watch_live_timestamp_list =
        context.GetIntListCommonAttr("watch_live_timestamp_list").value_or(default_list);
    auto watch_live_play_time_list =
        context.GetIntListCommonAttr("watch_live_play_time_list").value_or(default_list);
    auto browse_set_immunity_interval =
        context.GetIntCommonAttr("browse_set_immunity_interval").value_or(86400);

    auto get_aid_func = context.GetIntItemAttr("aId");
    auto set_dancing_browset_filter_flag_item_func = context.SetIntItemAttr("dancing_browset_filter_flag");
    auto set_dancing_aid_and_flag_item_func = context.SetStringItemAttr("dancing_aid_and_flag");

    int single_dancing_live_immunity_threshold = 5;
    int total_dancing_live_immunity_threshold = 10;

    auto current_timestamp = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1);
    int is_all_immunity = 0;

    folly::F14FastMap<int64, int> disco_dancing_author_map;
    folly::F14FastMap<int64, int> dancing_immunity_list;
    folly::F14FastMap<int64, int> dancing_filter_list;
    for (const auto &aid : disco_dancing_author_list) {
      disco_dancing_author_map.insert({aid, 1});
    }

    for (size_t i = 0; i < watch_live_timestamp_list.size(); ++i) {
      auto watch_live_timestamp = watch_live_timestamp_list[i];
      if (current_timestamp - watch_live_timestamp < browse_set_immunity_interval * 1000) {
        if (i < watch_live_play_time_list.size() && i < watch_live_aid_list.size() &&
            disco_dancing_author_map[watch_live_aid_list[i]]) {
          if (watch_live_play_time_list[i] > total_dancing_live_immunity_threshold * 1000) {
            is_all_immunity = 1;
          }

          if (watch_live_play_time_list[i] > single_dancing_live_immunity_threshold * 1000) {
            dancing_immunity_list[watch_live_aid_list[i]] = 1;
          }
        }
      }
    }

    if (is_all_immunity == 0 && browse_set_with_timestamp != nullptr) {
      for (auto it = browse_set_with_timestamp->begin(); it != browse_set_with_timestamp->end(); ++it) {
        auto realshow = *it;
        std::vector<absl::string_view> realshow_list = absl::StrSplit(realshow, ":");
        int64 realshow_time = 0;
        int64 realshow_aid = 0;
        if (realshow_list.size() >= 2 && (!absl::SimpleAtoi(realshow_list[0], &realshow_time) &&
                                          !absl::SimpleAtoi(realshow_list[1], &realshow_aid))) {
          if (disco_dancing_author_map.find(realshow_aid) != disco_dancing_author_map.end() &&
              dancing_immunity_list.find(realshow_aid) != dancing_immunity_list.end()) {
            auto gap = current_timestamp - realshow_time;
            if (gap < browse_set_time_interval_for_all * 1000) {
              dancing_filter_list = disco_dancing_author_map;
              break;
            }

            if (gap < browse_set_time_interval_for_single * 1000) {
              dancing_filter_list[realshow_aid] = 1;
            }
          }
        }
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      uint64 dancing_browset_filter_flag = 0;
      auto aId = get_aid_func(result).value_or(-1);

      if (aId > 0 && dancing_filter_list.count(aId) > 0) {
        dancing_browset_filter_flag = 1;
      } else if (disco_dancing_author_map.count(aId) > 0) {
        dancing_browset_filter_flag = 2;
      }
      std::string dancing_aid_and_flag =
          std::to_string(aId) + "_" + std::to_string(dancing_browset_filter_flag);
      set_dancing_browset_filter_flag_item_func(result, dancing_browset_filter_flag);
      set_dancing_aid_and_flag_item_func(result, dancing_aid_and_flag);
    });
    return true;
  }

  static bool GetUnFollowFilterFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    // un_follow_filter_list
    static std::vector<int64> default_list;
    auto un_follow_filter_string = context.GetStringCommonAttr("un_follow_filter_string").value_or("");
    std::vector<absl::string_view> un_follow_filter_aid_list = absl::StrSplit(un_follow_filter_string, ",");

    auto get_aid_func = context.GetIntItemAttr("aId");
    auto set_item_func = context.SetIntItemAttr("un_follow_filter_flag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      uint64 un_follow_filter_flag = 0;
      auto aId = get_aid_func(result).value_or(-1);
      for (const auto &v : un_follow_filter_aid_list) {
        std::vector<absl::string_view> aid_gap = absl::StrSplit(v, "_");
        if (aid_gap.size() > 0 && absl::SimpleAtoi(aid_gap[0], &aId)) {
          un_follow_filter_flag = 1;
          break;
        }
      }

      set_item_func(result, un_follow_filter_flag);
    });

    return true;
  }

  static bool GetUserSpecPackageFilterFlag(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    // user_spec_package_filter
    static std::vector<int64> default_list;
    auto user_spec_package_aids_list =
        context.GetIntListCommonAttr("user_spec_package_aids_list").value_or(default_list);

    auto get_audienceid_list_attr_func = context.GetIntListItemAttr("audienceId_list");
    auto set_item_func = context.SetIntItemAttr("user_spec_package_filter_flag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      uint64 user_spec_package_filter_flag = 3;
      auto audienceid_list = get_audienceid_list_attr_func(result);
      if (audienceid_list.has_value() && audienceid_list.value().size() > 0) {
        for (const auto &v : audienceid_list.value()) {
          std::vector<uint64> user_spec_package_aids_list_vec(user_spec_package_aids_list.begin(),
                                                              user_spec_package_aids_list.end());
          if (std::find(user_spec_package_aids_list_vec.begin(), user_spec_package_aids_list_vec.end(), v) !=
              user_spec_package_aids_list_vec.end()) {
            user_spec_package_filter_flag = 0;
          } else {
            user_spec_package_filter_flag = 2;
          }
        }
      }
      set_item_func(result, user_spec_package_filter_flag);
    });

    return true;
  }

  static bool GetAdDataAuthorScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    static std::vector<int64> default_list;
    auto ad_authors_list = context.GetIntListCommonAttr("ad_authors_list").value_or(default_list);
    auto ad_cpm_list = context.GetIntListCommonAttr("ad_cpm_list").value_or(default_list);
    auto ad_xtr_list = context.GetIntListCommonAttr("ad_xtr_list").value_or(default_list);
    auto ad_cpm_wo_bid_list = context.GetIntListCommonAttr("ad_cpm_wo_bid_list").value_or(default_list);

    auto get_aid_func = context.GetIntItemAttr("aId");

    auto set_ad_cpm_func = context.SetIntItemAttr("ad_cpm");
    auto set_ad_xtr_func = context.SetIntItemAttr("ad_xtr");
    auto set_ad_cpm_wo_bid_func = context.SetIntItemAttr("ad_cpm_wo_bid");

    auto is_contained = [](uint64 aid, std::vector<uint64> tab) {
      if (aid <= 0) {
        return -1;
      }
      for (int i = 0; i < tab.size(); ++i) {
        if (aid == tab[i]) {
          return i;
        }
      }
      return -1;
    };
    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      auto aid = get_aid_func(result).value_or(0);
      int ad_cpm = -1, ad_xtr = -1, ad_cpm_wo_bid = -1;
      std::vector<uint64> ad_authors_list_vec(ad_authors_list.begin(), ad_authors_list.end());
      auto idx = is_contained(aid, ad_authors_list_vec);
      if (idx != -1) {
        ad_cpm = (idx < ad_cpm_list.size()) ? ad_cpm_list[idx] : -1;
        ad_xtr = (idx < ad_xtr_list.size()) ? ad_xtr_list[idx] : -1;
        ad_cpm_wo_bid = (idx < ad_cpm_wo_bid_list.size()) ? ad_cpm_wo_bid_list[idx] : -1;
      }
      set_ad_cpm_func(result, ad_cpm);
      set_ad_xtr_func(result, ad_xtr);
      set_ad_cpm_wo_bid_func(result, ad_cpm_wo_bid);
    });

    return true;
  }

  static bool GenAudienceIdList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    // author_spec_package_filter_group_name
    static std::vector<int64> default_list;
    auto user_abtest_spec_package_group_name =
        context.GetStringCommonAttr("user_abtest_spec_package_group_name").value_or("");
    auto skip_abtest_spec_package_group_name =
        context.GetIntCommonAttr("skip_abtest_spec_package_group_name").value_or(1);

    auto get_audienceId_func = context.GetStringItemAttr("audienceId");

    auto set_audienceId_list_func = context.SetIntListItemAttr("audienceId_list");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      std::vector<int64> audienceId_list;
      auto audienceId_str = get_audienceId_func(result).value_or("");

      if (audienceId_str != "") {
        std::vector<absl::string_view> audienceId_sep_list = absl::StrSplit(audienceId_str, ",");
        if (audienceId_sep_list.size() > 0) {
          for (const auto &v : audienceId_sep_list) {
            uint64 audienceId = 0;
            if (skip_abtest_spec_package_group_name == 0) {
              std::vector<absl::string_view> audienceId_value = absl::StrSplit(v, ":");
              std::vector<absl::string_view> user_abtest_spec_package_group_name_vec =
                  absl::StrSplit(user_abtest_spec_package_group_name, ":");
              if (audienceId_value.size() >= 3 &&
                  std::find(user_abtest_spec_package_group_name_vec.begin(),
                            user_abtest_spec_package_group_name_vec.end(),
                            audienceId_value[2]) != user_abtest_spec_package_group_name_vec.end()) {
                if (!absl::SimpleAtoi(audienceId_value[1], &audienceId)) {
                  audienceId_list.push_back(audienceId);
                }
              }
            } else {
              if (!absl::SimpleAtoi(v, &audienceId)) {
                audienceId_list.push_back(audienceId);
              }
            }
          }
        }
      }
      set_audienceId_list_func(result, audienceId_list);
    });

    return true;
  }
  static bool GetUserAuthorRealshowFrequencyFilter(const CommonRecoLightFunctionContext &context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<absl::string_view> default_list;
    auto browse_set_with_timestamp_and_exptag =
        context.GetStringListCommonAttr("browse_set_with_timestamp_and_exptag")
            .value_or(default_list);  // 历史曝光集合，每个元素是一个字符串，格式为 "时间戳:aid:reason"
    auto all_1pp_amu_retrieve_enable_seller_frequency_threshold =
        context.GetIntCommonAttr("all_1pp_amu_retrieve_enable_seller_frequency_threshold").value_or(0);
    auto aId_func = context.GetIntItemAttr("aId");
    auto reason_func = context.GetIntItemAttr("reason");
    auto user_author_frequency_filter_flag_func = context.SetIntItemAttr("user_author_frequency_filter_flag");
    auto user_author_frequency_cnt_func = context.SetIntItemAttr("user_author_frequency_cnt");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 user_author_frequency_filter_flag = 0;      // 过滤标记，0 表示不过滤，1 表示过滤
      int64 user_author_frequency_cnt = 0;              // 曝光计数
      int64 aId = aId_func(result).value_or(-1);        // 当前的 aid, 如果没有则为 -1
      int64 reason = reason_func(result).value_or(-1);  // 当前的 reason, 如果没有则为 -1

      // 遍历历史曝光集合
      for (int64 i = 0; i < browse_set_with_timestamp_and_exptag.size(); i++) {
        // 获取当前的字符串
        auto ts_aid = browse_set_with_timestamp_and_exptag[i];
        // 使用":"作为分隔符，将字符串分割成三个子字符串
        std::vector<absl::string_view> ts_aid_pair = absl::StrSplit(ts_aid, ":");
        // 如果分割后的结果有三个元素，则继续处理
        if (ts_aid_pair.size() == 3) {
          // 将第二个元素转换为整数，表示历史曝光的 aid
          int64 history_aid = 0;
          if (!absl::SimpleAtoi(ts_aid_pair[1], &history_aid)) {
            LOG(ERROR) << "illegal char in ts_aid_pair[1]";
          }
          // 将第三个元素转换为整数，表示历史曝光的 reason
          int64 history_reason = 0;
          if (!absl::SimpleAtoi(ts_aid_pair[2], &history_reason)) {
            LOG(ERROR) << "illegal char in ts_aid_pair[2]";
          }

          // 如果历史曝光的 aid 和当前的 aid 相同，且历史曝光的 reason 是 1242, 则增加曝光计数
          if ((history_aid == aId) && (history_reason == 1242)) {
            user_author_frequency_cnt++;
          }
        }
      }

      // 如果曝光计数超过了设定的阈值，并且当前的 reason 是 1242，则将过滤标记设为 1
      if ((user_author_frequency_cnt > all_1pp_amu_retrieve_enable_seller_frequency_threshold) &&
          (reason == 1242)) {
        user_author_frequency_filter_flag = 1;
      }

      // 返回过滤标记和曝光计数
      user_author_frequency_filter_flag_func(result, user_author_frequency_filter_flag);
      user_author_frequency_cnt_func(result, user_author_frequency_cnt);
    });

    return true;
  }

  static bool GetHighQualityUserFixGpm(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<absl::string_view> default_string_list;
    static std::vector<int64> default_int_list;
    auto browse_set_with_timestamp_and_exptag =
        context.GetStringListCommonAttr("browse_set_with_timestamp_and_exptag")
            .value_or(default_string_list);  // 历史曝光集合，每个元素是一个字符串，格式为"时间戳:aid:reason"
    auto request_time = context.GetIntCommonAttr("request_time").value_or(0);
    auto merchant_showcase_reason_list =
        context.GetIntListCommonAttr("merchant_showcase_reason_list").value_or(default_int_list);
    auto all_1pp_time_interval_browset_fix_gpm =
        context.GetIntCommonAttr("all_1pp_time_interval_browset_fix_gpm").value_or(86400);
    auto browse_fix_gpm_flag_func = context.SetIntItemAttr("browse_fix_gpm_flag");

    folly::F14FastMap<int64, int64> reason_map;  // 用于存储 reason 的映射
    int64 min_diff = 86400;                      // 最小的时间差（秒）

    // 遍历商家橱窗的 reason 列表，将其存入映射中
    for (int64 idx = 0; idx < merchant_showcase_reason_list.size(); idx++) {
      // 获取当前的 reason
      const int64 &reason = merchant_showcase_reason_list[idx];
      // 将 reason 作为键，1 作为值，存入映射中
      reason_map[reason] = 1;
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 browse_fix_gpm_flag = 0;  // 浏览曝光的固定 gpm 标记，0 表示不固定，1 表示固定
      // 遍历历史曝光集合
      for (int64 idx = 0; idx < browse_set_with_timestamp_and_exptag.size(); idx++) {
        // 获取当前的字符串
        auto browse_set_str = browse_set_with_timestamp_and_exptag[idx];
        // 使用":"作为分隔符，将字符串分割成三个子字符串
        std::vector<absl::string_view> browse_set_sub_list = absl::StrSplit(browse_set_str, ":");
        if (browse_set_sub_list.size() == 3) {
          int64 browse_set_sub_value2 = 0;
          if (absl::SimpleAtoi(browse_set_sub_list[2], &browse_set_sub_value2) &&
              reason_map[browse_set_sub_value2]) {
            int64 browse_set_sub_value0 = 0;
            if (absl::SimpleAtoi(browse_set_sub_list[0], &browse_set_sub_value0)) {
              int64 diff = (request_time - browse_set_sub_value0) / 1000;
              if (diff < min_diff) {
                min_diff = diff;
              }
            }
          }
        }
      }
      // 如果最小时间差大于固定 gpm 的时间间隔阈值，则将浏览曝光的固定 gpm 标记设为 1
      if (min_diff > all_1pp_time_interval_browset_fix_gpm) {
        browse_fix_gpm_flag = 1;
      }

      // 返回浏览曝光的固定 gpm 标记
      browse_fix_gpm_flag_func(result, browse_fix_gpm_flag);
    });
    return true;
  }

  static bool GetFollowBuyLongPlayDay(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> default_int_list{-1};
    auto follow_aid_list = context.GetIntListCommonAttr("follow_aid_list").value_or(default_int_list);
    auto follow_time_list = context.GetIntListCommonAttr("follow_time_list").value_or(default_int_list);
    auto REQ_TIME = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1);
    auto buyed_author_id_list =
        context.GetIntListCommonAttr("buyed_author_id_list").value_or(default_int_list);
    auto buy_pay_time_list = context.GetIntListCommonAttr("buy_pay_time_list").value_or(default_int_list);
    auto extract_author_id_list =
        context.GetIntListCommonAttr("extract_author_id_list").value_or(default_int_list);
    auto extract_live_play_time_list =
        context.GetIntListCommonAttr("extract_live_play_time_list").value_or(default_int_list);
    auto extract_server_timestamp_list =
        context.GetIntListCommonAttr("extract_server_timestamp_list").value_or(default_int_list);
    auto valid_watch_time_threshold =
        context.GetDoubleCommonAttr("valid_watch_time_threshold").value_or(60.0);
    auto aId_func = context.GetIntItemAttr("aId");
    auto follow_time_day_func = context.SetIntItemAttr("follow_time_day");
    auto buy_time_day_func = context.SetIntItemAttr("buy_time_day");
    auto long_play_day_func = context.SetIntItemAttr("long_play_day");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 aId = aId_func(result).value_or(-1);
      int64 follow_time_day = -1;          // 关注天数，如果没有则为 -1
      int64 buy_time_day = -1;             // 购买天数，如果没有则为 -1
      int64 long_play_day = -1;            // 长播天数，如果没有则为 -1
      int64 current_timestamp = REQ_TIME;  // 当前的时间戳（毫秒），如果没有则为 -1

      // 遍历用户关注的 aid 列表和时间戳列表，查找是否有当前的 aid，如果有，则计算关注天数
      // TODO(tangyang06) 这些是查询，vector 可以改为 map
      for (int64 i = 0; i < follow_aid_list.size(); i++) {
        if (aId == follow_aid_list[i] && i < follow_time_list.size()) {
          follow_time_day = std::ceil(1.0 * (current_timestamp - follow_time_list[i]) /
                                      (static_cast<int64>(3600 * 24 * 1000)));
        }
      }

      // 遍历用户购买的作者 id 列表和支付时间戳列表，查找是否有当前的 aid，如果有，则计算购买天数
      for (int64 i = 0; i < buyed_author_id_list.size(); i++) {
        if (aId == buyed_author_id_list[i] && i < buy_pay_time_list.size()) {
          buy_time_day = std::ceil(1.0 * (current_timestamp - buy_pay_time_list[i]) /
                                   (static_cast<int64>(3600 * 24 * 1000)));
          break;
        }
      }

      // 将观看时长的有效阈值转换为毫秒
      valid_watch_time_threshold = valid_watch_time_threshold * 1000;

      // 反向遍历用户观看的作者 id 列表、直播时长列表和服务器时间戳列表，查找是否有当前的
      // aid，并且观看时长大于有效阈值，如果有，则计算长播天数
      for (int64 i = extract_author_id_list.size() - 1; i >= 0; i--) {
        if (aId == extract_author_id_list[i] && i < extract_live_play_time_list.size() &&
            extract_live_play_time_list[i] > valid_watch_time_threshold &&
            i < extract_server_timestamp_list.size()) {
          long_play_day = std::ceil(1.0 * (current_timestamp - extract_server_timestamp_list[i]) /
                                    (static_cast<int64>(3600 * 24 * 1000)));
          break;
        }
      }

      // 返回关注天数、购买天数和长播天数
      follow_time_day_func(result, follow_time_day);
      buy_time_day_func(result, buy_time_day);
      long_play_day_func(result, long_play_day);
    });
    return true;
  }

  static bool ExtractRealtimeLiveGmvAndOrder(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto sAll5MinsAmt_func = context.GetStringItemAttr("sAll5MinsAmt");
    auto sAll5MinsOrderCount_func = context.GetStringItemAttr("sAll5MinsOrderCount");
    auto sAll30MinsAmt_func = context.GetStringItemAttr("sAll30MinsAmt");
    auto sAll30MinsOrderCount_func = context.GetStringItemAttr("sAll30MinsOrderCount");
    auto sAllstartOrderCount_func = context.GetStringItemAttr("sAllstartOrderCount");
    auto sAllstartAmt_func = context.GetStringItemAttr("sAllstartAmt");
    auto sAll1MinsAmt_func = context.GetStringItemAttr("sAll1MinsAmt");
    auto sAll1MinsOrderCount_func = context.GetStringItemAttr("sAll1MinsOrderCount");

    auto live_last_5m_gmv_func = context.SetDoubleItemAttr("live_last_5m_gmv");
    auto live_last_5m_order_func = context.SetDoubleItemAttr("live_last_5m_order");
    auto live_last_5m_price_func = context.SetDoubleItemAttr("live_last_5m_price");
    auto live_last_30m_gmv_func = context.SetDoubleItemAttr("live_last_30m_gmv");
    auto live_last_30m_order_func = context.SetDoubleItemAttr("live_last_30m_order");
    auto live_last_30m_price_func = context.SetDoubleItemAttr("live_last_30m_price");
    auto live_last_1m_gmv_func = context.SetDoubleItemAttr("live_last_1m_gmv");
    auto live_last_1m_order_func = context.SetDoubleItemAttr("live_last_1m_order");
    auto live_last_1m_price_func = context.SetDoubleItemAttr("live_last_1m_price");
    auto live_start_gmv_func = context.SetDoubleItemAttr("live_start_gmv");
    auto live_start_order_func = context.SetDoubleItemAttr("live_start_order");
    auto live_start_price_func = context.SetDoubleItemAttr("live_start_price");

    auto sum_list_value = [](const absl::string_view &attr) {
      std::vector<absl::string_view> value_list = absl::StrSplit(attr, ",");
      double result = 0.0;
      for (const auto &v : value_list) {
        std::vector<absl::string_view> pair = absl::StrSplit(v, "=");
        if (pair.size() == 2) {
          double value = 0.0;
          if (absl::SimpleAtod(pair[1], &value)) {
            result += value;
          }
        }
      }
      return result;
    };

    // calculate
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto sAll5MinsAmt = sAll5MinsAmt_func(result).value_or("");
      auto sAll5MinsOrderCount = sAll5MinsOrderCount_func(result).value_or("");
      auto sAll30MinsAmt = sAll30MinsAmt_func(result).value_or("");
      auto sAll30MinsOrderCount = sAll30MinsOrderCount_func(result).value_or("");
      auto sAllstartOrderCount = sAllstartOrderCount_func(result).value_or("");
      auto sAllstartAmt = sAllstartAmt_func(result).value_or("");
      auto sAll1MinsAmt = sAll1MinsAmt_func(result).value_or("");
      auto sAll1MinsOrderCount = sAll1MinsOrderCount_func(result).value_or("");

      double live_last_5m_gmv = sum_list_value(sAll5MinsAmt) / 100.0;
      double live_last_5m_order = sum_list_value(sAll5MinsOrderCount);
      double live_last_5m_price = 0.0;
      if (live_last_5m_order > 0) {
        live_last_5m_price = live_last_5m_gmv / live_last_5m_order;
      }

      double live_last_30m_gmv = sum_list_value(sAll30MinsAmt) / 100.0;
      double live_last_30m_order = sum_list_value(sAll30MinsOrderCount);
      double live_last_30m_price = 0.0;
      if (live_last_30m_order > 0) {
        live_last_30m_price = live_last_30m_gmv / live_last_30m_order;
      }

      double live_last_1m_gmv = sum_list_value(sAll1MinsAmt) / 100.0;
      double live_last_1m_order = sum_list_value(sAll1MinsOrderCount);
      double live_last_1m_price = 0.0;
      if (live_last_1m_order > 0) {
        live_last_1m_price = live_last_1m_gmv / live_last_1m_order;
      }

      double live_start_gmv = sum_list_value(sAllstartAmt) / 100.0;
      double live_start_order = sum_list_value(sAllstartOrderCount);
      double live_start_price = 0.0;
      if (live_start_order > 0) {
        live_start_price = live_start_gmv / live_start_order;
      }

      live_last_5m_gmv_func(result, live_last_5m_gmv);
      live_last_5m_order_func(result, live_last_5m_order);
      live_last_5m_price_func(result, live_last_5m_price);
      live_last_30m_gmv_func(result, live_last_30m_gmv);
      live_last_30m_order_func(result, live_last_30m_order);
      live_last_30m_price_func(result, live_last_30m_price);
      live_last_1m_gmv_func(result, live_last_1m_gmv);
      live_last_1m_order_func(result, live_last_1m_order);
      live_last_1m_price_func(result, live_last_1m_price);
      live_start_gmv_func(result, live_start_gmv);
      live_start_order_func(result, live_start_order);
      live_start_price_func(result, live_start_price);
    });

    return true;
  }

  static bool GetFrScoreSubInRecallFrMerchants(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<absl::string_view> default_string_list;
    auto aid_fr_score_list =
        context.GetStringListCommonAttr("aid_fr_score_list").value_or(default_string_list);
    auto all_1pp_support_fr_price_weight =
        context.GetDoubleCommonAttr("all_1pp_support_fr_price_weight").value_or(0.0);
    auto aId_func = context.GetIntItemAttr("aId");
    auto sc_live_ctr_func = context.SetDoubleItemAttr("sc_live_ctr");
    auto sc_wtr_func = context.SetDoubleItemAttr("sc_wtr");
    auto sc_cvr_func = context.SetDoubleItemAttr("sc_cvr");
    auto sc_wtime_func = context.SetDoubleItemAttr("sc_wtime");
    auto realtime_price_func = context.SetDoubleItemAttr("realtime_price");
    auto sc_pexp_gmv_func = context.SetDoubleItemAttr("sc_pexp_gmv");
    auto sc_ctr_cvr_sub_func = context.SetDoubleItemAttr("sc_ctr_cvr_sub");
    auto fr_score_sub_func = context.SetDoubleItemAttr("fr_score_sub");
    auto is_filter_flag_func = context.SetIntItemAttr("is_filter_flag");
    auto fr_subflow_gpm_score_func = context.SetDoubleItemAttr("fr_subflow_gpm_score");
    auto inner_bid_gpm_score_func = context.SetDoubleItemAttr("inner_bid_gpm_score");
    auto longterm_follow_ltv_scores_func = context.SetDoubleItemAttr("longterm_follow_ltv_scores");
    auto support_pgpm_func = context.SetDoubleItemAttr("support_pgpm");

    folly::F14FastMap<int64, std::vector<double>> aid_score_map;
    for (const auto &aid_fr_score : aid_fr_score_list) {
      std::vector<absl::string_view> lst = absl::StrSplit(aid_fr_score, ":");
      if (lst.size() >= 7) {
        bool unused = true;
        int64 aid = -1;
        double sc_live_ctr = 0.0, sc_wtr = 0.0, sc_cvr = 0.0, sc_wtime = 0.0, realtime_price = 0.0,
               sc_pexp_gmv = 0.0, longterm_follow_ltv_scores = 0.0;
        unused = absl::SimpleAtoi(lst[0], &aid);
        unused = absl::SimpleAtod(lst[1], &sc_live_ctr);
        unused = absl::SimpleAtod(lst[2], &sc_wtr);
        unused = absl::SimpleAtod(lst[3], &sc_cvr);
        unused = absl::SimpleAtod(lst[4], &sc_wtime);
        unused = absl::SimpleAtod(lst[5], &realtime_price);
        unused = absl::SimpleAtod(lst[6], &sc_pexp_gmv);
        if (lst.size() > 11) unused = absl::SimpleAtod(lst[11], &longterm_follow_ltv_scores);
        aid_score_map[aid] = {
            sc_live_ctr, sc_wtr, sc_cvr, sc_wtime, realtime_price, sc_pexp_gmv, longterm_follow_ltv_scores};
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aId = aId_func(result).value_or(-1);
      double sc_live_ctr = 0.0, sc_wtr = 0.0, sc_cvr = 0.0, sc_wtime = 0.0, realtime_price = 0.0,
             sc_pexp_gmv = 0.0;
      double longterm_follow_ltv_scores = 0.0;
      double gpm_score_sub = 0.0;

      double sc_ctr_cvr_sub = 0.0;
      double fr_score_sub = 0.0;
      int64 is_filter_flag = 1;
      double support_pgpm = 0.0;

      auto it = aid_score_map.find(aId);
      if (aId > 0 && it != aid_score_map.end()) {
        auto fr_score = it->second;
        sc_live_ctr = (fr_score.size() > 0) ? fr_score[0] : 0;
        sc_wtr = (fr_score.size() > 1) ? fr_score[1] : 0;
        sc_cvr = (fr_score.size() > 2) ? fr_score[2] : 0;
        sc_wtime = (fr_score.size() > 3) ? fr_score[3] : 0;
        realtime_price = (fr_score.size() > 4) ? fr_score[4] : 0;
        sc_pexp_gmv = (fr_score.size() > 5) ? fr_score[5] : 0;
        longterm_follow_ltv_scores = (fr_score.size() > 6) ? fr_score[6] : 0;
        // 计算各种指标
        sc_ctr_cvr_sub = (sc_live_ctr + 0.001) * (sc_cvr + 0.001) * 1000000;
        fr_score_sub = sc_live_ctr * sc_cvr * std::pow(realtime_price, all_1pp_support_fr_price_weight);
        is_filter_flag = 0;

        // 如果 fr_score_sub 迭代成为 ensembleSort，则 gpm_score_sub 不可直接使用 fr_score_sub
        gpm_score_sub = fr_score_sub * 10000;

        // 扶持 bonus 使用 gpm
        if (sc_pexp_gmv == 0 && realtime_price != 0) {
          support_pgpm = sc_live_ctr * sc_cvr * realtime_price;
        } else if (sc_pexp_gmv != 0) {
          support_pgpm = sc_live_ctr * sc_cvr * sc_pexp_gmv;
        }
      }

      sc_live_ctr_func(result, sc_live_ctr);
      sc_wtr_func(result, sc_wtr);
      sc_cvr_func(result, sc_cvr);
      sc_wtime_func(result, sc_wtime);
      realtime_price_func(result, realtime_price);
      sc_pexp_gmv_func(result, sc_pexp_gmv);
      sc_ctr_cvr_sub_func(result, sc_ctr_cvr_sub);
      fr_score_sub_func(result, fr_score_sub);
      is_filter_flag_func(result, is_filter_flag);
      fr_subflow_gpm_score_func(result, gpm_score_sub);
      inner_bid_gpm_score_func(result, gpm_score_sub);
      longterm_follow_ltv_scores_func(result, longterm_follow_ltv_scores);
      support_pgpm_func(result, support_pgpm);
    });

    return true;
  }

  static bool FilterByRiskPunishSignalOfGenerateFilterFlag(const CommonRecoLightFunctionContext &context,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) {
    std::vector<absl::string_view> default_string_list;
    std::vector<int64> default_int_list;

    auto risk_punish_ab_source_params_list =
        context.GetStringListCommonAttr("risk_punish_ab_source_params_list").value_or(default_string_list);
    auto risk_punish_degrade_random = context.GetIntCommonAttr("risk_punish_degrade_random").value_or(100);
    auto sPunishActionList_func = context.GetIntListItemAttr("sPunishActionList");
    auto sPunishReasonList_func = context.GetStringListItemAttr("sPunishReasonList");
    auto sPunishSourceList_func = context.GetStringListItemAttr("sPunishSourceList");
    auto aLiveRealshow1ppCountToday_func = context.GetIntItemAttr("aLiveRealshow1ppCountToday");

    auto risk_punish_is_filter_nonfans_func = context.SetIntItemAttr("risk_punish_is_filter_nonfans");
    auto risk_punish_is_filter_all_func = context.SetIntItemAttr("risk_punish_is_filter_all");
    auto risk_punish_is_filter_degrade_func = context.SetIntItemAttr("risk_punish_is_filter_degrade");
    auto risk_punish_is_filter_flowhat_func = context.SetIntItemAttr("risk_punish_is_filter_flowhat");

    folly::F14FastMap<std::string, int64> ab_param_map;
    for (const auto ab_param : risk_punish_ab_source_params_list) {
      ab_param_map[std::string(ab_param)] = 1;
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto action_list = sPunishActionList_func(result).value_or(default_int_list);
      auto source_list = sPunishSourceList_func(result).value_or(default_string_list);
      auto reason_list = sPunishReasonList_func(result).value_or(default_string_list);
      auto author_1pp_pv = aLiveRealshow1ppCountToday_func(result).value_or(0);
      auto &degrade_random = risk_punish_degrade_random;
      int64 is_filter_public = 0;
      int64 is_filter_all = 0;
      int64 is_filter_degrade = 0;
      int64 is_filter_flowhat = 0;
      if (action_list.size() != source_list.size() || reason_list.size() != source_list.size() ||
          action_list.size() != reason_list.size()) {  // 如果三个列表的长度不相等
        risk_punish_is_filter_nonfans_func(result, 0);
        risk_punish_is_filter_all_func(result, 0);
        risk_punish_is_filter_degrade_func(result, 0);
        risk_punish_is_filter_flowhat_func(result, 0);
      } else {                                            // 否则
        for (int64 i = 0; i < source_list.size(); i++) {  // 遍历来源列表
          auto source = source_list[i];                   // 获取当前的来源
          auto action = action_list[i];                   // 获取当前的动作，并转换为整数
          if (ab_param_map[std::string(source)] == 1) {  // 如果当前的来源在 ab 参数 map 中有对应的值为 1
            if (action == 110 || action == 113 || action == 114) {  // 如果当前的动作是 110 或 113 或 114
              is_filter_public = 1;                                 // 设置过滤公开场景为 1
            }
            if (action == 111) {  // 如果当前的动作是 111
              is_filter_all = 1;  // 设置过滤所有场景为 1
            }
            if (action >= 10000 && author_1pp_pv >= action) {  // 如果当前的动作大于等于 10000 且作者的 1pp
                                                               // 曝光量大于等于动作值
              is_filter_flowhat = 1;  // 设置过滤流量帽场景为 1
            }
            if (action > 0 && action < 100) {  // 如果当前的动作大于 0 且小于 100
              if (action >= degrade_random) {  // 如果当前的动作大于等于降级随机数
                is_filter_degrade = 1;         // 设置过滤降级场景为 1
              }
            }
          }
        }
        risk_punish_is_filter_nonfans_func(result, is_filter_public);
        risk_punish_is_filter_all_func(result, is_filter_all);
        risk_punish_is_filter_degrade_func(result, is_filter_degrade);
        risk_punish_is_filter_flowhat_func(result, is_filter_flowhat);
      }
    });
    return true;
  }

  static bool WatchtimeRetargetingSetValue(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::string> default_string_list;
    std::vector<int64> default_int_list;
    std::vector<double> default_double_list;
    auto watchtime_retargeting_aid_list =
        context.GetIntListCommonAttr("watchtime_retargeting_aid_list").value_or(default_int_list);
    auto watchtime_retargeting_wtime_list =
        context.GetDoubleListCommonAttr("watchtime_retargeting_wtime_list").value_or(default_double_list);
    auto watchtime_retargeting_gmv_list =
        context.GetDoubleListCommonAttr("watchtime_retargeting_gmv_list").value_or(default_double_list);
    auto watchtime_retargeting_strategy =
        context.GetStringCommonAttr("watchtime_retargeting_strategy").value_or("");
    auto watchtime_retargeting_strategy_wtime =
        context.GetDoubleCommonAttr("watchtime_retargeting_strategy_wtime").value_or(0.0);
    auto watchtime_retargeting_strategy_gmv =
        context.GetDoubleCommonAttr("watchtime_retargeting_strategy_gmv").value_or(0.0);
    auto watchtime_retargeting_strategy_gmv_bias =
        context.GetDoubleCommonAttr("watchtime_retargeting_strategy_gmv_bias").value_or(0.0);
    auto aId_func = context.GetIntItemAttr("aId");
    auto exp_watch_time_func = context.SetDoubleItemAttr("exp_watch_time");

    folly::F14FastMap<int64, int64> watchtime_retargeting_aid_list_map;
    for (int64 i = 0; i < watchtime_retargeting_aid_list.size(); ++i) {
      watchtime_retargeting_aid_list_map[watchtime_retargeting_aid_list[i]] = i;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aId = aId_func(result).value_or(0);  // 用于存储当前的作者 id
      auto it = watchtime_retargeting_aid_list_map.find(aId);
      if (it != watchtime_retargeting_aid_list_map.end()) {
        auto idx = it->second;
        if (watchtime_retargeting_strategy == "wtime") {  // 如果观看时间重定向策略是 wtime
          if (idx < watchtime_retargeting_wtime_list.size()) {
            exp_watch_time_func(result, watchtime_retargeting_wtime_list[idx]);  // 返回对应的观看时间值
          } else {
            LOG(ERROR) << "array index out of range";
            exp_watch_time_func(result, 0.0);
          }
        } else if (watchtime_retargeting_strategy == "wtime_gmv") {  // 如果观看时间重定向策略是 wtime_gmv
          if (idx < watchtime_retargeting_wtime_list.size() && idx < watchtime_retargeting_gmv_list.size()) {
            auto set_value =
                pow(watchtime_retargeting_wtime_list[idx], watchtime_retargeting_strategy_wtime) *
                pow(watchtime_retargeting_gmv_list[idx] + watchtime_retargeting_strategy_gmv_bias,
                    watchtime_retargeting_strategy_gmv);  // 返回对应的计算结果，使用 pow 函数来计算幂运算
            exp_watch_time_func(result, set_value);  // 返回对应的观看时间值
          } else {
            LOG(ERROR) << "array index out of range";
            exp_watch_time_func(result, 0.0);
          }
        }
      } else {
        exp_watch_time_func(result, 0.0);
      }
    });
    return true;
  }

  static bool BigVAidForceInsert(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    std::vector<int64> default_bigv_aid_list;
    // getter
    auto follow_bigv_force_insert_aid_list =
        context.GetIntListCommonAttr("follow_bigv_force_insert_aid_list").value_or(default_bigv_aid_list);
    auto senior_bigv_follow_flag_func = context.GetIntItemAttr("senior_bigv_follow_flag");
    auto aid_func = context.GetIntItemAttr("aId");
    // setter
    auto follow_bigv_force_insert_flag_set = context.SetIntItemAttr("follow_bigv_force_insert_flag");

    std::set<int64> bigv_force_aid_set(follow_bigv_force_insert_aid_list.begin(),
                                       follow_bigv_force_insert_aid_list.end());

    for (auto r_it = begin; r_it != end; ++r_it) {
      const CommonRecoResult &result = *r_it;
      auto aId = aid_func(result).value_or(-1);
      auto senior_bigv_follow_flag = senior_bigv_follow_flag_func(result).value_or(0);
      if (1 == senior_bigv_follow_flag && bigv_force_aid_set.count(aId) > 0) {
        int follow_bigv_force_insert_flag = 0;
        follow_bigv_force_insert_flag_set(result, follow_bigv_force_insert_flag);
      }
    }

    return true;
  }

  static bool GenTelecomTag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    std::vector<int64> default_int_list;
    // getter
    auto uid_telecom_his = context.GetIntCommonAttr("uid_telecom_his").value_or(0);
    auto aid_telecom_white_list =
        context.GetIntListCommonAttr("aid_telecom_white").value_or(default_int_list);
    auto telecom_item_cate2_list =
        context.GetIntListCommonAttr("telecom_item_cate2").value_or(default_int_list);

    auto aid_func = context.GetIntItemAttr("aId");
    auto sCartItemCate2IdList_func = context.GetIntListItemAttr("sCartItemCate2IdList");
    // setter
    auto filter_telecom_tag_set = context.SetIntItemAttr("filter_telecom_tag");
    // item
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto sCartItemCate2IdList = sCartItemCate2IdList_func(result).value_or(default_int_list);
      int uid_realshow_his = 0;
      for (auto tele : telecom_item_cate2_list) {
        if (tele == 1853) uid_realshow_his = 1;
      }

      if (uid_telecom_his == 1 || uid_realshow_his == 1) {
        for (int i = 0; i < sCartItemCate2IdList.size(); ++i) {
          if (i > 0) break;
          if (sCartItemCate2IdList[i] == 1853) {
            filter_telecom_tag_set(result, 1);
            return;
          }
        }
      }

      int filter_telecom_tag = 0;
      for (int i = 0; i < sCartItemCate2IdList.size(); ++i) {
        if (i > 0) break;
        if (sCartItemCate2IdList[i] == 1853) {
          int64 aId = aid_func(result).value_or(-1);
          bool exist = false;
          for (auto tele : aid_telecom_white_list) {
            if (tele == aId) {
              exist = true;
              break;
            }
          }
          if (!exist) {
            filter_telecom_tag = 1;
          }
        }
      }
      // default
      filter_telecom_tag_set(result, filter_telecom_tag);
    });

    return true;
  }

  static bool MidRankGenAdsMultiAttrs(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    // getter
    auto ads_attr_redis_value_func = context.GetStringItemAttr("ads_attr_redis_value");
    // setter
    auto total_cost_set = context.SetDoubleItemAttr("total_cost");
    auto ad_mobj_weight_set = context.SetDoubleItemAttr("toB_ads_bonus");
    auto is_ad_set = context.SetIntItemAttr("is_toB_ads_attr_item");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto ads_attr_redis_value = ads_attr_redis_value_func(result).value_or("");
      std::string ads_attr_redis_value_str(ads_attr_redis_value.data(), ads_attr_redis_value.size());
      std::vector<std::string> ads_attr_vec;
      base::SplitStringWithOptions(ads_attr_redis_value_str, ",", true, true, &ads_attr_vec);

      double total_cost = 0.0;
      double ad_mobj_weight = 0.0;
      int is_ad = 0;
      if (2 <= ads_attr_vec.size()) {
        is_ad = 1;
        total_cost = std::stod(ads_attr_vec[0]);
        ad_mobj_weight = std::stod(ads_attr_vec[1]);
      }
      is_ad_set(result, is_ad);
      total_cost_set(result, total_cost);
      ad_mobj_weight_set(result, ad_mobj_weight);
    });

    return true;
  }

  static bool GetDedupTagSellerIdAttrs(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    // getter
    std::vector<absl::string_view> default_string_list;
    std::vector<int64> default_int_list;
    auto as_tag_seller_id_result =
        context.GetStringListCommonAttr("as_tag_seller_id_result").value_or(default_string_list);
    auto as_tag_min_time = context.GetIntListCommonAttr("as_tag_min_time").value_or(default_int_list);
    auto as_tag_max_time = context.GetIntListCommonAttr("as_tag_max_time").value_or(default_int_list);
    auto as_tag_author_list = context.GetIntListCommonAttr("as_tag_author_list").value_or(default_int_list);
    auto as_tag_author_item_list =
        context.GetIntListCommonAttr("as_tag_author_item_list").value_or(default_int_list);
    // setter
    std::vector<int64> as_dedup_tag_seller_id_list, as_recall_tag_min_time, as_recall_tag_max_time,
        as_recall_tag_author_id, as_recall_tag_author_item_id;
    int64 as_dedup_tag_seller_id_miss_ratio = 0;
    // local var
    folly::F14FastMap<int64, int64> tag_min_time_map, tag_max_time_map;
    folly::F14FastMap<int64, std::pair<int64, int64>> tag_info_map;
    int64 total_num = as_tag_seller_id_result.size();
    int64 miss_num = 0;
    // operation
    for (int i = 0; i < total_num; ++i) {
      auto seller_str = as_tag_seller_id_result[i];
      if (seller_str != "") {
        auto seller_list = absl::StrSplit(seller_str, ",");
        int64 author_id = 0, item_id = 0;
        if (i < as_tag_author_list.size()) {
          author_id = as_tag_author_list[i];
        }
        if (i < as_tag_author_item_list.size()) {
          item_id = as_tag_author_item_list[i];
        }
        std::for_each(seller_list.begin(), seller_list.end(), [&](const absl::string_view s) {
          int64 seller_id = 0;
          if (absl::SimpleAtoi(s, &seller_id)) {
            if (tag_min_time_map.find(seller_id) == tag_min_time_map.end()) {
              tag_min_time_map[seller_id] = (1LL << 60);
              tag_max_time_map[seller_id] = 0;
              tag_info_map[seller_id] = {author_id, item_id};
              as_dedup_tag_seller_id_list.emplace_back(seller_id);
            }
            int64 min_time = 0, max_time = 0;
            if (i < as_tag_min_time.size()) {
              min_time = as_tag_min_time[i];
            }
            if (i < as_tag_max_time.size()) {
              max_time = as_tag_max_time[i];
            }
            tag_min_time_map[seller_id] = std::min(tag_min_time_map[seller_id], min_time);
            if (max_time > tag_max_time_map[seller_id]) {
              tag_max_time_map[seller_id] = max_time;
              tag_info_map[seller_id] = {author_id, item_id};
            }
            tag_max_time_map[seller_id] = std::max(tag_max_time_map[seller_id], max_time);
          }
        });
      } else {
        miss_num++;
      }
    }

    std::for_each(as_dedup_tag_seller_id_list.begin(), as_dedup_tag_seller_id_list.end(),
                  [&](const auto seller_id) {
                    auto min_time = tag_min_time_map[seller_id];
                    auto max_time = tag_max_time_map[seller_id];
                    auto author_id = tag_info_map[seller_id].first;
                    auto item_id = tag_info_map[seller_id].second;
                    as_recall_tag_min_time.emplace_back(min_time);
                    as_recall_tag_max_time.emplace_back(max_time);
                    as_recall_tag_author_id.emplace_back(author_id);
                    as_recall_tag_author_item_id.emplace_back(item_id);
                  });

    as_dedup_tag_seller_id_miss_ratio = std::floor(miss_num * 1000.0 / (total_num + 0.001));
    context.SetIntCommonAttr("as_dedup_tag_seller_id_list_len", as_dedup_tag_seller_id_list.size());
    context.SetIntCommonAttr("as_dedup_tag_seller_id_miss_ratio", as_dedup_tag_seller_id_miss_ratio);
    context.SetIntCommonAttr("as_dedup_tag_seller_id_miss_num", miss_num);
    context.SetIntListCommonAttr("as_dedup_tag_seller_id_list", std::move(as_dedup_tag_seller_id_list));
    context.SetIntListCommonAttr("as_recall_tag_min_time", std::move(as_recall_tag_min_time));
    context.SetIntListCommonAttr("as_recall_tag_max_time", std::move(as_recall_tag_max_time));
    context.SetIntListCommonAttr("as_recall_tag_author_id", std::move(as_recall_tag_author_id));
    context.SetIntListCommonAttr("as_recall_tag_author_item_id", std::move(as_recall_tag_author_item_id));

    return true;
  }

  static bool GetABSellerInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    std::vector<absl::string_view> default_string_list;
    std::vector<int64> default_int_list;
    // getter
    auto common_recall_id = context.GetIntListCommonAttr("common_recall_id").value_or(default_int_list);
    auto seller_map_list = context.GetStringListCommonAttr("seller_map_list").value_or(default_string_list);
    // operation
    std::vector<int64> ab_seller_index_list, ab_seller_id_list;
    std::vector<int64> common_recall_id_return(common_recall_id.begin(), common_recall_id.end());
    folly::F14FastMap<int64, std::vector<int64>> shop_to_seller_map;
    // -- 解析 map
    std::for_each(seller_map_list.begin(), seller_map_list.end(), [&](const auto &seller_string) {
      std::vector<absl::string_view> tmp_list = absl::StrSplit(seller_string, ':');
      if (tmp_list.size() == 2) {
        int64 shop_id = 0;
        if (absl::SimpleAtoi(tmp_list[0], &shop_id)) {
          auto key_list = absl::StrSplit(tmp_list[1], ',');
          std::for_each(key_list.begin(), key_list.end(), [&](const auto &seller_id) {
            int64 id = 0;
            if (absl::SimpleAtoi(seller_id, &id)) {
              shop_to_seller_map[shop_id].emplace_back(id);
            }
          });
        }
      }
    });
    // -- 遍历 seller
    for (int i = 0; i < common_recall_id.size(); ++i) {
      auto seller_id = common_recall_id[i];
      if (shop_to_seller_map.find(seller_id) != shop_to_seller_map.end()) {
        std::for_each(shop_to_seller_map[seller_id].begin(), shop_to_seller_map[seller_id].end(),
                      [&](const auto &sid) {
                        ab_seller_id_list.emplace_back(sid);
                        ab_seller_index_list.emplace_back(i + 1);  // lua 是从 1 开始索引
                      });
      }
    }

    std::for_each(ab_seller_id_list.begin(), ab_seller_id_list.end(),
                  [&](const auto &seller_id) { common_recall_id_return.emplace_back(seller_id); });
    // setter
    context.SetIntListCommonAttr("common_recall_id", std::move(common_recall_id_return));
    context.SetIntCommonAttr("ab_seller_len", ab_seller_index_list.size());
    context.SetIntListCommonAttr("ab_seller_index_list", std::move(ab_seller_index_list));
    return true;
  }

  static bool aid_sort_cmp(const std::pair<int64, double> &a, const std::pair<int64, double> &b) {
      return a.second > b.second;
  }

  static bool ListSamplingProcessor(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    static std::vector<int64> default_int_list;
    static std::vector<double> default_double_list;

    auto sampling_aid_list =
        context.GetIntListCommonAttr("sampling_aid_list").value_or(default_int_list);
    auto sampling_aid_score_list =
        context.GetDoubleListCommonAttr("sampling_aid_score_list").value_or(default_double_list);
    auto sampling_aid_num =
        context.GetIntCommonAttr("sampling_aid_num").value_or(1);

    int aid_list_len = sampling_aid_list.size();
    int aid_score_list_len = sampling_aid_score_list.size();
    if (aid_list_len != aid_score_list_len) return false;

    std::vector<std::pair<int64, double>> aid_score_pair_list;
    for (int i = 0; i < aid_list_len; i++) {
        uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
        double random_num = (rand_r(&seed) % 100000) / 100000.0;
        double sampling_random_score = sampling_aid_score_list[i] - random_num;
        aid_score_pair_list.push_back(std::make_pair(sampling_aid_list[i], sampling_random_score));
    }

    std::sort(aid_score_pair_list.begin(), aid_score_pair_list.end(), aid_sort_cmp);
    std::vector<int64> return_sampling_aid_list;
    std::vector<double> return_sampling_aid_score_list;
    for (int i = 0; i < sampling_aid_num && i < aid_score_pair_list.size(); i++) {
        return_sampling_aid_list.push_back(aid_score_pair_list[i].first);
        return_sampling_aid_score_list.push_back(aid_score_pair_list[i].second);
    }

    context.SetIntListCommonAttr("return_sampling_aid_list",
                                 std::move(return_sampling_aid_list));
    context.SetDoubleListCommonAttr("return_sampling_aid_score_list",
                                    std::move(return_sampling_aid_score_list));

    return true;
  }

  static bool GetABExpName(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
    std::vector<absl::string_view> defaultStringList;
    auto uId = context.GetIntCommonAttr("uId").value_or(0);
    auto dId = context.GetStringCommonAttr("dId").value_or("");
    auto worldList = context.GetStringListCommonAttr("ABWorldList").value_or(defaultStringList);
    std::unordered_set<std::string> worldSet;
    if (!worldList.empty()) {
        for (auto world : worldList) {
        worldSet.insert(std::string(world));
        }
    }
    auto ExperimentInfoMap = ::ks::abtest::GetExperimentInfo(uId, std::string(dId), worldSet);
    std::string expString = "";
    for (auto &&[world, expInfo] : ExperimentInfoMap) {
        if (!expInfo.experimentId.empty() && !expInfo.groupId.empty()) {
        expString += expInfo.experimentId + "-" + expInfo.groupId + ",";
        }
    }
    if (expString.back() == ',') {
        expString.pop_back();
    }
    context.SetStringCommonAttr("expInfo", std::move(expString));
    return true;
  }

  static bool ExtractIntKvValueList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    std::vector<int64> default_list;
    std::vector<int64> output_int_value_list;
    auto default_int_val = context.GetIntCommonAttr("default_int_value").value_or(-1);
    auto input_key_list = context.GetIntListCommonAttr("input_int_key_list").value_or(default_list);
    auto int_int_map_ptr =
      context.GetPtrCommonAttr<std::unordered_map<uint64_t, std::string>>("input_int_int_map");
    if (int_int_map_ptr == nullptr || int_int_map_ptr->empty() || input_key_list.empty()) {
      LOG(INFO) << "ExtractIntKvValueList input key_list or map_list is null";
      context.SetIntListCommonAttr("output_int_value_list", std::move(output_int_value_list));
      return true;
    }
    for (int64 key : input_key_list) {
      int64 val = default_int_val;
      const auto iter = int_int_map_ptr->find(key);
      if (iter != int_int_map_ptr->end()) {
        uint64_t value = *reinterpret_cast<const uint64_t *>(iter->second.data());
        val = value;
      }
      output_int_value_list.emplace_back(val);
    }
    context.SetIntListCommonAttr("output_int_value_list", std::move(output_int_value_list));
    return true;
  }

  static bool ExplorationUcbScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    std::vector<int64> default_list;
    folly::F14FastSet<int64> skip_set;
    folly::F14FastSet<int64> interest_set;
    int64 topn_item = context.GetIntCommonAttr("topn_item").value_or(1);
    auto show_history = context.GetIntListCommonAttr("show_history").value_or(default_list);
    auto skip_show_history = context.GetIntListCommonAttr("skip_show_history").value_or(default_list);
    auto interest_list = context.GetIntListCommonAttr("interest_list").value_or(default_list);

    auto live_item_cate2_getter = context.GetIntListItemAttr("live_item_cate2");
    auto set_ucb_score_setter = context.SetDoubleItemAttr("ucb_bias_score");

    // 1. cate2 ucb score
    if (interest_list.empty()) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        set_ucb_score_setter(result, 0.0);
      });
      return true;
    }
    if (!skip_show_history.empty()) {
      skip_set.insert(skip_show_history.begin(), skip_show_history.end());
    }
    for (int64 item : interest_list) {
      if (skip_set.count(item)) {
        continue;
      }
      interest_set.insert(item);
    }
    if (interest_set.empty()) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        set_ucb_score_setter(result, 0.0);
      });
      return true;
    }
    folly::F14FastMap<int64, int64> cate_counter;
    // add default
    for (int64 item : interest_set) {
      if (skip_set.count(item)) {
        continue;
      }
      cate_counter[item] = 1;
    }
    // add realshow
    int64 total_show = 1;
    for (int64 item : show_history) {
      if (skip_set.count(item) || !interest_set.count(item)) {
        continue;
      }
      cate_counter[item]++;
      total_show++;
    }
    // 2. cate2 ucb score to item
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto live_item_cate2_list = live_item_cate2_getter(result).value_or(default_list);
      int item_count = 0;
      double ucb_bias_score = 0.0;
      if (live_item_cate2_list.empty()) {
        set_ucb_score_setter(result, 0.0);
      } else {
        for (auto item : live_item_cate2_list) {
          item_count++;
          if (item_count > topn_item) {
            break;
          }
          int item_show_cnt = 1;
          auto it = cate_counter.find(item);
          if (it != cate_counter.end()) {
            item_show_cnt = it->second;
            double item_ucb_bias = std::sqrt((total_show) / (item_show_cnt + 0.1));
            ucb_bias_score += item_ucb_bias;
          }
        }
        if (item_count > 0) {
          ucb_bias_score /= (item_count + 0.001);
        }
        set_ucb_score_setter(result, ucb_bias_score);
      }
    });
    return true;
  }

  static bool AddBasicBonus(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto boost_with_gpm = context.GetIntCommonAttr("boost_with_gpm").value_or(1);
    auto boost_weight = context.GetDoubleCommonAttr("boost_weight").value_or(0.0);
    auto boost_bias = context.GetDoubleCommonAttr("boost_bias").value_or(0.0);

    auto ctr_getter = context.GetDoubleItemAttr("ctr");
    auto cvr_getter = context.GetDoubleItemAttr("cvr");
    auto price_getter = context.GetDoubleItemAttr("price");
    auto bonus_getter = context.GetDoubleItemAttr("bonus");
    auto bonus_setter = context.SetDoubleItemAttr("bonus");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto ctr = ctr_getter(result).value_or(0.0001);
      auto cvr = cvr_getter(result).value_or(0.0001);
      auto price = price_getter(result).value_or(0.0001);
      auto bonus =  bonus_getter(result).value_or(0.0);
      double boost = ctr * cvr;
      if (boost_with_gpm > 0) {
        boost *= price;
      }
      bonus += boost_weight * boost + boost_bias;
      bonus_setter(result, bonus);
    });
    return true;
  }

  static bool ItemListTopKInCommonList(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> default_list;
    folly::F14FastSet<int64> target_set;
    auto topk = context.GetIntCommonAttr("top_k").value_or(1);
    auto target_list = context.GetIntListCommonAttr("target_list").value_or(default_list);
    target_set.insert(target_list.begin(), target_list.end());

    auto item_list_getter = context.GetIntListItemAttr("item_list");
    auto is_match_target_setter = context.SetIntItemAttr("is_match_target");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_list = item_list_getter(result).value_or(default_list);
      bool match = false;
      int count = 0;
      for (auto item : item_list) {
        if (count >= topk) { break; }
        count++;
        if (target_set.count(item)) {
          match = true;
          break;
        }
      }
      if (match) {
        is_match_target_setter(result, 1);
      } else {
        is_match_target_setter(result, 0);
      }
    });
    return true;
  }

  static bool LiveTopKItemHasNoOrderCate2(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> default_list;
    folly::F14FastSet<int64> history_cate2_set;
    auto topk = context.GetIntCommonAttr("top_k").value_or(1);
    auto history_cate2_list = context.GetIntListCommonAttr("history_order_cate2_list").value_or(default_list);
    history_cate2_set.insert(history_cate2_list.begin(), history_cate2_list.end());

    auto item_cate2_list_getter = context.GetIntListItemAttr("live_cart_cate2_list");
    auto noorder_cate2_setter = context.SetIntItemAttr("is_noorder_cate2_item");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_cate2_list = item_cate2_list_getter(result).value_or(default_list);
      if (item_cate2_list.size() <= 0) {
        noorder_cate2_setter(result, 0);
      } else if (history_cate2_set.size() <= 0) {
        noorder_cate2_setter(result, 1);
      } else {
        bool find_noorder = false;
        int count = 0;
        for (auto item : item_cate2_list) {
          if (count >= topk) { break; }
          count++;
          if (!history_cate2_set.count(item)) {
            find_noorder = true;
            break;
          }
        }
        if (find_noorder) {
          noorder_cate2_setter(result, 1);
        } else {
          noorder_cate2_setter(result, 0);
        }
      }
    });
    return true;
  }

  static bool GetRoiBonus(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_max = context.GetDoubleCommonAttr("cpm_max").value_or(500.0);
    auto cpm_bais = context.GetDoubleCommonAttr("cpm_bais").value_or(100.0);
    auto cpm_w = context.GetDoubleCommonAttr("cpm_w").value_or(0.0);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");

    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonus(roi_cpm, bid_egpm_ori, cpm_w);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonus(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_w, cpm_max, cpm_bais);
      roi_cpm = merchant::LiveCpm(bid_egpm_ori, roi_bonus, cpm_w);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
    });
    return true;
  }

  static bool GetRoiBonusMtb(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_weight = context.GetDoubleCommonAttr("cpm_weight").value_or(100.0);
    auto cpm_max = context.GetDoubleCommonAttr("cpm_max").value_or(50000.0);
    auto cpm_bais = context.GetDoubleCommonAttr("cpm_bais").value_or(1000.0);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.014);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_bias").value_or(1.0);
    auto merchant_score_coeff = context.GetDoubleCommonAttr("merchant_score_exp").value_or(2.6);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto ue_score_final_getter = context.GetDoubleItemAttr("ue_score_final");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");
    auto roi_gmv_bonus_setter = context.SetDoubleItemAttr("roi_gmv_bonus");


    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto ue_score_final = ue_score_final_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonusMtb(roi_cpm, bid_egpm_ori, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonusMtb(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_weight, cpm_max, cpm_bais, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      roi_cpm = merchant::LiveCpmMtb(bid_egpm_ori, roi_bonus, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      auto g_cpm_min = merchant::LiveCpmMtb(bid_egpm_ori, bonus_min, ue_score_final,
          merchant_score_weight, merchant_score_exp, merchant_score_coeff);
      auto f_gmv_min = merchant::LiveLog10(g_cpm_min, cpm_weight, cpm_max, cpm_bais);
      auto roi_gmv_bonus = (merchant::LiveLog10(roi_cpm, cpm_weight, cpm_max, cpm_bais) - f_gmv_min) /
        std::max(roi_bonus - bonus_min, 1e-6);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
      roi_gmv_bonus_setter(result, roi_gmv_bonus);
    });
    return true;
  }

  static bool GetRoiBonusMtbCpm(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_weight = context.GetDoubleCommonAttr("cpm_weight").value_or(100.0);
    auto cpm_max = context.GetDoubleCommonAttr("cpm_max").value_or(50000.0);
    auto cpm_bais = context.GetDoubleCommonAttr("cpm_bais").value_or(1000.0);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.014);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_bias").value_or(1.0);
    auto merchant_score_coeff = context.GetDoubleCommonAttr("merchant_score_exp").value_or(2.6);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto ue_score_final_getter = context.GetDoubleItemAttr("ue_score_final");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");
    auto roi_gmv_bonus_setter = context.SetDoubleItemAttr("roi_gmv_bonus");


    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto ue_score_final = ue_score_final_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonusMtb(roi_cpm, bid_egpm_ori, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonusMtbCpm(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_weight, cpm_max, cpm_bais, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      roi_cpm = merchant::LiveCpmMtb(bid_egpm_ori, roi_bonus, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      auto g_cpm_min = merchant::LiveCpmMtb(bid_egpm_ori, bonus_min, ue_score_final,
          merchant_score_weight, merchant_score_exp, merchant_score_coeff);
      auto f_gmv_min = merchant::LiveLog10(g_cpm_min, cpm_weight, cpm_max, cpm_bais);
      auto roi_gmv_bonus = (merchant::LiveLog10(roi_cpm, cpm_weight, cpm_max, cpm_bais) - f_gmv_min) /
        std::max(roi_cpm - g_cpm_min, 1e-6);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
      roi_gmv_bonus_setter(result, roi_gmv_bonus);
    });
    return true;
  }

  static bool GetRoiBonusMtb2(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_w1 = context.GetDoubleCommonAttr("cpm_w1").value_or(4.9182);
    auto cpm_b1 = context.GetDoubleCommonAttr("cpm_b1").value_or(3.4394);
    auto cpm_w2 = context.GetDoubleCommonAttr("cpm_w2").value_or(75.292);
    auto cpm_b2 = context.GetDoubleCommonAttr("cpm_b2").value_or(63.179);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.014);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_bias").value_or(1.0);
    auto merchant_score_coeff = context.GetDoubleCommonAttr("merchant_score_exp").value_or(2.6);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto ue_score_final_getter = context.GetDoubleItemAttr("ue_score_final");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");
    auto roi_gmv_bonus_setter = context.SetDoubleItemAttr("roi_gmv_bonus");


    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto ue_score_final = ue_score_final_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonusMtb(roi_cpm, bid_egpm_ori, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonusMtb2(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_w1, cpm_b1, cpm_w2, cpm_b2, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      roi_cpm = merchant::LiveCpmMtb(bid_egpm_ori, roi_bonus, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      auto g_cpm_min = merchant::LiveCpmMtb(bid_egpm_ori, bonus_min, ue_score_final,
          merchant_score_weight, merchant_score_exp, merchant_score_coeff);
      auto f_gmv_min = merchant::LiveLogMtb(g_cpm_min, cpm_w1, cpm_b1, cpm_w2, cpm_b2);
      auto roi_gmv_bonus = (merchant::LiveLogMtb(roi_cpm, cpm_w1, cpm_b1, cpm_w2, cpm_b2) - f_gmv_min) /
        std::max(roi_bonus - bonus_min, 1e-6);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
      roi_gmv_bonus_setter(result, roi_gmv_bonus);
    });
    return true;
  }

  static bool GetRoiBonusMtbCpm2(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_w1 = context.GetDoubleCommonAttr("cpm_w1").value_or(4.9182);
    auto cpm_b1 = context.GetDoubleCommonAttr("cpm_b1").value_or(3.4394);
    auto cpm_w2 = context.GetDoubleCommonAttr("cpm_w2").value_or(75.292);
    auto cpm_b2 = context.GetDoubleCommonAttr("cpm_b2").value_or(63.179);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.014);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_bias").value_or(1.0);
    auto merchant_score_coeff = context.GetDoubleCommonAttr("merchant_score_exp").value_or(2.6);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto ue_score_final_getter = context.GetDoubleItemAttr("ue_score_final");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");
    auto roi_gmv_bonus_setter = context.SetDoubleItemAttr("roi_gmv_bonus");


    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto ue_score_final = ue_score_final_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonusMtb(roi_cpm, bid_egpm_ori, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonusMtbCpm2(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_w1, cpm_b1, cpm_w2, cpm_b2, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      roi_cpm = merchant::LiveCpmMtb(bid_egpm_ori, roi_bonus, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      auto g_cpm_min = merchant::LiveCpmMtb(bid_egpm_ori, bonus_min, ue_score_final,
          merchant_score_weight, merchant_score_exp, merchant_score_coeff);
      auto f_gmv_min = merchant::LiveLogMtb(g_cpm_min, cpm_w1, cpm_b1, cpm_w2, cpm_b2);
      auto roi_gmv_bonus = (merchant::LiveLogMtb(roi_cpm, cpm_w1, cpm_b1, cpm_w2, cpm_b2) - f_gmv_min) /
        std::max(roi_cpm - g_cpm_min, 1e-6);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
      roi_gmv_bonus_setter(result, roi_gmv_bonus);
    });
    return true;
  }

  static bool GetRoiBonusMtbCom(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto bonus_roi = context.GetDoubleCommonAttr("bonus_roi").value_or(0.0);
    auto cpm_p1 = context.GetDoubleCommonAttr("cpm_p1").value_or(0.0);
    auto cpm_w1 = context.GetDoubleCommonAttr("cpm_w1").value_or(6.3743);
    auto cpm_a1 = context.GetDoubleCommonAttr("cpm_a1").value_or(1.0);
    auto cpm_b1 = context.GetDoubleCommonAttr("cpm_b1").value_or(0.0);
    auto cpm_l1 = context.GetDoubleCommonAttr("cpm_l1").value_or(0.0);
    auto cpm_c1 = context.GetDoubleCommonAttr("cpm_c1").value_or(-14.936);
    auto cpm_p2 = context.GetDoubleCommonAttr("cpm_p2").value_or(350.0);
    auto cpm_w2 = context.GetDoubleCommonAttr("cpm_w2").value_or(23.264);
    auto cpm_a2 = context.GetDoubleCommonAttr("cpm_a2").value_or(1.0);
    auto cpm_b2 = context.GetDoubleCommonAttr("cpm_b2").value_or(0.0);
    auto cpm_l2 = context.GetDoubleCommonAttr("cpm_l2").value_or(0.0);
    auto cpm_c2 = context.GetDoubleCommonAttr("cpm_c2").value_or(-113.0);
    auto egpm_min = context.GetDoubleCommonAttr("egpm_min").value_or(150.0);
    auto sort_tag = context.GetIntCommonAttr("sort_tag").value_or(1);
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.014);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_bias").value_or(1.0);
    auto merchant_score_coeff = context.GetDoubleCommonAttr("merchant_score_exp").value_or(2.6);

    auto bid_egpm_getter = context.GetDoubleItemAttr("bid_egpm");
    auto ue_score_final_getter = context.GetDoubleItemAttr("ue_score_final");
    auto roi_bonus_setter = context.SetDoubleItemAttr("mix_rank_total_bonus");
    auto roi_cpm_setter = context.SetDoubleItemAttr("roi_cpm");
    auto roi_gmv_bonus_setter = context.SetDoubleItemAttr("roi_gmv_bonus");


    auto roi_cpm = -1.0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto bid_egpm = bid_egpm_getter(result).value_or(0.0);
      auto ue_score_final = ue_score_final_getter(result).value_or(0.0);
      auto bid_egpm_ori = bid_egpm * 1000.0;
      auto bonus_min = 0.0;
      auto norm_egpm = std::max(egpm_min, bid_egpm_ori);
      auto last_bonus = norm_egpm;
      if (roi_cpm != -1.0 && sort_tag == 1) {
        last_bonus = merchant::LiveBonusMtb(roi_cpm, bid_egpm_ori, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
        last_bonus = std::max(last_bonus, 0.0);
      }
      auto bonus_max = std::min(norm_egpm, last_bonus);
      auto roi_bonus = merchant::LiveBinarySearchRoiBonusMtbCom(bid_egpm_ori,
      bonus_min, bonus_max, bonus_roi, cpm_p1, cpm_w1, cpm_a1, cpm_b1, cpm_l1, cpm_c1,
      cpm_p2, cpm_w2, cpm_a2, cpm_b2, cpm_l2, cpm_c2, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      roi_cpm = merchant::LiveCpmMtb(bid_egpm_ori, roi_bonus, ue_score_final, merchant_score_weight,
          merchant_score_exp, merchant_score_coeff);
      auto g_cpm_min = merchant::LiveCpmMtb(bid_egpm_ori, bonus_min, ue_score_final,
          merchant_score_weight, merchant_score_exp, merchant_score_coeff);
      auto f_gmv_min = merchant::LiveLogCom(g_cpm_min, cpm_p1, cpm_w1, cpm_a1, cpm_b1, cpm_l1, cpm_c1,
      cpm_p2, cpm_w2, cpm_a2, cpm_b2, cpm_l2, cpm_c2);
      auto roi_gmv_bonus = (merchant::LiveLogCom(roi_cpm, cpm_p1, cpm_w1, cpm_a1, cpm_b1, cpm_l1, cpm_c1,
      cpm_p2, cpm_w2, cpm_a2, cpm_b2, cpm_l2, cpm_c2) - f_gmv_min) / std::max(roi_bonus - bonus_min, 1e-6);
      roi_bonus = roi_bonus / 1000.0;
      roi_bonus_setter(result, roi_bonus);
      roi_cpm_setter(result, roi_cpm);
      roi_gmv_bonus_setter(result, roi_gmv_bonus);
    });
    return true;
  }

  static bool CalcTrafficWorthScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto merchant_score_weight = context.GetDoubleCommonAttr("merchant_score_weight").value_or(0.0);
    auto merchant_score_bias = context.GetDoubleCommonAttr("merchant_score_bias").value_or(0.0);
    auto merchant_score_exp = context.GetDoubleCommonAttr("merchant_score_exp").value_or(0.0);
    auto ue_score_weight = context.GetDoubleCommonAttr("ue_score_weight").value_or(0.0);
    auto ue_score_bias = context.GetDoubleCommonAttr("ue_score_bias").value_or(0.0);
    auto ue_score_exp = context.GetDoubleCommonAttr("ue_score_exp").value_or(0.0);
    // getter
    auto merchant_score_getter = context.GetDoubleItemAttr("merchant_score");
    auto ue_score_getter = context.GetDoubleItemAttr("ue_score");
    // setter
    auto traffic_worth_score_setter = context.SetDoubleItemAttr("traffic_worth_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto merchant_score = merchant_score_getter(result).value_or(0.0001);
      auto ue_score = ue_score_getter(result).value_or(1.0);
      double traffic_worth_score
        = pow(merchant_score_weight * merchant_score + merchant_score_bias, merchant_score_exp)
          * pow(ue_score_weight * ue_score + ue_score_bias, ue_score_exp);
      traffic_worth_score_setter(result, traffic_worth_score);
    });
    return true;
  }

  static bool TagIfBelowThreshold(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto threshold = context.GetDoubleCommonAttr("threshold").value_or(0.0);
    // getter
    auto value_getter = context.GetDoubleItemAttr("value");
    // setter
    auto tag_setter = context.SetIntItemAttr("tag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto value = value_getter(result).value_or(0.0);
      if (value < threshold) {
        tag_setter(result, 1);
      } else {
        tag_setter(result, 0);
      }
    });
    return true;
  }

  static bool ColossusReverseTruncateV4Attr(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    // 此函数只用于功能兼容，不用于通用实现
    // input
    auto v4_author_id = context.GetIntListCommonAttr("liveItemV4_author_id");
    auto v4_timestamp = context.GetIntListCommonAttr("liveItemV4_timestamp");
    auto v4_live_id = context.GetIntListCommonAttr("liveItemV4_live_id");
    auto v4_play_time = context.GetIntListCommonAttr("liveItemV4_play_time");
    auto v4_auto_play_time = context.GetIntListCommonAttr("liveItemV4_auto_play_time");
    auto v4_hetu_tag_channel = context.GetIntListCommonAttr("liveItemV4_hetu_tag_channel");
    auto v4_cluster_id = context.GetIntListCommonAttr("liveItemV4_cluster_id");
    auto v4_label = context.GetIntListCommonAttr("liveItemV4_label");
    auto v4_order_price = context.GetIntListCommonAttr("liveItemV4_order_price");
    auto v4_audience_count = context.GetIntListCommonAttr("liveItemV4_audience_count");

    auto limit_num = context.GetIntCommonAttr("reverse_limit_num").value_or(0);

    std::vector<int64> limit_reverse_author_id_list, limit_reverse_timestamp_list, limit_reverse_live_id_list,
        limit_reverse_play_time_list, limit_reverse_auto_play_time_list, limit_reverse_hetu_tag_channel_list,
        limit_reverse_cluster_id_list, limit_reverse_label_list, limit_reverse_order_price_list,
        limit_reverse_audience_count_list;

    if (v4_author_id.has_value() && !v4_author_id->empty()) {
      int start_idx = std::max(0, (int)(v4_author_id->size() - limit_num));
      for (int i = start_idx; i < v4_author_id->size(); ++i) {
        limit_reverse_author_id_list.emplace_back(v4_author_id->at(i));
      }
    }
    if (v4_timestamp.has_value() && !v4_timestamp->empty()) {
      int start_idx = std::max(0, (int)(v4_timestamp->size() - limit_num));
      for (int i = start_idx; i < v4_timestamp->size(); ++i) {
        limit_reverse_timestamp_list.emplace_back(v4_timestamp->at(i));
      }
    }
    if (v4_live_id.has_value() && !v4_live_id->empty()) {
      int start_idx = std::max(0, (int)(v4_live_id->size() - limit_num));
      for (int i = start_idx; i < v4_live_id->size(); ++i) {
        limit_reverse_live_id_list.emplace_back(v4_live_id->at(i));
      }
    }
    if (v4_play_time.has_value() && !v4_play_time->empty()) {
      int start_idx = std::max(0, (int)(v4_play_time->size() - limit_num));
      for (int i = start_idx; i < v4_play_time->size(); ++i) {
        limit_reverse_play_time_list.emplace_back(v4_play_time->at(i));
      }
    }
    if (v4_auto_play_time.has_value() && !v4_auto_play_time->empty()) {
      int start_idx = std::max(0, (int)(v4_auto_play_time->size() - limit_num));
      for (int i = start_idx; i < v4_auto_play_time->size(); ++i) {
        limit_reverse_auto_play_time_list.emplace_back(v4_auto_play_time->at(i));
      }
    }
    if (v4_hetu_tag_channel.has_value() && !v4_hetu_tag_channel->empty()) {
      int start_idx = std::max(0, (int)(v4_hetu_tag_channel->size() - limit_num));
      for (int i = start_idx; i < v4_hetu_tag_channel->size(); ++i) {
        limit_reverse_hetu_tag_channel_list.emplace_back(v4_hetu_tag_channel->at(i));
      }
    }
    if (v4_cluster_id.has_value() && !v4_cluster_id->empty()) {
      int start_idx = std::max(0, (int)(v4_cluster_id->size() - limit_num));
      for (int i = start_idx; i < v4_cluster_id->size(); ++i) {
        limit_reverse_cluster_id_list.emplace_back(v4_cluster_id->at(i));
      }
    }
    if (v4_label.has_value() && !v4_label->empty()) {
      int start_idx = std::max(0, (int)(v4_label->size() - limit_num));
      for (int i = start_idx; i < v4_label->size(); ++i) {
        limit_reverse_label_list.emplace_back(v4_label->at(i));
      }
    }
    if (v4_order_price.has_value() && !v4_order_price->empty()) {
      int start_idx = std::max(0, (int)(v4_order_price->size() - limit_num));
      for (int i = start_idx; i < v4_order_price->size(); ++i) {
        limit_reverse_order_price_list.emplace_back(v4_order_price->at(i));
      }
    }
    if (v4_audience_count.has_value() && !v4_audience_count->empty()) {
      int start_idx = std::max(0, (int)(v4_audience_count->size() - limit_num));
      for (int i = start_idx; i < v4_audience_count->size(); ++i) {
        limit_reverse_audience_count_list.emplace_back(v4_audience_count->at(i));
      }
    }

    context.SetIntListCommonAttr("limit_liveItemV4_author_id", std::move(limit_reverse_author_id_list));
    context.SetIntListCommonAttr("limit_liveItemV4_timestamp", std::move(limit_reverse_timestamp_list));
    context.SetIntListCommonAttr("limit_liveItemV4_live_id", std::move(limit_reverse_live_id_list));
    context.SetIntListCommonAttr("limit_liveItemV4_play_time", std::move(limit_reverse_play_time_list));
    context.SetIntListCommonAttr("limit_liveItemV4_auto_play_time",
                                 std::move(limit_reverse_auto_play_time_list));
    context.SetIntListCommonAttr("limit_liveItemV4_hetu_tag_channel",
                                 std::move(limit_reverse_hetu_tag_channel_list));
    context.SetIntListCommonAttr("limit_liveItemV4_cluster_id", std::move(limit_reverse_cluster_id_list));
    context.SetIntListCommonAttr("limit_liveItemV4_label", std::move(limit_reverse_label_list));
    context.SetIntListCommonAttr("limit_liveItemV4_order_price", std::move(limit_reverse_order_price_list));
    context.SetIntListCommonAttr("limit_liveItemV4_audience_count",
                                 std::move(limit_reverse_audience_count_list));
    return true;
  }

  static bool CalcGpmPrePostScore(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto pre_post_score_map_string = context.GetStringCommonAttr("me_mix_unify_mtb2_prepost").value_or("");
    auto pre_post_gpm_key = context.GetStringCommonAttr("pre_post_gpm_key").value_or("");
    auto sc_live_ctr_attr = context.GetDoubleItemAttr("sc_live_ctr");
    auto sc_cvr_attr = context.GetDoubleItemAttr("sc_cvr");
    auto sc_pexp_gmv_attr = context.GetDoubleItemAttr("sc_pexp_gmv");
    auto calibrate_gpm_setter = context.SetDoubleItemAttr("calibrate_gpm");
    // 取 pre_post_gpm_key 对应的上下界存入 score_map
    std::map<double, double> score_map;
    merchant::ConcatMap(std::string(pre_post_gpm_key), std::string(pre_post_score_map_string), &score_map);
    // 先后验校准
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double sc_live_ctr = sc_live_ctr_attr(result).value_or(0.0);
      double sc_cvr = sc_cvr_attr(result).value_or(0.0);
      double sc_pexp_gmv = sc_pexp_gmv_attr(result).value_or(0.0);
      double pre_gpm = 1000 * sc_live_ctr * sc_cvr * sc_pexp_gmv / 1.5;
      double post_gpm = pre_gpm;
      if (!score_map.empty()) {
        score_map.insert(score_map.begin(), {0.0, 0.0});
        auto iter = score_map.lower_bound(pre_gpm);
        if (iter == score_map.end())
          --iter;
        if (iter == score_map.begin())
          ++iter;
        double x2 = iter->first;
        double y2 = iter->second;
        double x1 = (--iter)->first;
        double y1 = iter->second;
        post_gpm = std::max(merchant::CalFittingValue(x1, y1, x2, y2, pre_gpm), 0.0);
        // 如果严格等于 map 中的 key 则直接返回 key 中对应的 val
        if (score_map.count(pre_gpm) > 0)
          post_gpm = score_map.at(pre_gpm);
      }
      calibrate_gpm_setter(result, post_gpm);
    });
    return true;
  }

  static bool CalcUePrePostScore(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::unordered_map<std::string, std::map<double, double>> pre_post_score_map;
    auto pre_post_score_map_string = context.GetStringCommonAttr("mix_unify_mtb_ue_prepost").value_or("");
    auto pre_post_uescore_key = context.GetStringCommonAttr("pre_post_uescore_key").value_or("");
    // pre_post_uescore_key 对应的 xtr 上下界存入 score_map
    std::vector<std::string> pre_ue_xtr_name_lis = {
      "ctr", "wtr", "lvtr", "etr", "htr", "ltr", "in_etr", "in_lvtr"
    };
    for (const auto& pre_ue_xtr_name : pre_ue_xtr_name_lis) {
        pre_post_score_map.insert({pre_ue_xtr_name, {}});
        merchant::ConcatMap(std::string(pre_post_uescore_key) + "-live-" + std::string(pre_ue_xtr_name),
          std::string(pre_post_score_map_string),
          &pre_post_score_map[pre_ue_xtr_name]);
        if (!pre_post_score_map[pre_ue_xtr_name].empty())
          pre_post_score_map[pre_ue_xtr_name].insert({0.0, 0.0});
    }
    // 先后验校准
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (const auto& pre_ue_xtr_name : pre_ue_xtr_name_lis) {
        auto pre_ue_xtr_attr = context.GetDoubleItemAttr(pre_ue_xtr_name);
        auto ue_xtr_setter = context.SetDoubleItemAttr(pre_ue_xtr_name);
        double pre_ue_xtr = pre_ue_xtr_attr(result).value_or(0.0);
        double post_ue_xtr = pre_ue_xtr;
        if (!pre_post_score_map[pre_ue_xtr_name].empty()) {
          auto score_map = pre_post_score_map[pre_ue_xtr_name];
          auto iter = score_map.lower_bound(pre_ue_xtr);
          if (iter == score_map.end())
            --iter;
          if (iter == score_map.begin())
            ++iter;
          double x2 = iter->first;
          double y2 = iter->second;
          double x1 = (--iter)->first;
          double y1 = iter->second;
          post_ue_xtr = std::max(merchant::CalFittingValue(x1, y1, x2, y2, pre_ue_xtr), 0.0);
          // 如果严格等于 map 中的 key 则直接返回 key 中对应的 val
          if (score_map.count(pre_ue_xtr) > 0)
            post_ue_xtr = score_map.at(pre_ue_xtr);
        }
        ue_xtr_setter(result, post_ue_xtr);
      }
    });
    return true;
  }

  static bool B2CRetrieveReverse(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    struct Compare {
        bool operator()(const std::pair<uint64, double> &a, const std::pair<uint64, double> &b) const {
          return a.second > b.second;
        }
    };

    std::unordered_map<std::string, std::priority_queue<std::pair<uint64, double>,
                                                        std::vector<std::pair<uint64, double>>, Compare>>
        user_to_items_map;
    std::vector<std::string> users_seq;
    std::vector<std::string> user_items_seq;
    //
    auto item_users_intlist_attr_func = context.GetIntListItemAttr("b2c_item_id_list");
    auto item_items_distance_attr_func = context.GetDoubleListItemAttr("b2c_item_distance_list");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_key_attr = result.GetId();
      auto user_lists = item_users_intlist_attr_func(result);
      auto ui_distance_lists = item_items_distance_attr_func(result);
      bool analyze_flag = true;
      if (!user_lists.has_value() || user_lists.value().size() <= 0) {
        analyze_flag = false;
      }
      if (!ui_distance_lists.has_value() || ui_distance_lists.value().size() <= 0) {
        analyze_flag = false;
      }
      if (user_lists.value().size() != ui_distance_lists.value().size()) {
        analyze_flag = false;
      }
      if (analyze_flag) {
        int length = user_lists.value().size();
        for (int i = 0; i < length; ++i) {
          auto user_id = user_lists.value()[i];
          auto user_distance = ui_distance_lists.value()[i];
          if (-1 != user_id) {
            std::pair<uint64, double> ui_p = {item_key_attr, user_distance};
            auto user_id_str = std::to_string(user_id);
            user_to_items_map[user_id_str].push(ui_p);
          }
        }
      }
    });
    //
    for (const auto &[key, value] : user_to_items_map) {
        std::unordered_set<uint64> dep_set;
        std::vector<std::pair<uint64, double>> dep_vec;
        auto pq_copy = value;
        while (!pq_copy.empty()) {
          const auto val = pq_copy.top();
          pq_copy.pop();
          uint64 item_key = val.first;
          // double item_score = val.second;
          if (dep_set.end() == dep_set.find(item_key)) {
          dep_set.insert(item_key);
          dep_vec.emplace_back(val);
          }
        }
        std::string dep_items("");
        for (int i = 0; i < (int)dep_vec.size(); ++i) {
          dep_items += (std::to_string(dep_vec[i].first) + ',' + std::to_string(dep_vec[i].second) + ';');
        }
        if (!dep_items.empty()) {
          dep_items.pop_back();
        }
        if (!key.empty() && !dep_items.empty()) {
          users_seq.emplace_back(key);
          user_items_seq.emplace_back(dep_items);
        }
    }
    //
    if (!users_seq.empty() && users_seq.size() == user_items_seq.size()) {
        context.SetStringListCommonAttr("b2c_users_seq", std::move(users_seq));
        context.SetStringListCommonAttr("b2c_user_items_seq", std::move(user_items_seq));
    }
    return true;
  }

  static bool B2CDataMerge(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
    //
    struct Compare {
        bool operator()(const std::pair<std::string, double> &a,
                        const std::pair<std::string, double> &b) const {
          return a.second > b.second;
        }
    };
    std::priority_queue<std::pair<std::string, double>, std::vector<std::pair<std::string, double>>, Compare>
        merge_pq;
    // 存量
    auto b2c_user_items_seq_attr = context.GetStringCommonAttr("b2c_user_items_record").value_or("");
    // 增量
    auto b2c_user_items_new_attr = context.GetStringCommonAttr("b2c_user_items_new").value_or("");

    std::vector<std::pair<std::string, std::string>> record_vec;
    std::vector<std::string> record_parts = absl::StrSplit(b2c_user_items_seq_attr, ";");
    for (const auto &part : record_parts) {
        std::vector<std::string> elements = absl::StrSplit(part, ',');
        if (elements.size() == 2) {
          record_vec.emplace_back(elements[0], elements[1]);
        }
    }

    std::vector<std::string> new_parts = absl::StrSplit(b2c_user_items_new_attr, ";");
    for (const auto &part : new_parts) {
        std::vector<std::string> elements = absl::StrSplit(part, ',');
        if (elements.size() == 2) {
          record_vec.emplace_back(elements[0], elements[1]);
        }
    }
    for (const auto &it : record_vec) {
      double value;
      if (absl::SimpleAtod(it.second, &value)) {
        std::pair n_p = {it.first, value};
        merge_pq.push(n_p);
      }
    }

    std::unordered_set<std::string> userid_set;
    int round = 0;
    std::string dep_items("");
    while (!merge_pq.empty()) {
        auto copy_p = merge_pq.top();
        merge_pq.pop();
        if (userid_set.end() == userid_set.find(copy_p.first) && round < 300) {
          userid_set.insert(copy_p.first);
          ++round;
          dep_items += (copy_p.first + ',' + std::to_string(copy_p.second) + ';');
        }
    }
    if (!dep_items.empty()) {
        dep_items.pop_back();
    }
    if (!dep_items.empty()) {
        context.SetStringCommonAttr("b2c_merge_user_items_seq", std::move(dep_items));
    }

    return true;
  }


  static bool FgCalculateTopkIndicesAndValues(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> default_list;
    std::vector<double> default_double_list;
    auto req_time = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1);
    auto photo_colossus_item_id_list =
        context.GetIntListCommonAttr("photo_colossus_item_id_has_itemid_list").value_or(default_list);
    auto photo_colossus_photo_id_list =
        context.GetIntListCommonAttr("photo_colossus_photo_id_has_itemid_list").value_or(default_list);
    auto photo_colossus_author_id_list =
        context.GetIntListCommonAttr("photo_colossus_author_id_has_itemid_list").value_or(default_list);
    auto photo_colossus_duration_list =
        context.GetIntListCommonAttr("photo_colossus_duration_has_itemid_list").value_or(default_list);
    auto photo_colossus_play_time_list =
        context.GetIntListCommonAttr("photo_colossus_play_time_has_itemid_list").value_or(default_list);
    auto photo_colossus_channel_list =
        context.GetIntListCommonAttr("photo_colossus_channel_has_itemid_list").value_or(default_list);
    auto photo_colossus_label_list =
        context.GetIntListCommonAttr("photo_colossus_label_has_itemid_list").value_or(default_list);
    auto photo_colossus_timestamp_list =
        context.GetIntListCommonAttr("photo_colossus_timestamp_has_itemid_list").value_or(default_list);
    auto photo_colossus_spu_id_list =
        context.GetIntListCommonAttr("photo_colossus_spu_id_has_itemid_list").value_or(default_list);
    auto photo_colossus_category_list =
        context.GetIntListCommonAttr("photo_colossus_category_has_itemid_list").value_or(default_list);

    auto get_topk_indices = context.GetIntListItemAttr("topk_indices");
    auto get_topk_values = context.GetDoubleListItemAttr("topk_values");

    auto set_new_cart_photo_gsu_pid = context.SetIntListItemAttr("new_cart_photo_gsu_pid");
    auto set_new_cart_photo_gsu_aid = context.SetIntListItemAttr("new_cart_photo_gsu_aid");
    auto set_new_cart_photo_gsu_iid = context.SetIntListItemAttr("new_cart_photo_gsu_iid");
    auto set_new_cart_photo_gsu_duration = context.SetIntListItemAttr("new_cart_photo_gsu_duration");
    auto set_new_cart_photo_gsu_playtime = context.SetIntListItemAttr("new_cart_photo_gsu_playtime");
    auto set_new_cart_photo_gsu_spuid = context.SetIntListItemAttr("new_cart_photo_gsu_spuid");
    auto set_new_cart_photo_gsu_channel = context.SetIntListItemAttr("new_cart_photo_gsu_channel");
    auto set_new_cart_photo_gsu_label = context.SetIntListItemAttr("new_cart_photo_gsu_label");
    auto set_new_cart_photo_gsu_cate1 = context.SetIntListItemAttr("new_cart_photo_gsu_cate1");
    auto set_new_cart_photo_gsu_cate2 = context.SetIntListItemAttr("new_cart_photo_gsu_cate2");
    auto set_new_cart_photo_gsu_cate3 = context.SetIntListItemAttr("new_cart_photo_gsu_cate3");
    auto set_new_cart_photo_gsu_lagdays = context.SetIntListItemAttr("new_cart_photo_gsu_lagdays");
    auto set_new_cart_photo_gsu_laghours = context.SetIntListItemAttr("new_cart_photo_gsu_laghours");
    auto set_new_cart_photo_gsu_distance = context.SetIntListItemAttr("new_cart_photo_gsu_distance");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      std::vector<int64> new_cart_photo_gsu_pid;
      std::vector<int64> new_cart_photo_gsu_aid;
      std::vector<int64> new_cart_photo_gsu_iid;
      std::vector<int64> new_cart_photo_gsu_duration;
      std::vector<int64> new_cart_photo_gsu_playtime;
      std::vector<int64> new_cart_photo_gsu_spuid;
      std::vector<int64> new_cart_photo_gsu_channel;
      std::vector<int64> new_cart_photo_gsu_label;
      std::vector<int64> new_cart_photo_gsu_cate1;
      std::vector<int64> new_cart_photo_gsu_cate2;
      std::vector<int64> new_cart_photo_gsu_cate3;
      std::vector<int64> new_cart_photo_gsu_lagdays;
      std::vector<int64> new_cart_photo_gsu_laghours;
      std::vector<int64> new_cart_photo_gsu_distance;

      auto topk_indices = get_topk_indices(result).value_or(default_list);
      auto topk_values = get_topk_values(result).value_or(default_double_list);

      auto extract_category_levels = [](int64 category_value) {
          int64 cate1 = (category_value >> 32) & 0xFFFF;
          int64 cate2 = (category_value >> 16) & 0xFFFF;
          int64 cate3 = category_value & 0xFFFF;
          return std::make_tuple(cate1, cate2, cate3);
      };

      auto calculate_time_diffs = [](int64 req_time, int64 timestamp) {
          int64 delta_seconds = req_time / 1000 - timestamp;
          int64 lag_days = delta_seconds / (3600 * 24);
          int64 lag_hours = delta_seconds / 3600;
          return std::make_tuple(lag_days, lag_hours);
      };
      int64 len = photo_colossus_photo_id_list.size();

      for (int i = 0; i < topk_indices.size(); i++) {
        int64 index = topk_indices[i];
        if (index >=0 && index < len) {
          new_cart_photo_gsu_pid.emplace_back(photo_colossus_photo_id_list[index]);
          new_cart_photo_gsu_aid.emplace_back(photo_colossus_author_id_list[index]);
          new_cart_photo_gsu_iid.emplace_back(photo_colossus_item_id_list[index]);
          new_cart_photo_gsu_duration.emplace_back(photo_colossus_duration_list[index]);
          new_cart_photo_gsu_playtime.emplace_back(photo_colossus_play_time_list[index]);
          new_cart_photo_gsu_channel.emplace_back(photo_colossus_channel_list[index]);
          new_cart_photo_gsu_spuid.emplace_back(photo_colossus_spu_id_list[index]);
          new_cart_photo_gsu_label.emplace_back(photo_colossus_label_list[index]);
          auto [cate1, cate2, cate3] = extract_category_levels(photo_colossus_category_list[index]);
          new_cart_photo_gsu_cate1.emplace_back(cate1);
          new_cart_photo_gsu_cate2.emplace_back(cate2);
          new_cart_photo_gsu_cate3.emplace_back(cate3);
          auto [lag_days, lag_hours] = calculate_time_diffs(req_time, photo_colossus_timestamp_list[index]);
          new_cart_photo_gsu_lagdays.emplace_back(lag_days);
          new_cart_photo_gsu_laghours.emplace_back(lag_hours);
        }
      }
      int64 size = topk_indices.size();
      for (int i = 0; i < topk_values.size(); i++) {
        if (i < size) {
          if (topk_values[i] > 0.0)
              new_cart_photo_gsu_distance.emplace_back((int)floor(topk_values[i] * 1000));
        }
      }
      set_new_cart_photo_gsu_pid(result, new_cart_photo_gsu_pid);
      set_new_cart_photo_gsu_aid(result, new_cart_photo_gsu_aid);
      set_new_cart_photo_gsu_iid(result, new_cart_photo_gsu_iid);
      set_new_cart_photo_gsu_duration(result, new_cart_photo_gsu_duration);
      set_new_cart_photo_gsu_playtime(result, new_cart_photo_gsu_playtime);
      set_new_cart_photo_gsu_spuid(result, new_cart_photo_gsu_spuid);
      set_new_cart_photo_gsu_channel(result, new_cart_photo_gsu_channel);
      set_new_cart_photo_gsu_label(result, new_cart_photo_gsu_label);
      set_new_cart_photo_gsu_cate1(result, new_cart_photo_gsu_cate1);
      set_new_cart_photo_gsu_cate2(result, new_cart_photo_gsu_cate2);
      set_new_cart_photo_gsu_cate3(result, new_cart_photo_gsu_cate3);
      set_new_cart_photo_gsu_lagdays(result, new_cart_photo_gsu_lagdays);
      set_new_cart_photo_gsu_laghours(result, new_cart_photo_gsu_laghours);
      set_new_cart_photo_gsu_distance(result, new_cart_photo_gsu_distance);
    });
    return true;
  }

  static bool FgCalculateTopkIndicesAndValuesIav2(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<int64> default_list;
    std::vector<double> default_double_list;
    auto req_time = context.GetIntCommonAttr("_REQ_TIME_").value_or(-1);
    auto photo_colossus_item_id_list =
        context.GetIntListCommonAttr("photo_colossus_item_id_list").value_or(default_list);
    auto photo_colossus_photo_id_list =
        context.GetIntListCommonAttr("photo_colossus_photo_id_list").value_or(default_list);
    auto photo_colossus_author_id_list =
        context.GetIntListCommonAttr("photo_colossus_author_id_list").value_or(default_list);
    auto photo_colossus_duration_list =
        context.GetIntListCommonAttr("photo_colossus_duration_list").value_or(default_list);
    auto photo_colossus_play_time_list =
        context.GetIntListCommonAttr("photo_colossus_play_time_list").value_or(default_list);
    auto photo_colossus_channel_list =
        context.GetIntListCommonAttr("photo_colossus_channel_list").value_or(default_list);
    auto photo_colossus_label_list =
        context.GetIntListCommonAttr("photo_colossus_label_list").value_or(default_list);
    auto photo_colossus_timestamp_list =
        context.GetIntListCommonAttr("photo_colossus_timestamp_list").value_or(default_list);
    auto photo_colossus_spu_id_list =
        context.GetIntListCommonAttr("photo_colossus_spu_id_list").value_or(default_list);
    auto photo_colossus_category_list =
        context.GetIntListCommonAttr("photo_colossus_category_list").value_or(default_list);

    auto get_topk_indices = context.GetIntListItemAttr("topk_indices_iav2_based");
    auto get_topk_values = context.GetDoubleListItemAttr("topk_values_iav2_based");

    auto set_merchant_video_iav2_based_gsu_pid =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_pid");
    auto set_merchant_video_iav2_based_gsu_aid =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_aid");
    auto set_merchant_video_iav2_based_gsu_iid =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_iid");
    auto set_merchant_video_iav2_based_gsu_duration =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_duration");
    auto set_merchant_video_iav2_based_gsu_playtime =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_playtime");
    auto set_merchant_video_iav2_based_gsu_spuid =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_spuid");
    auto set_merchant_video_iav2_based_gsu_channel =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_channel");
    auto set_merchant_video_iav2_based_gsu_label =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_label");
    auto set_merchant_video_iav2_based_gsu_cate1 =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_cate1");
    auto set_merchant_video_iav2_based_gsu_cate2 =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_cate2");
    auto set_merchant_video_iav2_based_gsu_cate3 =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_cate3");
    auto set_merchant_video_iav2_based_gsu_lagdays =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_lagdays");
    auto set_merchant_video_iav2_based_gsu_laghours =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_laghours");
    auto set_merchant_video_iav2_based_gsu_distance =
        context.SetIntListItemAttr("merchant_video_iav2_based_gsu_distance");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      std::vector<int64> merchant_video_iav2_based_gsu_pid;
      std::vector<int64> merchant_video_iav2_based_gsu_aid;
      std::vector<int64> merchant_video_iav2_based_gsu_iid;
      std::vector<int64> merchant_video_iav2_based_gsu_duration;
      std::vector<int64> merchant_video_iav2_based_gsu_playtime;
      std::vector<int64> merchant_video_iav2_based_gsu_spuid;
      std::vector<int64> merchant_video_iav2_based_gsu_channel;
      std::vector<int64> merchant_video_iav2_based_gsu_label;
      std::vector<int64> merchant_video_iav2_based_gsu_cate1;
      std::vector<int64> merchant_video_iav2_based_gsu_cate2;
      std::vector<int64> merchant_video_iav2_based_gsu_cate3;
      std::vector<int64> merchant_video_iav2_based_gsu_lagdays;
      std::vector<int64> merchant_video_iav2_based_gsu_laghours;
      std::vector<int64> merchant_video_iav2_based_gsu_distance;

      auto topk_indices = get_topk_indices(result).value_or(default_list);
      auto topk_values = get_topk_values(result).value_or(default_double_list);

      auto extract_category_levels = [](int64 category_value) {
          int64 cate1 = (category_value >> 32) & 0xFFFF;
          int64 cate2 = (category_value >> 16) & 0xFFFF;
          int64 cate3 = category_value & 0xFFFF;
          return std::make_tuple(cate1, cate2, cate3);
      };

      auto calculate_time_diffs = [](int64 req_time, int64 timestamp) {
          int64 delta_seconds = req_time / 1000 - timestamp;
          int64 lag_days = delta_seconds / (3600 * 24);
          int64 lag_hours = delta_seconds / 3600;
          return std::make_tuple(lag_days, lag_hours);
      };
      int64 len = photo_colossus_photo_id_list.size();

      for (int i = 0; i < topk_indices.size(); i++) {
        int64 index = topk_indices[i];
        if (index >=0 && index < len) {
          merchant_video_iav2_based_gsu_pid.emplace_back(photo_colossus_photo_id_list[index]);
          merchant_video_iav2_based_gsu_aid.emplace_back(photo_colossus_author_id_list[index]);
          merchant_video_iav2_based_gsu_iid.emplace_back(photo_colossus_item_id_list[index]);
          merchant_video_iav2_based_gsu_duration.emplace_back(photo_colossus_duration_list[index]);
          merchant_video_iav2_based_gsu_playtime.emplace_back(photo_colossus_play_time_list[index]);
          merchant_video_iav2_based_gsu_channel.emplace_back(photo_colossus_channel_list[index]);
          merchant_video_iav2_based_gsu_spuid.emplace_back(photo_colossus_spu_id_list[index]);
          merchant_video_iav2_based_gsu_label.emplace_back(photo_colossus_label_list[index]);
          auto [cate1, cate2, cate3] = extract_category_levels(photo_colossus_category_list[index]);
          merchant_video_iav2_based_gsu_cate1.emplace_back(cate1);
          merchant_video_iav2_based_gsu_cate2.emplace_back(cate2);
          merchant_video_iav2_based_gsu_cate3.emplace_back(cate3);
          auto [lag_days, lag_hours] = calculate_time_diffs(req_time, photo_colossus_timestamp_list[index]);
          merchant_video_iav2_based_gsu_lagdays.emplace_back(lag_days);
          merchant_video_iav2_based_gsu_laghours.emplace_back(lag_hours);
        }
      }
      int64 size = topk_indices.size();
      for (int i = 0; i < topk_values.size(); i++) {
        if (i < size) {
          if (topk_values[i] > 0.0)
              merchant_video_iav2_based_gsu_distance.emplace_back((int)floor(topk_values[i] * 1000));
        }
      }
      set_merchant_video_iav2_based_gsu_pid(result, merchant_video_iav2_based_gsu_pid);
      set_merchant_video_iav2_based_gsu_aid(result, merchant_video_iav2_based_gsu_aid);
      set_merchant_video_iav2_based_gsu_iid(result, merchant_video_iav2_based_gsu_iid);
      set_merchant_video_iav2_based_gsu_duration(result, merchant_video_iav2_based_gsu_duration);
      set_merchant_video_iav2_based_gsu_playtime(result, merchant_video_iav2_based_gsu_playtime);
      set_merchant_video_iav2_based_gsu_spuid(result, merchant_video_iav2_based_gsu_spuid);
      set_merchant_video_iav2_based_gsu_channel(result, merchant_video_iav2_based_gsu_channel);
      set_merchant_video_iav2_based_gsu_label(result, merchant_video_iav2_based_gsu_label);
      set_merchant_video_iav2_based_gsu_cate1(result, merchant_video_iav2_based_gsu_cate1);
      set_merchant_video_iav2_based_gsu_cate2(result, merchant_video_iav2_based_gsu_cate2);
      set_merchant_video_iav2_based_gsu_cate3(result, merchant_video_iav2_based_gsu_cate3);
      set_merchant_video_iav2_based_gsu_lagdays(result, merchant_video_iav2_based_gsu_lagdays);
      set_merchant_video_iav2_based_gsu_laghours(result, merchant_video_iav2_based_gsu_laghours);
      set_merchant_video_iav2_based_gsu_distance(result, merchant_video_iav2_based_gsu_distance);
    });
    return true;
  }

 private:
};

}  // namespace platform
}  // namespace ks
