#pragma once

#include <dirent.h>
#include <algorithm>
#include <cstddef>
#include <deque>
#include <map>
#include <sstream>
#include <string>
#include <string_view>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/module/common_reco_light_function.h"
#include "folly/String.h"
#include "third_party/abseil/absl/strings/numbers.h"

namespace ks {
namespace platform {

struct MallGoodsRetrievalItem {
  int64 id;
  float score;
  std::vector<std::pair<int64, float>> trigger_list;

  explicit MallGoodsRetrievalItem(int64 id) : id(id), score(0) {}
};

struct MallGoodsAggrItemList {
  int64 cate_id;
  double score;
  int dup_cnt;
  std::vector<std::pair<int64, float>> item_list;

  explicit MallGoodsAggrItemList(int64 cate_id) : cate_id(cate_id), score(0), dup_cnt(0) {}
};


class MerchantShoppingMallLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  MerchantShoppingMallLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CalcOrderedUserSeqAggOrderAttr);
    REGISTER_LIGHT_FUNCTION(CalcInvaildWordsFlag);
    REGISTER_LIGHT_FUNCTION(CalcIsAddressDiscount);
    REGISTER_LIGHT_FUNCTION(CalcSiftIdSupplyFlag);
    REGISTER_LIGHT_FUNCTION(CutColossusClickAttrListWithTimestamp);
    REGISTER_LIGHT_FUNCTION(SetChongDingXiangItemAttr);
    REGISTER_LIGHT_FUNCTION(SplitBySeparatorToList);
    REGISTER_LIGHT_FUNCTION(SplitBySeparatorsToMap);
    REGISTER_LIGHT_FUNCTION(CalcItemContentEmb);
    REGISTER_LIGHT_FUNCTION(PrerankGpmCal);
    REGISTER_LIGHT_FUNCTION(PrerankLinearCal);
    REGISTER_LIGHT_FUNCTION(SetShowAndNotclickTimesAttr);
    REGISTER_LIGHT_FUNCTION(CalculateUserExpressFee);
    REGISTER_LIGHT_FUNCTION(ItemMmuCateResultSplit);
    REGISTER_LIGHT_FUNCTION(GetSampleListCommonAttrKey);
    REGISTER_LIGHT_FUNCTION(FillReasonLevelKey);
    REGISTER_LIGHT_FUNCTION(GenerateMmuCateRedisKey);
    REGISTER_LIGHT_FUNCTION(ParseColossusToC64AndLc);
    REGISTER_LIGHT_FUNCTION(SetMmuCid3KeyFlag);
    REGISTER_LIGHT_FUNCTION(SetGcfRecallFlag);
    REGISTER_LIGHT_FUNCTION(GenerateClickAttrs);
    REGISTER_LIGHT_FUNCTION(GenerateGoodsDiversityFlag);
    REGISTER_LIGHT_FUNCTION(GenerateCateLastPayLag);
    REGISTER_LIGHT_FUNCTION(GenerateGoodsExploreFlag);
    REGISTER_LIGHT_FUNCTION(AuditionDiversityRerank);
    REGISTER_LIGHT_FUNCTION(I2IRetrievalRerank);
    REGISTER_LIGHT_FUNCTION(ModifyGoodsPriceByCoupon);
    REGISTER_LIGHT_FUNCTION(SelectGroupRoughBoost);
    REGISTER_LIGHT_FUNCTION(ReplaceToValidValue);
    REGISTER_LIGHT_FUNCTION(ItemPriceFeaCalc);
    REGISTER_LIGHT_FUNCTION(ItemUnionTags);
    REGISTER_LIGHT_FUNCTION(BHPrerankCateInsertNumCal);
    REGISTER_LIGHT_FUNCTION(DedupColossusAttrListWithItemid);
    REGISTER_LIGHT_FUNCTION(TruncateColossusOrderAttrList);
    REGISTER_LIGHT_FUNCTION(ComputeUserSlicedCpqidList);
    REGISTER_LIGHT_FUNCTION(ComputeUserHistoryCidPriceQuantile);
    REGISTER_LIGHT_FUNCTION(ComputeItemCidPriceQuantile);
    REGISTER_LIGHT_FUNCTION(SliceCidSearchUserCidPriceQuantile);
    REGISTER_LIGHT_FUNCTION(UserItemCpqidMatch);
    REGISTER_LIGHT_FUNCTION(LastBehaviorTsBeforeRequest);
    REGISTER_LIGHT_FUNCTION(GetVhListFlagFromPtr);
    REGISTER_LIGHT_FUNCTION(SwingP2IRetrRerank);
    REGISTER_LIGHT_FUNCTION(GenerateTriggerInfos);
    REGISTER_LIGHT_FUNCTION(MerchantPhotoListGen);
    REGISTER_LIGHT_FUNCTION(SetMatchGroupIdFlag);
    REGISTER_LIGHT_FUNCTION(SetItemStyleFilterFlag);
    REGISTER_LIGHT_FUNCTION(TriggerListInfos);
    REGISTER_LIGHT_FUNCTION(GetItemActionStep5);
    REGISTER_LIGHT_FUNCTION(ParseCalibrationScore);
    REGISTER_LIGHT_FUNCTION(ListwiseAllGpmScore);
    REGISTER_LIGHT_FUNCTION(ClkI2iTriggerQuotaReassign);
    REGISTER_LIGHT_FUNCTION(ClickUserHistoryBucketize);
    REGISTER_LIGHT_FUNCTION(OrderUserHistoryBucketize);
    REGISTER_LIGHT_FUNCTION(ItemPriceBucketize);
    REGISTER_LIGHT_FUNCTION(ContrastU2U2IRecallList);
    REGISTER_LIGHT_FUNCTION(ItemsWithCidMatch);
    REGISTER_LIGHT_FUNCTION(ParseUserShowButNotClick);
    REGISTER_LIGHT_FUNCTION(ParseStringUint64VectorMap);
    REGISTER_LIGHT_FUNCTION(GetSameGoodsExtend);
    REGISTER_LIGHT_FUNCTION(RoughRankCalLiveItemCat);
    REGISTER_LIGHT_FUNCTION(ShopScorefilter);
    REGISTER_LIGHT_FUNCTION(GetUserLiveShowCalLiveItemCat);
    REGISTER_LIGHT_FUNCTION(GetAddressMatchflag);
    REGISTER_LIGHT_FUNCTION(GenCtrPromoteFlag);
    REGISTER_LIGHT_FUNCTION(GenPricePowerPromoteFlag);
    REGISTER_LIGHT_FUNCTION(UnpackSpuEmbdResponse);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseIntListItemAttr);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseDoubleListItemAttr);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseIntListCommonAttr);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseDoubleListCommonAttr);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseIntListCommonAttrWithCnt);
    REGISTER_LIGHT_FUNCTION(UnpackListwiseDoubleListCommonAttrWithCnt);
    REGISTER_LIGHT_FUNCTION(ItemIntListAttrLen);
    REGISTER_LIGHT_FUNCTION(ItemDoubleListAttrLen);
    REGISTER_LIGHT_FUNCTION(ItemIntListAttrMaxCountEntity);
    REGISTER_LIGHT_FUNCTION(TransNoNaturaGoodPos);
    REGISTER_LIGHT_FUNCTION(MixTransCategory);
    REGISTER_LIGHT_FUNCTION(MixTransPrevsCategory);
    REGISTER_LIGHT_FUNCTION(MixVariantAfterGenerator);
    REGISTER_LIGHT_FUNCTION(ItemsWithAttrMatch);
    REGISTER_LIGHT_FUNCTION(GenerateCateFilterFlag);
    REGISTER_LIGHT_FUNCTION(GenPotentialBoostRedisAuthorKey);
    REGISTER_LIGHT_FUNCTION(WelfareBoostBonusEnrichRedisKeys);
    REGISTER_LIGHT_FUNCTION(MmuCateRedisKey);
    REGISTER_LIGHT_FUNCTION(MmuCid3RebuyRate);
    REGISTER_LIGHT_FUNCTION(Cid2Cid3ImpCnt);
    REGISTER_LIGHT_FUNCTION(GenClickInfo);
    REGISTER_LIGHT_FUNCTION(GenRealTimeInfo);
    REGISTER_LIGHT_FUNCTION(Split_i2i_list);
    REGISTER_LIGHT_FUNCTION(TriggerListInfosRealtimePrice);
    REGISTER_LIGHT_FUNCTION(KeyIntListValueIntListMatch);
    REGISTER_LIGHT_FUNCTION(GenClkBuyItemCate3StatInfo);
    REGISTER_LIGHT_FUNCTION(GetMMCntList);
    REGISTER_LIGHT_FUNCTION(HashCPVSearch);
    REGISTER_LIGHT_FUNCTION(GenSimKey);
    REGISTER_LIGHT_FUNCTION(QuantizeFrDistanceBuy);
    REGISTER_LIGHT_FUNCTION(ComputeLenBuy);
    REGISTER_LIGHT_FUNCTION(QuantizeFrDistanceRs);
    REGISTER_LIGHT_FUNCTION(ComputeLenRs);
    REGISTER_LIGHT_FUNCTION(QuantizeFrDistanceClick);
    REGISTER_LIGHT_FUNCTION(ComputeLenClick);
    REGISTER_LIGHT_FUNCTION(HashCPVSearchClick);
    REGISTER_LIGHT_FUNCTION(HashCPVSearchClickV2);
    REGISTER_LIGHT_FUNCTION(ParseAdOwnerInfo);
    REGISTER_LIGHT_FUNCTION(TransMixRankPrevSessionEntityList);
    REGISTER_LIGHT_FUNCTION(TimestampListSort);
    REGISTER_LIGHT_FUNCTION(FloatVec2DoubleList);
    REGISTER_LIGHT_FUNCTION(DoubleList2FloatVec);
  }
  static bool FloatVec2DoubleList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    const auto *pxtr_value_attr_ptr =
        context.GetPtrCommonAttr<const std::vector<float>>("pxtr_value_attr_float_vec_ptr");
    if (!pxtr_value_attr_ptr) {
      LOG(INFO) << "pxtr_value_attr_float_vec_ptr is nullptr";
      return true;
    }
    std::vector<double> double_list;
    double_list.reserve(pxtr_value_attr_ptr->size());
    for (auto x : *pxtr_value_attr_ptr) {
      double_list.emplace_back(x);
    }
    context.SetDoubleListCommonAttr("pxtr_value_attr_double_list", std::move(double_list));
    return true;
  }

  static bool DoubleList2FloatVec(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto pxtr_value_attr_double_list = context.GetDoubleListCommonAttr("pxtr_value_attr_double_list");
    if (!pxtr_value_attr_double_list) return true;
    thread_local std::vector<float> float_vec;
    float_vec.clear();
    float_vec.reserve(pxtr_value_attr_double_list->size());
    for (auto x : *pxtr_value_attr_double_list) {
      float_vec.emplace_back(x);
    }
    context.SetPtrCommonAttr<const std::vector<float> *>("pxtr_value_attr_float_vec_ptr", &float_vec);
    return true;
  }

  static bool TimestampListSort(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto input_item_id_list = context.GetIntListCommonAttr("origin_colossus_rs_item_id_list_ori")
                                  .value_or(absl::Span<const int64>());
    auto input_rs_timestamp_list = context.GetIntListCommonAttr("origin_colossus_rs_timestamp_list_ori")
                                       .value_or(absl::Span<const int64>());
    auto compare = [&](int a, int b) {
      return input_rs_timestamp_list.at(a) > input_rs_timestamp_list.at(b);
    };
    int64 colossus_size = std::min(input_item_id_list.size(), input_rs_timestamp_list.size());
    std::vector<int64> indices(colossus_size);
    std::iota(indices.begin(), indices.end(), 0);
    std::sort(indices.begin(), indices.end(), compare);
    context.SetIntListCommonAttr("origin_colossus_rs_timestamp_sorted_idx_list", std::move(indices));
    return true;
  }

  static bool TriggerListInfosRealtimePrice(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto click_item_id = context.GetIntListCommonAttr("click_item_id").value_or(absl::Span<const int64>());
    auto click_detail_page_view_time =
        context.GetIntListCommonAttr("click_detail_page_view_time").value_or(absl::Span<const int64>());
    auto click_item_lag = context.GetIntListCommonAttr("click_item_lag").value_or(absl::Span<const int64>());
    auto click_c2 = context.GetIntListCommonAttr("click_c2").value_or(absl::Span<const int64>());
    auto click_c3 = context.GetIntListCommonAttr("click_c3").value_or(absl::Span<const int64>());
    auto click_author_id =
        context.GetIntListCommonAttr("click_author_id").value_or(absl::Span<const int64>());
    auto click_item_price =
        context.GetDoubleListCommonAttr("click_item_price").value_or(absl::Span<const double>());
    auto guess_like_trigger_selection_limit =
        context.GetIntCommonAttr("guess_like_trigger_selection_limit").value_or(0);
    auto click_item_lag_min =
        context.GetIntListCommonAttr("click_item_lag_min").value_or(absl::Span<const int64>());
    auto guess_like_trigger_selection_sort_style =
        context.GetStringCommonAttr("guess_like_trigger_selection_sort_style").value_or("mul");
    auto guess_like_trigger_selection_time_style =
        context.GetStringCommonAttr("guess_like_trigger_selection_time_style").value_or("day");
    auto guess_like_trigger_selection_use_price =
        context.GetIntCommonAttr("guess_like_trigger_selection_use_price").value_or(0);
    auto guess_like_trigger_selection_use_emp =
        context.GetIntCommonAttr("guess_like_trigger_selection_use_emp").value_or(0);
    auto trigger_item_emp_score_list_for_map =
        context.GetDoubleListCommonAttr("trigger_item_emp_score_list_for_map")
            .value_or(absl::Span<const double>());
    auto trigger_selection_item_list_for_map =
        context.GetIntListCommonAttr("trigger_selection_item_list_for_map")
            .value_or(absl::Span<const int64>());

    struct ItemOldKey {
      int64 item_key;
      double score;
      int64 item_id;
      double time_score;
      double page_view_score;
      double price;
      double emp_score;
      int64 item_lag;
      int64 c2;
      int64 c3;
      int64 author;
      ItemOldKey(int64 key, double s, int64 id, double ts, double pvs, double pri, double emp, int64 lag,
                 int64 cid2, int64 cid3, int64 auth)
          : item_key(key)
          , score(s)
          , item_id(id)
          , time_score(ts)
          , page_view_score(pvs)
          , price(pri)
          , emp_score(emp)
          , item_lag(lag)
          , c2(cid2)
          , c3(cid3)
          , author(auth) {}
      ItemOldKey(int64 key, double s, int64 id, double ts, double pvs, int64 lag, int64 cid2, int64 cid3,
                 int64 auth)
          : item_key(key)
          , score(s)
          , item_id(id)
          , time_score(ts)
          , page_view_score(pvs)
          , item_lag(lag)
          , c2(cid2)
          , c3(cid3)
          , author(auth) {}
    };

    std::vector<ItemOldKey> item_keys;
    double time_score = 0.0;
    double decay = 0.5;
    int64 offset = 1;
    double scale = 5.0;
    double lamda = std::log(decay) / scale;
    int64 page_view = 10;
    int64 limit = guess_like_trigger_selection_limit;
    int64 zero = 0;
    folly::F14FastMap<int64, double> trigger_item_emp_score_map;
    int64 len_score = trigger_item_emp_score_list_for_map.size();
    int64 len_list = trigger_selection_item_list_for_map.size();
    if (len_score != 0 && len_list != 0) {
      // 合并成字典用于映射
      for (auto i = 0; i < std::min(len_score, len_list); ++i) {
        trigger_item_emp_score_map[trigger_selection_item_list_for_map[i]] =
            trigger_item_emp_score_list_for_map[i];
      }
    }

    int64 item_num = click_item_id.size();
    for (size_t i = 0; i < item_num; ++i) {
      page_view = 10;
      if (click_detail_page_view_time[i] < 10) {
        page_view = click_detail_page_view_time[i];
      }
      double page_view_score = std::log(2 + page_view);
      if (guess_like_trigger_selection_time_style == "min") {
        time_score = std::exp(lamda * std::max(zero, click_item_lag_min[i] - offset));
      } else {
        time_score = std::exp(lamda * std::max(zero, click_item_lag[i] - offset));
      }
      if (guess_like_trigger_selection_use_price == 1) {
        item_keys.emplace_back(ItemOldKey(i, 0.0, click_item_id[i], time_score, page_view_score,
                                          click_item_price[i], 0.0, click_item_lag[i], click_c2[i],
                                          click_c3[i], click_author_id[i]));
      } else if (guess_like_trigger_selection_use_emp == 1) {
        if (trigger_item_emp_score_map.find(click_item_id[i]) != trigger_item_emp_score_map.end()) {
          item_keys.emplace_back(ItemOldKey(i, 0.0, click_item_id[i], time_score, page_view_score, 0.0,
                                            trigger_item_emp_score_map[click_item_id[i]], click_item_lag[i],
                                            click_c2[i], click_c3[i], click_author_id[i]));
        } else {
          item_keys.emplace_back(ItemOldKey(i, 0.0, click_item_id[i], time_score, page_view_score, 0.0, 0.0,
                                            click_item_lag[i], click_c2[i], click_c3[i], click_author_id[i]));
        }
      } else {
        item_keys.emplace_back(ItemOldKey(i, 0.0, click_item_id[i], time_score, page_view_score,
                                          click_item_lag[i], click_c2[i], click_c3[i], click_author_id[i]));
      }
    }
    std::vector<std::string> sort_factors = {"time_score", "page_view_score"};
    if (guess_like_trigger_selection_use_price == 1) {
      sort_factors.emplace_back("price");
    } else if (guess_like_trigger_selection_use_emp == 1) {
      sort_factors.emplace_back("emp_score");
    }

    if (guess_like_trigger_selection_sort_style == "es") {
      folly::F14FastMap<int64, std::vector<int64>> sort_factor_ranks;
      for (const auto &key : item_keys) {
        sort_factor_ranks[key.item_key] = std::vector<int64>();
      }
      for (auto i = 0; i < sort_factors.size(); ++i) {
        if (sort_factors[i] == "time_score") {
          std::sort(item_keys.begin(), item_keys.end(),
                    [](const ItemOldKey &a, const ItemOldKey &b) { return a.time_score > b.time_score; });
        } else if (sort_factors[i] == "page_view_score") {
          std::sort(item_keys.begin(), item_keys.end(), [](const ItemOldKey &a, const ItemOldKey &b) {
            return a.page_view_score > b.page_view_score;
          });
        } else if (sort_factors[i] == "price") {
          std::sort(item_keys.begin(), item_keys.end(),
                    [](const ItemOldKey &a, const ItemOldKey &b) { return a.price > b.price; });
        } else {
          std::sort(item_keys.begin(), item_keys.end(),
                    [](const ItemOldKey &a, const ItemOldKey &b) { return a.emp_score > b.emp_score; });
        }
        for (auto j = 0; j < item_keys.size(); ++j) {
          sort_factor_ranks[item_keys[j].item_key].emplace_back(j + 1);
        }
      }
      for (auto i = 0; i < item_keys.size(); ++i) {
        std::vector<int64> item_vec = sort_factor_ranks[item_keys[i].item_key];
        for (auto j = 0; j < item_vec.size(); ++j) {
          double es_score = 1.0 - item_vec[j] / item_num;
          item_keys[i].score += es_score;
        }
      }
    } else {
      for (auto i = 0; i < item_keys.size(); ++i) {
        item_keys[i].score = 1.0;
        for (auto j = 0; j < sort_factors.size(); ++j) {
          std::string factor = sort_factors[j];
          double score_item = 0.0;
          if (factor == "time_score") {
            score_item = item_keys[i].time_score;
          } else if (factor == "page_view_score") {
            score_item = item_keys[i].page_view_score;
          } else if (factor == "price") {
            score_item = item_keys[i].price;
          } else {
            score_item = item_keys[i].emp_score;
          }
          item_keys[i].score *= score_item;
        }
      }
    }
    std::sort(item_keys.begin(), item_keys.end(),
              [](const ItemOldKey &a, const ItemOldKey &b) { return a.score > b.score; });

    folly::F14FastMap<int64, int64> item_id_exists;
    std::vector<int64> trigger_selection_item_list;
    std::vector<int64> trigger_selection_item_lags;
    std::vector<double> trigger_selection_score_list;
    std::vector<int64> trigger_selection_cid2;
    std::vector<int64> trigger_selection_cid3;
    std::vector<int64> trigger_selection_author_id;
    trigger_selection_item_list.reserve(item_keys.size());
    trigger_selection_item_lags.reserve(item_keys.size());
    trigger_selection_score_list.reserve(item_keys.size());
    trigger_selection_cid2.reserve(item_keys.size());
    trigger_selection_cid3.reserve(item_keys.size());
    trigger_selection_author_id.reserve(item_keys.size());
    int64 item_id_temp = 0;
    item_num = 1;
    for (const auto &item : item_keys) {
      if (item_num > limit) {
        break;
      }
      item_id_temp = item.item_id;
      if (item_id_exists.find(item_id_temp) != item_id_exists.end()) {
        continue;
      }
      item_id_exists[item_id_temp] = 1;
      trigger_selection_item_list.emplace_back(item.item_id);
      trigger_selection_item_lags.emplace_back(item.item_lag);
      trigger_selection_score_list.emplace_back(item.score);
      trigger_selection_cid2.emplace_back(item.c2);
      trigger_selection_cid3.emplace_back(item.c3);
      trigger_selection_author_id.emplace_back(item.author);
      item_num++;
    }
    context.SetIntListCommonAttr("trigger_selection_item_list", std::move(trigger_selection_item_list));
    context.SetDoubleListCommonAttr("trigger_selection_score_list", std::move(trigger_selection_score_list));
    context.SetIntListCommonAttr("trigger_selection_cid2", std::move(trigger_selection_cid2));
    context.SetIntListCommonAttr("trigger_selection_cid3", std::move(trigger_selection_cid3));
    context.SetIntListCommonAttr("trigger_selection_author_id", std::move(trigger_selection_author_id));
    return true;
  }

  static bool Split_i2i_list(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto sim_i2i_list_kv_attr = context.GetStringListItemAttr("sim_i2i_list_kv");
    auto sim_i2i_retrieval_id_list_set = context.SetStringListItemAttr("sim_i2i_retrieval_id_list");
    auto sim_i2i_retrieval_score_list_set = context.SetStringListItemAttr("sim_i2i_retrieval_score_list");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto sim_i2i_list_kv = sim_i2i_list_kv_attr(result);
      int length = (*sim_i2i_list_kv).size();
      if (length == 0) {
        return;
      }
      std::vector<std::string> sim_i2i_retrieval_id_list;
      std::vector<std::string> sim_i2i_retrieval_score_list;
      for (auto i = 0; i < length; i++) {
        absl::string_view item_strview = (*sim_i2i_list_kv)[i];
        std::string item_str(item_strview.data(), item_strview.size());
        std::stringstream ss(item_str);
        std::string s_tmp;
        std::vector<std::string> s_list_tmp;
        while (std::getline(ss, s_tmp, ':')) {
          s_list_tmp.emplace_back(s_tmp);
        }
        if (s_list_tmp.size() == 2) {
          sim_i2i_retrieval_id_list.emplace_back(s_list_tmp[0]);
          sim_i2i_retrieval_score_list.emplace_back(s_list_tmp[1]);
        }
      }
      sim_i2i_retrieval_id_list_set(result, sim_i2i_retrieval_id_list);
      sim_i2i_retrieval_score_list_set(result, sim_i2i_retrieval_score_list);
    });
    return true;
  }

  static bool GenRealTimeInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto cal_diff_price = [&](int ori_price, int price) -> int {
      if (ori_price == 0) {
        return 0;
      }
      int slice = 100;
      int res = floor((double)(ori_price - price) * slice / (double)ori_price);
      if (res < 0) {
        res = 0;
      }
      if (res > 100) {
        res = 100;
      }
      return res;
    };

    auto price_bucket = [&](double price) {
      std::vector<double> bucketRanges = {1.86,   3.08,  4.03,  4.9,   6.62, 7.76,  9.92,  13.6,   16.63,
                                          19.72,  25.37, 29.66, 36.65, 41.8, 59.68, 79.52, 119.83, 273.93,
                                          499.72, 800,   1000,  2000,  3000, 5000,  8000,  10000};
      auto numBuckets = bucketRanges.size();
      auto bucket_ind = numBuckets - 1;
      for (auto i = 0; i < numBuckets; ++i) {
        if (price <= bucketRanges[i]) {
          bucket_ind = i;
          break;
        }
      }
      return bucket_ind + 1;
    };

    auto ts = context.GetIntCommonAttr("_REQ_TIME_").value_or(0) / 1000;
    auto click_item_id_180d = context.GetIntListCommonAttr("click_item_id_180d");
    auto click_item_timestamp_180d = context.GetIntListCommonAttr("click_item_timestamp_180d");
    auto click_category_180d = context.GetIntListCommonAttr("click_category_180d");
    auto click_author_id_180d = context.GetIntListCommonAttr("click_author_id_180d");
    auto click_price_180d = context.GetIntListCommonAttr("click_price_180d");
    auto click_detail_page_view_time_180d = context.GetIntListCommonAttr("click_detail_page_view_time_180d");
    auto click_origin_price_180d = context.GetIntListCommonAttr("click_origin_price_180d");
    auto click_from_180d = context.GetIntListCommonAttr("click_from_180d");
    auto click_flow_type_180d = context.GetIntListCommonAttr("click_flow_type_180d");
    auto click_label_180d = context.GetIntListCommonAttr("click_label_180d");
    auto click_real_seller_id_180d = context.GetIntListCommonAttr("click_real_seller_id_180d");
    std::vector<int64> realtime_bsr_ts, realtime_bsr_iid, realtime_bsr_lag, realtime_bsr_c1, realtime_bsr_c2,
        realtime_bsr_c3, realtime_bsr_seller, realtime_bsr_price, realtime_bsr_stay_time, realtime_bsr_rank,
        realtime_bsr_click_from, realtime_bsr_origin_price, realtime_bsr_carry_type, realtime_bsr_click_type,
        realtime_bsr_price_diff, realtime_bsr_label, realtime_bsr_real_seller_id, realtime_bsr_price_bucket,
        realtime_lc_iid, realtime_lc_lag, realtime_lc_c1, realtime_lc_c2, realtime_lc_c3, realtime_lc_seller;

    auto bsr_rank = 1;

    int length_click_item_id_180d = click_item_id_180d ? (*click_item_id_180d).size() : 0;
    int length_click_item_timestamp_180d =
        click_item_timestamp_180d ? (*click_item_timestamp_180d).size() : 0;
    int length_click_category_180d = click_category_180d ? (*click_category_180d).size() : 0;
    int length_click_author_id_180d = click_author_id_180d ? (*click_author_id_180d).size() : 0;
    int length_click_price_180d = click_price_180d ? (*click_price_180d).size() : 0;
    int length_click_detail_page_view_time_180d =
        click_detail_page_view_time_180d ? (*click_detail_page_view_time_180d).size() : 0;
    int length_click_origin_price_180d = click_origin_price_180d ? (*click_origin_price_180d).size() : 0;
    int length_click_from_180d = click_from_180d ? (*click_from_180d).size() : 0;
    int length_click_flow_type_180d = click_flow_type_180d ? (*click_flow_type_180d).size() : 0;
    int length_click_label_180d = click_label_180d ? (*click_label_180d).size() : 0;
    int length_click_real_seller_id_180d =
        click_real_seller_id_180d ? (*click_real_seller_id_180d).size() : 0;
    if (length_click_item_id_180d != 0 && length_click_item_id_180d == length_click_item_timestamp_180d &&
        length_click_item_id_180d == length_click_category_180d &&
        length_click_item_id_180d == length_click_author_id_180d &&
        length_click_item_id_180d == length_click_price_180d &&
        length_click_item_id_180d == length_click_detail_page_view_time_180d &&
        length_click_item_id_180d == length_click_origin_price_180d &&
        length_click_item_id_180d == length_click_from_180d &&
        length_click_item_id_180d == length_click_flow_type_180d &&
        length_click_item_id_180d == length_click_label_180d &&
        length_click_item_id_180d == length_click_real_seller_id_180d) {
      for (auto i = 0; i < length_click_item_id_180d; ++i) {
        if (realtime_bsr_iid.size() >= 250) {
          break;
        }
        auto click_item_timestamp_180d_attr = (*click_item_timestamp_180d)[i];
        auto click_item_id_180d_attr = (*click_item_id_180d)[i];
        auto click_price_180d_attr = (*click_price_180d)[i];
        auto click_origin_price_180d_attr = (*click_origin_price_180d)[i];
        auto click_category_180d_attr = (*click_category_180d)[i];
        auto click_author_id_180d_attr = (*click_author_id_180d)[i];
        auto click_detail_page_view_time_180d_attr = (*click_detail_page_view_time_180d)[i];
        auto click_flow_type_180d_attr = (*click_flow_type_180d)[i];
        auto click_from_180d_attr = (*click_from_180d)[i];
        auto click_label_180d_attr = (*click_label_180d)[i];
        auto click_real_seller_id_180d_attr = (*click_real_seller_id_180d)[i];
        if (realtime_bsr_iid.size() < 64) {
          realtime_bsr_ts.emplace_back(click_item_timestamp_180d_attr);
          realtime_bsr_iid.emplace_back(click_item_id_180d_attr);
          realtime_bsr_lag.emplace_back(floor((double)(ts - click_item_timestamp_180d_attr) / (3600 * 24)));
          realtime_bsr_c1.emplace_back((click_category_180d_attr >> 48) & 0xffff);
          realtime_bsr_c2.emplace_back((click_category_180d_attr >> 32) & 0xffff);
          realtime_bsr_c3.emplace_back((click_category_180d_attr >> 16) & 0xffff);
          realtime_bsr_seller.emplace_back(click_author_id_180d_attr);
          realtime_bsr_price.emplace_back(floor(log((double)click_price_180d_attr / 100 + 1)));
          realtime_bsr_stay_time.emplace_back(click_detail_page_view_time_180d_attr);
          realtime_bsr_rank.emplace_back(bsr_rank);

          realtime_bsr_origin_price.emplace_back(floor(log((double)click_origin_price_180d_attr / 100 + 1)));
          realtime_bsr_price_diff.emplace_back(
              cal_diff_price(click_origin_price_180d_attr, click_price_180d_attr));
          realtime_bsr_click_type.emplace_back((click_flow_type_180d_attr >> 24) & 0xff);
          realtime_bsr_carry_type.emplace_back((click_flow_type_180d_attr >> 16) & 0xff);
          realtime_bsr_click_from.emplace_back(click_from_180d_attr);
          realtime_bsr_label.emplace_back(click_label_180d_attr);
          realtime_bsr_price_bucket.emplace_back(price_bucket(click_price_180d_attr / 100.0));
          realtime_bsr_real_seller_id.emplace_back(click_real_seller_id_180d_attr);

          ++bsr_rank;
        }
        realtime_lc_iid.emplace_back(click_item_id_180d_attr);
        realtime_lc_lag.emplace_back(floor((double)(ts - click_item_timestamp_180d_attr) / (3600 * 24)));
        realtime_lc_c1.emplace_back((click_category_180d_attr >> 48) & 0xffff);
        realtime_lc_c2.emplace_back((click_category_180d_attr >> 32) & 0xffff);
        realtime_lc_c3.emplace_back((click_category_180d_attr >> 16) & 0xffff);
        realtime_lc_seller.emplace_back(click_author_id_180d_attr);
      }
    }
    int realtime_bsr_length = realtime_bsr_iid.size();

    context.SetIntListCommonAttr("realtime_c64_ts", std::move(realtime_bsr_ts));
    context.SetIntListCommonAttr("realtime_c64_iid", std::move(realtime_bsr_iid));
    context.SetIntListCommonAttr("realtime_c64_lag", std::move(realtime_bsr_lag));
    context.SetIntListCommonAttr("realtime_c64_c1", std::move(realtime_bsr_c1));
    context.SetIntListCommonAttr("realtime_c64_c2", std::move(realtime_bsr_c2));
    context.SetIntListCommonAttr("realtime_c64_c3", std::move(realtime_bsr_c3));
    context.SetIntListCommonAttr("realtime_c64_seller", std::move(realtime_bsr_seller));
    context.SetIntListCommonAttr("realtime_c64_price", std::move(realtime_bsr_price));
    context.SetIntListCommonAttr("realtime_c64_stay_time", std::move(realtime_bsr_stay_time));
    context.SetIntListCommonAttr("realtime_c64_rank", std::move(realtime_bsr_rank));
    context.SetIntCommonAttr("realtime_c64_length", std::move(realtime_bsr_length));
    context.SetIntListCommonAttr("realtime_c64_click_from", std::move(realtime_bsr_click_from));
    context.SetIntListCommonAttr("realtime_c64_origin_price", std::move(realtime_bsr_origin_price));
    context.SetIntListCommonAttr("realtime_c64_carry_type", std::move(realtime_bsr_carry_type));
    context.SetIntListCommonAttr("realtime_c64_click_type", std::move(realtime_bsr_click_type));
    context.SetIntListCommonAttr("realtime_c64_price_diff", std::move(realtime_bsr_price_diff));
    context.SetIntListCommonAttr("realtime_c64_label", std::move(realtime_bsr_label));
    context.SetIntListCommonAttr("realtime_c64_real_seller_id", std::move(realtime_bsr_real_seller_id));
    context.SetIntListCommonAttr("realtime_c64_price_bucket", std::move(realtime_bsr_price_bucket));
    context.SetIntListCommonAttr("realtime_lc_iid", std::move(realtime_lc_iid));
    context.SetIntListCommonAttr("realtime_lc_lag", std::move(realtime_lc_lag));
    context.SetIntListCommonAttr("realtime_lc_c1", std::move(realtime_lc_c1));
    context.SetIntListCommonAttr("realtime_lc_c2", std::move(realtime_lc_c2));
    context.SetIntListCommonAttr("realtime_lc_c3", std::move(realtime_lc_c3));
    context.SetIntListCommonAttr("realtime_lc_seller", std::move(realtime_lc_seller));

    return true;
  }

  static bool GenClickInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
    auto request_ts = context.GetDoubleCommonAttr("request_ts").value_or(0);
    auto click_item_timestamp_180d = context.GetIntListCommonAttr("click_item_timestamp_180d");
    auto click_category_180d = context.GetIntListCommonAttr("click_category_180d");
    auto click_price_180d = context.GetIntListCommonAttr("click_price_180d");
    auto click_hot_cat2_recall_time = context.GetIntCommonAttr("click_hot_cat2_recall_time");
    auto click_hot_cat2_recall_click_threshold =
        context.GetIntCommonAttr("click_hot_cat2_recall_click_threshold");
    std::vector<int64> click_c1, click_c2, click_c3, click_cat2_id_list, click_item_lag, click_price_bucket,
        click_item_lag_min;
    std::vector<double> click_item_price;
    folly::F14FastMap<int64, int64> cat2_map;
    int length_item_timestamp = click_item_timestamp_180d ? (*click_item_timestamp_180d).size() : 0;
    int length_category = click_category_180d ? (*click_category_180d).size() : 0;
    int length_price = click_price_180d ? (*click_price_180d).size() : 0;
    if (length_item_timestamp != 0 && length_item_timestamp == length_category &&
        length_item_timestamp == length_price) {
      for (auto i = 0; i < length_item_timestamp; ++i) {
        auto click_item_timestamp = (*click_item_timestamp_180d)[i];
        auto click_category = (*click_category_180d)[i];
        auto click_price = (*click_price_180d)[i];
        click_item_lag.emplace_back(floor((double)(request_ts - click_item_timestamp) / (3600 * 24)));
        click_item_lag_min.emplace_back(floor((double)(request_ts - click_item_timestamp) / 60));
        click_c1.emplace_back((click_category >> 48) & 0xffff);
        click_c2.emplace_back((click_category >> 32) & 0xffff);
        click_c3.emplace_back((click_category >> 16) & 0xffff);
        click_price_bucket.emplace_back(floor(log((double)click_price / 100.0 + 1)));
        click_item_price.emplace_back(click_price / 100.0);
        if (click_item_timestamp >= click_hot_cat2_recall_time) {
          auto cat2 = (click_category >> 32) & 0xffff;
          if (cat2_map.find(cat2) == cat2_map.end()) {
            cat2_map[cat2] = 1;
          } else {
            ++cat2_map[cat2];
          }
          if (cat2_map[cat2] >= click_hot_cat2_recall_click_threshold) {
            click_cat2_id_list.emplace_back(cat2);
          }
        }
      }
    }
    context.SetIntListCommonAttr("click_item_lag", std::move(click_item_lag));
    context.SetIntListCommonAttr("click_c1", std::move(click_c1));
    context.SetIntListCommonAttr("click_c2", std::move(click_c2));
    context.SetIntListCommonAttr("click_c3", std::move(click_c3));
    context.SetIntListCommonAttr("click_price_bucket", std::move(click_price_bucket));
    context.SetIntListCommonAttr("click_cat2_id_list", std::move(click_cat2_id_list));
    context.SetIntListCommonAttr("click_item_lag_min", std::move(click_item_lag_min));
    context.SetDoubleListCommonAttr("click_item_price", std::move(click_item_price));

    return true;
  }

  static bool GetMMCntList(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_item_code_id = context.GetIntListItemAttr("item_code_id");
      auto click_code_list = context.GetIntListCommonAttr("click_code_list");
      auto click_timestamp = context.GetIntListCommonAttr("click_timestamp");
      auto order_code_list = context.GetIntListCommonAttr("order_code_list");
      auto order_timestamp = context.GetIntListCommonAttr("order_timestamp");
      auto request_time = context.GetIntCommonAttr("request_time").value_or(0);

      auto item_cd1 = context.SetIntItemAttr("item_cd1");
      auto item_cd2 = context.SetIntItemAttr("item_cd2");
      auto item_cd3 = context.SetIntItemAttr("item_cd3");
      auto item_cd4 = context.SetIntItemAttr("item_cd4");
      auto item_cd = context.SetIntItemAttr("item_cd");

      auto clk_c1_mcnt_times = context.SetIntListItemAttr("clk_c1_mcnt_times");
      auto clk_c1_mcnt_short = context.SetIntListItemAttr("clk_c1_mcnt_short");
      auto clk_c1_mcnt_long = context.SetIntListItemAttr("clk_c1_mcnt_long");
      auto clk_c2_mcnt_times = context.SetIntListItemAttr("clk_c2_mcnt_times");
      auto clk_c2_mcnt_short = context.SetIntListItemAttr("clk_c2_mcnt_short");
      auto clk_c2_mcnt_long = context.SetIntListItemAttr("clk_c2_mcnt_long");
      auto clk_c3_mcnt_times = context.SetIntListItemAttr("clk_c3_mcnt_times");
      auto clk_c3_mcnt_short = context.SetIntListItemAttr("clk_c3_mcnt_short");
      auto clk_c3_mcnt_long = context.SetIntListItemAttr("clk_c3_mcnt_long");

      auto order_c1_mcnt_times = context.SetIntListItemAttr("order_c1_mcnt_times");
      auto order_c1_mcnt_short = context.SetIntListItemAttr("order_c1_mcnt_short");
      auto order_c1_mcnt_long = context.SetIntListItemAttr("order_c1_mcnt_long");
      auto order_c2_mcnt_times = context.SetIntListItemAttr("order_c2_mcnt_times");
      auto order_c2_mcnt_short = context.SetIntListItemAttr("order_c2_mcnt_short");
      auto order_c2_mcnt_long = context.SetIntListItemAttr("order_c2_mcnt_long");
      auto order_c3_mcnt_times = context.SetIntListItemAttr("order_c3_mcnt_times");
      auto order_c3_mcnt_short = context.SetIntListItemAttr("order_c3_mcnt_short");
      auto order_c3_mcnt_long = context.SetIntListItemAttr("order_c3_mcnt_long");

      int click_code_list_lens = click_code_list ? (*click_code_list).size() : 0;
      int click_timestamp_lens = click_timestamp ? (*click_timestamp).size() : 0;
      int order_code_list_lens = order_code_list ? (*order_code_list).size() : 0;
      int order_timestamp_lens = order_timestamp ? (*order_timestamp).size() : 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::vector<int64> get_clk_c1_mcnt_times(5, 0);
        std::vector<int64> get_clk_c1_mcnt_short(5, 0);
        std::vector<int64> get_clk_c1_mcnt_long(5, 0);
        std::vector<int64> get_clk_c2_mcnt_times(5, 0);
        std::vector<int64> get_clk_c2_mcnt_short(5, 0);
        std::vector<int64> get_clk_c2_mcnt_long(5, 0);
        std::vector<int64> get_clk_c3_mcnt_times(5, 0);
        std::vector<int64> get_clk_c3_mcnt_short(5, 0);
        std::vector<int64> get_clk_c3_mcnt_long(5, 0);

        std::vector<int64> get_order_c1_mcnt_times(5, 0);
        std::vector<int64> get_order_c1_mcnt_short(5, 0);
        std::vector<int64> get_order_c1_mcnt_long(5, 0);

        std::vector<int64> get_order_c2_mcnt_times(5, 0);
        std::vector<int64> get_order_c2_mcnt_short(5, 0);
        std::vector<int64> get_order_c2_mcnt_long(5, 0);
        std::vector<int64> get_order_c3_mcnt_times(5, 0);
        std::vector<int64> get_order_c3_mcnt_short(5, 0);
        std::vector<int64> get_order_c3_mcnt_long(5, 0);
        int total_cnt = 0;
        int64 get_item_cd1 = -1;
        int64 get_item_cd2 = -1;
        int64 get_item_cd3 = -1;
        int64 get_item_cd4 = -1;
        int64 get_item_cd = -1;

        auto item_code_id = get_item_code_id(result);
        int item_code_id_lens = item_code_id ? (*item_code_id).size() : 0;

        if (item_code_id_lens >= 4) {
            get_item_cd1 = (*item_code_id)[0] - 1;
            get_item_cd2 = (*item_code_id)[1] - 1;
            get_item_cd3 = (*item_code_id)[2] - 1;
            get_item_cd4 = (*item_code_id)[3] - 1;
            if (get_item_cd1 >= 0) {
                get_item_cd = (get_item_cd1 << 36) | (get_item_cd2 << 24) |
                (get_item_cd3 << 12) | get_item_cd4;
            }
        }
        if (click_code_list_lens != 0 && click_code_list_lens == 8 * click_timestamp_lens) {
          for (auto i = 0; i < (*click_timestamp).size(); ++i) {
              total_cnt++;
              int lag = (request_time - (*click_timestamp)[i]) / (3600 * 24);
              int lag_hour = (request_time - (*click_timestamp)[i]) / 3600;

              if (lag >= 90 && total_cnt > 200) {
                  break;
              }

              int64 c1 = -1, c2 = -1, c3 = -1;
              if (!(*click_code_list).empty()) {
                  c1 = (*click_code_list)[8 * i] - 1;
                  c2 = (*click_code_list)[8 * i + 1] - 1;
                  c3 = (*click_code_list)[8 * i + 2] - 1;
              }

              if (c1 >= 0 && item_code_id_lens >=4) {
                  if (c1 == get_item_cd1) {
                      if (total_cnt <= 1) get_clk_c1_mcnt_times[0]++;
                      if (total_cnt <= 10) get_clk_c1_mcnt_times[1]++;
                      if (total_cnt <= 20) get_clk_c1_mcnt_times[2]++;
                      if (total_cnt <= 100) get_clk_c1_mcnt_times[3]++;
                      if (total_cnt <= 200) get_clk_c1_mcnt_times[4]++;
                      if (lag_hour < 3) get_clk_c1_mcnt_short[0]++;
                      if (lag_hour < 6) get_clk_c1_mcnt_short[1]++;
                      if (lag_hour < 12) get_clk_c1_mcnt_short[2]++;
                      if (lag < 1) get_clk_c1_mcnt_short[3]++;
                      if (lag < 3) get_clk_c1_mcnt_short[4]++;

                      if (lag < 7) get_clk_c1_mcnt_long[0]++;
                      if (lag < 30) get_clk_c1_mcnt_long[2]++;
                      if (lag < 60) get_clk_c1_mcnt_long[3]++;
                      if (lag < 90) get_clk_c1_mcnt_long[4]++;
                  }

                  if (((c1 << 12) | c2) == ((get_item_cd1 << 12) | get_item_cd2)) {
                      if (total_cnt <= 1) get_clk_c2_mcnt_times[0]++;
                      if (total_cnt <= 10) get_clk_c2_mcnt_times[1]++;
                      if (total_cnt <= 20) get_clk_c2_mcnt_times[2]++;
                      if (total_cnt <= 100) get_clk_c2_mcnt_times[3]++;
                      if (total_cnt <= 200) get_clk_c2_mcnt_times[4]++;
                      if (lag_hour < 3) get_clk_c2_mcnt_short[0]++;
                      if (lag_hour < 6) get_clk_c2_mcnt_short[1]++;
                      if (lag_hour < 12) get_clk_c2_mcnt_short[2]++;
                      if (lag < 1) get_clk_c2_mcnt_short[3]++;
                      if (lag < 3) get_clk_c2_mcnt_short[4]++;

                      if (lag < 7) get_clk_c2_mcnt_long[0]++;
                      if (lag < 30) get_clk_c2_mcnt_long[2]++;
                      if (lag < 60) get_clk_c2_mcnt_long[3]++;
                      if (lag < 90) get_clk_c2_mcnt_long[4]++;
                  }

                  if (((c1 << 24) | (c2 << 12) | c3) == ((get_item_cd1 << 24) |
                  (get_item_cd2 << 12) | get_item_cd3)) {
                      if (total_cnt <= 1) get_clk_c3_mcnt_times[0]++;
                      if (total_cnt <= 10) get_clk_c3_mcnt_times[1]++;
                      if (total_cnt <= 20) get_clk_c3_mcnt_times[2]++;
                      if (total_cnt <= 100) get_clk_c3_mcnt_times[3]++;
                      if (total_cnt <= 200) get_clk_c3_mcnt_times[4]++;
                      if (lag_hour < 3) get_clk_c3_mcnt_short[0]++;
                      if (lag_hour < 6) get_clk_c3_mcnt_short[1]++;
                      if (lag_hour < 12) get_clk_c3_mcnt_short[2]++;
                      if (lag < 1) get_clk_c3_mcnt_short[3]++;
                      if (lag < 3) get_clk_c3_mcnt_short[4]++;

                      if (lag < 7) get_clk_c3_mcnt_long[0]++;
                      if (lag < 30) get_clk_c3_mcnt_long[2]++;
                      if (lag < 60) get_clk_c3_mcnt_long[3]++;
                      if (lag < 90) get_clk_c3_mcnt_long[4]++;
                  }
              }
          }
        }

        total_cnt = 0;
        if (order_code_list_lens != 0 && order_code_list_lens == 8 * order_timestamp_lens) {
          for (auto i = 0; i < (*order_timestamp).size(); ++i) {
              total_cnt++;
              int lag = (request_time - (*order_timestamp)[i]) / (3600 * 24);
              int lag_hour = (request_time - (*order_timestamp)[i]) / 3600;

              if (lag >= 90 && total_cnt > 200) {
                  break;
              }

              int64 c1 = -1, c2 = -1, c3 = -1;
              if (!(*order_code_list).empty()) {
                  c1 = (*order_code_list)[8 * i] - 1;
                  c2 = (*order_code_list)[8 * i + 1] - 1;
                  c3 = (*order_code_list)[8 * i + 2] - 1;
              }

              if (c1 >= 0 && item_code_id_lens >=4) {
                  if (c1 == get_item_cd1) {
                      if (total_cnt <= 1) get_order_c1_mcnt_times[0]++;
                      if (total_cnt <= 10) get_order_c1_mcnt_times[1]++;
                      if (total_cnt <= 20) get_order_c1_mcnt_times[2]++;
                      if (total_cnt <= 100) get_order_c1_mcnt_times[3]++;
                      if (total_cnt <= 200) get_order_c1_mcnt_times[4]++;
                      if (lag_hour < 3) get_order_c1_mcnt_short[0]++;
                      if (lag_hour < 6) get_order_c1_mcnt_short[1]++;
                      if (lag_hour < 12) get_order_c1_mcnt_short[2]++;
                      if (lag < 1) get_order_c1_mcnt_short[3]++;
                      if (lag < 3) get_order_c1_mcnt_short[4]++;

                      if (lag < 7) get_order_c1_mcnt_long[0]++;
                      if (lag < 30) get_order_c1_mcnt_long[2]++;
                      if (lag < 60) get_order_c1_mcnt_long[3]++;
                      if (lag < 90) get_order_c1_mcnt_long[4]++;
                  }

                  if (((c1 << 12) | c2) == ((get_item_cd1 << 12) | get_item_cd2)) {
                      if (total_cnt <= 1) get_order_c2_mcnt_times[0]++;
                      if (total_cnt <= 10) get_order_c2_mcnt_times[1]++;
                      if (total_cnt <= 20) get_order_c2_mcnt_times[2]++;
                      if (total_cnt <= 100) get_order_c2_mcnt_times[3]++;
                      if (total_cnt <= 200) get_order_c2_mcnt_times[4]++;
                      if (lag_hour < 3) get_order_c2_mcnt_short[0]++;
                      if (lag_hour < 6) get_order_c2_mcnt_short[1]++;
                      if (lag_hour < 12) get_order_c2_mcnt_short[2]++;
                      if (lag < 1) get_order_c2_mcnt_short[3]++;
                      if (lag < 3) get_order_c2_mcnt_short[4]++;

                      if (lag < 7) get_order_c2_mcnt_long[0]++;
                      if (lag < 30) get_order_c2_mcnt_long[2]++;
                      if (lag < 60) get_order_c2_mcnt_long[3]++;
                      if (lag < 90) get_order_c2_mcnt_long[4]++;
                  }

                  if (((c1 << 24) | (c2 << 12) | c3) == ((get_item_cd1 << 24) |
                      (get_item_cd2 << 12) | get_item_cd3)) {
                      if (total_cnt <= 1) get_order_c3_mcnt_times[0]++;
                      if (total_cnt <= 10) get_order_c3_mcnt_times[1]++;
                      if (total_cnt <= 20) get_order_c3_mcnt_times[2]++;
                      if (total_cnt <= 100) get_order_c3_mcnt_times[3]++;
                      if (total_cnt <= 200) get_order_c3_mcnt_times[4]++;
                      if (lag_hour < 3) get_order_c3_mcnt_short[0]++;
                      if (lag_hour < 6) get_order_c3_mcnt_short[1]++;
                      if (lag_hour < 12) get_order_c3_mcnt_short[2]++;
                      if (lag < 1) get_order_c3_mcnt_short[3]++;
                      if (lag < 3) get_order_c3_mcnt_short[4]++;

                      if (lag < 7) get_order_c3_mcnt_long[0]++;
                      if (lag < 30) get_order_c3_mcnt_long[2]++;
                      if (lag < 60) get_order_c3_mcnt_long[3]++;
                      if (lag < 90) get_order_c3_mcnt_long[4]++;
                  }
              }
          }
        }
        clk_c1_mcnt_times(result, get_clk_c1_mcnt_times);
        clk_c1_mcnt_short(result, get_clk_c1_mcnt_short);
        clk_c1_mcnt_long(result, get_clk_c1_mcnt_long);

        clk_c2_mcnt_times(result, get_clk_c2_mcnt_times);
        clk_c2_mcnt_short(result, get_clk_c2_mcnt_short);
        clk_c2_mcnt_long(result, get_clk_c2_mcnt_long);

        clk_c3_mcnt_times(result, get_clk_c3_mcnt_times);
        clk_c3_mcnt_short(result, get_clk_c3_mcnt_short);
        clk_c3_mcnt_long(result, get_clk_c3_mcnt_long);

        order_c1_mcnt_times(result, get_order_c1_mcnt_times);
        order_c1_mcnt_short(result, get_order_c1_mcnt_short);
        order_c1_mcnt_long(result, get_order_c1_mcnt_long);

        order_c2_mcnt_times(result, get_order_c2_mcnt_times);
        order_c2_mcnt_short(result, get_order_c2_mcnt_short);
        order_c2_mcnt_long(result, get_order_c2_mcnt_long);

        order_c3_mcnt_times(result, get_order_c3_mcnt_times);
        order_c3_mcnt_short(result, get_order_c3_mcnt_short);
        order_c3_mcnt_long(result, get_order_c3_mcnt_long);

        item_cd1(result, get_item_cd1);
        item_cd2(result, get_item_cd2);
        item_cd3(result, get_item_cd3);
        item_cd4(result, get_item_cd4);
        item_cd(result, get_item_cd);
      });
      return true;
  }


  static bool HashCPVSearch(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_cpv_item_list = context.GetIntListItemAttr("cpv_item_list");
      auto get_iCate1Id = context.GetIntItemAttr("iCate1Id");


      auto realshow_cpv_list = context.GetIntListCommonAttr("realshow_cpv_list");
      auto origin_colossus_rs_item_id_list =
        context.GetIntListCommonAttr("origin_colossus_rs_item_id_list");
      auto origin_colossus_rs_timestamp_list =
        context.GetIntListCommonAttr("origin_colossus_rs_timestamp_list");
      auto origin_colossus_rs_pagecode_id_list =
        context.GetIntListCommonAttr("origin_colossus_rs_pagecode_id_list");
      auto origin_colossus_rs_uniform_spu_id_list =
        context.GetIntListCommonAttr("origin_colossus_rs_uniform_spu_id_list");
      auto origin_colossus_rs_exposure_ratio_list =
        context.GetIntListCommonAttr("origin_colossus_rs_exposure_ratio_list");
      auto origin_colossus_rs_seller_id_list =
        context.GetIntListCommonAttr("origin_colossus_rs_seller_id_list");
      auto origin_colossus_rs_category_list =
        context.GetIntListCommonAttr("origin_colossus_rs_category_list");
      auto request_time = context.GetIntCommonAttr("request_time").value_or(0);

      auto gsu_cpv_colossus_rs_item_id_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_item_id_list");
      auto gsu_cpv_colossus_rs_lag_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_lag_list");
      auto gsu_cpv_colossus_rs_pagecode_id_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_pagecode_id_list");
      auto gsu_cpv_colossus_rs_uniform_spu_id_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_uniform_spu_id_list");
      auto gsu_cpv_colossus_rs_exposure_ratio_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_exposure_ratio_list");
      auto gsu_cpv_colossus_rs_seller_id_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_seller_id_list");
      auto gsu_cpv_colossus_rs_c1_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_c1_list");
      auto gsu_cpv_colossus_rs_c2_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_c2_list");
      auto gsu_cpv_colossus_rs_c3_list =
        context.SetIntListItemAttr("gsu_cpv_colossus_rs_c3_list");

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::vector<int64> get_gsu_cpv_colossus_rs_item_id_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_lag_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_pagecode_id_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_uniform_spu_id_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_exposure_ratio_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_seller_id_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_c1_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_c2_list;
        std::vector<int64> get_gsu_cpv_colossus_rs_c3_list;
        folly::F14FastMap<int, int> cpv_hash;
        folly::F14FastMap<int, std::vector<int>> cpv_hash_num;
        std::vector<int> final_idx;
        auto cpv_item_list = get_cpv_item_list(result);
        auto iCate1Id = get_iCate1Id(result).value_or(0);
        int cpv_item_list_lens = cpv_item_list ? (*cpv_item_list).size() : 0;

        if (cpv_item_list_lens > 0) {
          for (auto i = 0; i < cpv_item_list_lens; ++i) {
            auto item_idx = (*cpv_item_list)[i];
            cpv_hash[item_idx] = 1;
          }

          int item_lens = origin_colossus_rs_item_id_list ? (*origin_colossus_rs_item_id_list).size() : 0;
          int realshow_lens = realshow_cpv_list ? (*realshow_cpv_list).size() : 0;

          if (item_lens > 0 && realshow_lens > 0) {
            for (auto j = 0; j < item_lens; ++j) {
              int total_cnt = 0;
              for (auto k = 0; k < 20; ++k) {
                int index = j * 20 + k;
                int attr = (*realshow_cpv_list)[index];

                if (attr > 0) {
                  auto iter = cpv_hash.find(attr);
                  if (iter != cpv_hash.end()) {
                    total_cnt = total_cnt + 1;
                  }
                }
              }

              if (total_cnt > 0) {
                cpv_hash_num[total_cnt].push_back(j);
              }
            }
          }

          int full_cnt = 0;
          for (auto m = 20; m > 0; --m) {
            if (full_cnt >= 50) {
              break;
            }

            auto iter = cpv_hash_num.find(m);
            if (iter != cpv_hash_num.end()) {
              auto v = iter->second;
              for (auto val : v) {
                if (full_cnt >= 50) {
                  break;
                }

                if (realshow_lens > 0 && origin_colossus_rs_category_list &&
                  ((((*origin_colossus_rs_category_list)[val] >> 48) & 0xffff) == iCate1Id)) {
                  final_idx.push_back(val);
                  full_cnt = full_cnt + 1;
                }
              }
            }
          }

          if (final_idx.size() > 0) {
            for (auto a = 0; a < final_idx.size(); a++) {
              int index = final_idx[a];
              if (origin_colossus_rs_item_id_list && origin_colossus_rs_timestamp_list
                 && origin_colossus_rs_pagecode_id_list
                 && origin_colossus_rs_uniform_spu_id_list && origin_colossus_rs_exposure_ratio_list
                 && origin_colossus_rs_seller_id_list && origin_colossus_rs_category_list) {
                  get_gsu_cpv_colossus_rs_item_id_list.push_back((*origin_colossus_rs_item_id_list)[index]);
                  get_gsu_cpv_colossus_rs_lag_list.push_back(
                    static_cast<int>(std::floor(
                      (request_time - (*origin_colossus_rs_timestamp_list)[index]) / 3600.0 * 24)));
                  get_gsu_cpv_colossus_rs_pagecode_id_list.push_back(
                    (*origin_colossus_rs_pagecode_id_list)[index]);
                  get_gsu_cpv_colossus_rs_uniform_spu_id_list.push_back(
                    (*origin_colossus_rs_uniform_spu_id_list)[index]);
                  get_gsu_cpv_colossus_rs_exposure_ratio_list.push_back(
                    (*origin_colossus_rs_exposure_ratio_list)[index]);
                  get_gsu_cpv_colossus_rs_seller_id_list.push_back(
                    (*origin_colossus_rs_seller_id_list)[index]);
                  get_gsu_cpv_colossus_rs_c1_list.push_back(
                    ((*origin_colossus_rs_category_list)[index] >> 48) & 0xffff);
                  get_gsu_cpv_colossus_rs_c2_list.push_back(
                    ((*origin_colossus_rs_category_list)[index] >> 32) & 0xffff);
                  get_gsu_cpv_colossus_rs_c3_list.push_back(
                    ((*origin_colossus_rs_category_list)[index] >> 16) & 0xffff);
              }
            }
          }
        }

        gsu_cpv_colossus_rs_item_id_list(result, get_gsu_cpv_colossus_rs_item_id_list);
        gsu_cpv_colossus_rs_lag_list(result, get_gsu_cpv_colossus_rs_lag_list);
        gsu_cpv_colossus_rs_pagecode_id_list(result, get_gsu_cpv_colossus_rs_pagecode_id_list);
        gsu_cpv_colossus_rs_uniform_spu_id_list(result, get_gsu_cpv_colossus_rs_uniform_spu_id_list);
        gsu_cpv_colossus_rs_exposure_ratio_list(result, get_gsu_cpv_colossus_rs_exposure_ratio_list);
        gsu_cpv_colossus_rs_seller_id_list(result, get_gsu_cpv_colossus_rs_seller_id_list);
        gsu_cpv_colossus_rs_c1_list(result, get_gsu_cpv_colossus_rs_c1_list);
        gsu_cpv_colossus_rs_c2_list(result, get_gsu_cpv_colossus_rs_c2_list);
        gsu_cpv_colossus_rs_c3_list(result, get_gsu_cpv_colossus_rs_c3_list);
      });
      return true;
  }


  static bool HashCPVSearchClick(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_cpv_item_list = context.GetIntListItemAttr("cpv_item_list");
      auto get_iCate1Id = context.GetIntItemAttr("iCate1Id");


      auto click_cpv_list = context.GetIntListCommonAttr("click_cpv_list");
      auto click_iid =
        context.GetIntListCommonAttr("click_iid");
      auto click_timestamp =
        context.GetIntListCommonAttr("click_timestamp");
      auto click_aid =
        context.GetIntListCommonAttr("click_aid");
      auto click_cate =
        context.GetIntListCommonAttr("click_cate");
      auto click_price =
        context.GetIntListCommonAttr("click_price");
      auto click_pv =
        context.GetIntListCommonAttr("click_pv");
      auto click_src =
        context.GetIntListCommonAttr("click_src");
      auto click_label =
        context.GetIntListCommonAttr("click_label");
      auto request_time = context.GetIntCommonAttr("request_time").value_or(0);

      auto gsu_click_iid =
        context.SetIntListItemAttr("gsu_click_iid");
      auto gsu_click_lag =
        context.SetIntListItemAttr("gsu_click_lag");
      auto gsu_click_aid =
        context.SetIntListItemAttr("gsu_click_aid");
      auto gsu_click_c1 =
        context.SetIntListItemAttr("gsu_click_c1");
      auto gsu_click_c2 =
        context.SetIntListItemAttr("gsu_click_c2");
      auto gsu_click_c3 =
        context.SetIntListItemAttr("gsu_click_c3");
      auto gsu_click_price =
        context.SetIntListItemAttr("gsu_click_price");
      auto gsu_click_pv =
        context.SetIntListItemAttr("gsu_click_pv");
      auto gsu_click_src =
        context.SetIntListItemAttr("gsu_click_src");
      auto gsu_click_label =
        context.SetIntListItemAttr("gsu_click_label");

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::vector<int64> get_gsu_click_iid;
        std::vector<int64> get_gsu_click_lag;
        std::vector<int64> get_gsu_click_aid;
        std::vector<int64> get_gsu_click_c1;
        std::vector<int64> get_gsu_click_c2;
        std::vector<int64> get_gsu_click_c3;
        std::vector<int64> get_gsu_click_price;
        std::vector<int64> get_gsu_click_pv;
        std::vector<int64> get_gsu_click_src;
        std::vector<int64> get_gsu_click_label;
        folly::F14FastMap<int, int> cpv_hash;
        folly::F14FastMap<int, std::vector<int>> cpv_hash_num;
        std::vector<int> final_idx;
        auto cpv_item_list = get_cpv_item_list(result);
        auto iCate1Id = get_iCate1Id(result).value_or(0);
        int cpv_item_list_lens = cpv_item_list ? (*cpv_item_list).size() : 0;

        if (cpv_item_list_lens > 0) {
          for (auto i = 0; i < cpv_item_list_lens; ++i) {
            auto item_idx = (*cpv_item_list)[i];
            cpv_hash[item_idx] = 1;
          }

          int item_lens = click_iid ? (*click_iid).size() : 0;
          int click_lens = click_cpv_list ? (*click_cpv_list).size() : 0;

          if (item_lens > 0 && click_lens > 0) {
            for (auto j = 0; j < item_lens; ++j) {
              int total_cnt = 0;
              for (auto k = 0; k < 20; ++k) {
                int index = j * 20 + k;
                int attr = (*click_cpv_list)[index];

                if (attr > 0) {
                  auto iter = cpv_hash.find(attr);
                  if (iter != cpv_hash.end()) {
                    total_cnt = total_cnt + 1;
                  }
                }
              }

              if (total_cnt > 0) {
                cpv_hash_num[total_cnt].push_back(j);
              }
            }
          }

          int full_cnt = 0;
          for (auto m = 20; m > 0; --m) {
            if (full_cnt >= 50) {
              break;
            }

            auto iter = cpv_hash_num.find(m);
            if (iter != cpv_hash_num.end()) {
              auto v = iter->second;
              for (auto val : v) {
                if (full_cnt >= 50) {
                  break;
                }

                if (click_lens > 0 && click_cate &&
                  ((((*click_cate)[val] >> 48) & 0xffff) == iCate1Id)) {
                  final_idx.push_back(val);
                  full_cnt = full_cnt + 1;
                }
              }
            }
          }

          if (final_idx.size() > 0) {
            for (auto a = 0; a < final_idx.size(); a++) {
              int index = final_idx[a];
              if (click_iid && click_timestamp
                 && click_aid
                 && click_cate && click_price
                 && click_pv && click_src
                 && click_label) {
                  get_gsu_click_iid.push_back((*click_iid)[index]);
                  get_gsu_click_lag.push_back(
                    static_cast<int>(std::floor(
                      (request_time - (*click_timestamp)[index]) / (3600.0 * 24))));
                  get_gsu_click_aid.push_back(
                    (*click_aid)[index]);
                  get_gsu_click_c1.push_back(
                    ((*click_cate)[index] >> 48) & 0xffff);
                  get_gsu_click_c2.push_back(
                    ((*click_cate)[index] >> 32) & 0xffff);
                  get_gsu_click_c3.push_back(
                    ((*click_cate)[index] >> 16) & 0xffff);
                  get_gsu_click_price.push_back(
                    static_cast<int>(std::floor(std::log((*click_price)[index] / 100.0 + 1))));
                  get_gsu_click_pv.push_back(
                    (*click_pv)[index]);
                  get_gsu_click_src.push_back(
                    (*click_src)[index]);
                  get_gsu_click_label.push_back(
                    (*click_label)[index]);
              }
            }
          }
        }

        gsu_click_iid(result, get_gsu_click_iid);
        gsu_click_lag(result, get_gsu_click_lag);
        gsu_click_aid(result, get_gsu_click_aid);
        gsu_click_c1(result, get_gsu_click_c1);
        gsu_click_c2(result, get_gsu_click_c2);
        gsu_click_c3(result, get_gsu_click_c3);
        gsu_click_price(result, get_gsu_click_price);
        gsu_click_pv(result, get_gsu_click_pv);
        gsu_click_src(result, get_gsu_click_src);
        gsu_click_label(result, get_gsu_click_label);
      });
      return true;
  }


  static bool HashCPVSearchClickV2(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_cpv_item_list = context.GetIntListItemAttr("cpv_item_list");
      auto get_iCate1Id = context.GetIntItemAttr("iCate1Id");


      auto click_cpv_list = context.GetIntListCommonAttr("click_cpv_list");
      auto click_iid =
        context.GetIntListCommonAttr("click_iid");
      auto click_timestamp =
        context.GetIntListCommonAttr("click_timestamp");
      auto click_aid =
        context.GetIntListCommonAttr("click_aid");
      auto click_cate =
        context.GetIntListCommonAttr("click_cate");
      auto click_price =
        context.GetIntListCommonAttr("click_price");
      auto click_pv =
        context.GetIntListCommonAttr("click_pv");
      auto click_src =
        context.GetIntListCommonAttr("click_src");
      auto click_label =
        context.GetIntListCommonAttr("click_label");
      auto request_time = context.GetIntCommonAttr("request_time").value_or(0);

      auto gsu_click_iid =
        context.SetIntListItemAttr("gsu_click_iid");
      auto gsu_click_lag =
        context.SetIntListItemAttr("gsu_click_lag");
      auto gsu_click_aid =
        context.SetIntListItemAttr("gsu_click_aid");
      auto gsu_click_c1 =
        context.SetIntListItemAttr("gsu_click_c1");
      auto gsu_click_c2 =
        context.SetIntListItemAttr("gsu_click_c2");
      auto gsu_click_c3 =
        context.SetIntListItemAttr("gsu_click_c3");
      auto gsu_click_price =
        context.SetIntListItemAttr("gsu_click_price");
      auto gsu_click_pv =
        context.SetIntListItemAttr("gsu_click_pv");
      auto gsu_click_src =
        context.SetIntListItemAttr("gsu_click_src");
      auto gsu_click_label =
        context.SetIntListItemAttr("gsu_click_label");

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::vector<int64> get_gsu_click_iid;
        std::vector<int64> get_gsu_click_lag;
        std::vector<int64> get_gsu_click_aid;
        std::vector<int64> get_gsu_click_c1;
        std::vector<int64> get_gsu_click_c2;
        std::vector<int64> get_gsu_click_c3;
        std::vector<int64> get_gsu_click_price;
        std::vector<int64> get_gsu_click_pv;
        std::vector<int64> get_gsu_click_src;
        std::vector<int64> get_gsu_click_label;
        folly::F14FastMap<int, int> cpv_hash;
        folly::F14FastMap<int, std::vector<int>> cpv_hash_num;
        std::vector<int> final_idx;
        auto cpv_item_list = get_cpv_item_list(result);
        int cpv_item_list_lens = cpv_item_list ? (*cpv_item_list).size() : 0;

        if (cpv_item_list_lens > 0) {
          for (auto i = 0; i < cpv_item_list_lens; ++i) {
            auto item_idx = (*cpv_item_list)[i];
            cpv_hash[item_idx] = 1;
          }

          int item_lens = click_iid ? (*click_iid).size() : 0;
          int click_lens = click_cpv_list ? (*click_cpv_list).size() : 0;

          if (item_lens > 0 && click_lens > 0) {
            for (auto j = 0; j < item_lens; ++j) {
              int total_cnt = 0;
              for (auto k = 0; k < 20; ++k) {
                int index = j * 20 + k;
                int attr = (*click_cpv_list)[index];

                if (attr > 0) {
                  auto iter = cpv_hash.find(attr);
                  if (iter != cpv_hash.end()) {
                    total_cnt = total_cnt + 1;
                  }
                }
              }

              if (total_cnt > 0) {
                cpv_hash_num[total_cnt].push_back(j);
              }
            }
          }

          int full_cnt = 0;
          for (auto m = 20; m > 0; --m) {
            if (full_cnt >= 50) {
              break;
            }

            auto iter = cpv_hash_num.find(m);
            if (iter != cpv_hash_num.end()) {
              auto v = iter->second;
              for (auto val : v) {
                if (full_cnt >= 50) {
                  break;
                }

                if (click_lens > 0 && click_cate) {
                  final_idx.push_back(val);
                  full_cnt = full_cnt + 1;
                }
              }
            }
          }

          if (final_idx.size() > 0) {
            for (auto a = 0; a < final_idx.size(); a++) {
              int index = final_idx[a];
              if (click_iid && click_timestamp
                 && click_aid
                 && click_cate && click_price
                 && click_pv && click_src
                 && click_label) {
                  get_gsu_click_iid.push_back((*click_iid)[index]);
                  get_gsu_click_lag.push_back(
                    static_cast<int>(std::floor(
                      (request_time - (*click_timestamp)[index]) / (3600.0 * 24))));
                  get_gsu_click_aid.push_back(
                    (*click_aid)[index]);
                  get_gsu_click_c1.push_back(
                    ((*click_cate)[index] >> 48) & 0xffff);
                  get_gsu_click_c2.push_back(
                    ((*click_cate)[index] >> 32) & 0xffff);
                  get_gsu_click_c3.push_back(
                    ((*click_cate)[index] >> 16) & 0xffff);
                  get_gsu_click_price.push_back(
                    static_cast<int>(std::floor(std::log((*click_price)[index] / 100.0 + 1))));
                  get_gsu_click_pv.push_back(
                    (*click_pv)[index]);
                  get_gsu_click_src.push_back(
                    (*click_src)[index]);
                  get_gsu_click_label.push_back(
                    (*click_label)[index]);
              }
            }
          }
        }

        gsu_click_iid(result, get_gsu_click_iid);
        gsu_click_lag(result, get_gsu_click_lag);
        gsu_click_aid(result, get_gsu_click_aid);
        gsu_click_c1(result, get_gsu_click_c1);
        gsu_click_c2(result, get_gsu_click_c2);
        gsu_click_c3(result, get_gsu_click_c3);
        gsu_click_price(result, get_gsu_click_price);
        gsu_click_pv(result, get_gsu_click_pv);
        gsu_click_src(result, get_gsu_click_src);
        gsu_click_label(result, get_gsu_click_label);
      });
      return true;
  }


  static bool GenSimKey(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_sim_item_key = context.GetIntItemAttr("sim_item_key");
      auto set_sim_item_key = context.SetIntItemAttr("new_sim_item_key");
      int64 mask_value = 1;
      mask_value = ((mask_value << 54) - 1);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto sim_item_key = get_sim_item_key(result).value_or(0);
        int64 get_new_sim_item_key = (sim_item_key & mask_value);
        set_sim_item_key(result, get_new_sim_item_key);
      });
      return true;
  }

  static bool QuantizeFrDistanceBuy(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_fr_distance_item_dist_buy = context.GetDoubleListItemAttr("fr_distance_item_dist_buy");
      auto get_item_buy_gsu_signs_extend = context.GetIntListItemAttr("item_buy_gsu_signs_extend");
      auto set_fr_distance_item_dist_buy_buk = context.SetIntListItemAttr("fr_distance_item_dist_buy_buk");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto fr_distance_item_dist_buy = get_fr_distance_item_dist_buy(result);
        auto item_buy_gsu_signs_extend = get_item_buy_gsu_signs_extend(result);
        std::vector<int64> fr_distance_item_dist_buy_buk;
        if (fr_distance_item_dist_buy && item_buy_gsu_signs_extend) {
          auto len_fr_distance_item_dist_buy = (*fr_distance_item_dist_buy).size();
          auto len_item_buy_gsu_signs_extend = ((*item_buy_gsu_signs_extend).size())/22;
          auto len_fr_distance_item_dist_buy_buk =
            std::min(len_fr_distance_item_dist_buy, len_item_buy_gsu_signs_extend);
          for (int j = 0; j < len_fr_distance_item_dist_buy_buk; ++j) {
            fr_distance_item_dist_buy_buk.emplace_back(std::ceil((*fr_distance_item_dist_buy)[j] / 0.01));
          }
        }
        set_fr_distance_item_dist_buy_buk(result, fr_distance_item_dist_buy_buk);
      });
      return true;
  }

  static bool ComputeLenBuy(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_item_buy_gsu_signs_extend = context.GetIntListItemAttr("item_buy_gsu_signs_extend");
      auto get_i2i_buy_fr_dist_slots = context.GetIntListItemAttr("i2i_buy_fr_dist_slots");
      auto set_len_i2i_buy_1 = context.SetIntItemAttr("len_i2i_buy_1");
      auto set_len_i2i_buy_2 = context.SetIntItemAttr("len_i2i_buy_2");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto item_buy_gsu_signs_extend = get_item_buy_gsu_signs_extend(result);
        auto i2i_buy_fr_dist_slots = get_i2i_buy_fr_dist_slots(result);
        int64 len_i2i_buy_1 = 0;
        int64 len_i2i_buy_2 = 0;
        if (item_buy_gsu_signs_extend) {
          len_i2i_buy_1 = ((*item_buy_gsu_signs_extend).size())/22;
        }
        if (i2i_buy_fr_dist_slots) {
          len_i2i_buy_2 = ((*i2i_buy_fr_dist_slots).size());
        }
       set_len_i2i_buy_1(result, len_i2i_buy_1);
       set_len_i2i_buy_2(result, len_i2i_buy_2);
      });
      return true;
  }

  static bool QuantizeFrDistanceRs(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_fr_distance_item_dist_rs = context.GetDoubleListItemAttr("fr_distance_item_dist_rs");
      auto get_item_rs_gsu_signs_extend = context.GetIntListItemAttr("item_rs_gsu_signs_extend");
      auto set_fr_distance_item_dist_rs_buk = context.SetIntListItemAttr("fr_distance_item_dist_rs_buk");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto fr_distance_item_dist_rs = get_fr_distance_item_dist_rs(result);
        auto item_rs_gsu_signs_extend = get_item_rs_gsu_signs_extend(result);
        std::vector<int64> fr_distance_item_dist_rs_buk;
        if (fr_distance_item_dist_rs && item_rs_gsu_signs_extend) {
          auto len_fr_distance_item_dist_rs = (*fr_distance_item_dist_rs).size();
          auto len_item_rs_gsu_signs_extend = ((*item_rs_gsu_signs_extend).size())/11;
          auto len_fr_distance_item_dist_rs_buk =
            std::min(len_fr_distance_item_dist_rs, len_item_rs_gsu_signs_extend);
          for (int j = 0; j < len_fr_distance_item_dist_rs_buk; ++j) {
            fr_distance_item_dist_rs_buk.emplace_back(std::ceil((*fr_distance_item_dist_rs)[j] / 0.01));
          }
        }
        set_fr_distance_item_dist_rs_buk(result, fr_distance_item_dist_rs_buk);
      });
      return true;
  }

  static bool ComputeLenRs(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_item_rs_gsu_signs_extend = context.GetIntListItemAttr("item_rs_gsu_signs_extend");
      auto get_i2i_rs_fr_dist_slots = context.GetIntListItemAttr("i2i_rs_fr_dist_slots");
      auto set_len_i2i_rs_1 = context.SetIntItemAttr("len_i2i_rs_1");
      auto set_len_i2i_rs_2 = context.SetIntItemAttr("len_i2i_rs_2");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto item_rs_gsu_signs_extend = get_item_rs_gsu_signs_extend(result);
        auto i2i_rs_fr_dist_slots = get_i2i_rs_fr_dist_slots(result);
        int64 len_i2i_rs_1 = 0;
        int64 len_i2i_rs_2 = 0;
        if (item_rs_gsu_signs_extend) {
          len_i2i_rs_1 = ((*item_rs_gsu_signs_extend).size())/11;
        }
        if (i2i_rs_fr_dist_slots) {
          len_i2i_rs_2 = ((*i2i_rs_fr_dist_slots).size());
        }
       set_len_i2i_rs_1(result, len_i2i_rs_1);
       set_len_i2i_rs_2(result, len_i2i_rs_2);
      });
      return true;
  }

  static bool QuantizeFrDistanceClick(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_fr_distance_item_dist_click = context.GetDoubleListItemAttr("fr_distance_item_dist_click");
      auto get_item_click_gsu_signs_extend = context.GetIntListItemAttr("item_click_gsu_signs_extend");
      auto set_fr_distance_item_dist_click_buk =
        context.SetIntListItemAttr("fr_distance_item_dist_click_buk");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto fr_distance_item_dist_click = get_fr_distance_item_dist_click(result);
        auto item_click_gsu_signs_extend = get_item_click_gsu_signs_extend(result);
        std::vector<int64> fr_distance_item_dist_click_buk;
        if (fr_distance_item_dist_click && item_click_gsu_signs_extend) {
          auto len_fr_distance_item_dist_click = (*fr_distance_item_dist_click).size();
          auto len_item_click_gsu_signs_extend = ((*item_click_gsu_signs_extend).size())/16;
          auto len_fr_distance_item_dist_click_buk =
            std::min(len_fr_distance_item_dist_click, len_item_click_gsu_signs_extend);
          for (int j = 0; j < len_fr_distance_item_dist_click_buk; ++j) {
            fr_distance_item_dist_click_buk.emplace_back(
              std::ceil((*fr_distance_item_dist_click)[j] / 0.01));
          }
        }
        set_fr_distance_item_dist_click_buk(result, fr_distance_item_dist_click_buk);
      });
      return true;
  }

  static bool ComputeLenClick(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
      auto get_item_click_gsu_signs_extend = context.GetIntListItemAttr("item_click_gsu_signs_extend");
      auto get_i2i_click_fr_dist_slots = context.GetIntListItemAttr("i2i_click_fr_dist_slots");
      auto set_len_i2i_click_1 = context.SetIntItemAttr("len_i2i_click_1");
      auto set_len_i2i_click_2 = context.SetIntItemAttr("len_i2i_click_2");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto item_click_gsu_signs_extend = get_item_click_gsu_signs_extend(result);
        auto i2i_click_fr_dist_slots = get_i2i_click_fr_dist_slots(result);
        int64 len_i2i_click_1 = 0;
        int64 len_i2i_click_2 = 0;
        if (item_click_gsu_signs_extend) {
          len_i2i_click_1 = ((*item_click_gsu_signs_extend).size())/16;
        }
        if (i2i_click_fr_dist_slots) {
          len_i2i_click_2 = ((*i2i_click_fr_dist_slots).size());
        }
       set_len_i2i_click_1(result, len_i2i_click_1);
       set_len_i2i_click_2(result, len_i2i_click_2);
      });
      return true;
  }

  static bool Cid2Cid3ImpCnt(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto cid2_invalid_imp_result_attr = context.GetStringItemAttr("cid2_invalid_imp_result");
    auto cid3_invalid_imp_result_attr = context.GetStringItemAttr("cid3_invalid_imp_result");
    auto cid2_invalid_imp_cnt_set = context.SetIntItemAttr("cid2_invalid_imp_cnt");
    auto cid3_invalid_imp_cnt_set = context.SetIntItemAttr("cid3_invalid_imp_cnt");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cid2_invalid_imp_result = cid2_invalid_imp_result_attr(result).value_or("");
      auto cid3_invalid_imp_result = cid3_invalid_imp_result_attr(result).value_or("");
      auto cid2_invalid_imp_cnt = 0;
      auto cid3_invalid_imp_cnt = 0;
      if (cid2_invalid_imp_result.size() > 0) {
        if (!absl::SimpleAtoi(cid2_invalid_imp_result, &cid2_invalid_imp_cnt)) {
          cid2_invalid_imp_cnt = 0;
        }
      }
      if (cid3_invalid_imp_result.size() > 0) {
        if (!absl::SimpleAtoi(cid3_invalid_imp_result, &cid3_invalid_imp_cnt)) {
          cid3_invalid_imp_cnt = 0;
        }
      }
      cid2_invalid_imp_cnt_set(result, cid2_invalid_imp_cnt);
      cid3_invalid_imp_cnt_set(result, cid3_invalid_imp_cnt);
    });
    return true;
  }

  static bool MmuCid3RebuyRate(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto is_buy_mmu_cid3_attr = context.GetDoubleItemAttr("is_buy_mmu_cid3");
    auto mmu_cid3_rebuy_rate_result_attr = context.GetStringItemAttr("mmu_cid3_rebuy_rate_result");
    auto mmu_cid3_rebuy_rate_set = context.SetDoubleItemAttr("mmu_cid3_rebuy_rate");
    auto is_buy_mmu_cid3_rebuy_rate_set = context.SetDoubleItemAttr("is_buy_mmu_cid3_rebuy_rate");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto is_buy_mmu_cid3 = is_buy_mmu_cid3_attr(result).value_or(0);
      auto mmu_cid3_rebuy_rate_result = mmu_cid3_rebuy_rate_result_attr(result).value_or("");
      auto mmu_cid3_rebuy_rate = -1.0;
      auto is_buy_mmu_cid3_rebuy_rate = -1.0;
      if (!mmu_cid3_rebuy_rate_result.empty() && mmu_cid3_rebuy_rate_result.size() > 0) {
        if (absl::SimpleAtod(mmu_cid3_rebuy_rate_result, &mmu_cid3_rebuy_rate)) {
          mmu_cid3_rebuy_rate = -1.0;
        }
      }
      mmu_cid3_rebuy_rate_set(result, mmu_cid3_rebuy_rate);
      is_buy_mmu_cid3_rebuy_rate_set(result, is_buy_mmu_cid3 * mmu_cid3_rebuy_rate);
    });
    return true;
  }

  static bool MmuCateRedisKey(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto mmu_cate_redis_key_set = context.SetStringItemAttr("mmu_cate_redis_key");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      std::string import_string = "mmu_cate_" + std::to_string(item_id);
      mmu_cate_redis_key_set(result, import_string);
    });
    return true;
  }

  static bool CalcOrderedUserSeqAggOrderAttr(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto u_seq_bsr_key_attr = context.GetIntListCommonAttr("u_seq_bsr_key_attr");
    auto u_seq_bsr_lag_attr = context.GetIntListCommonAttr("u_seq_bsr_lag_attr");
    auto u_seq_bsm_key_attr = context.GetIntListCommonAttr("u_seq_bsm_key_attr");
    auto u_seq_bsm_lag_attr = context.GetIntListCommonAttr("u_seq_bsm_lag_attr");
    folly::F14FastMap<int64, std::map<int64, int64>> u_seq_bsm_lag_dict;
    folly::F14FastMap<int64, int64> i_agg_bsm_cnt_dict;
    int bsr_key_len = u_seq_bsr_key_attr ? (*u_seq_bsr_key_attr).size() : 0;
    int bsr_lag_len = u_seq_bsr_lag_attr ? (*u_seq_bsr_lag_attr).size() : 0;
    int bsm_key_len = u_seq_bsm_key_attr ? (*u_seq_bsm_key_attr).size() : 0;
    int bsm_lag_len = u_seq_bsm_lag_attr ? (*u_seq_bsm_lag_attr).size() : 0;
    if (bsr_key_len != bsr_lag_len || bsm_key_len != bsm_lag_len) {
      return false;
    }
    for (int k = bsm_key_len - 1; k >= 0; --k) {
      auto v = (*u_seq_bsm_key_attr)[k];
      i_agg_bsm_cnt_dict[v] += 1;
      u_seq_bsm_lag_dict[v][(*u_seq_bsm_lag_attr)[k]] = i_agg_bsm_cnt_dict[v];
    }
    std::vector<int64> export_agg_pay(bsr_key_len, 0);
    std::vector<int64> export_agg_lag(bsr_key_len, -1);
    std::vector<int64> export_agg_cnt(bsr_key_len, 0);
    for (int i = 0; i < bsr_key_len; ++i) {
      auto key = (*u_seq_bsr_key_attr)[i];
      auto lag = (*u_seq_bsr_lag_attr)[i];
      auto iter = u_seq_bsm_lag_dict.find(key);
      if (iter != u_seq_bsm_lag_dict.end()) {
        auto bsm_lag_it = iter->second;
        auto it = bsm_lag_it.lower_bound(lag);
        if (it != bsm_lag_it.end()) {
          export_agg_pay[i] = 1;
          export_agg_lag[i] = it->first - lag;
          export_agg_cnt[i] = it->second;
        }
      }
    }
    context.SetIntListCommonAttr("export_agg_pay_attr", std::move(export_agg_pay));
    context.SetIntListCommonAttr("export_agg_lag_attr", std::move(export_agg_lag));
    context.SetIntListCommonAttr("export_agg_cnt_attr", std::move(export_agg_cnt));
    return true;
  }

  static bool CalcInvaildWordsFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto title_attr = context.GetStringItemAttr("iTitle");
    auto title_split_attr = context.GetStringListItemAttr("iGoodsTitleSplitList");
    auto black_key_word_list = context.GetStringListCommonAttr("black_key_word_list");
    auto special_black_word_group = context.GetStringListCommonAttr("special_black_word_group");
    auto invaild_words_flag_set = context.SetIntItemAttr("invaild_words_flag");
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> black_list;
    if (black_key_word_list) {
      for (auto key_word : *black_key_word_list) {
        black_list.insert(key_word);
      }
    }
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> special_black_group;
    if (special_black_word_group) {
      for (auto key_word : *special_black_word_group) {
        special_black_group.insert(key_word);
      }
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto title = title_attr(result).value_or("");
      auto title_split = title_split_attr(result);
      int invaild_words_flag = 0;
      int hit_special_count = 0;
      if (title_split) {
        // title 分词过滤
        for (auto word : *title_split) {
          if (black_list.find(word) != black_list.end()) {
            invaild_words_flag = 1;
            break;
          }
          if (special_black_group.find(word) != special_black_group.end()) {
            hit_special_count++;
          }
        }
      } else {
        // title 过滤
        for (auto word : black_list) {
          if (title.find(word) != absl::string_view::npos) {
            invaild_words_flag = 1;
          }
        }
        for (auto word : special_black_group) {
          if (title.find(word) != absl::string_view::npos) {
            hit_special_count++;
          } else {
            break;
          }
        }
      }
      if (hit_special_count == special_black_group.size()) {
        invaild_words_flag = 1;
      }
      invaild_words_flag_set(result, invaild_words_flag);
    });
    return true;
  }

  static bool CalcIsAddressDiscount(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto exclude_province_code_attr = context.GetIntListItemAttr("iExcludeProvinceCode");
    auto user_delivery_address_province = context.GetIntListCommonAttr("user_delivery_address_province");
    auto user_delivery_address_city = context.GetIntListCommonAttr("user_delivery_address_city");
    auto user_delivery_address_district = context.GetIntListCommonAttr("user_delivery_address_district");
    auto is_address_discount_set = context.SetIntItemAttr("is_address_discount");
    folly::F14FastSet<int64> user_default_address_list;
    if (user_delivery_address_province && (*user_delivery_address_province).size() > 0) {
      user_default_address_list.insert((*user_delivery_address_province)[0]);
    }
    if (user_delivery_address_city && (*user_delivery_address_city).size() > 0) {
      user_default_address_list.insert((*user_delivery_address_city)[0]);
    }
    if (user_delivery_address_district && (*user_delivery_address_district).size() > 0) {
      user_default_address_list.insert((*user_delivery_address_district)[0]);
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto exclude_province_code = exclude_province_code_attr(result);
      int is_address_discount = 0;
      if (user_default_address_list.size() == 0 || !exclude_province_code ||
          (*exclude_province_code).size() == 0) {
        is_address_discount_set(result, is_address_discount);
        return;
      }
      for (auto code : *exclude_province_code) {
        if (user_default_address_list.find(code) != user_default_address_list.end()) {
          is_address_discount = 1;
          break;
        }
      }
      is_address_discount_set(result, is_address_discount);
    });
    return true;
  }

  static bool CalcSiftIdSupplyFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto goodsbase_switch_supply_sift_id_list =
        context.GetIntListCommonAttr("goodsbase_switch_supply_sift_id_list");
    auto i_selection_group_id_list_attr = context.GetIntListItemAttr("iSelectionGroupIdList");
    auto supply_sift_id_flag_attr = context.SetIntItemAttr("supply_sift_id_flag");

    folly::F14FastSet<int64> sift_id_set;
    if (goodsbase_switch_supply_sift_id_list) {
      for (auto sift_id : *goodsbase_switch_supply_sift_id_list) {
        sift_id_set.insert(sift_id);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto i_selection_group_id_list = i_selection_group_id_list_attr(result);
      int supply_sift_id_flag = 0;
      if (i_selection_group_id_list) {
        for (auto group_id : *i_selection_group_id_list) {
          if (sift_id_set.find(group_id) != sift_id_set.end()) {
            supply_sift_id_flag = 1;
            break;
          }
        }
      }
      supply_sift_id_flag_attr(result, supply_sift_id_flag);
    });
    return true;
  }

  static bool CutColossusClickAttrListWithTimestamp(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto click_item_id_attr = context.GetIntListCommonAttr("click_item_id_unlimited");
    auto click_item_timestamp_attr = context.GetIntListCommonAttr("click_item_timestamp_unlimited");
    auto click_category_attr = context.GetIntListCommonAttr("click_category_unlimited");
    auto click_author_id_attr = context.GetIntListCommonAttr("click_author_id_unlimited");
    auto click_price_attr = context.GetIntListCommonAttr("click_price_unlimited");
    auto click_detail_page_view_time_attr =
        context.GetIntListCommonAttr("click_detail_page_view_time_unlimited");
    auto click_flow_type_attr = context.GetIntListCommonAttr("click_flow_type_unlimited");
    auto colossus_filter_ts_180d = context.GetIntCommonAttr("colossus_filter_ts_180d");
    int click_item_id_count = click_item_id_attr ? (*click_item_id_attr).size() : 0;
    int click_item_timestamp_count = click_item_timestamp_attr ? (*click_item_timestamp_attr).size() : 0;
    int click_category_count = click_category_attr ? (*click_category_attr).size() : 0;
    int click_author_id_count = click_author_id_attr ? (*click_author_id_attr).size() : 0;
    int click_price_count = click_price_attr ? (*click_price_attr).size() : 0;
    int click_detail_page_view_time_count =
        click_detail_page_view_time_attr ? (*click_detail_page_view_time_attr).size() : 0;
    int click_flow_type_count = click_flow_type_attr ? (*click_flow_type_attr).size() : 0;
    if (click_item_timestamp_attr) {
      if (click_item_id_count == click_item_timestamp_count &&
          click_category_count == click_item_timestamp_count &&
          click_author_id_count == click_item_timestamp_count &&
          click_price_count == click_item_timestamp_count &&
          click_detail_page_view_time_count == click_item_timestamp_count &&
          click_flow_type_count == click_item_timestamp_count) {
        std::vector<int32> valid_index(click_item_timestamp_count, -1);
        int32 valid_list_size = 0;
        for (int index = 0; index < click_item_timestamp_count; index++) {
          auto timestamp = (*click_item_timestamp_attr)[index];
          if (timestamp > colossus_filter_ts_180d) {
            valid_index[valid_list_size] = index;
            valid_list_size += 1;
          }
        }
        std::vector<int64> final_click_item_id(valid_list_size, 0);
        std::vector<int64> final_click_item_timestamp(valid_list_size, 0);
        std::vector<int64> final_click_category(valid_list_size, 0);
        std::vector<int64> final_click_author_id(valid_list_size, 0);
        std::vector<int64> final_click_price(valid_list_size, 0);
        std::vector<int64> final_click_detail_page_view_time(valid_list_size, 0);
        std::vector<int64> final_click_flow_type(valid_list_size, 0);
        for (int i = 0; i < valid_list_size; i++) {
          auto index = valid_index[i];
          if (index == -1) {
            continue;
          }
          final_click_item_id[i] = (*click_item_id_attr)[index];
          final_click_item_timestamp[i] = (*click_item_timestamp_attr)[index];
          final_click_category[i] = (*click_category_attr)[index];
          final_click_author_id[i] = (*click_author_id_attr)[index];
          final_click_price[i] = (*click_price_attr)[index];
          final_click_detail_page_view_time[i] = (*click_detail_page_view_time_attr)[index];
          final_click_flow_type[i] = (*click_flow_type_attr)[index];
        }
        context.SetIntListCommonAttr("click_item_id", std::move(final_click_item_id));
        context.SetIntListCommonAttr("click_item_timestamp", std::move(final_click_item_timestamp));
        context.SetIntListCommonAttr("click_category", std::move(final_click_category));
        context.SetIntListCommonAttr("click_author_id", std::move(final_click_author_id));
        context.SetIntListCommonAttr("click_price", std::move(final_click_price));
        context.SetIntListCommonAttr("click_detail_page_view_time",
                                     std::move(final_click_detail_page_view_time));
        context.SetIntListCommonAttr("click_flow_type", std::move(final_click_flow_type));
      } else {
        LOG(ERROR) << "MerchantShoppingMallLightFunctionSet CutColossusClickAttrListWithTimestamp failed, "
                      "required attr size inconsistent";
      }
    }
    return true;
  }

  static bool SetChongDingXiangItemAttr(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto uId = context.GetIntCommonAttr("uId");
    auto colossus_ordered_item_id_list = context.GetIntListCommonAttr("colossus_ordered_item_id_list");
    auto colossus_click_item_id_list = context.GetIntListCommonAttr("colossus_click_item_id_list");
    auto colossus_click_item_lagdays_list = context.GetIntListCommonAttr("colossus_click_item_lagdays_list");
    auto colossus_click_item_flow_type_list =
        context.GetIntListCommonAttr("colossus_click_item_flow_type_list");
    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto reason_300016_if_ordered_attr = context.SetIntItemAttr("reason_300016_if_ordered");
    auto reason_300016_click_lag_days_attr = context.SetIntItemAttr("reason_300016_click_lag_days");
    auto reason_300016_click_flow_type_attr = context.SetIntItemAttr("reason_300016_click_flow_type");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      if (item_id == 0) {
        return;
      }

      int64 reason_300016_if_ordered = 0;
      if (colossus_ordered_item_id_list) {
        for (auto colossus_ordered_item_id : *colossus_ordered_item_id_list) {
          if (colossus_ordered_item_id == item_id) {
            reason_300016_if_ordered = 1;
            break;
          }
        }
      }

      reason_300016_if_ordered_attr(result, reason_300016_if_ordered);

      int64 reason_300016_click_lag_days = -1;
      int64 reason_300016_click_flow_type = -1;
      if (colossus_click_item_id_list && colossus_click_item_lagdays_list &&
          colossus_click_item_flow_type_list) {
        for (int i = 0; i < (*colossus_click_item_id_list).size(); ++i) {
          if ((*colossus_click_item_id_list)[i] == item_id) {
            reason_300016_click_lag_days = (*colossus_click_item_lagdays_list)[i];
            reason_300016_click_flow_type = (*colossus_click_item_flow_type_list)[i];
            break;
          }
        }
      }

      reason_300016_click_lag_days_attr(result, reason_300016_click_lag_days);
      reason_300016_click_flow_type_attr(result, reason_300016_click_flow_type);
    });

    return true;
  }

  static bool ParseStringUint64VectorMap(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto key_map_attr = context.GetPtrCommonAttr<folly::F14FastMap<std::string,
                          std::vector<int64>>>("same_goods_item_list_ptr");
    bool valid_key_map = (key_map_attr && key_map_attr->size() > 0);

    auto item_id_attr = context.GetIntItemAttr("item_id");
    if (valid_key_map) {
      auto same_goods_result_set = context.SetIntListItemAttr("same_goods_candidate_list");

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::string key_string = std::to_string(item_id_attr(result).value_or(0));

        auto it = key_map_attr->find(key_string);
        if (it != key_map_attr->end()) {
          const auto &result_vec = it->second;
          std::vector<int64> tmp_1d;
          for (int j = 0; j < result_vec.size(); ++j) {
            tmp_1d.emplace_back(result_vec[j]);
          }
          same_goods_result_set(result, tmp_1d);
        }
      });
    }
    return true;
  }

  static bool GetSameGoodsExtend(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto same_goods_extend_num_per_key =
               context.GetIntCommonAttr("same_goods_extend_num_per_key").value_or(1);
    auto same_goods_extend_recall_num =
               context.GetIntCommonAttr("same_goods_extend_recall_num").value_or(1000);

    auto same_goods_candidate_list_attr = context.GetStringListItemAttr("iGoodsPriceOrderCompetitorIdList");
    auto this_score_attr = context.GetIntItemAttr("iGoodsShelf30dOrder");
    auto item_id_attr = context.GetIntItemAttr("item_id");

    std::vector<std::pair<int64, int64>> score_list;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      auto same_goods_candidate_list = same_goods_candidate_list_attr(result);
      auto this_score = this_score_attr(result).value_or(0);

      if (same_goods_candidate_list->size() > 0) {
        int num_per_key = same_goods_extend_num_per_key;
        if (same_goods_candidate_list->size() < num_per_key) {
          num_per_key = same_goods_candidate_list->size();
        }
        for (int j = 0; j < num_per_key; j++) {
          const absl::string_view item_strview = (*same_goods_candidate_list)[j];
          int64 id = 0;
          if (!absl::SimpleAtoi(item_strview, &id)) {
            id = 0;
          }
          if (id > 0) {
            score_list.emplace_back(std::make_pair(this_score, id));
          }
        }
      }
    });

    std::vector<int64> item_result_list;
    folly::F14FastSet<int64> recall_set;
    int retr_num_cnt = 0;

    if (score_list.size() > 0) {
      if (score_list.size() > same_goods_extend_recall_num) {
        std::sort(score_list.begin(), score_list.end(),
                  [](const auto &a, const auto &b) { return a.first > b.first; });
      }

      for (int i = 0; i < same_goods_extend_recall_num && i < score_list.size() &&
                  retr_num_cnt < same_goods_extend_recall_num; i++) {
        if (recall_set.find(score_list[i].second) == recall_set.end()) {
          item_result_list.emplace_back(score_list[i].second);
          recall_set.insert(score_list[i].second);
          retr_num_cnt++;
        }
      }
      score_list.clear();
    }

    context.SetIntListCommonAttr("same_goods_item_list", std::move(item_result_list));
    return true;
  }

  static bool GetVhListFlagFromPtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto vh_blacklist_seller_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("vh_seller_memory_data_list");
    bool valid_aid_set = (vh_blacklist_seller_set && vh_blacklist_seller_set->size() > 0);

    auto seller_id_attr = context.GetIntItemAttr("seller_id");
    auto write_isFilterSellerIdFlag = context.SetIntItemAttr("isFilterSellerIdFlag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto seller_id = seller_id_attr(result).value_or(0);
      int is_filter_seller_id_flag = 0;
      if (valid_aid_set && vh_blacklist_seller_set->count(seller_id) > 0) {
        is_filter_seller_id_flag = 1;
      }
      write_isFilterSellerIdFlag(result, is_filter_seller_id_flag);
    });

    return true;
  }

  static bool GenerateCateLastPayLag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto order_category_list = context.GetIntListCommonAttr("order_category_unlimited");
    auto order_item_timestamp_list = context.GetIntListCommonAttr("order_pay_timestamp_unlimited");
    auto create_refund_time_list =
        context.GetIntListCommonAttr("uStandardMerchantCreateRefundOrderPayTimeList");
    auto ts = context.GetIntCommonAttr("_REQ_TIME_").value_or(0) / 1000;
    auto cate_id_attr = context.GetIntItemAttr("iCate3OrLeafId");
    auto cate_last_pay_lag = context.SetIntItemAttr("cate_last_pay_lag");
    std::unordered_set<int64> refund_time_set;
    if (create_refund_time_list) {
      for (auto t : *create_refund_time_list) {
        refund_time_set.insert(t);
      }
    }
    std::unordered_map<int64, int64> cate_last_time_map;
    if (order_category_list && order_item_timestamp_list) {
      for (auto i = 0; i < order_category_list->size(); i++) {
        auto cateid = (*order_category_list)[i];
        auto pay_ts = order_item_timestamp_list->size() > i ? (*order_item_timestamp_list)[i] : 0;
        if (pay_ts <= 0) {
          continue;
        }
        if (refund_time_set.find(pay_ts) != refund_time_set.end()) {
          continue;
        }
        auto cid = (cateid >> 16) & 0xffff;
        if (cid <= 0) {
          cid = (cateid >> 32) & 0xffff;
        }
        if (cid <= 0) {
          cid = (cateid >> 48) & 0xffff;
        }
        auto it = cate_last_time_map.find(cid);
        if (it == cate_last_time_map.end() || it->second < pay_ts) {
          cate_last_time_map[cid] = pay_ts;
        }
      }
    }
    int total_repay_cate_num = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cate_id = cate_id_attr(result).value_or(0);
      auto it = cate_last_time_map.find(cate_id);
      if (it == cate_last_time_map.end()) {
        return;
      }
      cate_last_pay_lag(result, ts - it->second);
      ++total_repay_cate_num;
    });
    context.SetIntCommonAttr("total_repay_cate_item_num", total_repay_cate_num);
    return true;
  }

  static bool GenClkBuyItemCate3StatInfo(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
    auto good_click_cate3_list = context.GetIntListCommonAttr("good_click_cate3_list");
    auto good_click_cate3_count_list = context.GetIntListCommonAttr(
      "good_click_cate3_count_list");
    auto good_click_cate3_ratio_list = context.GetIntListCommonAttr(
      "good_click_cate3_ratio_list");
    auto good_click_cate3_last_lag_list = context.GetIntListCommonAttr(
      "good_click_cate3_last_lag_list");
    auto good_order_cate3_list = context.GetIntListCommonAttr(
      "good_order_cate3_list");
    auto good_order_cate3_count_list = context.GetIntListCommonAttr(
      "good_order_cate3_count_list");
    auto good_order_cate3_ratio_list = context.GetIntListCommonAttr(
      "good_order_cate3_ratio_list");
    auto good_order_cate3_last_lag_list = context.GetIntListCommonAttr(
      "good_order_cate3_last_lag_list");

    auto cate_id_attr = context.GetIntItemAttr("iCate3Id");
    std::unordered_map<int64, int64> cate3_click_lag_map;
    std::unordered_map<int64, int64> cate3_click_cnt_map;
    std::unordered_map<int64, double> cate3_click_ratio_map;

    std::unordered_map<int64, int64> cate3_order_lag_map;
    std::unordered_map<int64, int64> cate3_order_cnt_map;
    std::unordered_map<int64, double> cate3_order_ratio_map;
    if (good_click_cate3_list && good_click_cate3_last_lag_list &&
    good_click_cate3_count_list && good_click_cate3_ratio_list) {
      for (auto i = 0; i < good_click_cate3_list->size(); i++) {
        auto cateid = good_click_cate3_list->at(i);
        cate3_click_lag_map[cateid] = good_click_cate3_last_lag_list->at(i);
        cate3_click_cnt_map[cateid] = good_click_cate3_count_list->at(i);
        cate3_click_ratio_map[cateid] = good_click_cate3_ratio_list->at(i);
      }
    }
    if (good_order_cate3_list && good_order_cate3_last_lag_list &&
    good_order_cate3_count_list && good_order_cate3_ratio_list) {
      for (auto i = 0; i < good_order_cate3_list->size(); i++) {
        auto cateid = good_order_cate3_list->at(i);
        cate3_order_lag_map[cateid] = good_order_cate3_last_lag_list->at(i);
        cate3_order_cnt_map[cateid] = good_order_cate3_count_list->at(i);
        cate3_order_ratio_map[cateid] = good_order_cate3_ratio_list->at(i);
      }
    }

    auto icate3_click_last_lag = context.SetIntItemAttr("icate3_click_last_lag");
    auto icate3_click_cnt = context.SetIntItemAttr("icate3_click_cnt");
    auto icate3_click_ratio = context.SetDoubleItemAttr("icate3_click_ratio");
    auto icate3_order_last_lag = context.SetIntItemAttr("icate3_order_last_lag");
    auto icate3_order_cnt = context.SetIntItemAttr("icate3_order_cnt");
    auto icate3_order_ratio = context.SetDoubleItemAttr("icate3_order_ratio");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cate_id = cate_id_attr(result).value_or(0);
      auto it1 = cate3_click_lag_map.find(cate_id);
      if (it1 != cate3_click_lag_map.end()) {
        icate3_click_last_lag(result, cate3_click_lag_map.at(cate_id));
      }
      auto it2 = cate3_click_cnt_map.find(cate_id);
      if (it2 != cate3_click_cnt_map.end()) {
        icate3_click_cnt(result, cate3_click_cnt_map.at(cate_id));
      }
      auto it3 = cate3_click_ratio_map.find(cate_id);
      if (it3 != cate3_click_ratio_map.end()) {
        icate3_click_ratio(result, cate3_click_ratio_map.at(cate_id));
      }

      auto it4 = cate3_order_lag_map.find(cate_id);
      if (it4 != cate3_order_lag_map.end()) {
        icate3_order_last_lag(result, cate3_order_lag_map.at(cate_id));
      }
      auto it5 = cate3_order_cnt_map.find(cate_id);
      if (it5 != cate3_order_cnt_map.end()) {
        icate3_order_cnt(result, cate3_order_cnt_map.at(cate_id));
      }
      auto it6 = cate3_order_ratio_map.find(cate_id);
      if (it6 != cate3_order_ratio_map.end()) {
        icate3_order_ratio(result, cate3_order_ratio_map.at(cate_id));
      }
    });
    return true;
  }

  static bool GenerateGoodsExploreFlag(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto ban_explore_cate1_list = context.GetIntListCommonAttr("ban_explore_cate1_list");
    auto click_flow_type_180d = context.GetIntListCommonAttr("click_flow_type_180d");
    auto click_category_180d = context.GetIntListCommonAttr("click_category_180d");
    auto click_item_timestamp_180d = context.GetIntListCommonAttr("click_item_timestamp_180d");
    auto ts = context.GetIntCommonAttr("_REQ_TIME_").value_or(0) / 1000;
    auto bh_gyl_get_item_action_n_days =
        context.GetIntCommonAttr("bh_gyl_get_item_action_n_days").value_or(0);
    auto realtime_order_thres =
        context.GetIntCommonAttr("bh_gyl_bestauction_realtime_order_thres").value_or(0);
    auto offline_order_thres = context.GetIntCommonAttr("bh_gyl_bestauction_offline_order_thres").value_or(0);
    auto min_ts = ts - bh_gyl_get_item_action_n_days * 86400;
    auto cate3_id_attr = context.GetIntItemAttr("iCate3Id");
    auto cate2_id_attr = context.GetIntItemAttr("iCate2Id");
    auto cate1_id_attr = context.GetIntItemAttr("iCate1Id");
    auto reason_attr = context.GetIntItemAttr("reason");
    auto is_dpdb = context.GetIntItemAttr("iGoodsIfDpdb");
    auto price_star = context.GetIntItemAttr("iGoodsNewPriceStarLevel");
    auto realtime_order = context.GetIntItemAttr("shelfGoodsLast12hOrderCnt");
    auto offline_order = context.GetIntItemAttr("iLast30dSalesOrderCnt");
    auto cate1_explore_flag = context.SetIntItemAttr("cate1_explore_flag");
    auto cate2_explore_flag = context.SetIntItemAttr("cate2_explore_flag");
    auto cate3_explore_flag = context.SetIntItemAttr("cate3_explore_flag");
    folly::F14FastSet<int64> ban_c1_set;
    if (ban_explore_cate1_list) {
      for (auto id : *ban_explore_cate1_list) {
        ban_c1_set.insert(id);
      }
    }
    folly::F14FastSet<int64> c1_set;
    folly::F14FastSet<int64> c2_set;
    folly::F14FastSet<int64> c3_set;
    if (click_flow_type_180d && click_category_180d && click_item_timestamp_180d) {
      for (auto i = 0; i < click_category_180d->size(); i++) {
        auto cid = (*click_category_180d)[i];
        auto click_ts = click_item_timestamp_180d->size() > i ? (*click_item_timestamp_180d)[i] : 0;
        auto click_type = click_flow_type_180d->size() > i ? (*click_flow_type_180d)[i] : 0;
        if (click_ts <= min_ts) {
          continue;
        }
        click_type = (click_type >> 16) & 0xff;
        if (click_type != 3) {
          continue;
        }
        c1_set.insert((cid >> 48) & 0xffff);
        c2_set.insert((cid >> 32) & 0xffff);
        c3_set.insert((cid >> 16) & 0xffff);
      }
    }
    int total_c3_num = 0;
    int total_c2_num = 0;
    int total_c1_num = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto reason = reason_attr(result).value_or(0);
      if (reason == 300016) {
        return;
      }
      bool set_flag = false;
      if (is_dpdb(result).value_or(0) > 0) {
        set_flag = true;
      } else if (price_star(result).value_or(0) >= 104) {
        set_flag = true;
      } else if (realtime_order(result).value_or(0) >= realtime_order_thres) {
        set_flag = true;
      } else if (offline_order(result).value_or(0) >= offline_order_thres) {
        set_flag = true;
      }
      if (!set_flag) {
        return;
      }
      auto cate1_id = cate1_id_attr(result).value_or(0);
      if (ban_c1_set.find(cate1_id) != ban_c1_set.end()) {
        return;
      }
      auto cate3_id = cate3_id_attr(result).value_or(0);
      if (c3_set.find(cate3_id) == c3_set.end()) {
        cate3_explore_flag(result, 1);
        ++total_c3_num;
      }
      auto cate2_id = cate2_id_attr(result).value_or(0);
      if (c2_set.find(cate2_id) == c2_set.end()) {
        cate2_explore_flag(result, 1);
        ++total_c2_num;
      }
      if (c1_set.find(cate1_id) == c1_set.end()) {
        cate1_explore_flag(result, 1);
        ++total_c1_num;
      }
    });
    context.SetIntCommonAttr("diversity_explore_c3_item_num", total_c3_num);
    context.SetIntCommonAttr("diversity_explore_c2_item_num", total_c2_num);
    context.SetIntCommonAttr("diversity_explore_c1_item_num", total_c1_num);
    return true;
  }

  static bool KeyIntListValueIntListMatch(const CommonRecoLightFunctionContext &context,
                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto key_int_list = context.GetIntListCommonAttr("key_int_list");
    auto value_int_list = context.GetIntListCommonAttr("value_int_list");
    auto item_key_attr = context.GetIntItemAttr("item_match_key");
    auto output_attr = context.SetIntItemAttr("output_attr");
    folly::F14FastMap<int64, int64> kv_map;
    if (key_int_list) {
      if (value_int_list) {
        if (key_int_list->size() == value_int_list->size()) {
          for (int i = 0; i < key_int_list->size(); i++) {
            int64 key = (*key_int_list)[i];
            int64 value = (*value_int_list)[i];
            kv_map[key] = value;
          }
        }
      }
    }
    if (kv_map.empty()) {
      return true;
    }
    int hit_num = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_key = item_key_attr(result).value_or(0);
      if (item_key == 0) {
        return;
      }
      auto it = kv_map.find(item_key);
      if (it == kv_map.end()) {
        return;
      }
      ++hit_num;
      output_attr(result, it->second);
    });
    context.SetIntCommonAttr("kv_match_hit_num", hit_num);
    return true;
  }

  static bool AuditionDiversityRerank(const CommonRecoLightFunctionContext &context,
                                 RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto discount_coeff = context.GetDoubleCommonAttr("audition_diversity_dicount_coeff").value_or(0.0);
    auto audition_diversity_truncate_num =
        context.GetIntCommonAttr("audition_diversity_truncate_num").value_or(10000);
    auto cate_id_attr = context.GetIntItemAttr("leafCateId");
    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto score_attr = context.GetDoubleItemAttr("huge_recall_score");
    auto audition_diversity_rerank_attr = context.SetIntItemAttr("audition_diversity_rank");
    std::unordered_map<int64, MallGoodsAggrItemList> cate_aggr_item_list_map;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      if (item_id == 0) {
        return;
      }
      auto cate_id = cate_id_attr(result).value_or(0);
      auto score = std::exp(score_attr(result).value_or(0.0));
      auto it = cate_aggr_item_list_map.find(cate_id);
      if (it != cate_aggr_item_list_map.end()) {
          it->second.item_list.push_back(std::make_pair(item_id, score));
          it->second.score = std::max(it->second.score, score);
      } else {
          MallGoodsAggrItemList item_list(cate_id);
          item_list.score = std::max(item_list.score, score);
          item_list.item_list.push_back(std::make_pair(item_id, score));
          cate_aggr_item_list_map.insert(std::make_pair(cate_id, item_list));
      }
    });
    std::vector<MallGoodsAggrItemList*> aggr_item_lists;
    for (auto it = cate_aggr_item_list_map.begin(); it != cate_aggr_item_list_map.end(); it++) {
        std::sort(it->second.item_list.begin(), it->second.item_list.end(),
            [](const std::pair<int64, float> & a, const std::pair<int64, float> & b) {
                return a.second < b.second; });
        aggr_item_lists.push_back(&it->second);
    }
    auto cate_aggr_item_list_heap_cmp = [](const MallGoodsAggrItemList* a, const MallGoodsAggrItemList* b) {
        return a->score < b->score; };
    std::make_heap(aggr_item_lists.begin(), aggr_item_lists.end(), cate_aggr_item_list_heap_cmp);
    std::unordered_map<int64, int64> ret_rank_map;
    int64 rank_offset = audition_diversity_truncate_num;
    while (ret_rank_map.size() < audition_diversity_truncate_num && aggr_item_lists.size() > 0) {
        std::pop_heap(aggr_item_lists.begin(), aggr_item_lists.end(), cate_aggr_item_list_heap_cmp);
        MallGoodsAggrItemList* top = aggr_item_lists.back();
        aggr_item_lists.pop_back();
        if (top == nullptr || top->item_list.size() == 0) {
            continue;
        }
        int64 id = top->item_list.back().first;
        ret_rank_map.insert(std::make_pair(id, rank_offset--));
        top->item_list.pop_back();
        if (top->item_list.size() == 0) {
            continue;
        }
        ++top->dup_cnt;
        top->score = top->item_list.back().second - top->dup_cnt * discount_coeff;
        aggr_item_lists.push_back(top);
        std::push_heap(aggr_item_lists.begin(), aggr_item_lists.end(), cate_aggr_item_list_heap_cmp);
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      auto rank = ret_rank_map[item_id];
      audition_diversity_rerank_attr(result, rank);
    });

    return true;
  }

  static bool I2IRetrievalRerank(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto click_item_id_unlimited = context.GetIntListCommonAttr("click_item_id_unlimited");
    auto i2i_trigger_score_list = context.GetDoubleListCommonAttr("i2i_trigger_score_list");
    auto i2i_trigger_id_list = context.GetIntListCommonAttr("i2i_trigger_id_list");
    auto guess_like_swing_retrieval_value_list =
        context.GetStringListCommonAttr("guess_like_swing_retrieval_value_list");
    auto guess_like_swing_retrieval_presort_truncnum =
        context.GetIntCommonAttr("guess_like_swing_retrieval_presort_truncnum").value_or(0);
    auto guess_like_swing_retrieval_merge_type =
        context.GetIntCommonAttr("guess_like_swing_retrieval_merge_type").value_or(0);
    folly::F14FastSet<int64> item_set;
    if (click_item_id_unlimited) {
      for (auto item_id : *click_item_id_unlimited) {
        item_set.insert(item_id);
      }
    }
    if (!guess_like_swing_retrieval_value_list) {
      return true;
    }
    if (!i2i_trigger_score_list) {
      return true;
    }
    if (!i2i_trigger_id_list) {
      return true;
    }
    std::unordered_map<int64, MallGoodsRetrievalItem> retrievel_map;
    for (auto i = 0; i < guess_like_swing_retrieval_value_list->size(); i++) {
      std::vector<std::string> n_split_array =
          absl::StrSplit((*guess_like_swing_retrieval_value_list)[i], ",");
      auto trigger_score = i2i_trigger_score_list->size() > i ? (*i2i_trigger_score_list)[i] : 0;
      auto trigger_id = i2i_trigger_id_list->size() > i ? (*i2i_trigger_id_list)[i] : 0;
      for (auto &str : n_split_array) {
        std::vector<std::string> kv = absl::StrSplit(str, ":");
        if (kv.size() < 2) {
          continue;
        }
        int64 id = 0;
        if (!absl::SimpleAtoi(kv[0], &id)) {
          id = 0;
        }
        if (id <= 0 || item_set.find(id) != item_set.end()) {
          continue;
        }
        float sim_score = 0.0f;
        if (!absl::SimpleAtof(kv[1], &sim_score)) {
          sim_score = 0.0f;
        }
        if (guess_like_swing_retrieval_merge_type == 1) {
          sim_score = 1;
        }
        auto it = retrievel_map.find(id);
        if (it == retrievel_map.end()) {
          retrievel_map.insert(std::make_pair(id, MallGoodsRetrievalItem(id)));
          it = retrievel_map.find(id);
        }
        it->second.score += trigger_score * sim_score;
        it->second.trigger_list.emplace_back(std::make_pair(trigger_id, trigger_score * sim_score));
        if (guess_like_swing_retrieval_merge_type == 2) {
          if (retrievel_map.size() >= guess_like_swing_retrieval_presort_truncnum) {
            break;
          }
        }
      }
      if (guess_like_swing_retrieval_merge_type == 2) {
        if (retrievel_map.size() >= guess_like_swing_retrieval_presort_truncnum) {
          break;
        }
      }
    }
    std::vector<MallGoodsRetrievalItem> rank_list;
    for (auto it = retrievel_map.begin(); it != retrievel_map.end(); it++) {
      rank_list.emplace_back(it->second);
    }
    if (rank_list.size() > guess_like_swing_retrieval_presort_truncnum) {
      std::sort(rank_list.begin(), rank_list.end(),
                [](const auto &a, const auto &b) { return a.score > b.score; });
    }
    std::vector<int64> id_list;
    std::vector<double> score_list;
    std::vector<std::string> trigger_item_list;
    std::vector<std::string> trigger_items_list;
    for (auto i = 0; i < rank_list.size() && i < guess_like_swing_retrieval_presort_truncnum; i++) {
      auto &item = rank_list[i];
      if (item.trigger_list.size() < 1) {
        continue;
      }
      if (item.trigger_list.size() > 1) {
        std::sort(item.trigger_list.begin(), item.trigger_list.end(),
                  [](const auto &a, const auto &b) { return a.second > b.second; });
      }
      id_list.push_back(item.id);
      score_list.push_back(item.score);
      trigger_item_list.emplace_back(std::to_string(item.trigger_list[0].first));
      std::vector<int64> trigger_list;
      for (auto trigger : item.trigger_list) {
        trigger_list.push_back(trigger.first);
      }
      trigger_items_list.emplace_back(folly::join(",", trigger_list));
    }
    context.SetIntListCommonAttr("retrieval_id_list", std::move(id_list));
    context.SetDoubleListCommonAttr("retrieval_score_list", std::move(score_list));
    context.SetStringListCommonAttr("trigger_item_list", std::move(trigger_item_list));
    context.SetStringListCommonAttr("trigger_items_list", std::move(trigger_items_list));
    return true;
  }

  static bool GenerateGoodsDiversityFlag(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto pageshow_before_sessions_item_id_list =
        context.GetIntListCommonAttr("pageshow_before_sessions_item_id_list");
    auto diversity_c1_num = context.GetIntCommonAttr("guess_like_diversity_cate1_num").value_or(0);
    auto diversity_item_num = context.GetIntCommonAttr("guess_like_diversity_item_num").value_or(0);
    diversity_c1_num = diversity_c1_num < diversity_item_num ? diversity_c1_num : diversity_item_num;
    auto cate1_id_attr = context.GetIntItemAttr("iGoodsMmuCate1Id");
    auto seller_id_attr = context.GetIntItemAttr("iAuthorId");
    auto spu_id_attr = context.GetIntItemAttr("iGoodsSpuV3Id");
    auto brand_id_attr = context.GetIntItemAttr("brandId");
    auto reason_attr = context.GetIntItemAttr("reason");
    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto cate_last_pay_lag_attr = context.GetIntItemAttr("cate_last_pay_lag");
    auto diversity_force_flag = context.SetIntItemAttr("diversity_force_flag");
    folly::F14FastSet<int64> c1_set;
    folly::F14FastSet<int64> seller_set;
    folly::F14FastSet<int64> spu_set;
    folly::F14FastSet<int64> brand_set;
    folly::F14FastSet<int64> showed_item_set;
    if (pageshow_before_sessions_item_id_list) {
      for (auto showed_item_id : *pageshow_before_sessions_item_id_list) {
        showed_item_set.insert(showed_item_id);
      }
    }
    int total_num = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (total_num >= diversity_item_num) {
        return;
      }
      auto item_id = item_id_attr(result).value_or(0);
      if (item_id == 0) {
        return;
      }
      auto reason = reason_attr(result).value_or(0);
      if (reason == 300016) {
        return;
      }
      auto cate_last_pay_lag = cate_last_pay_lag_attr(result).value_or(-1);
      if (cate_last_pay_lag > 0) {
        return;
      }
      if (showed_item_set.find(item_id) != showed_item_set.end()) {
        return;
      }
      auto seller_id = seller_id_attr(result).value_or(0);
      if (seller_set.find(seller_id) != seller_set.end()) {
        return;
      }
      auto spu_id = spu_id_attr(result).value_or(0);
      if (spu_set.find(spu_id) != spu_set.end()) {
        return;
      }
      auto brand_id = brand_id_attr(result).value_or(0);
      if (brand_set.find(brand_id) != brand_set.end()) {
        return;
      }
      auto cate1_id = cate1_id_attr(result).value_or(0);
      bool set_flag = false;
      if (total_num - c1_set.size() < diversity_item_num - diversity_c1_num) {
        set_flag = true;
      } else if (c1_set.find(cate1_id) == c1_set.end()) {
        set_flag = true;
      }
      if (!set_flag) {
        return;
      }
      diversity_force_flag(result, 1);
      ++total_num;
      c1_set.insert(cate1_id);
      seller_set.insert(seller_id);
      brand_set.insert(brand_id);
      spu_set.insert(spu_id);
    });
    context.SetIntCommonAttr("diversity_force_item_num", total_num);
    return true;
  }

  static bool SplitBySeparatorToList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    /*本函数将s zFullString 按 szSeparator 分割为字符串*/
    auto sz_separator = context.GetStringCommonAttr("sz_separator");
    auto sz_full_string = context.GetStringCommonAttr("sz_full_string");

    if (sz_separator && sz_full_string) {
      std::vector<std::string> n_split_array = absl::StrSplit(*sz_full_string, *sz_separator);
      context.SetStringListCommonAttr("n_split_array", std::move(n_split_array));
    }

    return true;
  }

  static bool SplitBySeparatorsToMap(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    /*
    本函数将 szFullString 按 szSeparator1 和 szSeparator2 分割为 map
    由于 CommonRecoLightFunctionContext 函数限制
    将 key 和 value 分别存储于 nSplitArrayKey nSplitArrayValue, 其中相应下边分别代表同一对象的 key, value
     */
    auto sz_separator1 = context.GetStringCommonAttr("sz_separator1");
    auto sz_separator2 = context.GetStringCommonAttr("sz_separator2");
    auto sz_full_string = context.GetStringCommonAttr("sz_full_string");

    if (sz_separator1 && sz_separator2 && sz_full_string) {
      std::vector<std::string> n_split_array_key;
      std::vector<std::string> n_split_array_value;
      for (auto sv : absl::StrSplit(*sz_full_string, *sz_separator1)) {
        std::vector<std::string> n_split_list = absl::StrSplit(sv, *sz_separator2);
        for (int i = 0; i < n_split_list.size() / 2; ++i) {
          // nSplitList 如果为奇数长度，将丢弃最后一个
          n_split_array_key.push_back(n_split_list[2 * i]);
          n_split_array_value.push_back(n_split_list[2 * i + 1]);
        }
      }
      context.SetStringListCommonAttr("n_split_array_key", std::move(n_split_array_key));
      context.SetStringListCommonAttr("n_split_array_value", std::move(n_split_array_value));
    }

    return true;
  }

  static bool CalcItemContentEmb(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto ocr_dim = context.GetIntCommonAttr("ocr_dim").value_or(0);
    auto text_dim = context.GetIntCommonAttr("text_dim").value_or(0);
    auto image_dim = context.GetIntCommonAttr("image_dim").value_or(0);
    auto top_n = context.GetIntCommonAttr("mm_top_n").value_or(0);

    auto version = context.GetIntCommonAttr("version").value_or(0);

    if (version == 0) {
      auto target_photo_content_embedding_attr =
          context.GetDoubleListItemAttr("target_photo_content_embedding");

      auto target_photo_content_embedding_ocr_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_ocr");
      auto target_photo_content_embedding_text_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_text");
      auto target_photo_content_embedding_image1_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image1");
      auto target_photo_content_embedding_image2_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image2");
      auto target_photo_content_embedding_image3_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image3");

      int64 item_dims = ocr_dim + text_dim + image_dim * 3;
      std::for_each(begin, end, [&](const CommonRecoResult &result) -> void {
        auto target_photo_content_embedding =
            target_photo_content_embedding_attr(result).value_or(absl::Span<const double>());
        if (target_photo_content_embedding.empty()) {
          return;
        }

        std::vector<double> ocr_emb, text_emb, image_emb1, image_emb2, image_emb3;
        for (int i = 0; i < item_dims; ++i) {
          double item = target_photo_content_embedding[i];
          if (i < ocr_dim) {
            ocr_emb.push_back(item);
          } else if (i < (ocr_dim + text_dim)) {
            text_emb.push_back(item);
          } else if (i < (ocr_dim + text_dim + image_dim)) {
            image_emb1.push_back(item);
          } else if (i < (ocr_dim + text_dim + image_dim * 2)) {
            image_emb2.push_back(item);
          } else if (i < (ocr_dim + text_dim + image_dim * 3)) {
            image_emb3.push_back(item);
          }
        }

        target_photo_content_embedding_ocr_attr(result, ocr_emb);
        target_photo_content_embedding_text_attr(result, text_emb);
        target_photo_content_embedding_image1_attr(result, image_emb1);
        target_photo_content_embedding_image2_attr(result, image_emb2);
        target_photo_content_embedding_image3_attr(result, image_emb3);
      });

      auto user_buy_item_content_emb_raw = context.GetDoubleListCommonAttr("user_buy_item_content_emb_raw");
      if (user_buy_item_content_emb_raw && !(*user_buy_item_content_emb_raw).empty()) {
        auto content_emb = (*user_buy_item_content_emb_raw);
        std::vector<double> ocr_emb, text_emb, image_emb1, image_emb2, image_emb3;
        int remainder = 0;
        for (int i = 0; i < top_n * item_dims; ++i) {
          double item = (i >= content_emb.size() ? 0.0 : content_emb[i]);
          remainder = i % item_dims;
          if (remainder < ocr_dim) {
            ocr_emb.push_back(item);
          } else if (remainder < (ocr_dim + text_dim)) {
            text_emb.push_back(item);
          } else if (remainder < (ocr_dim + text_dim + image_dim)) {
            image_emb1.push_back(item);
          } else if (remainder < (ocr_dim + text_dim + image_dim * 2)) {
            image_emb2.push_back(item);
          } else if (remainder < (ocr_dim + text_dim + image_dim * 3)) {
            image_emb3.push_back(item);
          }
        }

        context.SetDoubleListCommonAttr("user_buy_ocr_emb_latest50", std::move(ocr_emb));
        context.SetDoubleListCommonAttr("user_buy_text_emb_latest50", std::move(text_emb));
        context.SetDoubleListCommonAttr("user_buy_image1_emb_latest50", std::move(image_emb1));
        context.SetDoubleListCommonAttr("user_buy_image2_emb_latest50", std::move(image_emb2));
        context.SetDoubleListCommonAttr("user_buy_image3_emb_latest50", std::move(image_emb3));
      }
    } else if (version == 1) {
      auto target_photo_content_embedding_attr =
          context.GetDoubleListItemAttr("target_photo_content_embedding");

      auto target_photo_content_embedding_text_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_text");
      auto target_photo_content_embedding_image1_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image1");
      auto target_photo_content_embedding_image2_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image2");
      auto target_photo_content_embedding_image3_attr =
          context.SetDoubleListItemAttr("target_photo_content_embedding_image3");

      int64 item_dims = text_dim + image_dim * 3;
      std::for_each(begin, end, [&](const CommonRecoResult &result) -> void {
        auto target_photo_content_embedding =
            target_photo_content_embedding_attr(result).value_or(absl::Span<const double>());
        if (target_photo_content_embedding.empty()) {
          return;
        }

        std::vector<double> text_emb, image_emb1, image_emb2, image_emb3;
        for (int i = 0; i < item_dims; ++i) {
          double item = target_photo_content_embedding[i];
          if (i < text_dim) {
            text_emb.push_back(item);
          } else if (i < (text_dim + image_dim)) {
            image_emb1.push_back(item);
          } else if (i < (text_dim + image_dim * 2)) {
            image_emb2.push_back(item);
          } else if (i < (text_dim + image_dim * 3)) {
            image_emb3.push_back(item);
          }
        }

        target_photo_content_embedding_text_attr(result, text_emb);
        target_photo_content_embedding_image1_attr(result, image_emb1);
        target_photo_content_embedding_image2_attr(result, image_emb2);
        target_photo_content_embedding_image3_attr(result, image_emb3);
      });

      auto user_buy_item_content_emb_raw = context.GetDoubleListCommonAttr("user_buy_item_content_emb_raw");
      if (user_buy_item_content_emb_raw && !(*user_buy_item_content_emb_raw).empty()) {
        auto content_emb = (*user_buy_item_content_emb_raw);
        std::vector<double> text_emb, image_emb1, image_emb2, image_emb3;
        int remainder = 0;
        for (int i = 0; i < top_n * item_dims; ++i) {
          double item = (i >= content_emb.size() ? 0.0 : content_emb[i]);
          remainder = i % item_dims;
          if (remainder < text_dim) {
            text_emb.push_back(item);
          } else if (remainder < (text_dim + image_dim)) {
            image_emb1.push_back(item);
          } else if (remainder < (text_dim + image_dim * 2)) {
            image_emb2.push_back(item);
          } else if (remainder < (text_dim + image_dim * 3)) {
            image_emb3.push_back(item);
          }
        }

        context.SetDoubleListCommonAttr("user_buy_text_emb_latest50", std::move(text_emb));
        context.SetDoubleListCommonAttr("user_buy_image1_emb_latest50", std::move(image_emb1));
        context.SetDoubleListCommonAttr("user_buy_image2_emb_latest50", std::move(image_emb2));
        context.SetDoubleListCommonAttr("user_buy_image3_emb_latest50", std::move(image_emb3));
      }

      auto user_click_item_content_emb_raw =
          context.GetDoubleListCommonAttr("user_click_item_content_emb_raw");
      if (user_click_item_content_emb_raw && !(*user_click_item_content_emb_raw).empty()) {
        auto content_emb = (*user_click_item_content_emb_raw);
        std::vector<double> text_emb, image_emb1, image_emb2, image_emb3;
        int remainder = 0;
        for (int i = 0; i < top_n * item_dims; ++i) {
          double item = (i >= content_emb.size() ? 0.0 : content_emb[i]);
          remainder = i % item_dims;
          if (remainder < text_dim) {
            text_emb.push_back(item);
          } else if (remainder < (text_dim + image_dim)) {
            image_emb1.push_back(item);
          } else if (remainder < (text_dim + image_dim * 2)) {
            image_emb2.push_back(item);
          } else if (remainder < (text_dim + image_dim * 3)) {
            image_emb3.push_back(item);
          }
        }

        context.SetDoubleListCommonAttr("user_click_text_emb_latest50", std::move(text_emb));
        context.SetDoubleListCommonAttr("user_click_image1_emb_latest50", std::move(image_emb1));
        context.SetDoubleListCommonAttr("user_click_image2_emb_latest50", std::move(image_emb2));
        context.SetDoubleListCommonAttr("user_click_image3_emb_latest50", std::move(image_emb3));
      }
    }
    return true;
  }

  static bool PrerankGpmCal(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    auto ctr_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_ctr_weight").value_or(1.0);
    auto cvr_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_cvr_weight").value_or(0.8);
    auto price_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_price_weight").value_or(0.0);
    auto gmv_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_gmv_weight").value_or(0.0);
    auto cascade_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_cascade_new_weight").value_or(0.0);
    auto stayTime_w = context.GetDoubleCommonAttr("guess_like_bh_pre_rank_stayTime_weight").value_or(0.0);

    auto prerank_ctr_attr = context.GetDoubleItemAttr("prerank_ctr");
    auto prerank_cvr_attr = context.GetDoubleItemAttr("prerank_cvr");
    auto iLast30dAvgPrice_attr = context.GetDoubleItemAttr("iLast30dAvgPrice");
    auto itemRealTimePrice_attr = context.GetDoubleItemAttr("itemRealTimePrice");
    auto prerank_gmv_attr = context.GetDoubleItemAttr("prerank_gmv");
    auto prerank_cascade_attr = context.GetDoubleItemAttr("prerank_cascade");
    auto reason_attr = context.GetIntItemAttr("reason");
    auto prerank_stayTime_attr = context.GetDoubleItemAttr("prerank_stayTime");

    auto prerank_score_attr = context.SetDoubleItemAttr("prerank_score");
    auto prerank_ctcvr_attr = context.SetDoubleItemAttr("prerank_ctcvr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto prerank_ctr = prerank_ctr_attr(result).value_or(0.0);
      auto prerank_cvr = prerank_cvr_attr(result).value_or(0.0);
      auto prerank_gmv = prerank_gmv_attr(result).value_or(1.0);
      auto prerank_stayTime = prerank_stayTime_attr(result).value_or(0.0);
      prerank_stayTime = (1 - prerank_ctr) * prerank_stayTime / (1 - prerank_stayTime);
      if (prerank_stayTime <= 0) {
        prerank_stayTime = 1.0;
      }
      auto reason = reason_attr(result).value_or(0);
      auto prerank_cascade = prerank_cascade_attr(result).value_or(1.0);
      auto iLast30dAvgPrice = iLast30dAvgPrice_attr(result).value_or(-1.0);
      auto itemRealTimePrice = itemRealTimePrice_attr(result).value_or(-1.0);

      double item_price = 100.0;
      if (iLast30dAvgPrice > 0) {
        item_price = iLast30dAvgPrice;
      } else if (itemRealTimePrice > 0) {
        item_price = itemRealTimePrice;
      }

      double gyl_rough_score = pow(prerank_ctr, ctr_w) * pow(prerank_cvr, cvr_w) * pow(item_price, price_w);
      double prerank_ctcvr = pow(prerank_ctr, ctr_w) * pow(prerank_cvr, cvr_w);

      gyl_rough_score = gyl_rough_score * pow(prerank_gmv, gmv_w) * pow(prerank_cascade, cascade_w);
      gyl_rough_score = gyl_rough_score * pow(prerank_stayTime, stayTime_w);

      prerank_score_attr(result, gyl_rough_score);
      prerank_ctcvr_attr(result, prerank_ctcvr);
    });

    return true;
  }

  static bool PrerankLinearCal(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto ctr_w = context.GetDoubleCommonAttr("bh_prerank_liner_ctr_weight").value_or(1.0);
    auto ctcvr_w = context.GetDoubleCommonAttr("bh_prerank_liner_ctvr_weight").value_or(10);
    auto egpm_w = context.GetDoubleCommonAttr("bh_prerank_liner_egpm_weight").value_or(1.0);
    auto dac_w = context.GetDoubleCommonAttr("bh_prerank_dac_bonus_weight").value_or(0.0);
    auto dac_state = context.GetIntCommonAttr("bh_dac_state").value_or(0);
    if (dac_state <= 0) {
      ctcvr_w += dac_w;
    }
    auto cascade_w = context.GetDoubleCommonAttr("bh_prerank_liner_cas_weight").value_or(0.0);
    auto cascade_scale_w = context.GetDoubleCommonAttr("bh_prerank_liner_cas_scale_weight").value_or(1.0);

    auto prerank_ctr_attr = context.GetDoubleItemAttr("prerank_ctr");
    auto prerank_cvr_attr = context.GetDoubleItemAttr("prerank_cvr");
    auto prerank_cascade_attr = context.GetDoubleItemAttr("prerank_cascade");
    auto price_attr = context.GetIntItemAttr("iRankingPrice");
    auto score_attr = context.SetDoubleItemAttr("score");
    auto bh_goods_income_price_control =
        context.GetIntCommonAttr("bh_goods_income_price_control").value_or(0);
    auto bh_goods_commision_rate_high_control =
        context.GetIntCommonAttr("bh_goods_commision_rate_high_control").value_or(0);
    auto bh_goods_commision_rate_low_control =
        context.GetIntCommonAttr("bh_goods_commision_rate_low_control").value_or(0);
    auto bh_prerank_liner_income_weight =
        context.GetDoubleCommonAttr("bh_prerank_liner_income_weight").value_or(0.0);
    auto commission_rate_attr = context.GetIntItemAttr("iGoodsCommissionRate");
    auto itemRealTimePrice_attr = context.GetIntItemAttr("itemRealTimePrice");

    auto prerank_income_score_attr = context.SetDoubleItemAttr("prerank_income_score");
    auto income_commission_rate_attr = context.SetDoubleItemAttr("income_commission_rate");
    auto income_price_attr = context.SetIntItemAttr("income_price");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto prerank_ctr = prerank_ctr_attr(result).value_or(0.0);
      auto prerank_cvr = prerank_cvr_attr(result).value_or(0.0);
      auto prerank_cascade = prerank_cascade_attr(result).value_or(0.0);
      auto p = std::pow(prerank_cascade + (1 - prerank_cascade) * cascade_w, cascade_scale_w);
      if (p > 0) {
        prerank_cascade = prerank_cascade / p;
      }
      auto price = price_attr(result).value_or(100);
      auto income_price = itemRealTimePrice_attr(result).value_or(100);
      income_price = std::min(income_price, bh_goods_income_price_control);
      auto commission = commission_rate_attr(result).value_or(0.0);
      commission = std::min(commission, bh_goods_commision_rate_high_control);
      double commission_rate = 0.0;
      double income_score = 0.0;
      if (commission > bh_goods_commision_rate_low_control) {
        commission_rate = commission / 1000.0;
        income_score = prerank_ctr * prerank_cvr * income_price * commission_rate;
      }
      double score = egpm_w * prerank_ctr * prerank_cvr * price;
      score += ctr_w * prerank_ctr + ctcvr_w * prerank_ctr * prerank_cvr;
      score += bh_prerank_liner_income_weight * income_score;
      score *= prerank_cascade;
      score_attr(result, score);
      prerank_income_score_attr(result, income_score);
      income_commission_rate_attr(result, commission_rate);
      income_price_attr(result, income_price);
    });

    return true;
  }

  static bool SetShowAndNotclickTimesAttr(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto sanc_item_id_list = context.GetIntListCommonAttr("sanc_item_id_list");
    auto sanc_id_showtimes_list = context.GetIntListCommonAttr("sanc_id_showtimes_list");
    auto sanc_item_cat3_list = context.GetIntListCommonAttr("sanc_item_cat3_list");
    auto sanc_cat3_showtimes_list = context.GetIntListCommonAttr("sanc_cat3_showtimes_list");

    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto iCate3Id_attr = context.GetIntItemAttr("iCate3Id");

    auto sanc_item_times_attr = context.SetIntItemAttr("sanc_item_times");
    auto sanc_cat3_times_attr = context.SetIntItemAttr("sanc_cat3_times");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result);
      auto iCate3Id = iCate3Id_attr(result);

      int64 sanc_item_times = 0;
      if (item_id && sanc_item_id_list && sanc_id_showtimes_list) {
        for (int index = 0; index < sanc_item_id_list->size(); ++index) {
          if ((*sanc_item_id_list)[index] == *item_id && (*sanc_id_showtimes_list)[index] > 0) {
            sanc_item_times = (*sanc_id_showtimes_list)[index];
          }
        }
      }

      int64 sanc_cat3_times = 0;
      if (iCate3Id && sanc_item_cat3_list && sanc_cat3_showtimes_list) {
        for (int index = 0; index < sanc_item_cat3_list->size(); ++index) {
          if ((*sanc_item_cat3_list)[index] == *iCate3Id && (*sanc_cat3_showtimes_list)[index] > 0) {
            sanc_cat3_times = (*sanc_cat3_showtimes_list)[index];
          }
        }
      }

      sanc_item_times_attr(result, sanc_item_times);
      sanc_cat3_times_attr(result, sanc_cat3_times);
    });

    return true;
  }

  static bool CalculateUserExpressFee(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_delivery_address_province =
        context.GetIntListCommonAttr("user_delivery_address_province").value_or(absl::Span<const int64>());
    auto user_address_province =
        context.GetIntListCommonAttr("user_address_province").value_or(absl::Span<const int64>());
    auto request_type = context.GetIntCommonAttr("request_type").value_or(-1);

    auto iGoodsFeeExpressProvinceCodeList_attr =
        context.GetIntListItemAttr("iGoodsFeeExpressProvinceCodeList");
    auto iGoodsFeeExpressList_attr = context.GetIntListItemAttr("iGoodsFeeExpressList");
    auto goodsQuotedPrice_attr = context.GetIntItemAttr("goodsQuotedPrice");

    auto item_user_express_fee_attr = context.SetIntItemAttr("item_user_express_fee");
    auto goodsFinalOrderPrice_attr = context.SetIntItemAttr("goodsFinalOrderPrice");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iGoodsFeeExpressProvinceCodeList = iGoodsFeeExpressProvinceCodeList_attr(result);
      auto iGoodsFeeExpressList = iGoodsFeeExpressList_attr(result);
      auto item_user_express_fee = 0;
      auto goodsQuotedPrice = goodsQuotedPrice_attr(result).value_or(-1);
      auto goodsFinalOrderPrice = goodsQuotedPrice;

      if (iGoodsFeeExpressProvinceCodeList && iGoodsFeeExpressList) {
        if (iGoodsFeeExpressProvinceCodeList->size() != iGoodsFeeExpressList->size()) {
          item_user_express_fee_attr(result, item_user_express_fee);
          goodsFinalOrderPrice_attr(result, goodsFinalOrderPrice);
          return;
        }

        std::string user_province_code;
        if (request_type != 102 && request_type != 301) {
          if (user_address_province.size() == 0) {
            user_province_code = "11";
          } else {
            user_province_code = std::to_string(user_address_province[0]);
          }
        } else {
          if (user_delivery_address_province.size() == 0) {
            user_province_code = "11";
          } else {
            user_province_code = std::to_string(user_delivery_address_province[0]);
          }
        }

        for (int i = 0; i < iGoodsFeeExpressProvinceCodeList->size(); ++i) {
          if (user_province_code == std::to_string((*iGoodsFeeExpressProvinceCodeList)[i])) {
            item_user_express_fee = (*iGoodsFeeExpressList)[i];
            break;
          }
        }

        if (goodsQuotedPrice < 0) {
          item_user_express_fee_attr(result, item_user_express_fee);
          goodsFinalOrderPrice_attr(result, goodsFinalOrderPrice);
          return;
        } else {
          goodsFinalOrderPrice = goodsQuotedPrice + item_user_express_fee;
        }

        item_user_express_fee_attr(result, item_user_express_fee);
        goodsFinalOrderPrice_attr(result, goodsFinalOrderPrice);
        return;
      }
    });

    return true;
  }

  static bool ItemMmuCateResultSplit(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto item_mmu_cate_result = context.GetStringItemAttr("item_mmu_cate_result");
    auto mmu_cid1 = context.SetIntItemAttr("mmu_cid1");
    auto mmu_cid2 = context.SetIntItemAttr("mmu_cid2");
    auto mmu_cid3 = context.SetIntItemAttr("mmu_cid3");
    std::vector<std::string> n_split_array(3);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_mmu_cate = item_mmu_cate_result(result);
      if (!item_mmu_cate || item_mmu_cate->size() == 0) {
        item_mmu_cate = "-1_-1_-1";
      }
      n_split_array = absl::StrSplit(*item_mmu_cate, "_");
      mmu_cid1(result, std::move(std::stoi(n_split_array[0])));
      mmu_cid2(result, std::move(std::stoi(n_split_array[1])));
      mmu_cid3(result, std::move(std::stoi(n_split_array[2])));
    });
    return true;
  }

  static bool GetSampleListCommonAttrKey(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto common_attrs_from_request = context.GetStringListCommonAttr("request_common_attrs");
    auto common_attrs_from_sample_list = context.GetStringListCommonAttr("send_sample_attrs_list_name");
    auto common_attrs_black_list = context.GetStringListCommonAttr("common_attrs_blackList");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> black_list;
    if (common_attrs_black_list) {
      for (auto common_attr_black : *common_attrs_black_list) {
        black_list.insert(common_attr_black);
      }
    }

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> union_attrs_sample_list_and_request;
    if (common_attrs_from_request) {
      for (const auto &common_attr_from_request : *common_attrs_from_request) {
        union_attrs_sample_list_and_request.insert(common_attr_from_request);
      }
    }
    if (common_attrs_from_sample_list) {
      for (const auto &common_attr_from_sample_list : *common_attrs_from_sample_list) {
        union_attrs_sample_list_and_request.insert(common_attr_from_sample_list);
      }
    }

    std::vector<std::string> common_attrs_name_for_key;
    common_attrs_name_for_key.reserve(union_attrs_sample_list_and_request.size());

    for (const auto &attr : union_attrs_sample_list_and_request) {
      if (black_list.find(attr) == black_list.end()) {
        common_attrs_name_for_key.emplace_back(attr);
      }
    }

    context.SetStringListCommonAttr("common_attrs_key_for_send", std::move(common_attrs_name_for_key));
    return true;
  }

  static bool FillReasonLevelKey(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_id = context.GetIntItemAttr("item_id");
    auto reason = context.GetIntItemAttr("reason");
    auto reason_level_key = context.SetStringItemAttr("reason_level_key");
    std::string prefix = "reason_level_bucket_";
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string level_key = "";
      level_key += prefix;
      level_key += std::to_string(reason(result).value_or(0));
      level_key += "_";
      level_key += std::to_string(reason(result).value_or(0) % 10);
      reason_level_key(result, level_key);
    });
    return true;
  }

  static bool GenerateMmuCateRedisKey(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_id = context.GetIntItemAttr("item_id");
    auto mmu_cate_redis_key = context.SetStringItemAttr("mmu_cate_redis_key");
    std::string prefix = "mmu_cate_";
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string level_key = "";
      level_key += prefix;
      level_key += std::to_string(item_id(result).value_or(0));
      mmu_cate_redis_key(result, level_key);
    });
    return true;
  }

  static bool ParseColossusToC64AndLc(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto REQ_TIME = context.GetIntCommonAttr("_REQ_TIME_");
    auto click_item_id = context.GetIntListCommonAttr("click_item_id");
    auto click_item_timestamp = context.GetIntListCommonAttr("click_item_timestamp");
    auto click_category = context.GetIntListCommonAttr("click_category");
    auto click_author_id = context.GetIntListCommonAttr("click_author_id");
    auto click_price = context.GetIntListCommonAttr("click_price");
    auto click_detail_page_view_time = context.GetIntListCommonAttr("click_detail_page_view_time");
    auto colossus_filter_ts_14d = context.GetIntCommonAttr("colossus_filter_ts_14d");
    auto colossus_filter_ts_30d = context.GetIntCommonAttr("colossus_filter_ts_30d");
    std::vector<int64> realtime_bsr_ts, realtime_bsr_iid, realtime_bsr_lag, realtime_bsr_c1, realtime_bsr_c2,
        realtime_bsr_c3, realtime_bsr_seller, realtime_bsr_price, realtime_bsr_stay_time, realtime_bsr_rank,
        realtime_lc_iid, realtime_lc_lag, realtime_lc_c1, realtime_lc_c2, realtime_lc_c3, realtime_lc_seller;
    int click_item_id_count = click_item_id ? (*click_item_id).size() : 0;
    int click_item_timestamp_count = click_item_timestamp ? (*click_item_timestamp).size() : 0;
    int click_category_count = click_category ? (*click_category).size() : 0;
    int click_author_id_count = click_author_id ? (*click_author_id).size() : 0;
    int click_price_count = click_price ? (*click_price).size() : 0;
    int click_detail_page_view_time_count =
        click_detail_page_view_time ? (*click_detail_page_view_time).size() : 0;
    std::atomic<int64> bsr_rank(1);
    if (click_item_id != absl::nullopt && click_item_timestamp_count == click_item_id_count &&
        click_category_count == click_item_id_count && click_author_id_count == click_item_id_count &&
        click_price_count == click_item_id_count &&
        click_detail_page_view_time_count == click_item_id_count && REQ_TIME.has_value() &&
        colossus_filter_ts_14d.has_value() && colossus_filter_ts_30d.has_value()) {
      realtime_bsr_ts.reserve(click_item_id_count);
      realtime_bsr_iid.reserve(click_item_id_count);
      realtime_bsr_lag.reserve(click_item_id_count);
      realtime_bsr_c1.reserve(click_item_id_count);
      realtime_bsr_c2.reserve(click_item_id_count);
      realtime_bsr_c3.reserve(click_item_id_count);
      realtime_bsr_seller.reserve(click_item_id_count);
      realtime_bsr_price.reserve(click_item_id_count);
      realtime_bsr_stay_time.reserve(click_item_id_count);
      realtime_bsr_rank.reserve(click_item_id_count);
      realtime_lc_iid.resize(click_item_id_count, 0);
      realtime_lc_lag.resize(click_item_id_count, 0);
      realtime_lc_c1.resize(click_item_id_count, 0);
      realtime_lc_c2.resize(click_item_id_count, 0);
      realtime_lc_c3.resize(click_item_id_count, 0);
      realtime_lc_seller.resize(click_item_id_count, 0);
      for (int i = 0; i < click_item_id_count; ++i) {
        if (i >= 250) {
          break;
        }
        if (i < 64) {
          realtime_bsr_ts.emplace_back((*click_item_timestamp)[i]);
          realtime_bsr_iid.emplace_back((*click_item_id)[i]);
          realtime_bsr_lag.emplace_back(
              floor((*REQ_TIME / 1000.0 - (*click_item_timestamp)[i]) / (3600.0 * 24)));
          realtime_bsr_c1.emplace_back(((*click_category)[i] >> 48) & 0xffff);
          realtime_bsr_c2.emplace_back(((*click_category)[i] >> 32) & 0xffff);
          realtime_bsr_c3.emplace_back(((*click_category)[i] >> 16) & 0xffff);
          realtime_bsr_seller.emplace_back((*click_author_id)[i]);
          realtime_bsr_price.emplace_back(floor(log((*click_price)[i] / 100.0 + 1.0)));
          realtime_bsr_stay_time.emplace_back((*click_detail_page_view_time)[i]);
          realtime_bsr_rank.emplace_back(bsr_rank.load());
          ++bsr_rank;
        }
        realtime_lc_iid[i] = (*click_item_id)[i];
        realtime_lc_lag[i] = floor((*REQ_TIME / 1000.0 - (*click_item_timestamp)[i]) / (3600.0 * 24));
        realtime_lc_c1[i] = ((*click_category)[i] >> 48) & 0xffff;
        realtime_lc_c2[i] = ((*click_category)[i] >> 32) & 0xffff;
        realtime_lc_c3[i] = ((*click_category)[i] >> 16) & 0xffff;
        realtime_lc_seller[i] = (*click_author_id)[i];
      }
    }
    context.SetIntCommonAttr("realtime_c64_length", realtime_bsr_iid.size());
    context.SetIntListCommonAttr("realtime_c64_ts", std::move(realtime_bsr_ts));
    context.SetIntListCommonAttr("realtime_c64_iid", std::move(realtime_bsr_iid));
    context.SetIntListCommonAttr("realtime_c64_lag", std::move(realtime_bsr_lag));
    context.SetIntListCommonAttr("realtime_c64_c1", std::move(realtime_bsr_c1));
    context.SetIntListCommonAttr("realtime_c64_c2", std::move(realtime_bsr_c2));
    context.SetIntListCommonAttr("realtime_c64_c3", std::move(realtime_bsr_c3));
    context.SetIntListCommonAttr("realtime_c64_seller", std::move(realtime_bsr_seller));
    context.SetIntListCommonAttr("realtime_c64_price", std::move(realtime_bsr_price));
    context.SetIntListCommonAttr("realtime_c64_stay_time", std::move(realtime_bsr_stay_time));
    context.SetIntListCommonAttr("realtime_c64_rank", std::move(realtime_bsr_rank));
    context.SetIntListCommonAttr("realtime_lc_iid", std::move(realtime_lc_iid));
    context.SetIntListCommonAttr("realtime_lc_lag", std::move(realtime_lc_lag));
    context.SetIntListCommonAttr("realtime_lc_c1", std::move(realtime_lc_c1));
    context.SetIntListCommonAttr("realtime_lc_c2", std::move(realtime_lc_c2));
    context.SetIntListCommonAttr("realtime_lc_c3", std::move(realtime_lc_c3));
    context.SetIntListCommonAttr("realtime_lc_seller", std::move(realtime_lc_seller));
    return true;
  }

  static bool SetMmuCid3KeyFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto mmu_cid3_rebuy_rate_key_attr = context.SetStringItemAttr("mmu_cid3_rebuy_rate_key");
    auto mmu_cid3_flag_attr = context.SetIntItemAttr("mmu_cid3_flag");
    auto mmu_cid3_attr = context.GetIntItemAttr("mmu_cid3");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto mmu_cid3 = mmu_cid3_attr(result).value_or(-1);
      auto mmu_cid3_flag = 0;
      if (mmu_cid3 != -1) {
        mmu_cid3_flag = 1;
      }
      mmu_cid3_flag_attr(result, mmu_cid3_flag);
      mmu_cid3_rebuy_rate_key_attr(result, std::string("mmu_cid3_rebuy_rate_") + std::to_string(mmu_cid3));
    });
    return true;
  }

  static bool SetGcfRecallFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto reason_attr = context.GetIntItemAttr("reason");
    auto iItemPrice_attr = context.GetIntItemAttr("iItemPrice");
    auto iLast30dAvgPrice_attr = context.GetIntItemAttr("iLast30dAvgPrice");
    auto goodsPromotionPrice_attr = context.GetIntItemAttr("goodsPromotionPrice");
    auto gcf_recall_flag_attr = context.SetIntItemAttr("gcf_recall_flag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto gcf_recall_flag = 1;
      auto reason = reason_attr(result).value_or(0);
      auto iItemPrice = iItemPrice_attr(result).value_or(50000);
      auto iLast30dAvgPrice = iLast30dAvgPrice_attr(result).value_or(50000);
      auto goodsPromotionPrice = goodsPromotionPrice_attr(result).value_or(30000);
      if ((goodsPromotionPrice <= 105 && goodsPromotionPrice > 0) ||
          (iLast30dAvgPrice <= 110 && iLast30dAvgPrice > 0) || (iItemPrice <= 100 && iItemPrice > 0)) {
        gcf_recall_flag = 0;
      }
      gcf_recall_flag_attr(result, gcf_recall_flag);
    });
    return true;
  }

  static bool GenerateClickAttrs(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto REQ_TIME = context.GetIntCommonAttr("_REQ_TIME_");
    auto click_item_timestamp = context.GetIntListCommonAttr("click_item_timestamp");
    auto click_category = context.GetIntListCommonAttr("click_category");
    auto click_price = context.GetIntListCommonAttr("click_price");
    auto click_hot_cat2_recall_time = context.GetIntCommonAttr("click_hot_cat2_recall_time");
    auto click_hot_cat2_recall_click_threshold =
        context.GetIntCommonAttr("click_hot_cat2_recall_click_threshold");
    std::vector<int64> click_item_lag, click_c1, click_c2, click_c3, click_price_bucket, click_cat2_id_list;
    folly::F14FastMap<int64, int64> cat2_map;
    int click_item_timestamp_count = click_item_timestamp ? (*click_item_timestamp).size() : 0;
    int click_category_count = click_category ? (*click_category).size() : 0;
    int click_price_count = click_price ? (*click_price).size() : 0;
    if (click_item_timestamp.has_value() && click_item_timestamp_count == click_category_count &&
        click_item_timestamp_count == click_price_count && REQ_TIME.has_value() &&
        click_hot_cat2_recall_time.has_value() && click_hot_cat2_recall_click_threshold.has_value()) {
      auto size = (*click_item_timestamp).size();
      click_item_lag.reserve(size);
      click_c1.reserve(size);
      click_c2.reserve(size);
      click_c3.reserve(size);
      click_price_bucket.reserve(size);
      click_cat2_id_list.reserve(size);
      for (int index = 0; index < (*click_item_timestamp).size(); index++) {
        click_item_lag.emplace_back(
            std::floor((*(REQ_TIME) / 1000.0 - (*click_item_timestamp)[index]) / (3600.0 * 24)));
        click_c1.emplace_back(((*click_category)[index] >> 48) & 0xffff);
        click_c2.emplace_back(((*click_category)[index] >> 32) & 0xffff);
        click_c3.emplace_back(((*click_category)[index] >> 16) & 0xffff);
        click_price_bucket.emplace_back(std::floor(std::log((*click_price)[index] / 100.0 + 1)));

        // 用于二级类目热门召回的用户点击二级类目列表
        if ((*click_item_timestamp)[index] >= click_hot_cat2_recall_time) {
          auto cat2 = ((*click_category)[index] >> 32) & 0xffff;
          auto iter = cat2_map.find(cat2);
          if (iter == cat2_map.end()) {
            cat2_map[cat2] = 1;
          } else {
            cat2_map[cat2] = cat2_map[cat2] + 1;
          }
          if (cat2_map[cat2] >= click_hot_cat2_recall_click_threshold) {
            click_cat2_id_list.emplace_back(cat2);
          }
        }
      }
    }
    context.SetIntListCommonAttr("click_item_lag", std::move(click_item_lag));
    context.SetIntListCommonAttr("click_c1", std::move(click_c1));
    context.SetIntListCommonAttr("click_c2", std::move(click_c2));
    context.SetIntListCommonAttr("click_c3", std::move(click_c3));
    context.SetIntListCommonAttr("click_price_bucket", std::move(click_price_bucket));
    context.SetIntListCommonAttr("click_cat2_id_list", std::move(click_cat2_id_list));
    return true;
  }

  static bool ModifyGoodsPriceByCoupon(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto feed_guess_like_fullchain_price_upbound =
        context.GetIntCommonAttr("feed_guess_like_fullchain_price_upbound").value_or(50000);
    auto bh_goods_yushou_item_quoted_price_enable =
        context.GetIntCommonAttr("bh_goods_yushou_item_quoted_price_enable").value_or(0);
    auto feed_guess_like_if_low_price_debias =
        context.GetIntCommonAttr("feed_guess_like_if_low_price_debias").value_or(0);
    auto feed_guess_like_price_debias_coeff =
        context.GetDoubleCommonAttr("feed_guess_like_price_debias_coeff").value_or(1.0);
    auto feed_guess_like_low_price_debias_threshold =
        context.GetDoubleCommonAttr("feed_guess_like_low_price_debias_threshold").value_or(0.0);
    auto feed_guess_like_low_price_debias_slope =
        context.GetDoubleCommonAttr("feed_guess_like_low_price_debias_slope").value_or(0.0);
    auto feed_guess_like_low_price_debias_intercept =
        context.GetDoubleCommonAttr("feed_guess_like_low_price_debias_intercept").value_or(1.0);

    auto iLast30dAvgPrice = context.GetIntItemAttr("iLast30dAvgPrice");
    auto goodsFinalOrderPrice = context.GetIntItemAttr("goodsFinalOrderPrice");
    auto goodsQuotedPrice = context.GetIntItemAttr("goodsQuotedPrice");
    auto iGoodsMallItemSmoothPrice = context.GetIntItemAttr("iGoodsMallItemSmoothPrice");
    auto iGoodsItemTypeList = context.GetIntListItemAttr("iGoodsItemTypeList");

    auto iRankingPrice = context.SetIntItemAttr("iRankingPrice");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iGoodsMallItemSmoothPriceForResult = iGoodsMallItemSmoothPrice(result).value_or(-1);
      auto iLast30dAvgPriceForResult = iLast30dAvgPrice(result).value_or(-1);
      auto iRankingPriceTmp = iGoodsMallItemSmoothPriceForResult;
      auto goodsFinalOrderPriceForResult = goodsFinalOrderPrice(result).value_or(-1);
      auto goodsQuotedPriceForResult = goodsQuotedPrice(result).value_or(-1);
      auto iGoodsItemTypeListForResult = iGoodsItemTypeList(result).value_or(absl::Span<const int64>());
      if (iRankingPriceTmp < 0) {
        if (iLast30dAvgPriceForResult > 0) {
          iRankingPriceTmp = iLast30dAvgPriceForResult;
        } else if (goodsFinalOrderPriceForResult > 0) {
          iRankingPriceTmp = goodsFinalOrderPriceForResult;
        } else if (goodsQuotedPriceForResult > 0) {
          iRankingPriceTmp = goodsQuotedPriceForResult;
        }
      }
      if (goodsFinalOrderPriceForResult > 0) {
        iRankingPriceTmp = iRankingPriceTmp < goodsFinalOrderPriceForResult ? iRankingPriceTmp
                                                                            : goodsFinalOrderPriceForResult;
      } else if (goodsQuotedPriceForResult > 0) {
        iRankingPriceTmp =
            iRankingPriceTmp < goodsQuotedPriceForResult ? iRankingPriceTmp : goodsQuotedPriceForResult;
      }
      // 预售商品的券后价写入了 goodsQuotedPrice 直接用 goodsQuotedPrice
      if (bh_goods_yushou_item_quoted_price_enable == 1) {
        if (iGoodsItemTypeListForResult.size() > 0) {
          iRankingPriceTmp = goodsQuotedPriceForResult;
        }
      }
      iRankingPriceTmp = iRankingPriceTmp < feed_guess_like_fullchain_price_upbound
                             ? iRankingPriceTmp
                             : feed_guess_like_fullchain_price_upbound;
      iRankingPriceTmp = std::floor(feed_guess_like_price_debias_coeff * iRankingPriceTmp);

      // 低价纠偏
      if (feed_guess_like_if_low_price_debias > 0 &&
          iRankingPriceTmp < feed_guess_like_low_price_debias_threshold) {
        auto debias_factor = -feed_guess_like_low_price_debias_slope * iRankingPriceTmp +
                             feed_guess_like_low_price_debias_intercept;
        iRankingPriceTmp = std::ceil(iRankingPriceTmp * debias_factor);
      }
      iRankingPrice(result, iRankingPriceTmp);
    });
    return true;
  }

  static bool SelectGroupRoughBoost(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto boost_supply_sift_id_list =
        context.GetIntListCommonAttr("boost_supply_sift_id_list").value_or(absl::Span<const int64>());
    auto boost_supply_sift_weight_list =
        context.GetDoubleListCommonAttr("boost_supply_sift_weight_list").value_or(absl::Span<const double>());

    auto score = context.GetDoubleItemAttr("score");
    auto i_selection_group_id_list = context.GetIntListItemAttr("iSelectionGroupIdList");

    folly::F14FastMap<int64, double> select_group_dict_map;
    int64 list_size = 0;
    if (!boost_supply_sift_id_list.empty() && !boost_supply_sift_weight_list.empty()) {
      list_size = boost_supply_sift_id_list.size() < boost_supply_sift_weight_list.size()
                      ? boost_supply_sift_id_list.size()
                      : boost_supply_sift_weight_list.size();
    }
    select_group_dict_map.reserve(list_size);
    for (int i = 0; i < list_size; ++i) {
      select_group_dict_map.emplace(boost_supply_sift_id_list[i], boost_supply_sift_weight_list[i]);
    }
    // write
    auto write_score = context.SetDoubleItemAttr("score");
    auto write_select_rough_boost_weight = context.SetDoubleItemAttr("select_rough_boost_weight");
    auto write_max_rough_boost_select_group = context.SetIntItemAttr("write_max_rough_boost_select_group");
    auto write_isFilterSelectGroupFlag = context.SetIntItemAttr("isFilterSelectGroupFlag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double boost_weight = 1.0;
      double max_boost_select_group = -1;
      double is_filter_select_group_flag = 0;
      auto score_for_result = score(result).value_or(0.0);
      auto i_selection_group_id_list_for_result =
          i_selection_group_id_list(result).value_or(absl::Span<const int64>());
      if (!i_selection_group_id_list_for_result.empty()) {
        for (auto select_group_id : i_selection_group_id_list_for_result) {
          if (select_group_dict_map.find(select_group_id) != select_group_dict_map.end()) {
            boost_weight = boost_weight > select_group_dict_map[select_group_id]
                               ? boost_weight
                               : select_group_dict_map[select_group_id];
            max_boost_select_group = boost_weight > select_group_dict_map[select_group_id]
                                         ? max_boost_select_group
                                         : select_group_id;
            if (select_group_dict_map[select_group_id] == -1) {
              is_filter_select_group_flag = 1;
              break;
            }
          }
        }
      }
      write_score(result, score_for_result * boost_weight);
      write_select_rough_boost_weight(result, boost_weight);
      write_max_rough_boost_select_group(result, max_boost_select_group);
      write_isFilterSelectGroupFlag(result, is_filter_select_group_flag);
    });
    return true;
  }

  static bool ReplaceToValidValue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto value_prepare_to_replace = context.GetIntItemAttr("value_prepare_to_replace");
    auto value_prepare_to_compare = context.GetIntItemAttr("value_prepare_to_compare");
    auto final_attr_value = context.SetIntItemAttr("final_attr_value");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto value_prepare_to_compare_for_result = value_prepare_to_compare(result).value_or(-1);
      final_attr_value(result, value_prepare_to_compare_for_result <= 0
                                   ? *value_prepare_to_replace(result)
                                   : value_prepare_to_compare_for_result);
    });
    return true;
  }

  static bool ItemPriceFeaCalc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto calc_ratio = [](double a, double b, double thre) {
      double res = 0.0;
      if (a > 0 && b > 0) {
        res = a / b > thre ? thre : a / b;
      }
      return res;
    };

    auto iGoodsItemAvgBuyPrice1dV2_attr = context.GetIntItemAttr("iGoodsItemAvgBuyPrice1dV2");
    auto iGoodsItemAvgBuyPrice7dV2_attr = context.GetIntItemAttr("iGoodsItemAvgBuyPrice7dV2");
    auto iGoodsItemAvgBuyPrice30dV2_attr = context.GetIntItemAttr("iGoodsItemAvgBuyPrice30dV2");
    auto iGoodsItemMinPayPrice1dV2_attr = context.GetIntItemAttr("iGoodsItemMinPayPrice1dV2");
    auto iGoodsItemMinPayPrice7dV2_attr = context.GetIntItemAttr("iGoodsItemMinPayPrice7dV2");
    auto iGoodsItemMinPayPrice30dV2_attr = context.GetIntItemAttr("iGoodsItemMinPayPrice30dV2");
    auto iGoodsItemMinPayPrice60dV2_attr = context.GetIntItemAttr("iGoodsItemMinPayPrice60dV2");
    auto iGoodsItemMinPayPrice90dV2_attr = context.GetIntItemAttr("iGoodsItemMinPayPrice90dV2");
    auto iGoodsUnriskPayItemMinPrice30dV2_attr = context.GetIntItemAttr("iGoodsUnriskPayItemMinPrice30dV2");
    auto iGoodsUnriskPayItemMinPrice60dV2_attr = context.GetIntItemAttr("iGoodsUnriskPayItemMinPrice60dV2");
    auto iGoodsUnriskPayItemMinPrice90dV2_attr = context.GetIntItemAttr("iGoodsUnriskPayItemMinPrice90dV2");
    auto iGoodsUnriskPayItemMinPrice180dV2_attr = context.GetIntItemAttr("iGoodsUnriskPayItemMinPrice180dV2");
    auto iGoodsKsSameItemAvgBuyPrice1dV2_attr = context.GetIntItemAttr("iGoodsKsSameItemAvgBuyPrice1dV2");
    auto iGoodsKsSameItemMinBuyPriceV2_attr = context.GetIntItemAttr("iGoodsKsSameItemMinBuyPriceV2");
    auto iGoodsKsSameItemMinBuyPrice7dV2_attr = context.GetIntItemAttr("iGoodsKsSameItemMinBuyPrice7dV2");
    auto iGoodsKsSameItemMinBuyPrice30dV2_attr = context.GetIntItemAttr("iGoodsKsSameItemMinBuyPrice30dV2");
    auto iGoodsKsSameItemMinBuyPrice60dV2_attr = context.GetIntItemAttr("iGoodsKsSameItemMinBuyPrice60dV2");
    auto iGoodsKsSameItemMinBuyPrice90dV2_attr = context.GetIntItemAttr("iGoodsKsSameItemMinBuyPrice90dV2");

    auto item_avg_buy_price_1_7_rate_attr = context.SetDoubleItemAttr("item_avg_buy_price_1_7_rate");
    auto item_avg_buy_price_1_30_rate_attr = context.SetDoubleItemAttr("item_avg_buy_price_1_30_rate");
    auto item_avg_buy_price_min_pay_price_1_1_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_min_pay_price_1_1_rate");
    auto item_avg_buy_price_min_pay_price_1_7_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_min_pay_price_1_7_rate");
    auto item_avg_buy_price_min_pay_price_1_30_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_min_pay_price_1_30_rate");
    auto item_avg_buy_price_min_pay_price_1_60_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_min_pay_price_1_60_rate");
    auto item_avg_buy_price_min_pay_price_1_90_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_min_pay_price_1_90_rate");
    auto item_avg_buy_price_unrisk_min_pay_price_1_30_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_unrisk_min_pay_price_1_30_rate");
    auto item_avg_buy_price_unrisk_min_pay_price_1_60_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_unrisk_min_pay_price_1_60_rate");
    auto item_avg_buy_price_unrisk_min_pay_price_1_90_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_unrisk_min_pay_price_1_90_rate");
    auto item_avg_buy_price_unrisk_min_pay_price_1_180_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_unrisk_min_pay_price_1_180_rate");
    auto item_avg_buy_price_same_avg_buy_price_1_1_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_avg_buy_price_1_1_rate");
    auto item_avg_buy_price_same_min_buy_price_1_1_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_min_buy_price_1_1_rate");
    auto item_avg_buy_price_same_min_buy_price_1_7_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_min_buy_price_1_7_rate");
    auto item_avg_buy_price_same_min_buy_price_1_30_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_min_buy_price_1_30_rate");
    auto item_avg_buy_price_same_min_buy_price_1_60_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_min_buy_price_1_60_rate");
    auto item_avg_buy_price_same_min_buy_price_1_90_rate_attr =
        context.SetDoubleItemAttr("item_avg_buy_price_same_min_buy_price_1_90_rate");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iGoodsItemAvgBuyPrice1dV2 = iGoodsItemAvgBuyPrice1dV2_attr(result).value_or(0);
      auto iGoodsItemAvgBuyPrice7dV2 = iGoodsItemAvgBuyPrice7dV2_attr(result).value_or(0);
      auto iGoodsItemAvgBuyPrice30dV2 = iGoodsItemAvgBuyPrice30dV2_attr(result).value_or(0);
      auto iGoodsItemMinPayPrice1dV2 = iGoodsItemMinPayPrice1dV2_attr(result).value_or(0);
      auto iGoodsItemMinPayPrice7dV2 = iGoodsItemMinPayPrice7dV2_attr(result).value_or(0);
      auto iGoodsItemMinPayPrice30dV2 = iGoodsItemMinPayPrice30dV2_attr(result).value_or(0);
      auto iGoodsItemMinPayPrice60dV2 = iGoodsItemMinPayPrice60dV2_attr(result).value_or(0);
      auto iGoodsItemMinPayPrice90dV2 = iGoodsItemMinPayPrice90dV2_attr(result).value_or(0);
      auto iGoodsUnriskPayItemMinPrice30dV2 = iGoodsUnriskPayItemMinPrice30dV2_attr(result).value_or(0);
      auto iGoodsUnriskPayItemMinPrice60dV2 = iGoodsUnriskPayItemMinPrice60dV2_attr(result).value_or(0);
      auto iGoodsUnriskPayItemMinPrice90dV2 = iGoodsUnriskPayItemMinPrice90dV2_attr(result).value_or(0);
      auto iGoodsUnriskPayItemMinPrice180dV2 = iGoodsUnriskPayItemMinPrice180dV2_attr(result).value_or(0);
      auto iGoodsKsSameItemAvgBuyPrice1dV2 = iGoodsKsSameItemAvgBuyPrice1dV2_attr(result).value_or(0);
      auto iGoodsKsSameItemMinBuyPriceV2 = iGoodsKsSameItemMinBuyPriceV2_attr(result).value_or(0);
      auto iGoodsKsSameItemMinBuyPrice7dV2 = iGoodsKsSameItemMinBuyPrice7dV2_attr(result).value_or(0);
      auto iGoodsKsSameItemMinBuyPrice30dV2 = iGoodsKsSameItemMinBuyPrice30dV2_attr(result).value_or(0);
      auto iGoodsKsSameItemMinBuyPrice60dV2 = iGoodsKsSameItemMinBuyPrice60dV2_attr(result).value_or(0);
      auto iGoodsKsSameItemMinBuyPrice90dV2 = iGoodsKsSameItemMinBuyPrice90dV2_attr(result).value_or(0);

      double item_avg_buy_price_1_7_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemAvgBuyPrice7dV2, 5.0);
      double item_avg_buy_price_1_30_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemAvgBuyPrice30dV2, 10.0);
      double item_avg_buy_price_min_pay_price_1_1_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemMinPayPrice1dV2, 5.0);
      double item_avg_buy_price_min_pay_price_1_7_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemMinPayPrice7dV2, 10.0);
      double item_avg_buy_price_min_pay_price_1_30_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemMinPayPrice30dV2, 10.0);
      double item_avg_buy_price_min_pay_price_1_60_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemMinPayPrice60dV2, 10.0);
      double item_avg_buy_price_min_pay_price_1_90_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsItemMinPayPrice90dV2, 10.0);
      double item_avg_buy_price_unrisk_min_pay_price_1_30_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsUnriskPayItemMinPrice30dV2, 10.0);
      double item_avg_buy_price_unrisk_min_pay_price_1_60_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsUnriskPayItemMinPrice60dV2, 10.0);
      double item_avg_buy_price_unrisk_min_pay_price_1_90_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsUnriskPayItemMinPrice90dV2, 10.0);
      double item_avg_buy_price_unrisk_min_pay_price_1_180_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsUnriskPayItemMinPrice180dV2, 10.0);
      double item_avg_buy_price_same_avg_buy_price_1_1_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemAvgBuyPrice1dV2, 5.0);
      double item_avg_buy_price_same_min_buy_price_1_1_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemMinBuyPriceV2, 10.0);
      double item_avg_buy_price_same_min_buy_price_1_7_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemMinBuyPrice7dV2, 10.0);
      double item_avg_buy_price_same_min_buy_price_1_30_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemMinBuyPrice30dV2, 10.0);
      double item_avg_buy_price_same_min_buy_price_1_60_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemMinBuyPrice60dV2, 10.0);
      double item_avg_buy_price_same_min_buy_price_1_90_rate =
          calc_ratio(iGoodsItemAvgBuyPrice1dV2, iGoodsKsSameItemMinBuyPrice90dV2, 10.0);

      item_avg_buy_price_1_7_rate_attr(result, item_avg_buy_price_1_7_rate);
      item_avg_buy_price_1_30_rate_attr(result, item_avg_buy_price_1_30_rate);
      item_avg_buy_price_min_pay_price_1_1_rate_attr(result, item_avg_buy_price_min_pay_price_1_1_rate);
      item_avg_buy_price_min_pay_price_1_7_rate_attr(result, item_avg_buy_price_min_pay_price_1_7_rate);
      item_avg_buy_price_min_pay_price_1_30_rate_attr(result, item_avg_buy_price_min_pay_price_1_30_rate);
      item_avg_buy_price_min_pay_price_1_60_rate_attr(result, item_avg_buy_price_min_pay_price_1_60_rate);
      item_avg_buy_price_min_pay_price_1_90_rate_attr(result, item_avg_buy_price_min_pay_price_1_90_rate);
      item_avg_buy_price_unrisk_min_pay_price_1_30_rate_attr(
          result, item_avg_buy_price_unrisk_min_pay_price_1_30_rate);
      item_avg_buy_price_unrisk_min_pay_price_1_60_rate_attr(
          result, item_avg_buy_price_unrisk_min_pay_price_1_60_rate);
      item_avg_buy_price_unrisk_min_pay_price_1_90_rate_attr(
          result, item_avg_buy_price_unrisk_min_pay_price_1_90_rate);
      item_avg_buy_price_unrisk_min_pay_price_1_180_rate_attr(
          result, item_avg_buy_price_unrisk_min_pay_price_1_180_rate);
      item_avg_buy_price_same_avg_buy_price_1_1_rate_attr(result,
                                                          item_avg_buy_price_same_avg_buy_price_1_1_rate);
      item_avg_buy_price_same_min_buy_price_1_1_rate_attr(result,
                                                          item_avg_buy_price_same_min_buy_price_1_1_rate);
      item_avg_buy_price_same_min_buy_price_1_7_rate_attr(result,
                                                          item_avg_buy_price_same_min_buy_price_1_7_rate);
      item_avg_buy_price_same_min_buy_price_1_30_rate_attr(result,
                                                           item_avg_buy_price_same_min_buy_price_1_30_rate);
      item_avg_buy_price_same_min_buy_price_1_60_rate_attr(result,
                                                           item_avg_buy_price_same_min_buy_price_1_60_rate);
      item_avg_buy_price_same_min_buy_price_1_90_rate_attr(result,
                                                           item_avg_buy_price_same_min_buy_price_1_90_rate);
    });
    return true;
  }

  static bool ItemUnionTags(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    auto iGoodsTagList_attr = context.GetStringListItemAttr("iGoodsTagList");
    auto comment_show_bucket_attr = context.GetIntItemAttr("comment_show_bucket");
    auto good_comment_rate_bucket_attr = context.GetIntItemAttr("good_comment_rate_bucket");
    auto title_length_bucket_attr = context.GetIntItemAttr("title_length_bucket");
    auto comments_tag_attr = context.GetStringItemAttr("comments_tag");
    auto good_comment_tag_attr = context.GetStringItemAttr("good_comment_tag");
    auto title_len_tag_attr = context.GetStringItemAttr("title_len_tag");
    auto iGoodsUnionTags_attr = context.SetStringListItemAttr("iGoodsUnionTags");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iGoodsTagList = iGoodsTagList_attr(result);
      std::vector<std::string> iGoodsUnionTags;
      iGoodsUnionTags.reserve((*iGoodsTagList).size() + 3);
      if (iGoodsTagList) {
        for (const auto &iGoodsTag : *iGoodsTagList) {
          iGoodsUnionTags.emplace_back(iGoodsTag);
        }
      }

      auto comment_show_bucket = comment_show_bucket_attr(result).value_or(-1);
      auto good_comment_rate_bucket = good_comment_rate_bucket_attr(result).value_or(-1);
      auto title_length_bucket = title_length_bucket_attr(result).value_or(-1);
      auto comments_tag = comments_tag_attr(result);
      auto good_comment_tag = good_comment_tag_attr(result);
      auto title_len_tag = title_len_tag_attr(result);
      if (comments_tag && comment_show_bucket != -1) {
        iGoodsUnionTags.emplace_back(*comments_tag);
      }
      if (good_comment_tag && good_comment_rate_bucket != -1) {
        iGoodsUnionTags.emplace_back(*good_comment_tag);
      }
      if (title_len_tag && title_length_bucket != -1) {
        iGoodsUnionTags.emplace_back(*title_len_tag);
      }
      // iGoodsUnionTags_attr(result, std::move(iGoodsUnionTags));
      iGoodsUnionTags_attr(result, iGoodsUnionTags);
    });
    return true;
  }

  static bool BHPrerankCateInsertNumCal(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto bh_goods_pre_rank_cate_insert_type =
        context.GetIntCommonAttr("bh_goods_pre_rank_cate_insert_type").value_or(1);
    auto bh_goods_pre_rank_cate_insert_total_num =
        context.GetIntCommonAttr("bh_goods_pre_rank_cate_insert_total_num").value_or(50);
    auto buyer_home_pre_rank_no_redirect_truncate_num =
        context.GetIntCommonAttr("buyer_home_pre_rank_no_redirect_truncate_num").value_or(1700);
    auto iCate1Id_list = context.GetIntListCommonAttr("iCate1Id_list");
    auto iCate2Id_list = context.GetIntListCommonAttr("iCate2Id_list");
    auto iCate3Id_list = context.GetIntListCommonAttr("iCate3Id_list");
    auto iCate1Id_attr = context.GetIntItemAttr("iCate1Id");
    auto iCate2Id_attr = context.GetIntItemAttr("iCate2Id");
    auto iCate3Id_attr = context.GetIntItemAttr("iCate3Id");
    auto pre_rank_cate_insert_flag_attr = context.SetIntItemAttr("pre_rank_cate_insert_flag");
    auto iCateId_list = iCate1Id_list;
    int cate_num = 0;
    std::map<int, int> dup, dup_sum;

    if (bh_goods_pre_rank_cate_insert_type == 2) {
      iCateId_list = iCate2Id_list;
    } else if (bh_goods_pre_rank_cate_insert_type == 3) {
      iCateId_list = iCate3Id_list;
    }
    if (iCateId_list) {
      for (auto cate : *iCateId_list) {
        if (dup.find(cate) == dup.end()) {
          cate_num = cate_num + 1;
          dup[cate] = 0;
        }
        dup[cate] = dup[cate] + 1;
      }
    }

    cate_num = cate_num < 1 ? 1 : cate_num;
    auto per_cate_num = bh_goods_pre_rank_cate_insert_total_num / cate_num;
    int seq = 0;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iCate1Id = iCate1Id_attr(result).value_or(0);
      auto iCate2Id = iCate2Id_attr(result).value_or(0);
      auto iCate3Id = iCate3Id_attr(result).value_or(0);
      auto iCateId = iCate1Id;

      if (bh_goods_pre_rank_cate_insert_type == 2) {
        iCateId = iCate2Id;
      } else if (bh_goods_pre_rank_cate_insert_type == 3) {
        iCateId = iCate3Id;
      }

      if (dup_sum.find(iCateId) == dup_sum.end()) {
        dup_sum[iCateId] = 0;
      }
      dup_sum[iCateId] = dup_sum[iCateId] + 1;
      auto pre_rank_cate_insert_flag = 0;
      if (seq > buyer_home_pre_rank_no_redirect_truncate_num && dup_sum[iCateId] <= per_cate_num) {
        pre_rank_cate_insert_flag = 1;
      }
      pre_rank_cate_insert_flag_attr(result, pre_rank_cate_insert_flag);
      seq = seq + 1;
    });
    return true;
  }

  static bool DedupColossusAttrListWithItemid(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    auto seq_limit = context.GetIntCommonAttr("seq_limit").value_or(256);
    request_ts /= 1000;
    auto origin_item_id_list = context.GetIntListCommonAttr("origin_item_id_list_unlimited");
    auto origin_ts_list = context.GetIntListCommonAttr("origin_ts_list_unlimited");
    auto origin_category_list = context.GetIntListCommonAttr("origin_category_list_unlimited");
    auto origin_author_id_list = context.GetIntListCommonAttr("origin_author_id_list_unlimited");
    auto origin_price_list = context.GetIntListCommonAttr("origin_price_list_unlimited");
    auto origin_dp_duration_list = context.GetIntListCommonAttr("origin_dp_duration_list_unlimited");
    auto origin_flow_type_list = context.GetIntListCommonAttr("origin_flow_type_list_unlimited");
    int item_id_list_size = origin_item_id_list ? (*origin_item_id_list).size() : 0;
    int ts_list_size = origin_ts_list ? (*origin_ts_list).size() : 0;
    int category_list_size = origin_category_list ? (*origin_category_list).size() : 0;
    int author_id_list_size = origin_author_id_list ? (*origin_author_id_list).size() : 0;
    int price_list_size = origin_price_list ? (*origin_price_list).size() : 0;
    int dp_duration_list_size = origin_dp_duration_list ? (*origin_dp_duration_list).size() : 0;
    int flow_type_list_size = origin_flow_type_list ? (*origin_flow_type_list).size() : 0;
    if (request_ts > 0 && origin_item_id_list && origin_ts_list && origin_category_list &&
        origin_author_id_list && origin_price_list && origin_dp_duration_list && origin_flow_type_list &&
        item_id_list_size == ts_list_size && item_id_list_size == category_list_size &&
        item_id_list_size == author_id_list_size && item_id_list_size == price_list_size &&
        item_id_list_size == dp_duration_list_size && item_id_list_size == flow_type_list_size) {
      folly::F14FastMap<int64, int64> item2cnt;
      std::vector<int> dedup_item_index;
      dedup_item_index.reserve(item_id_list_size);
      for (int i = item_id_list_size - 1; i >= 0; --i) {
        auto item_id = (*origin_item_id_list)[i];
        auto ts = (*origin_ts_list)[i];
        if (ts < request_ts) {
          auto iter = item2cnt.find(item_id);
          if (iter == item2cnt.end()) {
            dedup_item_index.emplace_back(i);
          }
          item2cnt[item_id] += 1;
        }
      }
      dedup_item_index.shrink_to_fit();
      size_t dedup_size = dedup_item_index.size() > seq_limit ? seq_limit : dedup_item_index.size();
      std::vector<int64> dedup_item_id_list(dedup_size, -1);
      std::vector<int64> dedup_ts_list(dedup_size, -1);
      std::vector<int64> dedup_cid1_list(dedup_size, -1);
      std::vector<int64> dedup_cid2_list(dedup_size, -1);
      std::vector<int64> dedup_cid3_list(dedup_size, -1);
      std::vector<int64> dedup_author_id_list(dedup_size, -1);
      std::vector<int64> dedup_price_list(dedup_size, -1);
      std::vector<int64> dedup_dp_duration_list(dedup_size, -1);
      std::vector<int64> dedup_flow_type_list(dedup_size, -1);
      std::vector<int64> dedup_cnt_list(dedup_size, 1);
      for (size_t i = 0; i < dedup_size; ++i) {
        auto index = dedup_item_index[i];
        auto item_id = (*origin_item_id_list)[index];
        dedup_item_id_list[i] = item_id;
        dedup_ts_list[i] = (*origin_ts_list)[index];
        auto cid = (*origin_category_list)[index];
        auto cid1 = (cid >> 48) & 0xffff;
        if (cid1 > 0) {
          dedup_cid1_list[i] = cid1;
        }
        auto cid2 = (cid >> 32) & 0xffff;
        if (cid2 > 0) {
          dedup_cid2_list[i] = cid2;
        }
        auto cid3 = (cid >> 16) & 0xffff;
        if (cid3 > 0) {
          dedup_cid3_list[i] = cid3;
        }
        dedup_author_id_list[i] = (*origin_author_id_list)[index];
        dedup_price_list[i] = (*origin_price_list)[index];
        dedup_dp_duration_list[i] = (*origin_dp_duration_list)[index];
        dedup_flow_type_list[i] = (*origin_flow_type_list)[index];
        auto iter = item2cnt.find(item_id);
        if (iter != item2cnt.end()) {
          dedup_cnt_list[i] = iter->second;
        }
      }
      context.SetIntListCommonAttr("dedup_item_id_list_final", std::move(dedup_item_id_list));
      context.SetIntListCommonAttr("dedup_ts_list_final", std::move(dedup_ts_list));
      context.SetIntListCommonAttr("dedup_cid1_list_final", std::move(dedup_cid1_list));
      context.SetIntListCommonAttr("dedup_cid2_list_final", std::move(dedup_cid2_list));
      context.SetIntListCommonAttr("dedup_cid3_list_final", std::move(dedup_cid3_list));
      context.SetIntListCommonAttr("dedup_author_id_list_final", std::move(dedup_author_id_list));
      context.SetIntListCommonAttr("dedup_price_list_final", std::move(dedup_price_list));
      context.SetIntListCommonAttr("dedup_dp_duration_list_final", std::move(dedup_dp_duration_list));
      context.SetIntListCommonAttr("dedup_flow_type_list_final", std::move(dedup_flow_type_list));
      context.SetIntListCommonAttr("dedup_cnt_list_final", std::move(dedup_cnt_list));
    }
    return true;
  }

  static bool TruncateColossusOrderAttrList(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    auto seq_limit = context.GetIntCommonAttr("seq_limit").value_or(256);
    request_ts /= 1000;
    auto origin_ts_list = context.GetIntListCommonAttr("origin_ts_list_unlimited");
    auto origin_category_list = context.GetIntListCommonAttr("origin_category_list_unlimited");
    auto origin_price_list = context.GetIntListCommonAttr("origin_price_list_unlimited");
    int ts_list_size = origin_ts_list ? (*origin_ts_list).size() : 0;
    int category_list_size = origin_category_list ? (*origin_category_list).size() : 0;
    int price_list_size = origin_price_list ? (*origin_price_list).size() : 0;
    if (request_ts > 0 && origin_ts_list && origin_category_list && origin_price_list &&
        ts_list_size == category_list_size && ts_list_size == price_list_size) {
      int index = ts_list_size - 1;
      for (int i = index; i >= 0; --i) {
        auto ts = (*origin_ts_list)[i];
        if (ts < request_ts) {
          index = i;
          break;
        }
      }
      int truncated_size = (index + 1) > seq_limit ? seq_limit : (index + 1);
      std::vector<int64> truncated_ts_list(truncated_size, -1);
      std::vector<int64> truncated_cid1_list(truncated_size, -1);
      std::vector<int64> truncated_cid2_list(truncated_size, -1);
      std::vector<int64> truncated_cid3_list(truncated_size, -1);
      std::vector<int64> truncated_price_list(truncated_size, -1);
      for (int i = 0; i < truncated_size; ++i) {
        truncated_ts_list[i] = (*origin_ts_list)[i];
        auto cid = (*origin_category_list)[i];
        auto cid1 = (cid >> 48) & 0xffff;
        if (cid1 > 0) {
          truncated_cid1_list[i] = cid1;
        }
        auto cid2 = (cid >> 32) & 0xffff;
        if (cid2 > 0) {
          truncated_cid2_list[i] = cid2;
        }
        auto cid3 = (cid >> 16) & 0xffff;
        if (cid3 > 0) {
          truncated_cid3_list[i] = cid3;
        }
        truncated_price_list[i] = (*origin_price_list)[i];
      }
      context.SetIntListCommonAttr("truncated_ts_list", std::move(truncated_ts_list));
      context.SetIntListCommonAttr("truncated_cid1_list", std::move(truncated_cid1_list));
      context.SetIntListCommonAttr("truncated_cid2_list", std::move(truncated_cid2_list));
      context.SetIntListCommonAttr("truncated_cid3_list", std::move(truncated_cid3_list));
      context.SetIntListCommonAttr("truncated_price_list", std::move(truncated_price_list));
    }
    return true;
  }

  static bool ComputeUserSlicedCpqidList(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    request_ts /= 1000;
    int64 ts_1d = request_ts - 3600 * 24;
    int64 ts_1w = request_ts - 3600 * 24 * 7;
    int64 ts_1m = request_ts - 3600 * 24 * 30;
    int64 ts_3m = request_ts - 3600 * 24 * 90;
    int64 ts_6m = request_ts - 3600 * 24 * 180;
    int64 ts_1y = request_ts - 3600 * 24 * 360;
    auto source_cid_list = context.GetIntListCommonAttr("source_cid_list_all");
    auto source_price_quantile_list = context.GetStringListCommonAttr("source_price_quantile_list_all");
    auto cid_level_value = context.GetIntCommonAttr("cid_level_value").value_or(0);
    int source_cid_list_size = source_cid_list ? (*source_cid_list).size() : 0;
    int source_price_quantile_list_size =
        source_price_quantile_list ? (*source_price_quantile_list).size() : 0;
    auto user_cid_list_final = context.GetIntListCommonAttr("user_cid_list_final");
    auto user_ts_list_final = context.GetIntListCommonAttr("user_ts_list_final");
    auto user_price_list_final = context.GetIntListCommonAttr("user_price_list_final");
    int user_cid_list_size = user_cid_list_final ? (*user_cid_list_final).size() : 0;
    int user_ts_list_size = user_ts_list_final ? (*user_ts_list_final).size() : 0;
    int user_price_list_size = user_price_list_final ? (*user_price_list_final).size() : 0;
    folly::F14FastMap<int64, std::vector<float>> cid2price_quantile;
    if (source_cid_list && source_price_quantile_list &&
        source_cid_list_size == source_price_quantile_list_size) {
      for (int i = 0; i < source_cid_list_size; ++i) {
        auto cid = (*source_cid_list)[i];
        if (cid > 0) {
          const auto &quantile_str = (*source_price_quantile_list)[i];
          if (!quantile_str.empty()) {
            std::vector<absl::string_view> quantile_list = absl::StrSplit(quantile_str, ',');
            std::vector<float> quantiles;
            quantiles.reserve(quantile_list.size());
            for (size_t j = 0; j < quantile_list.size(); ++j) {
              float value = 0.0;
              if (absl::SimpleAtof(quantile_list[j], &value)) {
                quantiles.emplace_back(value);
              }
            }
            quantiles.shrink_to_fit();
            cid2price_quantile[cid] = std::move(quantiles);
          }
        }
      }
    }
    if (request_ts > 0 && cid_level_value > 0 && user_cid_list_final && user_ts_list_final &&
        user_price_list_final && user_cid_list_size == user_ts_list_size &&
        user_cid_list_size == user_price_list_size) {
      std::vector<int64> user_cpqid_list_1d;
      user_cpqid_list_1d.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_1w;
      user_cpqid_list_1w.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_1m;
      user_cpqid_list_1m.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_3m;
      user_cpqid_list_3m.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_6m;
      user_cpqid_list_6m.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_1y;
      user_cpqid_list_1y.reserve(user_cid_list_size);
      std::vector<int64> user_cpqid_list_all;
      user_cpqid_list_all.reserve(user_cid_list_size);
      for (int i = user_cid_list_size - 1; i >= 0; --i) {
        auto cid = (*user_cid_list_final)[i];
        auto ts = (*user_ts_list_final)[i];
        auto price = (*user_price_list_final)[i];
        if (cid > 0) {
          auto iter = cid2price_quantile.find(cid);
          int64 quantile = 0;
          if (iter != cid2price_quantile.end() && price > 0) {
            quantile = std::upper_bound(iter->second.begin(), iter->second.end(), static_cast<float>(price)) -
                       iter->second.begin() + 1;
          }
          quantile += cid * 100000 + 100 * cid_level_value;
          user_cpqid_list_all.emplace_back(quantile);
          if (ts > ts_1d) {
            user_cpqid_list_1d.emplace_back(quantile);
            user_cpqid_list_1w.emplace_back(quantile);
            user_cpqid_list_1m.emplace_back(quantile);
            user_cpqid_list_3m.emplace_back(quantile);
            user_cpqid_list_6m.emplace_back(quantile);
            user_cpqid_list_1y.emplace_back(quantile);
          } else if (ts > ts_1w) {
            user_cpqid_list_1w.emplace_back(quantile);
            user_cpqid_list_1m.emplace_back(quantile);
            user_cpqid_list_3m.emplace_back(quantile);
            user_cpqid_list_6m.emplace_back(quantile);
            user_cpqid_list_1y.emplace_back(quantile);
          } else if (ts > ts_1m) {
            user_cpqid_list_1m.emplace_back(quantile);
            user_cpqid_list_3m.emplace_back(quantile);
            user_cpqid_list_6m.emplace_back(quantile);
            user_cpqid_list_1y.emplace_back(quantile);
          } else if (ts > ts_3m) {
            user_cpqid_list_3m.emplace_back(quantile);
            user_cpqid_list_6m.emplace_back(quantile);
            user_cpqid_list_1y.emplace_back(quantile);
          } else if (ts > ts_6m) {
            user_cpqid_list_6m.emplace_back(quantile);
            user_cpqid_list_1y.emplace_back(quantile);
          } else if (ts > ts_1y) {
            user_cpqid_list_1y.emplace_back(quantile);
          }
        }
      }
      user_cpqid_list_1d.shrink_to_fit();
      user_cpqid_list_1w.shrink_to_fit();
      user_cpqid_list_1m.shrink_to_fit();
      user_cpqid_list_3m.shrink_to_fit();
      user_cpqid_list_6m.shrink_to_fit();
      user_cpqid_list_1y.shrink_to_fit();
      user_cpqid_list_all.shrink_to_fit();
      context.SetIntListCommonAttr("user_cpqid_list_1d", std::move(user_cpqid_list_1d));
      context.SetIntListCommonAttr("user_cpqid_list_1w", std::move(user_cpqid_list_1w));
      context.SetIntListCommonAttr("user_cpqid_list_1m", std::move(user_cpqid_list_1m));
      context.SetIntListCommonAttr("user_cpqid_list_3m", std::move(user_cpqid_list_3m));
      context.SetIntListCommonAttr("user_cpqid_list_6m", std::move(user_cpqid_list_6m));
      context.SetIntListCommonAttr("user_cpqid_list_1y", std::move(user_cpqid_list_1y));
      context.SetIntListCommonAttr("user_cpqid_list_all", std::move(user_cpqid_list_all));
    }
    return true;
  }

  static bool ComputeUserHistoryCidPriceQuantile(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_cid_list = context.GetIntListCommonAttr("source_cid_list_all");
    auto source_price_quantile_list = context.GetStringListCommonAttr("source_price_quantile_list_all");
    auto cid_level_value = context.GetIntCommonAttr("cid_level_value").value_or(0);
    int source_cid_list_size = source_cid_list ? (*source_cid_list).size() : 0;
    int source_price_quantile_list_size =
        source_price_quantile_list ? (*source_price_quantile_list).size() : 0;
    auto dedup_cid_list_final = context.GetIntListCommonAttr("dedup_cid_list_final");
    auto dedup_ts_list_final = context.GetIntListCommonAttr("dedup_ts_list_final");
    auto dedup_price_list_final = context.GetIntListCommonAttr("dedup_price_list_final");
    auto dedup_cnt_list_final = context.GetIntListCommonAttr("dedup_cnt_list_final");
    int dedup_cid_list_size = dedup_cid_list_final ? (*dedup_cid_list_final).size() : 0;
    int dedup_ts_list_size = dedup_ts_list_final ? (*dedup_ts_list_final).size() : 0;
    int dedup_price_list_size = dedup_price_list_final ? (*dedup_price_list_final).size() : 0;
    int dedup_cnt_list_size = dedup_cnt_list_final ? (*dedup_cnt_list_final).size() : 0;
    folly::F14FastMap<int64, std::vector<float>> cid2price_quantile;
    if (source_cid_list && source_price_quantile_list &&
        source_cid_list_size == source_price_quantile_list_size) {
      for (int i = 0; i < source_cid_list_size; ++i) {
        auto cid = (*source_cid_list)[i];
        if (cid > 0) {
          const auto &quantile_str = (*source_price_quantile_list)[i];
          if (!quantile_str.empty()) {
            std::vector<absl::string_view> quantile_list = absl::StrSplit(quantile_str, ',');
            std::vector<float> quantiles;
            quantiles.reserve(quantile_list.size());
            for (size_t j = 0; j < quantile_list.size(); ++j) {
              float value = 0.0;
              if (absl::SimpleAtof(quantile_list[j], &value)) {
                quantiles.emplace_back(value);
              }
            }
            cid2price_quantile[cid] = quantiles;
          }
        }
      }
    }
    if (cid_level_value > 0 && dedup_cid_list_final && dedup_ts_list_final && dedup_price_list_final &&
        dedup_cnt_list_final && dedup_cid_list_size == dedup_ts_list_size &&
        dedup_cid_list_size == dedup_price_list_size && dedup_cid_list_size == dedup_cnt_list_size) {
      int64 total_cnt = 0;
      for (int i = 0; i < dedup_cnt_list_size; ++i) {
        auto cnt = (*dedup_cnt_list_final)[i];
        if (cnt > 8) {
          cnt = 8;
        }
        if (cnt > 0) {
          total_cnt += cnt;
        } else {
          total_cnt += 1;
        }
      }
      std::vector<int64> dedup_cpqid_list_final(dedup_cid_list_size, -1);
      std::vector<int64> flatten_cpqid_list_final;
      flatten_cpqid_list_final.reserve(total_cnt);
      std::vector<int64> flatten_cid_list_final;
      flatten_cid_list_final.reserve(total_cnt);
      std::vector<int64> flatten_ts_list_final;
      flatten_ts_list_final.reserve(total_cnt);
      for (int i = 0; i < dedup_cid_list_size; ++i) {
        auto cid = (*dedup_cid_list_final)[i];
        auto ts = (*dedup_ts_list_final)[i];
        auto price = (*dedup_price_list_final)[i];
        auto cnt = (*dedup_cnt_list_final)[i];
        if (cid > 0) {
          auto iter = cid2price_quantile.find(cid);
          int64 quantile = 0;
          if (iter != cid2price_quantile.end() && price > 0) {
            quantile = std::upper_bound(iter->second.begin(), iter->second.end(), static_cast<float>(price)) -
                       iter->second.begin() + 1;
          }
          quantile += cid * 100000 + 100 * cid_level_value;
          dedup_cpqid_list_final[i] = quantile;
        }
        for (int64 j = 0; j < cnt; ++j) {
          flatten_cpqid_list_final.emplace_back(dedup_cpqid_list_final[i]);
          flatten_cid_list_final.emplace_back(cid);
          flatten_ts_list_final.emplace_back(ts);
        }
      }
      context.SetIntListCommonAttr("dedup_cpqid_list_final", std::move(dedup_cpqid_list_final));
      context.SetIntListCommonAttr("flatten_cpqid_list_final", std::move(flatten_cpqid_list_final));
      context.SetIntListCommonAttr("flatten_cid_list_final", std::move(flatten_cid_list_final));
      context.SetIntListCommonAttr("flatten_ts_list_final", std::move(flatten_ts_list_final));
    }
    return true;
  }

  static bool ComputeItemCidPriceQuantile(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto pack_cid_list = context.GetIntListCommonAttr("pack_cid_list");
    auto pack_price_quantile_list = context.GetStringListCommonAttr("pack_price_quantile_list");
    auto cid_level_value = context.GetIntCommonAttr("cid_level_value").value_or(0);
    int pack_cid_list_size = pack_cid_list ? (*pack_cid_list).size() : 0;
    int pack_price_quantile_list_size = pack_price_quantile_list ? (*pack_price_quantile_list).size() : 0;
    auto cid = context.GetIntItemAttr("cid");
    auto price = context.GetIntItemAttr("price");
    folly::F14FastMap<int64, std::vector<float>> cid2price_quantile;
    if (pack_cid_list && pack_price_quantile_list && cid_level_value > 0 &&
        pack_cid_list_size == pack_price_quantile_list_size) {
      for (int i = 0; i < pack_cid_list_size; ++i) {
        auto catid = (*pack_cid_list)[i];
        if (catid > 0) {
          const auto &quantile_str = (*pack_price_quantile_list)[i];
          if (!quantile_str.empty()) {
            std::vector<absl::string_view> quantile_list = absl::StrSplit(quantile_str, ',');
            std::vector<float> quantiles;
            quantiles.reserve(quantile_list.size());
            for (size_t j = 0; j < quantile_list.size(); ++j) {
              float value = 0.0;
              if (absl::SimpleAtof(quantile_list[j], &value)) {
                quantiles.emplace_back(value);
              }
            }
            cid2price_quantile[catid] = quantiles;
          }
        }
      }
    }
    auto cpqid = context.SetIntItemAttr("cpqid");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_cid = cid(result).value_or(0);
      auto item_price = price(result).value_or(0);
      int64 quantile = -1;
      if (item_cid > 0) {
        auto iter = cid2price_quantile.find(item_cid);
        quantile = 0;
        if (iter != cid2price_quantile.end() && item_price > 0) {
          quantile =
              std::upper_bound(iter->second.begin(), iter->second.end(), static_cast<float>(item_price)) -
              iter->second.begin() + 1;
        }
        quantile += item_cid * 100000 + 100 * cid_level_value;
      }
      cpqid(result, quantile);
    });
    return true;
  }

  static bool SliceCidSearchUserCidPriceQuantile(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    request_ts /= 1000;
    int64 ts_1d = request_ts - 3600 * 24;
    int64 ts_1w = request_ts - 3600 * 24 * 7;
    int64 ts_1m = request_ts - 3600 * 24 * 30;
    int64 ts_3m = request_ts - 3600 * 24 * 90;
    int64 ts_6m = request_ts - 3600 * 24 * 180;
    int64 ts_1y = request_ts - 3600 * 24 * 360;
    auto flatten_cid_list_final = context.GetIntListCommonAttr("flatten_cid_list_final");
    auto flatten_cpqid_list_final = context.GetIntListCommonAttr("flatten_cpqid_list_final");
    auto flatten_ts_list_final = context.GetIntListCommonAttr("flatten_ts_list_final");
    int flatten_cid_list_size = flatten_cid_list_final ? (*flatten_cid_list_final).size() : 0;
    int flatten_cpqid_list_size = flatten_cpqid_list_final ? (*flatten_cpqid_list_final).size() : 0;
    int flatten_ts_list_size = flatten_ts_list_final ? (*flatten_ts_list_final).size() : 0;
    folly::F14FastMap<int64, std::vector<int64>> cid2cpqid_list_all;
    folly::F14FastMap<int64, std::vector<int64>> cid2ts_slice_cnt_all;
    if (request_ts > 0 && flatten_cid_list_final && flatten_cpqid_list_final && flatten_ts_list_final &&
        flatten_cid_list_size == flatten_cpqid_list_size && flatten_cid_list_size == flatten_ts_list_size) {
      for (int i = 0; i < flatten_cid_list_size; ++i) {
        auto catid = (*flatten_cid_list_final)[i];
        auto cpqid = (*flatten_cpqid_list_final)[i];
        auto ts = (*flatten_ts_list_final)[i];
        if (catid > 0 && cpqid > 0) {
          auto iter1 = cid2ts_slice_cnt_all.emplace(catid, std::vector<int64>(6, 0));
          auto iter2 = cid2cpqid_list_all.emplace(catid, std::vector<int64>());
          auto &ts_slice_vec = iter1.first->second;
          auto &cpqid_list_all = iter2.first->second;
          cpqid_list_all.emplace_back(cpqid);
          if (ts > ts_1d) {
            ts_slice_vec[0] += 1;
            ts_slice_vec[1] += 1;
            ts_slice_vec[2] += 1;
            ts_slice_vec[3] += 1;
            ts_slice_vec[4] += 1;
            ts_slice_vec[5] += 1;
          } else if (ts > ts_1w) {
            ts_slice_vec[1] += 1;
            ts_slice_vec[2] += 1;
            ts_slice_vec[3] += 1;
            ts_slice_vec[4] += 1;
            ts_slice_vec[5] += 1;
          } else if (ts > ts_1m) {
            ts_slice_vec[2] += 1;
            ts_slice_vec[3] += 1;
            ts_slice_vec[4] += 1;
            ts_slice_vec[5] += 1;
          } else if (ts > ts_3m) {
            ts_slice_vec[3] += 1;
            ts_slice_vec[4] += 1;
            ts_slice_vec[5] += 1;
          } else if (ts > ts_6m) {
            ts_slice_vec[4] += 1;
            ts_slice_vec[5] += 1;
          } else if (ts > ts_1y) {
            ts_slice_vec[5] += 1;
          }
        }
      }
    }
    auto cid = context.GetIntItemAttr("cid");
    auto cid_search_cpqid_list_1d = context.SetIntListItemAttr("cid_search_cpqid_list_1d");
    auto cid_search_cpqid_list_1w = context.SetIntListItemAttr("cid_search_cpqid_list_1w");
    auto cid_search_cpqid_list_1m = context.SetIntListItemAttr("cid_search_cpqid_list_1m");
    auto cid_search_cpqid_list_3m = context.SetIntListItemAttr("cid_search_cpqid_list_3m");
    auto cid_search_cpqid_list_6m = context.SetIntListItemAttr("cid_search_cpqid_list_6m");
    auto cid_search_cpqid_list_1y = context.SetIntListItemAttr("cid_search_cpqid_list_1y");
    auto cid_search_cpqid_list_all = context.SetIntListItemAttr("cid_search_cpqid_list_all");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_cid = cid(result).value_or(0);
      auto iter1 = cid2cpqid_list_all.find(item_cid);
      auto iter2 = cid2ts_slice_cnt_all.find(item_cid);
      std::vector<int64> tmp_1d;
      std::vector<int64> tmp_1w;
      std::vector<int64> tmp_1m;
      std::vector<int64> tmp_3m;
      std::vector<int64> tmp_6m;
      std::vector<int64> tmp_1y;
      std::vector<int64> tmp_all;
      if (iter1 != cid2cpqid_list_all.end() && iter2 != cid2ts_slice_cnt_all.end()) {
        const auto &cpqid_list_all = iter1->second;
        const auto &ts_slice_vec = iter2->second;
        if (ts_slice_vec.size() == 6) {
          if (ts_slice_vec[0] > 0) {
            tmp_1d.reserve(ts_slice_vec[0]);
            for (int j = 0; j < ts_slice_vec[0]; ++j) {
              tmp_1d.emplace_back(cpqid_list_all[j]);
            }
          }
          if (ts_slice_vec[1] > 0) {
            tmp_1w.reserve(ts_slice_vec[1]);
            for (int j = 0; j < ts_slice_vec[1]; ++j) {
              tmp_1w.emplace_back(cpqid_list_all[j]);
            }
          }
          if (ts_slice_vec[2] > 0) {
            tmp_1m.reserve(ts_slice_vec[2]);
            for (int j = 0; j < ts_slice_vec[2]; ++j) {
              tmp_1m.emplace_back(cpqid_list_all[j]);
            }
          }
          if (ts_slice_vec[3] > 0) {
            tmp_3m.reserve(ts_slice_vec[3]);
            for (int j = 0; j < ts_slice_vec[3]; ++j) {
              tmp_3m.emplace_back(cpqid_list_all[j]);
            }
          }
          if (ts_slice_vec[4] > 0) {
            tmp_6m.reserve(ts_slice_vec[4]);
            for (int j = 0; j < ts_slice_vec[4]; ++j) {
              tmp_6m.emplace_back(cpqid_list_all[j]);
            }
          }
          if (ts_slice_vec[5] > 0) {
            tmp_1y.reserve(ts_slice_vec[5]);
            for (int j = 0; j < ts_slice_vec[5]; ++j) {
              tmp_1y.emplace_back(cpqid_list_all[j]);
            }
          }
        }
        tmp_all = cpqid_list_all;
      }
      cid_search_cpqid_list_1d(result, tmp_1d);
      cid_search_cpqid_list_1w(result, tmp_1w);
      cid_search_cpqid_list_1m(result, tmp_1m);
      cid_search_cpqid_list_3m(result, tmp_3m);
      cid_search_cpqid_list_6m(result, tmp_6m);
      cid_search_cpqid_list_1y(result, tmp_1y);
      cid_search_cpqid_list_all(result, tmp_all);
    });
    return true;
  }

  static bool UserItemCpqidMatch(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto upper_bound = context.GetIntCommonAttr("upper_bound").value_or(0);
    auto lower_bound = context.GetIntCommonAttr("lower_bound").value_or(0);
    auto user_cpqid_list = context.GetIntListCommonAttr("user_cpqid_list");
    folly::F14FastMap<int64, uint16> cid2bitset;
    if (user_cpqid_list) {
      int user_cpqid_list_size = (*user_cpqid_list).size();
      for (int i = 0; i < user_cpqid_list_size; ++i) {
        auto cpqid = (*user_cpqid_list)[i];
        if (cpqid > 0) {
          auto cid = cpqid / 100000;
          auto level = cpqid % 100;
          if (level > 0) {
            auto &bitset = cid2bitset[cid];
            auto left = level - lower_bound;
            if (left < 1) {
              left = 1;
            }
            auto right = level + upper_bound;
            if (right > 10) {
              right = 10;
            }
            for (int j = left; j <= right; ++j) {
              bitset |= (1 << j);
            }
          }
        }
      }
    }
    auto cid = context.GetIntItemAttr("cid");
    auto cpqid = context.GetIntItemAttr("cpqid");
    auto cpqid_not_match = context.SetIntItemAttr("cpqid_not_match");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 flag_not_match = 0;
      if (!cid2bitset.empty()) {
        auto item_cid = cid(result).value_or(0);
        if (item_cid > 0) {
          auto iter = cid2bitset.find(item_cid);
          if (iter != cid2bitset.end()) {
            auto item_cpqid = cpqid(result).value_or(0);
            if (item_cpqid > 0) {
              auto item_level = item_cpqid % 100;
              if (item_level > 0) {
                flag_not_match = ((1 << item_level) & iter->second) > 0 ? 0 : 1;
              }
            }
          }
        }
      }
      cpqid_not_match(result, flag_not_match);
    });
    return true;
  }

  static bool LastBehaviorTsBeforeRequest(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    auto ts_ms_or_not = context.GetIntCommonAttr("ts_ms_or_not").value_or(0);
    if (ts_ms_or_not > 0) {
      request_ts /= 1000;
    }
    auto reversed = context.GetIntCommonAttr("reversed").value_or(0);
    auto user_ts_list = context.GetIntListCommonAttr("user_ts_list");
    int64 last_ts = 0;
    if (request_ts > 0 && user_ts_list) {
      if (reversed > 0) {
        for (auto iter = user_ts_list->rbegin(); iter != user_ts_list->rend(); ++iter) {
          auto ts = (*iter);
          if (ts < request_ts) {
            last_ts = ts;
            break;
          }
        }
      } else {
        for (auto iter = user_ts_list->begin(); iter != user_ts_list->end(); ++iter) {
          auto ts = (*iter);
          if (ts < request_ts) {
            last_ts = ts;
            break;
          }
        }
      }
    }
    context.SetIntCommonAttr("last_behav_ts_before_request", last_ts);
    return true;
  }

  static bool SwingP2IRetrRerank(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto p2i_per_photo_limit_num = context.GetIntCommonAttr("p2i_swing_retrieval_per_photo_limit_num");
    auto p2i_limit_num = context.GetIntCommonAttr("p2i_swing_retrieval_limit_photo_num");
    auto p2i_item_list = context.GetStringListCommonAttr("p2i_swing_retrieval_value_list");
    int item_id_list_size = p2i_item_list ? (*p2i_item_list).size() : 0;
    std::vector<int64> swing_item_result_list;
    folly::F14FastSet<int64> mem_set;
    int retr_num_cnt = 0;
    if (item_id_list_size > 0 && p2i_item_list) {
      for (int index = 0; index < item_id_list_size; index++) {
        std::vector<std::string> swing_list_tmp = absl::StrSplit((*p2i_item_list)[index], ",");
        std::vector<std::pair<int64, float>> rank_vec;
        int per_photo_retr_num_cnt = 0;
        for (int i = 0; i < swing_list_tmp.size(); i++) {
          std::vector<std::string> kv = absl::StrSplit(swing_list_tmp[i], ":");
          int64 item_id = 0;
          float score = 0.0;
          if (kv.size() == 2) {
            if (!absl::SimpleAtoi(kv[0], &item_id) || !absl::SimpleAtof(kv[1], &score)) {
              continue;
            }
            rank_vec.emplace_back(std::make_pair(item_id, score));
            per_photo_retr_num_cnt++;
          }
          kv.clear();
        }
        std::sort(rank_vec.begin(), rank_vec.end(),
                  [](const auto &a, const auto &b) { return a.second > b.second; });
        for (int i = 0; i < p2i_per_photo_limit_num && i < rank_vec.size() && retr_num_cnt < p2i_limit_num;
             i++) {
          if (mem_set.find(rank_vec[i].first) == mem_set.end()) {
            swing_item_result_list.emplace_back(rank_vec[i].first);
            mem_set.insert(rank_vec[i].first);
            retr_num_cnt++;
          }
        }
        swing_list_tmp.clear();
        rank_vec.clear();
      }
    }
    context.SetIntListCommonAttr("swing_p2i_result_id_list", std::move(swing_item_result_list));
    return true;
  }

  static bool GenerateTriggerInfos(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto clickItemId = context.GetIntListCommonAttr("click_item_id").value_or(absl::Span<const int64>());
    auto clickDetailPageViewTime =
        context.GetIntListCommonAttr("click_detail_page_view_time").value_or(absl::Span<const int64>());
    auto clickItemLag = context.GetIntListCommonAttr("click_item_lag").value_or(absl::Span<const int64>());
    auto clickCid2 = context.GetIntListCommonAttr("click_c2").value_or(absl::Span<const int64>());
    auto clickCid3 = context.GetIntListCommonAttr("click_c3").value_or(absl::Span<const int64>());
    auto clickAuthorId = context.GetIntListCommonAttr("click_author_id").value_or(absl::Span<const int64>());
    auto guess_like_trigger_selection_limit =
        context.GetIntCommonAttr("guess_like_trigger_selection_limit").value_or(0);
    auto guess_like_trigger_selection_method =
        context.GetIntCommonAttr("guess_like_trigger_selection_method").value_or(0);
    auto guess_like_trigger_selection_time_decay =
        context.GetDoubleCommonAttr("guess_like_trigger_selection_time_decay").value_or(1.0);
    auto guessLikeTriggerSelectionVvMax =
        context.GetIntCommonAttr("guess_like_trigger_selection_vv_max").value_or(0);
    auto guessLikeTriggerSelectionVvMin =
        context.GetIntCommonAttr("guess_like_trigger_selection_vv_min").value_or(0);

    // init
    folly::F14FastMap<int64, int64> sumVvDict;
    folly::F14FastMap<int64, int64> sumVvWithTimeDecayDict;
    std::vector<int64> sumVvList;
    sumVvList.reserve(clickItemId.size());

    int curIid = -1;
    int64 curVv = 0;

    if (!clickItemId.empty() && clickItemId.size() > 0) {
      for (int i = clickItemId.size() - 1; i >= 0; --i) {
        curIid = clickItemId[i];
        curVv = clickDetailPageViewTime[i];
        curVv = std::min(curVv, guessLikeTriggerSelectionVvMax);
        curVv = std::max(curVv, guessLikeTriggerSelectionVvMin);
        double curVvWithTimeDecay =
            curVv * std::pow(guess_like_trigger_selection_time_decay, clickItemLag[i]);

        if (sumVvDict.find(curIid) == sumVvDict.end()) {
          sumVvDict[curIid] = curVv;
          sumVvWithTimeDecayDict[curIid] = curVvWithTimeDecay;
        } else {
          sumVvDict[curIid] += curVv;
          sumVvWithTimeDecayDict[curIid] += curVvWithTimeDecay;
        }

        sumVvList.push_back(sumVvDict[curIid]);
      }
    }

    struct ItemData {
      double score;
      int itemId;
      int pageView;
      int itemLag;
      int c2;
      int c3;
      int author;

      ItemData(double s, int id, int pv, int lag, int cid2, int cid3, int auth)
          : score(s), itemId(id), pageView(pv), itemLag(lag), c2(cid2), c3(cid3), author(auth) {}
    };
    // calculate
    std::vector<ItemData> itemKeys;
    double triggerSelectionScore = 0.0;
    double timeScore = 0.0;
    double decay = 0.5;
    int offset = 1;
    double scale = 5.0;
    double lambda = std::log(decay) / scale;
    int pageView = 10;
    int limit = guess_like_trigger_selection_limit;
    int guessLikeTriggerSelectionMethod = guess_like_trigger_selection_method;
    double guessLikeTriggerSelectionTimeDecay = guess_like_trigger_selection_time_decay;
    itemKeys.reserve(clickItemId.size());
    std::vector<int64> triggerSelectionItemList;
    std::vector<double> triggerSelectionScoreList;
    std::vector<int64> triggerSelectionCid2;
    std::vector<int64> triggerSelectionCid3;
    std::vector<int64> triggerSelectionAuthorId;
    triggerSelectionItemList.reserve(clickItemId.size());
    triggerSelectionScoreList.reserve(clickItemId.size());
    triggerSelectionCid2.reserve(clickItemId.size());
    triggerSelectionCid3.reserve(clickItemId.size());
    triggerSelectionAuthorId.reserve(clickItemId.size());

    for (int i = 0; i < clickItemId.size(); ++i) {
      pageView = 10;
      if (clickDetailPageViewTime[i] < 10) {
        pageView = clickDetailPageViewTime[i];
      }
      int64 zero = 0;
      timeScore = std::exp(lambda * std::max(zero, clickItemLag[i] - offset));

      if (guessLikeTriggerSelectionMethod == 1) {
        pageView = sumVvDict[clickItemId[i]];
      } else if (guessLikeTriggerSelectionMethod == 2) {
        pageView = sumVvWithTimeDecayDict[clickItemId[i]];
      } else if (guessLikeTriggerSelectionMethod == 3) {
        timeScore = std::pow(guessLikeTriggerSelectionTimeDecay, clickItemLag[i]);
        pageView = sumVvWithTimeDecayDict[clickItemId[i]];
      }

      triggerSelectionScore = timeScore * std::log(2 + pageView);
      itemKeys.emplace_back(ItemData(triggerSelectionScore, clickItemId[i], pageView, clickItemLag[i],
                                     clickCid2[i], clickCid3[i], clickAuthorId[i]));
    }

    std::sort(itemKeys.begin(), itemKeys.end(),
              [](const ItemData &a, const ItemData &b) { return a.score > b.score; });

    std::unordered_map<int, bool> itemIdExists;
    int itemNum = 1;

    for (const auto &item : itemKeys) {
      if (itemNum > limit) {
        break;
      }

      int itemIdTemp = item.itemId;
      if (itemIdExists.find(itemIdTemp) != itemIdExists.end() && itemIdExists[itemIdTemp]) {
        continue;
      }

      itemIdExists[itemIdTemp] = true;
      triggerSelectionItemList.push_back(item.itemId);
      triggerSelectionScoreList.push_back(item.score);
      triggerSelectionCid2.push_back(item.c2);
      triggerSelectionCid3.push_back(item.c3);
      triggerSelectionAuthorId.push_back(item.author);
      itemNum++;
    }

    auto trigger_selection_item_list =
        context.SetIntListCommonAttr("trigger_selection_item_list", std::move(triggerSelectionItemList));
    auto trigger_selection_score_list =
        context.SetDoubleListCommonAttr("trigger_selection_score_list", std::move(triggerSelectionScoreList));
    auto trigger_selection_cid2 =
        context.SetIntListCommonAttr("trigger_selection_cid2", std::move(triggerSelectionCid2));
    auto trigger_selection_cid3 =
        context.SetIntListCommonAttr("trigger_selection_cid3", std::move(triggerSelectionCid3));
    auto trigger_selection_author_id =
        context.SetIntListCommonAttr("trigger_selection_author_id", std::move(triggerSelectionAuthorId));
    return true;
  }

  static bool MerchantPhotoListGen(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto request_time = context.GetIntCommonAttr("_REQ_TIME_");
    auto shelf_trigger_photo_num_limit = context.GetIntCommonAttr("shelf_trigger_photo_num_limit");
    auto shelf_trigger_photo_date_limit = context.GetIntCommonAttr("shelf_trigger_photo_date_limit");
    auto shelf_trigger_photo_short_term_hour_limit =
        context.GetIntCommonAttr("shelf_trigger_photo_short_term_hour_limit");
    auto shelf_trigger_photo_short_term_minite_limit =
        context.GetIntCommonAttr("shelf_trigger_photo_short_term_minite_limit");
    auto shelf_lvtr_thesh = context.GetDoubleCommonAttr("shelf_lvtr_thesh");
    auto merchant_video_imp_colosuss_label_list =
        context.GetIntListCommonAttr("merchant_video_imp_colosuss_label_list");
    auto merchant_video_imp_colosuss_photo_id_list =
        context.GetIntListCommonAttr("merchant_video_imp_colosuss_photo_id_list");
    auto merchant_video_imp_colosuss_duration_list =
        context.GetIntListCommonAttr("merchant_video_imp_colosuss_duration_list");
    auto merchant_video_imp_colosuss_play_time_list =
        context.GetIntListCommonAttr("merchant_video_imp_colosuss_play_time_list");
    auto merchant_video_imp_colosuss_timestamp_list =
        context.GetIntListCommonAttr("merchant_video_imp_colosuss_timestamp_list");

    std::vector<int64> trigger_id_list;
    std::vector<int64> trigger_id_list_short_term;
    int label_len =
        merchant_video_imp_colosuss_label_list ? (*merchant_video_imp_colosuss_label_list).size() : 0;
    int photo_len =
        merchant_video_imp_colosuss_photo_id_list ? (*merchant_video_imp_colosuss_photo_id_list).size() : 0;
    int duration_len =
        merchant_video_imp_colosuss_duration_list ? (*merchant_video_imp_colosuss_duration_list).size() : 0;
    int play_time_len =
        merchant_video_imp_colosuss_play_time_list ? (*merchant_video_imp_colosuss_play_time_list).size() : 0;
    int timestamp_len =
        merchant_video_imp_colosuss_timestamp_list ? (*merchant_video_imp_colosuss_timestamp_list).size() : 0;
    if (label_len != photo_len || label_len != photo_len || label_len != duration_len ||
        label_len != play_time_len || label_len != timestamp_len || !request_time.has_value()) {
      context.SetIntListCommonAttr("merchant_trigger_photo_list", std::move(trigger_id_list));
      context.SetIntListCommonAttr("merchant_trigger_photo_list_short_term",
                                   std::move(trigger_id_list_short_term));
      return true;
    }
    int days_gap = 400;
    int hours_gap = 6;
    int minites_gap = 30;
    int cnt = 0;
    for (int i = 0; i < label_len; i++) {
      int64 photo_id = (*merchant_video_imp_colosuss_photo_id_list)[i];
      int duration = (*merchant_video_imp_colosuss_duration_list)[i];
      int play_time = (*merchant_video_imp_colosuss_play_time_list)[i];
      int label = (*merchant_video_imp_colosuss_label_list)[i];
      int64 ts = (*merchant_video_imp_colosuss_timestamp_list)[i];
      float lvtr = static_cast<float>(play_time) / duration;
      int cart_flag = ((label >> 0) & 1);
      int like = ((label >> 2) & 1);
      int follow = ((label >> 3) & 1);
      int forward = ((label >> 4) & 1);
      int comment = ((label >> 6) & 1);
      int enter_profile = ((label >> 7) & 1);
      int collect = ((label >> 8) & 1);
      int enter_live = ((label >> 9) & 1);
      int click = ((label >> 10) & 1) + ((label >> 11) & 1);
      int pay = ((label >> 15) & 1);

      days_gap = floor((*request_time / 1000.0 - ts) / (3600.0 * 24));
      hours_gap = floor((*request_time / 1000.0 - ts) / 3600.0);
      minites_gap = floor((*request_time / 1000.0 - ts) / 60.0);
      bool insert_flag = cart_flag != 0 &&
                         (click != 0 || like != 0 || follow != 0 || forward != 0 || comment != 0 ||
                          collect != 0 || lvtr > shelf_lvtr_thesh || enter_profile != 0 || enter_live != 0);
      if (photo_id > 0 && days_gap < shelf_trigger_photo_date_limit && insert_flag != 0 &&
          cnt < shelf_trigger_photo_num_limit) {
        trigger_id_list.emplace_back(photo_id);
        cnt++;
      }
      if (photo_id > 0 && hours_gap < shelf_trigger_photo_short_term_hour_limit && insert_flag != 0 &&
          minites_gap < shelf_trigger_photo_short_term_minite_limit) {
        trigger_id_list_short_term.emplace_back(photo_id);
      }
    }
    context.SetIntListCommonAttr("merchant_trigger_photo_list", std::move(trigger_id_list));
    context.SetIntListCommonAttr("merchant_trigger_photo_list_short_term",
                                 std::move(trigger_id_list_short_term));
    return true;
  }

  static bool SetMatchGroupIdFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto goods_operate_item_group_id_list =
        context.GetStringListCommonAttr("goods_operate_item_group_id_list");
    auto iSelectionGroupIdList_attr = context.GetIntListItemAttr("iSelectionGroupIdList");
    auto iGoodsIfDpdb_attr = context.GetIntItemAttr("iGoodsIfDpdb");
    auto iGoodsIsLowPrice_attr = context.GetIntItemAttr("iGoodsIsLowPrice");
    auto mactch_group_id_flag_attr = context.SetIntItemAttr("mactch_group_id_flag");

    folly::F14FastMap<std::string, int64> targetGroupIdMap;
    int list_size = goods_operate_item_group_id_list ? (*goods_operate_item_group_id_list).size() : 0;
    if (list_size > 0 && goods_operate_item_group_id_list) {
      for (int index = 0; index < list_size; index++) {
        targetGroupIdMap[std::string((*goods_operate_item_group_id_list)[index])] = 1;
        // targetGroupIdMap[goods_operate_item_group_id_list[index]] = 1;
      }
    }
    // for (auto value : goods_operate_item_group_id_list) {
    //   targetGroupIdMap[std::string(value)] = 1;
    // }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto iGoodsIfDpdb = iGoodsIfDpdb_attr(result).value_or(0);
      auto iGoodsIsLowPrice = iGoodsIsLowPrice_attr(result).value_or(0);
      auto iSelectionGroupIdList = iSelectionGroupIdList_attr(result).value_or(absl::Span<const int64>());
      int64 flag = 0;

      // 大牌大补和低价特卖是有单独索引标识的，不用判断筛选包了。
      if (iGoodsIfDpdb == 1 || iGoodsIsLowPrice == 1) {
        flag = 1;
        mactch_group_id_flag_attr(result, flag);
        return;
      }

      for (const auto &v : iSelectionGroupIdList) {
        if (targetGroupIdMap.find(std::to_string(v)) != targetGroupIdMap.end()) {
          flag = 1;
          mactch_group_id_flag_attr(result, flag);
          break;
        }
      }
      if (flag == 0) {
        mactch_group_id_flag_attr(result, flag);
      }
    });
    return true;
  }

  static bool SetItemStyleFilterFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto item_id_attr = context.GetIntItemAttr("item_id");
    auto iGoodsSameItemList_attr = context.GetIntListItemAttr("iGoodsSameItemList");
    auto same_item_style_filter_flag_attr = context.SetIntItemAttr("same_item_style_filter_flag");
    folly::F14FastMap<int64, int> spuItemScoreDict;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or(0);
      auto iGoodsSameItemList = iGoodsSameItemList_attr(result).value_or(absl::Span<const int64>());
      int64 flag = 0;

      // 如果当前 item 是前面某个 item 的同款，则做标记，同时直接返回，不存储当前品的同款品
      if (spuItemScoreDict.find(item_id) != spuItemScoreDict.end()) {
        flag = 1;
      }

      // 存储当前品的同款品，用作后面的优选
      for (const auto &sameItemId : iGoodsSameItemList) {
        spuItemScoreDict[sameItemId] = 1;
      }

      same_item_style_filter_flag_attr(result, flag);
    });
    return true;
  }

  static bool TriggerListInfos(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto click_item_id = context.GetIntListCommonAttr("click_item_id").value_or(absl::Span<const int64>());
    auto click_detail_page_view_time =
        context.GetIntListCommonAttr("click_detail_page_view_time").value_or(absl::Span<const int64>());
    auto click_item_lag = context.GetIntListCommonAttr("click_item_lag").value_or(absl::Span<const int64>());
    auto click_c2 = context.GetIntListCommonAttr("click_c2").value_or(absl::Span<const int64>());
    auto click_c3 = context.GetIntListCommonAttr("click_c3").value_or(absl::Span<const int64>());
    auto click_author_id =
        context.GetIntListCommonAttr("click_author_id").value_or(absl::Span<const int64>());
    auto guess_like_trigger_selection_limit =
        context.GetIntCommonAttr("guess_like_trigger_selection_limit").value_or(0);
    folly::F14FastMap<int64, int> item_id_exists;

    struct ItemKey {
      double score;
      int item_id;
      int page_view;
      int item_lag;
      int c2;
      int c3;
      int author;
      ItemKey(double s, int id, int pv, int lag, int cid2, int cid3, int auth)
          : score(s), item_id(id), page_view(pv), item_lag(lag), c2(cid2), c3(cid3), author(auth) {}
    };

    std::vector<ItemKey> item_keys;
    item_keys.reserve(click_item_id.size());
    double trigger_selection_score = 0.0;
    double time_score = 0.0;
    double decay = 0.5;
    int offset = 1;
    double scale = 5.0;
    double lamda = std::log(decay) / scale;
    int page_view = 10;
    int limit = guess_like_trigger_selection_limit;
    int64 zero = 0;

    for (size_t i = 0; i < click_item_id.size(); i++) {
      page_view = 10;
      if (click_detail_page_view_time[i] < 10) {
        page_view = click_detail_page_view_time[i];
      }
      time_score = std::exp(lamda * std::max(zero, click_item_lag[i] - offset));
      trigger_selection_score = time_score * std::log(2 + page_view);
      item_keys.push_back(ItemKey(trigger_selection_score, click_item_id[i], page_view, click_item_lag[i],
                                  click_c2[i], click_c3[i], click_author_id[i]));
    }

    std::sort(item_keys.begin(), item_keys.end(),
              [](const ItemKey &a, const ItemKey &b) { return a.score > b.score; });

    std::vector<int64> trigger_selection_item_list;
    std::vector<double> trigger_selection_score_list;
    std::vector<int64> trigger_selection_cid2;
    std::vector<int64> trigger_selection_cid3;
    std::vector<int64> trigger_selection_author_id;
    trigger_selection_item_list.reserve(item_keys.size());
    trigger_selection_score_list.reserve(item_keys.size());
    trigger_selection_cid2.reserve(item_keys.size());
    trigger_selection_cid3.reserve(item_keys.size());
    trigger_selection_author_id.reserve(item_keys.size());
    int64 item_id_temp = 0;
    int item_num = 1;

    for (const auto &item : item_keys) {
      if (item_num > limit) {
        break;
      }
      item_id_temp = item.item_id;
      if (item_id_exists.find(item_id_temp) != item_id_exists.end()) {
        continue;
      }
      item_id_exists[item_id_temp] = 1;
      trigger_selection_item_list.push_back(item.item_id);
      trigger_selection_score_list.push_back(item.score);
      trigger_selection_cid2.push_back(item.c2);
      trigger_selection_cid3.push_back(item.c3);
      trigger_selection_author_id.push_back(item.author);
      item_num++;
    }

    context.SetIntListCommonAttr("trigger_selection_item_list", std::move(trigger_selection_item_list));
    context.SetDoubleListCommonAttr("trigger_selection_score_list", std::move(trigger_selection_score_list));
    context.SetIntListCommonAttr("trigger_selection_cid2", std::move(trigger_selection_cid2));
    context.SetIntListCommonAttr("trigger_selection_cid3", std::move(trigger_selection_cid3));
    context.SetIntListCommonAttr("trigger_selection_author_id", std::move(trigger_selection_author_id));
    return true;
  }

  static bool GetItemActionStep5(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto bh_gyl_is_no_click_cate1id_w =
        context.GetDoubleCommonAttr("bh_gyl_is_no_click_cate1id_w").value_or(0.0);
    auto bh_gyl_is_no_click_cate2id_w =
        context.GetDoubleCommonAttr("bh_gyl_is_no_click_cate2id_w").value_or(0.0);
    auto bh_gyl_is_no_click_cate3id_w =
        context.GetDoubleCommonAttr("bh_gyl_is_no_click_cate3id_w").value_or(0.0);

    auto is_no_click_cate1id_flag_attr = context.GetIntItemAttr("is_no_click_cate1id_flag");
    auto is_no_click_cate2id_flag_attr = context.GetIntItemAttr("is_no_click_cate2id_flag");
    auto is_no_click_cate3id_flag_attr = context.GetIntItemAttr("is_no_click_cate3id_flag");

    auto ee_weight_attr = context.SetDoubleItemAttr("ee_weight");

    std::srand(std::time(nullptr));
    double random_number = static_cast<double>(std::rand()) / RAND_MAX;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto is_no_click_cate1id_flag = is_no_click_cate1id_flag_attr(result).value_or(0);
      auto is_no_click_cate2id_flag = is_no_click_cate2id_flag_attr(result).value_or(0);
      auto is_no_click_cate3id_flag = is_no_click_cate3id_flag_attr(result).value_or(0);

      double ee_weight = is_no_click_cate1id_flag * bh_gyl_is_no_click_cate1id_w +
                         is_no_click_cate2id_flag * bh_gyl_is_no_click_cate2id_w +
                         is_no_click_cate3id_flag * bh_gyl_is_no_click_cate3id_w;

      ee_weight = ee_weight * random_number;

      ee_weight_attr(result, ee_weight);
    });
    return true;
  }

  static bool ParseCalibrationScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto calibration_score_value_ptr = context.GetStringCommonAttr("calibration_score_value");
    auto gyl_calibration_upbound = context.GetDoubleCommonAttr("gyl_calibration_upbound").value_or(1.5);
    auto gyl_calibration_lowbound = context.GetDoubleCommonAttr("gyl_calibration_lowbound").value_or(0.5);
    auto gyl_calibration_gmv_upbound =
        context.GetDoubleCommonAttr("gyl_calibration_gmv_upbound").value_or(1.5);
    auto gyl_calibration_gmv_lowbound =
        context.GetDoubleCommonAttr("gyl_calibration_gmv_lowbound").value_or(0.5);
    auto cid3 = context.GetIntItemAttr("iCate3Id");

    auto calibration_ctr_weight_attr = context.SetDoubleItemAttr("calibration_ctr_weight");
    auto calibration_cvr_weight_attr = context.SetDoubleItemAttr("calibration_cvr_weight");
    auto calibration_gmv_weight_attr = context.SetDoubleItemAttr("calibration_gmv_weight");
    std::string calibration_score_value =
        calibration_score_value_ptr ? std::string(*calibration_score_value_ptr) : "";

    folly::F14FastMap<int, std::vector<double>> cid3_score_hash_map;

    if (!calibration_score_value.empty()) {
      std::vector<std::string> kv = absl::StrSplit(calibration_score_value, ";");
      for (int i = 0; i < kv.size(); i++) {
        int cate3id = 0;
        std::vector<std::string> str_tmp = absl::StrSplit(kv[i], ":");
        if (str_tmp.size() == 2 && absl::SimpleAtoi(str_tmp[0], &cate3id)) {
          std::vector<std::string> weight_str = absl::StrSplit(str_tmp[1], ",");
          for (int i = 0; i < weight_str.size() && weight_str.size() == 3; i++) {
            float weight = 1.0;
            if (!absl::SimpleAtof(weight_str[i], &weight)) {
              weight = 1.0;
            }
            cid3_score_hash_map[cate3id].push_back(weight);
          }
        }
        str_tmp.clear();
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cate = cid3(result).value_or(0);
      double calibration_ctr_weight = 1.0;
      double calibration_cvr_weight = 1.0;
      double calibration_gmv_weight = 1.0;

      if (cid3_score_hash_map.find(cate) != cid3_score_hash_map.end() &&
          cid3_score_hash_map[cate].size() == 3) {
        calibration_ctr_weight = cid3_score_hash_map[cate][0] > 0 ? 1.0 / cid3_score_hash_map[cate][0] : 1.0;
        calibration_cvr_weight = cid3_score_hash_map[cate][1] > 0 ? 1.0 / cid3_score_hash_map[cate][1] : 1.0;
        calibration_gmv_weight = cid3_score_hash_map[cate][2] > 0 ? 1.0 / cid3_score_hash_map[cate][2] : 1.0;
        calibration_ctr_weight = calibration_ctr_weight > gyl_calibration_upbound ? gyl_calibration_upbound
                                                                                  : calibration_ctr_weight;
        calibration_cvr_weight = calibration_cvr_weight > gyl_calibration_upbound ? gyl_calibration_upbound
                                                                                  : calibration_cvr_weight;
        calibration_gmv_weight = calibration_gmv_weight > gyl_calibration_gmv_upbound
                                     ? gyl_calibration_gmv_upbound
                                     : calibration_gmv_weight;
        calibration_ctr_weight = calibration_ctr_weight < gyl_calibration_lowbound ? gyl_calibration_lowbound
                                                                                   : calibration_ctr_weight;
        calibration_cvr_weight = calibration_cvr_weight < gyl_calibration_lowbound ? gyl_calibration_lowbound
                                                                                   : calibration_cvr_weight;
        calibration_gmv_weight = calibration_gmv_weight < gyl_calibration_gmv_lowbound
                                     ? gyl_calibration_gmv_lowbound
                                     : calibration_gmv_weight;
      }
      calibration_ctr_weight_attr(result, calibration_ctr_weight);
      calibration_cvr_weight_attr(result, calibration_cvr_weight);
      calibration_gmv_weight_attr(result, calibration_gmv_weight);
    });

    return true;
  }

  static bool ListwiseAllGpmScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto rerank_context_scores = context.GetDoubleItemAttr("rerank_context");
    auto listwise_generated_variant_lists_attr = context.GetIntListItemAttr("generated_variant_lists");
    auto listwise_generated_variant_item_type_lists_attr =
        context.GetIntListItemAttr("generated_variant_item_type_lists");
    auto listwise_generated_variant_ctr_lists_attr =
        context.GetDoubleListItemAttr("generated_variant_ctr_lists");
    auto listwise_generated_variant_cvr_lists_attr =
        context.GetDoubleListItemAttr("generated_variant_cvr_lists");
    auto listwise_generated_variant_price_lists_attr =
        context.GetDoubleListItemAttr("generated_variant_price_lists");

    double live_ctr_w = context.GetDoubleCommonAttr("listwise_gpm_score_live_ctr_weight").value_or(1.0);
    double live_cvr_w = context.GetDoubleCommonAttr("listwise_gpm_score_live_cvr_weight").value_or(1.0);
    double live_price_w = context.GetDoubleCommonAttr("listwise_gpm_score_live_price_weight").value_or(1.0);
    double goods_ctr_w = context.GetDoubleCommonAttr("listwise_gpm_score_goods_ctr_weight").value_or(1.0);
    double goods_cvr_w = context.GetDoubleCommonAttr("listwise_gpm_score_goods_cvr_weight").value_or(1.0);
    double goods_price_w = context.GetDoubleCommonAttr("listwise_gpm_score_goods_price_weight").value_or(1.0);
    double live_gpm_alpha = context.GetDoubleCommonAttr("listwise_gpm_score_live_gpm_alpha").value_or(1.0);
    double goods_gpm_alpha = context.GetDoubleCommonAttr("listwise_gpm_score_goods_gpm_alpha").value_or(1.0);
    auto live_position_weight_list_str =
        context.GetStringCommonAttr("listwise_gpm_live_position_weight_list");
    auto good_position_weight_list_str =
        context.GetStringCommonAttr("listwise_gpm_good_position_weight_list");
    auto enable_listwise_egpm_eval_live_pos_disc =
        context.GetIntCommonAttr("listwise_enable_egpm_eval_live_pos_disc");
    auto enable_listwise_egpm_eval_good_pos_disc =
        context.GetIntCommonAttr("listwise_enable_egpm_eval_good_pos_disc");
    auto enable_listwise_control_live_prop =
        context.GetIntCommonAttr("listwise_enable_listwise_control_live_prop");
    auto enable_listwise_control_live_position =
        context.GetIntCommonAttr("listwise_enable_listwise_control_live_position");

    std::string live_position_weight_vec_final_str =
        live_position_weight_list_str ? std::string(*live_position_weight_list_str) : "";
    std::string good_position_weight_vec_final_str =
        good_position_weight_list_str ? std::string(*good_position_weight_list_str) : "";
    std::vector<std::string> live_position_weight_vec =
        absl::StrSplit(live_position_weight_vec_final_str, ",");
    std::vector<std::string> good_position_weight_vec =
        absl::StrSplit(good_position_weight_vec_final_str, ",");

    auto set_rerank_context_scores = context.SetDoubleItemAttr("rerank_context_final");
    int listwise_live_pos_w_len = live_position_weight_vec.size();
    int listwise_good_pos_w_len = good_position_weight_vec.size();

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto listwise_generated_variant_list = listwise_generated_variant_lists_attr(result);
      auto listwise_generated_variant_item_type_list =
          listwise_generated_variant_item_type_lists_attr(result);
      auto listwise_generated_variant_ctr_list = listwise_generated_variant_ctr_lists_attr(result);
      auto listwise_generated_variant_cvr_list = listwise_generated_variant_cvr_lists_attr(result);
      auto listwise_generated_variant_price_list = listwise_generated_variant_price_lists_attr(result);
      auto listwise_seq_score = rerank_context_scores(result).value_or(0.0);

      int listwise_item_type_len =
          listwise_generated_variant_item_type_list ? (*listwise_generated_variant_item_type_list).size() : 0;
      int listwise_ctr_len =
          listwise_generated_variant_ctr_list ? (*listwise_generated_variant_ctr_list).size() : 0;
      int listwise_cvr_len =
          listwise_generated_variant_cvr_list ? (*listwise_generated_variant_cvr_list).size() : 0;
      int listwise_price_len =
          listwise_generated_variant_price_list ? (*listwise_generated_variant_price_list).size() : 0;

      double seq_gpm_score = 0.0;
      int nums_live = 0;
      int seq_index = 0;

      if (listwise_generated_variant_list && listwise_item_type_len == listwise_ctr_len &&
          listwise_item_type_len == listwise_cvr_len && listwise_item_type_len == listwise_price_len &&
          listwise_item_type_len == listwise_live_pos_w_len &&
          listwise_item_type_len == listwise_good_pos_w_len) {
        for (int j = 0; j < listwise_item_type_len; ++j) {
          auto cur_item_type = (*listwise_generated_variant_item_type_list)[j];
          double ctr_value = (*listwise_generated_variant_ctr_list)[j];
          double cvr_value = (*listwise_generated_variant_cvr_list)[j];
          double price_value = (*listwise_generated_variant_price_list)[j];
          double cur_gpm_score = 0.0;
          double cur_goods_gpm_score = 0.0;
          double cur_live_pos_w = 1.0;
          double cur_good_pos_w = 1.0;
          double pos_tem = 1.0;
          if (absl::SimpleAtod(live_position_weight_vec[j], &pos_tem)) {
            cur_live_pos_w = pos_tem;
          }
          if (absl::SimpleAtod(good_position_weight_vec[j], &pos_tem)) {
            cur_good_pos_w = pos_tem;
          }
          seq_index += 1;

          if (cur_item_type == 1) {
            cur_gpm_score = live_gpm_alpha * std::pow(ctr_value, live_ctr_w) *
                            std::pow(cvr_value, live_cvr_w) * std::pow(price_value, live_price_w);
            nums_live += 1;
            if (enable_listwise_control_live_prop) {
              cur_gpm_score = cur_gpm_score * std::min(std::max(0.5, 1.25 - 0.25 * nums_live), 1.0);
            }
            if (enable_listwise_control_live_position) {
              cur_gpm_score = cur_gpm_score * std::min(std::max(0.4, 1.06 - 0.06 * nums_live), 1.0);
            }
            if (enable_listwise_egpm_eval_live_pos_disc) {
              cur_gpm_score = cur_gpm_score * cur_live_pos_w;
            }
            seq_gpm_score += cur_gpm_score;
          } else {
            cur_goods_gpm_score = goods_gpm_alpha * std::pow(ctr_value, goods_ctr_w) *
                                  std::pow(cvr_value, goods_cvr_w) * std::pow(price_value, goods_price_w);
            if (enable_listwise_egpm_eval_good_pos_disc) {
              cur_goods_gpm_score = cur_goods_gpm_score * cur_good_pos_w;
            }
            seq_gpm_score += cur_goods_gpm_score;
          }
        }
      }
      listwise_seq_score += seq_gpm_score;
      set_rerank_context_scores(result, listwise_seq_score);
    });

    return true;
  }

  static bool ClkI2iTriggerQuotaReassign(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto trigger_item_id_attr = context.GetIntItemAttr("trigger_item_id");
    auto bh_goods_trigger_reassign_range =
        context.GetIntListCommonAttr("bh_goods_trigger_reassign_range").value_or(absl::Span<const int64>());
    auto bh_goods_trigger_reassign_quota =
        context.GetIntListCommonAttr("bh_goods_trigger_reassign_quota").value_or(absl::Span<const int64>());
    auto offline_clk_item_list =
        context.GetIntListCommonAttr("offline_clk_item_list").value_or(absl::Span<const int64>());
    auto set_i2i_final_filter_flag = context.SetIntItemAttr("i2i_final_filter_flag");
    if (bh_goods_trigger_reassign_range.size() == 0 ||
        bh_goods_trigger_reassign_range.size() != bh_goods_trigger_reassign_quota.size()) {
      LOG(WARNING) << "MerchantShoppingMallLightFunctionSet ClkI2iTriggerQuotaReassign warning, "
                      "bh_goods_trigger_reassign_range empty or bh_goods_trigger_reassign_range != "
                      "bh_goods_trigger_reassign_quota";
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        // 没有设置就先都不过滤
        set_i2i_final_filter_flag(result, 0);
      });
      return true;
    }

    folly::F14FastMap<int64, int> trigger_quota_dict;
    folly::F14FastMap<int64, int> trigger_item_cnt_dict;
    for (int j = 0; j < offline_clk_item_list.size(); ++j) {
      trigger_item_cnt_dict[offline_clk_item_list[j]] = 0;
      trigger_quota_dict[offline_clk_item_list[j]] =
          bh_goods_trigger_reassign_quota[bh_goods_trigger_reassign_quota.size() - 1];
      for (int r = 0; r < bh_goods_trigger_reassign_range.size(); ++r) {
        if (j <= bh_goods_trigger_reassign_range[r]) {
          trigger_quota_dict[offline_clk_item_list[j]] = bh_goods_trigger_reassign_quota[r];
          break;
        }
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto trigger_item_id = trigger_item_id_attr(result).value_or(0);
      if (0 == trigger_quota_dict.count(trigger_item_id)) {
        // 没有找到 trigger 先暂时不过滤
        set_i2i_final_filter_flag(result, 0);
      } else {
        if (trigger_item_cnt_dict.at(trigger_item_id) < trigger_quota_dict.at(trigger_item_id)) {
          ++trigger_item_cnt_dict[trigger_item_id];
          set_i2i_final_filter_flag(result, 0);
        } else {
          // 超出范围，直接过滤
          set_i2i_final_filter_flag(result, 1);
        }
      }
    });

    return true;
  }

  static bool ClickUserHistoryBucketize(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    request_ts /= 1000;
    auto click_ts_list = context.GetIntListCommonAttr("click_user_ts_list");
    auto click_price_list = context.GetIntListCommonAttr("click_user_price_list");
    auto click_cnt_list = context.GetIntListCommonAttr("click_user_cnt_list");
    int ts_list_size = click_ts_list ? (*click_ts_list).size() : 0;
    int price_list_size = click_price_list ? (*click_price_list).size() : 0;
    int cnt_list_size = click_cnt_list ? (*click_cnt_list).size() : 0;
    if (click_ts_list && click_price_list && click_cnt_list && ts_list_size == price_list_size &&
        ts_list_size == cnt_list_size) {
      std::vector<int64> click_user_lag_list;
      std::vector<int64> click_user_price_bucket_list;
      std::vector<int64> click_user_cnt_bucket_list;
      if (ts_list_size > 0) {
        click_user_lag_list.reserve(ts_list_size);
        click_user_price_bucket_list.reserve(ts_list_size);
        click_user_cnt_bucket_list.reserve(ts_list_size);
      }
      for (int i = 0; i < ts_list_size; ++i) {
        int64 ts = (*click_ts_list)[i];
        int64 lag = -1;
        if (ts > 0 && request_ts > 0) {
          lag = request_ts - ts;
          if (lag < 60) {
            lag = 1;
          } else if (lag < 300) {
            lag = 2;
          } else if (lag < 600) {
            lag = 3;
          } else if (lag < 900) {
            lag = 4;
          } else if (lag < 1800) {
            lag = 5;
          } else if (lag < 3600) {
            lag = 6;
          } else if (lag < 86400) {
            lag = 6 + lag / 3600;
          } else {
            lag = 29 + lag / 86400;
          }
        }
        click_user_lag_list.emplace_back(lag);
        int64 price = (*click_price_list)[i];
        int64 price_bucket = -1;
        if (price > 0) {
          price_bucket = price / 100;
          if (price_bucket > 10000) {
            price_bucket = 10000;
          }
        }
        click_user_price_bucket_list.emplace_back(price_bucket);
        int64 cnt = (*click_cnt_list)[i];
        if (cnt > 100) {
          cnt = 100;
        }
        click_user_cnt_bucket_list.emplace_back(cnt);
      }
      context.SetIntListCommonAttr("click_user_lag_list", std::move(click_user_lag_list));
      context.SetIntListCommonAttr("click_user_price_bucket_list", std::move(click_user_price_bucket_list));
      context.SetIntListCommonAttr("click_user_cnt_bucket_list", std::move(click_user_cnt_bucket_list));
    }
    return true;
  }

  static bool OrderUserHistoryBucketize(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto request_ts = context.GetIntCommonAttr("request_ts").value_or(0);
    request_ts /= 1000;
    auto order_ts_list = context.GetIntListCommonAttr("order_user_ts_list");
    auto order_price_list = context.GetIntListCommonAttr("order_user_price_list");
    auto order_cnt_list = context.GetIntListCommonAttr("order_user_cnt_list");
    int ts_list_size = order_ts_list ? (*order_ts_list).size() : 0;
    int price_list_size = order_price_list ? (*order_price_list).size() : 0;
    int cnt_list_size = order_cnt_list ? (*order_cnt_list).size() : 0;
    if (order_ts_list && order_price_list && order_cnt_list && ts_list_size == price_list_size &&
        ts_list_size == cnt_list_size) {
      std::vector<int64> order_user_lag_list;
      std::vector<int64> order_user_price_bucket_list;
      std::vector<int64> order_user_cnt_bucket_list;
      if (ts_list_size > 0) {
        order_user_lag_list.reserve(ts_list_size);
        order_user_price_bucket_list.reserve(ts_list_size);
        order_user_cnt_bucket_list.reserve(ts_list_size);
      }
      for (int i = 0; i < ts_list_size; ++i) {
        int64 ts = (*order_ts_list)[i];
        int64 lag = -1;
        if (ts > 0 && request_ts > 0) {
          lag = request_ts - ts;
          lag /= 86400;
        }
        order_user_lag_list.emplace_back(lag);
        int64 price = (*order_price_list)[i];
        int64 price_bucket = -1;
        if (price > 0) {
          price_bucket = price / 100;
          if (price_bucket > 10000) {
            price_bucket = 10000;
          }
        }
        order_user_price_bucket_list.emplace_back(price_bucket);
        int64 cnt = (*order_cnt_list)[i];
        if (cnt > 100) {
          cnt = 100;
        }
        order_user_cnt_bucket_list.emplace_back(cnt);
      }
      context.SetIntListCommonAttr("order_user_lag_list", std::move(order_user_lag_list));
      context.SetIntListCommonAttr("order_user_price_bucket_list", std::move(order_user_price_bucket_list));
      context.SetIntListCommonAttr("order_user_cnt_bucket_list", std::move(order_user_cnt_bucket_list));
    }
    return true;
  }

  static bool ItemPriceBucketize(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto origin_price = context.GetIntItemAttr("origin_price");
    auto price_bucket = context.SetIntItemAttr("price_bucket");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 bucket = -1;
      auto price = origin_price(result).value_or(0);
      if (price > 0) {
        bucket = price / 100;
        if (bucket > 10000) {
          bucket = 10000;
        }
      }
      price_bucket(result, bucket);
    });
    return true;
  }

  static bool ItemsWithCidMatch(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto user_behav_item_list = context.GetIntListCommonAttr("user_behav_item_list");
    auto user_behav_cid_list = context.GetIntListCommonAttr("user_behav_cid_list");
    auto benchmark_item_list = context.GetIntListCommonAttr("benchmark_item_list");
    auto benchmark_cid_list = context.GetIntListCommonAttr("benchmark_cid_list");
    int len_item = user_behav_item_list ? (*user_behav_item_list).size() : 0;
    int len_cid = user_behav_cid_list ? (*user_behav_cid_list).size() : 0;
    if (benchmark_item_list && benchmark_cid_list && user_behav_item_list && user_behav_cid_list &&
        len_item == len_cid) {
      folly::F14FastSet<int64> item_set((*benchmark_item_list).begin(), (*benchmark_item_list).end());
      folly::F14FastSet<int64> cid_set((*benchmark_cid_list).begin(), (*benchmark_cid_list).end());
      std::vector<int64> items_with_cid_match;
      if (len_item > 0) {
        items_with_cid_match.reserve(len_item);
      }
      for (int i = 0; i < len_item; ++i) {
        int64 item = (*user_behav_item_list)[i];
        int64 cid = (*user_behav_cid_list)[i];
        if (cid > 0 && (cid_set.find(cid) != cid_set.end()) && (item_set.find(item) == item_set.end())) {
          items_with_cid_match.emplace_back(item);
        }
      }
      items_with_cid_match.shrink_to_fit();
      context.SetIntListCommonAttr("items_with_cid_match", std::move(items_with_cid_match));
    }
    return true;
  }

  static bool ContrastU2U2IRecallList(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto contrast_u2u2i_retr_num = context.GetIntCommonAttr("contrast_u2u2i_retr_num");
    auto contrast_u2u2i_value_list = context.GetStringListCommonAttr("contrast_u2u2i_value_list");
    int item_id_list_size = contrast_u2u2i_value_list ? (*contrast_u2u2i_value_list).size() : 0;
    std::vector<int64> item_result_list;
    folly::F14FastSet<int64> mem_set;
    int retr_num_cnt = 0;
    if (item_id_list_size > 0 && contrast_u2u2i_value_list) {
      for (int index = 0; index < item_id_list_size; index++) {
        std::vector<std::string> list_tmp = absl::StrSplit((*contrast_u2u2i_value_list)[index], ",");
        for (int i = 0; i < list_tmp.size(); i++) {
          int64 pid = 0;
          if (!absl::SimpleAtoi(list_tmp[i], &pid)) {
            continue;
          }
          if (mem_set.find(pid) == mem_set.end() && retr_num_cnt < contrast_u2u2i_retr_num) {
            retr_num_cnt++;
            item_result_list.emplace_back(pid);
            mem_set.insert(pid);
          }
        }
      }
    }
    context.SetIntListCommonAttr("u2u2i_result_id_list", std::move(item_result_list));
    return true;
  }

  // parse_user_show_but_not_click_step4
  static bool ParseUserShowButNotClick(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto pageshow_item_type = context.GetIntListCommonAttr("pageshow_item_type");
    auto pageshow_author_id = context.GetIntListCommonAttr("pageshow_author_id");
    auto pageshow_item_cate1 = context.GetIntListCommonAttr("pageshow_item_cate1");
    auto pageshow_item_cate2 = context.GetIntListCommonAttr("pageshow_item_cate2");
    auto pageshow_item_cate3 = context.GetIntListCommonAttr("pageshow_item_cate3");
    auto pageshow_time_ms = context.GetIntListCommonAttr("pageshow_time_ms");
    auto bh_live_show_but_not_click_stat_last_n_min =
        context.GetIntCommonAttr("bh_live_show_but_not_click_stat_last_n_min").value_or(0);
    auto REQ_TIME = context.GetIntCommonAttr("_REQ_TIME_").value_or(0);

    auto live_last_click_ts_attr = context.GetIntItemAttr("live_last_click_ts");
    auto aId_attr = context.GetIntItemAttr("aId");
    auto itemCat1_attr = context.GetIntItemAttr("itemCat1");
    auto itemCat2_attr = context.GetIntItemAttr("itemCat2");
    auto itemCat3_attr = context.GetIntItemAttr("itemCat3");

    auto same_author_show_cnt_after_last_click_set =
        context.SetIntItemAttr("same_author_show_cnt_after_last_click");
    auto same_cate1_show_cnt_after_last_click_set =
        context.SetIntItemAttr("same_cate1_show_cnt_after_last_click");
    auto same_cate2_show_cnt_after_last_click_set =
        context.SetIntItemAttr("same_cate2_show_cnt_after_last_click");
    auto same_cate3_show_cnt_after_last_click_set =
        context.SetIntItemAttr("same_cate3_show_cnt_after_last_click");

    int pageshow_item_type_size = pageshow_item_type ? (*pageshow_item_type).size() : 0;
    int pageshow_author_id_size = pageshow_author_id ? (*pageshow_author_id).size() : 0;
    int pageshow_item_cate1_size = pageshow_item_cate1 ? (*pageshow_item_cate1).size() : 0;
    int pageshow_item_cate2_size = pageshow_item_cate2 ? (*pageshow_item_cate2).size() : 0;
    int pageshow_item_cate3_size = pageshow_item_cate3 ? (*pageshow_item_cate3).size() : 0;
    int pageshow_time_ms_size = pageshow_time_ms ? (*pageshow_time_ms).size() : 0;

    int64 filter_time = REQ_TIME / 1000 - bh_live_show_but_not_click_stat_last_n_min * 60;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 index = 1;
      auto live_last_click_ts = live_last_click_ts_attr(result).value_or(-1);
      auto aId = aId_attr(result).value_or(0);
      auto itemCat1 = itemCat1_attr(result).value_or(0);
      auto itemCat2 = itemCat2_attr(result).value_or(0);
      auto itemCat3 = itemCat3_attr(result).value_or(0);

      int64 same_author_show_cnt_after_last_click = 0;
      int64 same_cate1_show_cnt_after_last_click = 0;
      int64 same_cate2_show_cnt_after_last_click = 0;
      int64 same_cate3_show_cnt_after_last_click = 0;
      if (pageshow_item_type_size > 0) {
        for (int value : (*pageshow_item_type)) {
          // 只用直播的数据
          if (pageshow_time_ms_size && value == 1 && (*pageshow_time_ms)[index] / 1000 > live_last_click_ts &&
              (*pageshow_time_ms)[index] / 1000 > filter_time) {
            if (pageshow_author_id_size && (*pageshow_author_id)[index] == aId) {
              same_author_show_cnt_after_last_click = same_author_show_cnt_after_last_click + 1;
            }
            if (pageshow_item_cate1_size && (*pageshow_item_cate1)[index] == itemCat1) {
              same_cate1_show_cnt_after_last_click = same_cate1_show_cnt_after_last_click + 1;
            }
            if (pageshow_item_cate2_size && (*pageshow_item_cate2)[index] == itemCat2) {
              same_cate2_show_cnt_after_last_click = same_cate2_show_cnt_after_last_click + 1;
            }
            if (pageshow_item_cate3_size && (*pageshow_item_cate3)[index] == itemCat3) {
              same_cate3_show_cnt_after_last_click = same_cate3_show_cnt_after_last_click + 1;
            }
          }
          index++;
        }
      }
      same_author_show_cnt_after_last_click_set(result, same_author_show_cnt_after_last_click);
      same_cate1_show_cnt_after_last_click_set(result, same_cate1_show_cnt_after_last_click);
      same_cate2_show_cnt_after_last_click_set(result, same_cate2_show_cnt_after_last_click);
      same_cate3_show_cnt_after_last_click_set(result, same_cate3_show_cnt_after_last_click);
    });
    return true;
  }

  static bool RoughRankCalLiveItemCat(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    std::map<int64, int64> stat_map;
    auto cal_most_cat = [&](absl::optional<absl::Span<const int64>> item_cate_vec) {
      stat_map.clear();
      int64 cate_id = 0;
      if (!item_cate_vec) {
        return cate_id;
      }
      for (size_t i = 0; i < item_cate_vec->size(); ++i) {
        // int64 cate_str = std::string((*item_cate_vec)[i]);
        int64 cate_str = (*item_cate_vec)[i];
        if (0 == stat_map.count(cate_str)) {
          stat_map[cate_str] = 1;
        } else {
          stat_map[cate_str] += 1;
        }
      }
      int64 cate_num = 0;
      for (auto m_it : stat_map) {
        if (m_it.second > cate_num) {
          cate_id = m_it.first;
          cate_num = m_it.second;
        }
      }
      return cate_id;
    };

    auto bh_live_item_cat_option = context.GetIntCommonAttr("bh_live_item_cat_option").value_or(0);
    // input item attr
    auto cart_item_cate1id_list_attr = context.GetIntListItemAttr("sCartItemCate1IdList");
    auto cart_item_cate2id_list_attr = context.GetIntListItemAttr("sCartItemCate2IdList");
    auto cart_item_cate3id_list_attr = context.GetIntListItemAttr("sCartItemCate3IdList");
    auto industry_attr = context.GetStringItemAttr("aIndustry");
    // output item attr
    auto cart_first_item_cate1_id_attr = context.SetIntItemAttr("sCartFirstItemCate1Id");
    auto cart_first_item_cate2_id_attr = context.SetIntItemAttr("sCartFirstItemCate2Id");
    auto cart_first_item_cate3_id_attr = context.SetIntItemAttr("sCartFirstItemCate3Id");
    auto cart_most_item_cate1_id_attr = context.SetIntItemAttr("sCartMostItemCate1Id");
    auto cart_most_item_cate2_id_attr = context.SetIntItemAttr("sCartMostItemCate2Id");
    auto cart_most_item_cate3_id_attr = context.SetIntItemAttr("sCartMostItemCate3Id");
    auto live_item_cate_attr = context.SetIntItemAttr("aLiveItemCat");
    auto item_cate1_attr = context.SetIntItemAttr("itemCat1");
    auto item_cate2_attr = context.SetIntItemAttr("itemCat2");
    auto item_cate3_attr = context.SetIntItemAttr("itemCat3");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cart_item_cate1id_list = cart_item_cate1id_list_attr(result);
      auto cart_item_cate2id_list = cart_item_cate2id_list_attr(result);
      auto cart_item_cate3id_list = cart_item_cate3id_list_attr(result);
      auto cart_most_item_cate1_id = cal_most_cat(cart_item_cate1id_list);
      auto cart_most_item_cate2_id = cal_most_cat(cart_item_cate2id_list);
      auto cart_most_item_cate3_id = cal_most_cat(cart_item_cate3id_list);
      auto cart_first_item_cate1_id =
          (cart_item_cate1id_list && cart_item_cate1id_list->size()) > 0 ? (*cart_item_cate1id_list)[0] : 0;
      auto cart_first_item_cate2_id =
          (cart_item_cate2id_list && cart_item_cate2id_list->size()) > 0 ? (*cart_item_cate2id_list)[0] : 0;
      auto cart_first_item_cate3_id =
          (cart_item_cate3id_list && cart_item_cate3id_list->size()) > 0 ? (*cart_item_cate3id_list)[0] : 0;

      int64 live_item_cate;
      if (bh_live_item_cat_option == 1) {
        live_item_cate = cart_first_item_cate1_id;
      } else if (bh_live_item_cat_option == 2) {
        live_item_cate = cart_first_item_cate2_id;
      } else if (bh_live_item_cat_option == 3) {
        live_item_cate = cart_first_item_cate3_id;
      } else if (bh_live_item_cat_option == 4) {
        live_item_cate = cart_most_item_cate1_id;
      } else if (bh_live_item_cat_option == 5) {
        live_item_cate = cart_most_item_cate2_id;
      } else if (bh_live_item_cat_option == 6) {
        live_item_cate = cart_most_item_cate3_id;
      } else {
        std::string industry = std::string(industry_attr(result).value_or("other"));
        if (industry == "UnKnow" || industry == "未知" || industry == "-") {
          industry = "other";
        }
        if (industry == "0" || industry == "null") {
          industry = "other";
        }
        // live_item_cate = industry;
        // industry 是 string，但是 live_item_cate 应该是 int 按照 ab 参数应该走不到这里，所以取 0 默认值
        live_item_cate = 0;
      }
      cart_first_item_cate1_id_attr(result, cart_first_item_cate1_id);
      cart_first_item_cate2_id_attr(result, cart_first_item_cate2_id);
      cart_first_item_cate3_id_attr(result, cart_first_item_cate3_id);
      cart_most_item_cate1_id_attr(result, cart_most_item_cate1_id);
      cart_most_item_cate2_id_attr(result, cart_most_item_cate2_id);
      cart_most_item_cate3_id_attr(result, cart_most_item_cate3_id);
      live_item_cate_attr(result, live_item_cate);
      item_cate1_attr(result, cart_most_item_cate1_id);
      item_cate2_attr(result, cart_most_item_cate2_id);
      item_cate3_attr(result, cart_most_item_cate3_id);
    });

    return true;
  }

  static bool ShopScorefilter(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto sSellerShopScoreAll_Item_attr = context.GetStringItemAttr("sSellerShopScoreAll");
    auto sSellerShopScoreValid_Item_attr = context.SetDoubleItemAttr("sSellerShopScoreValid");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto sSellerShopScoreAll = sSellerShopScoreAll_Item_attr(result).value_or("");
      double sSellerShopScoreValid = 0.0;
      // std::string sSellerShopScoreValid = "0.0";
      std::vector<absl::string_view> shop_score_list;
      if (!sSellerShopScoreAll.empty()) {
        shop_score_list = absl::StrSplit(sSellerShopScoreAll, ';');
        for (const auto &v : shop_score_list) {
          if (v.size() > 0) {
            std::vector<std::string> cur_shop_score_list = absl::StrSplit(v, ':');
            if (cur_shop_score_list.size() == 2) {
              if (cur_shop_score_list[0] == "online") {
                double result = 0.0;
                if (absl::SimpleAtod(cur_shop_score_list[1], &result)) {
                  sSellerShopScoreValid = result;
                }
                // sSellerShopScoreValid = absl::SimpleAtod(cur_shop_score_list[1]);
                // sSellerShopScoreValid = cur_shop_score_list[1];
                break;
              }
            }
          }
        }
      }
      sSellerShopScoreValid_Item_attr(result, sSellerShopScoreValid);
    });
    return true;
  }

  static bool GetUserLiveShowCalLiveItemCat(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    std::map<int64, int64> stat_map;
    auto cal_most_cat = [&](absl::optional<absl::Span<const int64>> item_cate_vec) {
      stat_map.clear();
      int64 cate_id = 0;
      if (!item_cate_vec) {
        return cate_id;
      }
      for (size_t i = 0; i < item_cate_vec->size(); ++i) {
        int64 cate_str = (*item_cate_vec)[i];
        if (0 == stat_map.count(cate_str)) {
          stat_map[cate_str] = 1;
        } else {
          stat_map[cate_str] += 1;
        }
      }
      int64 cate_num = 0;
      for (auto m_it : stat_map) {
        if (m_it.second > cate_num) {
          cate_id = m_it.first;
          cate_num = m_it.second;
        }
      }
      return cate_id;
    };

    // input item attr
    auto cart_item_cate1id_list_attr = context.GetIntListItemAttr("sCartItemCate1IdList");
    auto cart_item_cate2id_list_attr = context.GetIntListItemAttr("sCartItemCate2IdList");
    auto cart_item_cate3id_list_attr = context.GetIntListItemAttr("sCartItemCate3IdList");
    // output item attr
    auto item_cate1_attr = context.SetIntItemAttr("itemCat1");
    auto item_cate2_attr = context.SetIntItemAttr("itemCat2");
    auto item_cate3_attr = context.SetIntItemAttr("itemCat3");
    for (auto it = begin; it != end; ++it) {
      const CommonRecoResult &result = *it;
      auto cart_item_cate1id_list = cart_item_cate1id_list_attr(result);
      auto cart_item_cate2id_list = cart_item_cate2id_list_attr(result);
      auto cart_item_cate3id_list = cart_item_cate3id_list_attr(result);
      auto cart_most_item_cate1_id = cal_most_cat(cart_item_cate1id_list);
      auto cart_most_item_cate2_id = cal_most_cat(cart_item_cate2id_list);
      auto cart_most_item_cate3_id = cal_most_cat(cart_item_cate3id_list);

      item_cate1_attr(result, cart_most_item_cate1_id);
      item_cate2_attr(result, cart_most_item_cate2_id);
      item_cate3_attr(result, cart_most_item_cate3_id);
    }

    return true;
  }

  static bool GetAddressMatchflag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto excludeProvinceCodeList_Item_attr = context.GetIntListItemAttr("excludeProvinceCodeList");
    auto excludeProvinceSizeList_Item_attr = context.GetIntListItemAttr("excludeProvinceSizeList");
    auto user_delivery_address_city = context.GetIntListCommonAttr("user_delivery_address_city");
    auto user_delivery_address_province = context.GetIntListCommonAttr("user_delivery_address_province");
    auto user_delivery_address_district = context.GetIntListCommonAttr("user_delivery_address_district");
    int user_delivery_address_city_size =
        user_delivery_address_city ? (*user_delivery_address_city).size() : 0;
    int user_delivery_address_province_size =
        user_delivery_address_province ? (*user_delivery_address_province).size() : 0;
    int user_delivery_address_district_size =
        user_delivery_address_district ? (*user_delivery_address_district).size() : 0;
    auto buyer_home_address_len = context.GetIntCommonAttr("buyer_home_address_len").value_or(5);
    auto buyer_home_address_min = context.GetIntCommonAttr("buyer_home_address_min").value_or(0);
    auto is_address_discount_item_attr = context.SetIntItemAttr("is_address_discount");
    auto address_mismatch_goods_count_item_attr = context.SetIntItemAttr("address_mismatch_goods_count");
    // 优化方法，原来是数组，现在改成集合查找效率更好
    folly::F14FastSet<int64, absl::Hash<int64>> user_default_address_list;
    if (user_delivery_address_province_size > 0 && user_delivery_address_province) {
      user_default_address_list.insert((*user_delivery_address_province)[0]);
    }
    if (user_delivery_address_city_size > 0 && user_delivery_address_city) {
      user_default_address_list.insert((*user_delivery_address_city)[0]);
    }
    if (user_delivery_address_district_size > 0 && user_delivery_address_district) {
      user_default_address_list.insert((*user_delivery_address_district)[0]);
    }
    // 对于每一个 item 属性都要使用 result 获取，commonattr 在外部使用就可以索引到数据
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto excludeProvinceCodeList = excludeProvinceCodeList_Item_attr(result).value_or(std::vector<int64>());
      auto excludeProvinceSizeList = excludeProvinceSizeList_Item_attr(result).value_or(std::vector<int64>());
      int64 is_address_discount = 0;
      int64 address_mismatch_goods_count = 0;
      if (excludeProvinceCodeList.size() == 0 || user_default_address_list.size() == 0) {
        is_address_discount_item_attr(result, is_address_discount);
        address_mismatch_goods_count_item_attr(result, address_mismatch_goods_count);
        return;
      }
      int64 index = 0;
      int64 temp_len = 0;
      int64 index_end = 0;
      // int64 is_break = 0;
      for (int h = 0; h < excludeProvinceSizeList.size(); h++) {
        if ((h + 1) > buyer_home_address_len) {
          break;
        }
        // is_break = 0;
        temp_len = excludeProvinceSizeList[h];
        index_end = index + temp_len;
        if (temp_len > 0) {
          // 对下面的代码使用哈希表优化
          for (int64 i = index; i < index_end; i++) {
            if (user_default_address_list.count(excludeProvinceCodeList[i]) > 0) {
              address_mismatch_goods_count = address_mismatch_goods_count + 1;
              // is_break = 1;
              break;
            }
          }
        }
        index = index + temp_len;
      }
      if (address_mismatch_goods_count > buyer_home_address_min) {
        is_address_discount = 1;
      }
      is_address_discount_item_attr(result, is_address_discount);
      address_mismatch_goods_count_item_attr(result, address_mismatch_goods_count);
      return;
    });
    return true;
  }

  static bool GenCtrPromoteFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto ctr_promote_id_list = context.GetIntListCommonAttr("ctr_promote_id_list");
    auto i_selection_group_id_list_attr = context.GetIntListItemAttr("iSelectionGroupIdList");
    auto i_last7d_order_cnt_attr = context.GetIntItemAttr("iLast7dSalesOrderCnt");
    auto ctr_promote_order_thres =
        context.GetIntCommonAttr("ctr_promote_last_7d_order_cnt_thres").value_or(0);
    auto ctr_promote_flag_attr = context.SetIntItemAttr("ctr_promote_flag");

    folly::F14FastSet<int64> ctr_promote_id_set;
    if (ctr_promote_id_list) {
      for (auto ctr_promote_id : *ctr_promote_id_list) {
        ctr_promote_id_set.insert(ctr_promote_id);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto i_selection_group_id_list = i_selection_group_id_list_attr(result);
      auto i_last7d_order_cnt = i_last7d_order_cnt_attr(result).value_or(0);
      int ctr_promote_flag = 0;
      if (i_last7d_order_cnt > ctr_promote_order_thres && i_selection_group_id_list) {
        for (auto group_id : *i_selection_group_id_list) {
          if (ctr_promote_id_set.find(group_id) != ctr_promote_id_set.end()) {
            ctr_promote_flag = 1;
            break;
          }
        }
      }
      ctr_promote_flag_attr(result, ctr_promote_flag);
    });
    return true;
  }

  static bool GenPricePowerPromoteFlag(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto price_power_leaf_cate_avail_id_list =
        context.GetIntListCommonAttr("price_power_leaf_cate_avail_id_list");
    auto price_power_confident_discount =
        context.GetDoubleCommonAttr("price_power_confident_discount").value_or(0.0);
    auto price_power_confident_amount = context.GetIntCommonAttr("price_power_confident_amount").value_or(0);
    auto i_compare_sku_price_list_attr = context.GetIntListItemAttr("iGoodsCompareSkuPriceList");
    auto i_compare_competitor_price_list_attr =
        context.GetIntListItemAttr("iGoodsCompareCompetitorPriceList");
    auto leaf_cate_id_attr = context.GetIntItemAttr("leafCateId");
    auto price_power_promote_flag_attr = context.SetIntItemAttr("price_power_promote_flag");

    folly::F14FastSet<int64> price_power_leaf_cate_avail_id_set;
    if (price_power_leaf_cate_avail_id_list) {
      for (auto price_power_leaf_cate_avail_id : *price_power_leaf_cate_avail_id_list) {
        price_power_leaf_cate_avail_id_set.insert(price_power_leaf_cate_avail_id);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto leaf_cate_id = leaf_cate_id_attr(result).value_or(0);
      int price_power_promote_flag = 0;
      if (price_power_leaf_cate_avail_id_set.find(leaf_cate_id) != price_power_leaf_cate_avail_id_set.end()) {
        auto i_compare_sku_price_list = i_compare_sku_price_list_attr(result);
        auto i_compare_competitor_price_list = i_compare_competitor_price_list_attr(result);
        int sku_price_list_len = i_compare_sku_price_list ? (*i_compare_sku_price_list).size() : 0;
        int competitor_price_list_len =
            i_compare_competitor_price_list ? (*i_compare_competitor_price_list).size() : 0;

        if (sku_price_list_len > 0 && competitor_price_list_len > 0 &&
            sku_price_list_len == competitor_price_list_len) {
          price_power_promote_flag = 1;
          for (int i = 0; i < sku_price_list_len; ++i) {
            if ((*i_compare_sku_price_list)[i] >=
                    (*i_compare_competitor_price_list)[i] * (1 + price_power_confident_discount) &&
                (*i_compare_sku_price_list)[i] >=
                    (*i_compare_competitor_price_list)[i] + price_power_confident_amount) {
              price_power_promote_flag = -1;
              break;
            }
            if ((*i_compare_sku_price_list)[i] >=
                    (*i_compare_competitor_price_list)[i] * (1 - price_power_confident_discount) ||
                (*i_compare_sku_price_list)[i] >=
                    (*i_compare_competitor_price_list)[i] - price_power_confident_amount) {
              price_power_promote_flag = 0;
            }
          }
        }
      }
      price_power_promote_flag_attr(result, price_power_promote_flag);
    });
    return true;
  }

  static bool UnpackSpuEmbdResponse(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto resp = context.GetPtrCommonAttr<std::unordered_map<uint64_t, std::string>>("spu_embd_resp");
    auto item_id_list = context.GetIntListCommonAttr("item_id_list");
    int list_len = item_id_list ? (*item_id_list).size() : 0;
    if (item_id_list) {
      std::vector<int64> spu_id_list(list_len, -1);
      if (resp && !(resp->empty())) {
        for (int i = 0; i < list_len; ++i) {
          auto iter = resp->find((*item_id_list)[i]);
          if (iter != resp->end()) {
            const auto &data = iter->second;
            if (data.size() == 8) {
              memcpy(&(spu_id_list[i]), data.data(), 8);
            }
          }
        }
      }
      context.SetIntListCommonAttr("spu_id_list", std::move(spu_id_list));
    }
    return true;
  }

  static bool UnpackListwiseIntListItemAttr(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_pack_list = context.GetIntListItemAttr("source_pack_list");
    std::vector<std::function<void(const CommonRecoResult &, const int64)>> func_vec;
    func_vec.reserve(10);
    func_vec.emplace_back(context.SetIntItemAttr("target_0"));
    func_vec.emplace_back(context.SetIntItemAttr("target_1"));
    func_vec.emplace_back(context.SetIntItemAttr("target_2"));
    func_vec.emplace_back(context.SetIntItemAttr("target_3"));
    func_vec.emplace_back(context.SetIntItemAttr("target_4"));
    func_vec.emplace_back(context.SetIntItemAttr("target_5"));
    func_vec.emplace_back(context.SetIntItemAttr("target_6"));
    func_vec.emplace_back(context.SetIntItemAttr("target_7"));
    func_vec.emplace_back(context.SetIntItemAttr("target_8"));
    func_vec.emplace_back(context.SetIntItemAttr("target_9"));
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto list_attr = source_pack_list(result);
      if (list_attr) {
        int64 len = (*list_attr).size();
        if (len > 10) {
          len = 10;
        }
        for (int64 i = 0; i < len; ++i) {
          func_vec[i](result, (*list_attr)[i]);
        }
      }
    });
    return true;
  }

  static bool UnpackListwiseDoubleListItemAttr(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_pack_list = context.GetDoubleListItemAttr("source_pack_list");
    std::vector<std::function<void(const CommonRecoResult &, const double)>> func_vec;
    func_vec.reserve(10);
    func_vec.emplace_back(context.SetDoubleItemAttr("target_0"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_1"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_2"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_3"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_4"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_5"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_6"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_7"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_8"));
    func_vec.emplace_back(context.SetDoubleItemAttr("target_9"));
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto list_attr = source_pack_list(result);
      if (list_attr) {
        int64 len = (*list_attr).size();
        if (len > 10) {
          len = 10;
        }
        for (int64 i = 0; i < len; ++i) {
          func_vec[i](result, (*list_attr)[i]);
        }
      }
    });
    return true;
  }

  static bool UnpackListwiseIntListCommonAttr(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto extra_plus = context.GetIntCommonAttr("extra_plus").value_or(0);
    auto source_pack_list = context.GetIntListCommonAttr("source_pack_list");
    int64 len = source_pack_list ? (*source_pack_list).size() : 0;
    if (len > 0) {
      if (len > 10) {
        len = 10;
      }
      std::vector<std::function<void(const CommonRecoResult &, const int64)>> func_vec;
      func_vec.reserve(10);
      func_vec.emplace_back(context.SetIntItemAttr("target_0"));
      func_vec.emplace_back(context.SetIntItemAttr("target_1"));
      func_vec.emplace_back(context.SetIntItemAttr("target_2"));
      func_vec.emplace_back(context.SetIntItemAttr("target_3"));
      func_vec.emplace_back(context.SetIntItemAttr("target_4"));
      func_vec.emplace_back(context.SetIntItemAttr("target_5"));
      func_vec.emplace_back(context.SetIntItemAttr("target_6"));
      func_vec.emplace_back(context.SetIntItemAttr("target_7"));
      func_vec.emplace_back(context.SetIntItemAttr("target_8"));
      func_vec.emplace_back(context.SetIntItemAttr("target_9"));
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        for (int64 i = 0; i < len; ++i) {
          func_vec[i](result, (*source_pack_list)[i] + extra_plus);
        }
      });
    }
    return true;
  }

  static bool UnpackListwiseDoubleListCommonAttr(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_pack_list = context.GetDoubleListCommonAttr("source_pack_list");
    int64 len = source_pack_list ? (*source_pack_list).size() : 0;
    if (len > 0) {
      if (len > 10) {
        len = 10;
      }
      std::vector<std::function<void(const CommonRecoResult &, const double)>> func_vec;
      func_vec.reserve(10);
      func_vec.emplace_back(context.SetDoubleItemAttr("target_0"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_1"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_2"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_3"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_4"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_5"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_6"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_7"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_8"));
      func_vec.emplace_back(context.SetDoubleItemAttr("target_9"));
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        for (int64 i = 0; i < len; ++i) {
          func_vec[i](result, (*source_pack_list)[i]);
        }
      });
    }
    return true;
  }

  static bool UnpackListwiseIntListCommonAttrWithCnt(const CommonRecoLightFunctionContext &context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_pack_list = context.GetIntListCommonAttr("source_pack_list");
    auto source_pack_cnt_list = context.GetIntListCommonAttr("source_pack_cnt_list");
    int64 data_len = source_pack_list ? (*source_pack_list).size() : 0;
    int64 len = source_pack_cnt_list ? (*source_pack_cnt_list).size() : 0;
    int64 total_len = 0;
    if (source_pack_cnt_list) {
      for (auto cnt : *source_pack_cnt_list) {
        total_len += cnt;
      }
    }
    if (source_pack_list && len > 0 && total_len == data_len) {
      if (len > 10) {
        len = 10;
      }
      std::vector<std::function<void(const CommonRecoResult &, std::vector<int64> &)>> func_vec;
      func_vec.reserve(10);
      func_vec.emplace_back(context.SetIntListItemAttr("target_0"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_1"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_2"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_3"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_4"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_5"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_6"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_7"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_8"));
      func_vec.emplace_back(context.SetIntListItemAttr("target_9"));
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto iter = (*source_pack_list).begin();
        for (int64 i = 0; i < len; ++i) {
          int64 cnt = (*source_pack_cnt_list)[i];
          if (cnt > 0) {
            std::vector<int64> tmp(iter, iter + cnt);
            func_vec[i](result, tmp);
            iter += cnt;
          }
        }
      });
    }
    return true;
  }

  static bool UnpackListwiseDoubleListCommonAttrWithCnt(const CommonRecoLightFunctionContext &context,
                                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_pack_list = context.GetDoubleListCommonAttr("source_pack_list");
    auto source_pack_cnt_list = context.GetIntListCommonAttr("source_pack_cnt_list");
    int64 data_len = source_pack_list ? (*source_pack_list).size() : 0;
    int64 len = source_pack_cnt_list ? (*source_pack_cnt_list).size() : 0;
    int64 total_len = 0;
    if (source_pack_cnt_list) {
      for (auto cnt : *source_pack_cnt_list) {
        total_len += cnt;
      }
    }
    if (source_pack_list && len > 0 && total_len == data_len) {
      if (len > 10) {
        len = 10;
      }
      std::vector<std::function<void(const CommonRecoResult &, std::vector<double> &)>> func_vec;
      func_vec.reserve(10);
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_0"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_1"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_2"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_3"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_4"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_5"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_6"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_7"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_8"));
      func_vec.emplace_back(context.SetDoubleListItemAttr("target_9"));
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto iter = (*source_pack_list).begin();
        for (int64 i = 0; i < len; ++i) {
          int64 cnt = (*source_pack_cnt_list)[i];
          if (cnt > 0) {
            std::vector<double> tmp(iter, iter + cnt);
            func_vec[i](result, tmp);
            iter += cnt;
          }
        }
      });
    }
    return true;
  }

  static bool ItemIntListAttrLen(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto source_list_attr = context.GetIntListItemAttr("source_list_attr");
    auto list_len = context.SetIntItemAttr("list_attr_len");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto list_attr = source_list_attr(result);
      int64 len = list_attr ? (*list_attr).size() : 0;
      list_len(result, len);
    });
    return true;
  }

  static bool ItemDoubleListAttrLen(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto source_list_attr = context.GetDoubleListItemAttr("source_list_attr");
    auto list_len = context.SetIntItemAttr("list_attr_len");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto list_attr = source_list_attr(result);
      int64 len = list_attr ? (*list_attr).size() : 0;
      list_len(result, len);
    });
    return true;
  }

  static bool ItemIntListAttrMaxCountEntity(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto source_list_attr = context.GetIntListItemAttr("source_list_attr");
    auto max_count_entity = context.SetIntItemAttr("max_count_entity");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto list_attr = source_list_attr(result);
      if (list_attr) {
        std::unordered_map<int64, int64> cnt_map;
        int64 max_cnt = 0;
        int64 max_cnt_entity = -1;
        for (const auto entity : (*list_attr)) {
          auto iter = cnt_map.find(entity);
          if (iter == cnt_map.end()) {
            cnt_map.emplace(entity, 1);
            if (1 > max_cnt) {
              max_cnt = 1;
              max_cnt_entity = entity;
            }
          } else {
            iter->second += 1;
            if (iter->second > max_cnt) {
              max_cnt = iter->second;
              max_cnt_entity = entity;
            }
          }
        }
        if (max_cnt > 0) {
          max_count_entity(result, max_cnt_entity);
        }
      }
    });
    return true;
  }
  static bool TransNoNaturaGoodPos(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto replace_id_list = context.GetIntListCommonAttr("replace_list_final");
    auto replace_pos_list = context.GetIntListCommonAttr("replace_pos_list");
    auto super_link_value = context.GetIntItemAttr("replace_item_id_value");
    auto replace_pos = context.GetIntItemAttr("replace_pos");
    auto super_link_insert_pos = context.SetIntItemAttr("replace_insert_pos");
    std::unordered_map<int64, int64> result_dict;
    if (replace_id_list && replace_pos_list) {
      if (replace_id_list->size() == replace_pos_list->size()) {
        for (size_t i = 0; i < replace_id_list->size(); ++i) {
          result_dict[(*replace_id_list)[i]] = (*replace_pos_list)[i];
        }
      }
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto super_link = super_link_value(result).value_or(-1);
      auto nonatural_replace_pos = replace_pos(result).value_or(-1);
      if (super_link != -1 && nonatural_replace_pos != -1) {
        auto iter = result_dict.find(super_link);
        if (iter != result_dict.end()) {
          auto pos = iter->second;
          if (pos > -1 && pos < nonatural_replace_pos) {
            super_link_insert_pos(result, pos);
          }
        }
      }
    });
    return true;
  }

  static bool MixTransCategory(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto cat_map_list = context.GetStringListCommonAttr("cat_map_list");
    std::unordered_map<std::tuple<int64, int64, int64>, std::tuple<int64, int64, int64>> m;
    if (cat_map_list) {
      for (const auto &s : (*cat_map_list)) {
        std::vector<absl::string_view> cids = absl::StrSplit(s, ':');
        if (cids.size() == 2) {
          std::vector<absl::string_view> cid_source = absl::StrSplit(cids[0], '_');
          std::vector<absl::string_view> cid_target = absl::StrSplit(cids[1], '_');
          if (cid_source.size() == 3 && cid_target.size() == 3) {
            int64 c1_s = 0;
            int64 c2_s = 0;
            int64 c3_s = 0;
            int64 c1_t = 0;
            int64 c2_t = 0;
            int64 c3_t = 0;
            if (absl::SimpleAtoi(cid_source[0], &c1_s) && absl::SimpleAtoi(cid_source[1], &c2_s) &&
                absl::SimpleAtoi(cid_source[2], &c3_s) && absl::SimpleAtoi(cid_target[0], &c1_t) &&
                absl::SimpleAtoi(cid_target[1], &c2_t) && absl::SimpleAtoi(cid_target[2], &c3_t)) {
              m.emplace(std::make_tuple(c1_s, c2_s, c3_s), std::make_tuple(c1_t, c2_t, c3_t));
            }
          }
        }
      }
    }
    auto cid1 = context.GetIntItemAttr("cid1");
    auto cid2 = context.GetIntItemAttr("cid2");
    auto cid3 = context.GetIntItemAttr("cid3");
    auto trans_cid1 = context.SetIntItemAttr("trans_cid1");
    auto trans_cid2 = context.SetIntItemAttr("trans_cid2");
    auto trans_cid3 = context.SetIntItemAttr("trans_cid3");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto c1 = cid1(result).value_or(0);
      auto c2 = cid2(result).value_or(0);
      auto c3 = cid3(result).value_or(0);
      if (!(m.empty())) {
        if (c1 > 0 && c2 > 0 && c3 > 0) {
          auto iter = m.find(std::make_tuple(c1, c2, c3));
          if (iter != m.end()) {
            c1 = std::get<0>(iter->second);
            c2 = std::get<1>(iter->second);
            c3 = std::get<2>(iter->second);
          }
        }
      }
      trans_cid1(result, c1);
      trans_cid2(result, c2);
      trans_cid3(result, c3);
    });
    return true;
  }

  static bool MixTransPrevsCategory(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto cat_map_list = context.GetStringListCommonAttr("cat_map_list");
    std::unordered_map<std::tuple<int64, int64, int64>, std::tuple<int64, int64, int64>> m;
    if (cat_map_list) {
      for (const auto &s : (*cat_map_list)) {
        std::vector<absl::string_view> cids = absl::StrSplit(s, ':');
        if (cids.size() == 2) {
          std::vector<absl::string_view> cid_source = absl::StrSplit(cids[0], '_');
          std::vector<absl::string_view> cid_target = absl::StrSplit(cids[1], '_');
          if (cid_source.size() == 3 && cid_target.size() == 3) {
            int64 c1_s = 0;
            int64 c2_s = 0;
            int64 c3_s = 0;
            int64 c1_t = 0;
            int64 c2_t = 0;
            int64 c3_t = 0;
            if (absl::SimpleAtoi(cid_source[0], &c1_s) && absl::SimpleAtoi(cid_source[1], &c2_s) &&
                absl::SimpleAtoi(cid_source[2], &c3_s) && absl::SimpleAtoi(cid_target[0], &c1_t) &&
                absl::SimpleAtoi(cid_target[1], &c2_t) && absl::SimpleAtoi(cid_target[2], &c3_t)) {
              m.emplace(std::make_tuple(c1_s, c2_s, c3_s), std::make_tuple(c1_t, c2_t, c3_t));
            }
          }
        }
      }
    }
    auto prevs_cid1_list = context.GetIntListCommonAttr("prevs_cid1_list");
    auto prevs_cid2_list = context.GetIntListCommonAttr("prevs_cid2_list");
    auto prevs_cid3_list = context.GetIntListCommonAttr("prevs_cid3_list");
    int64 cid1_len = prevs_cid1_list ? (*prevs_cid1_list).size() : 0;
    int64 cid2_len = prevs_cid2_list ? (*prevs_cid2_list).size() : 0;
    int64 cid3_len = prevs_cid3_list ? (*prevs_cid3_list).size() : 0;
    if (cid1_len > 0 &&
        cid1_len == cid2_len &&
        cid1_len == cid3_len) {
      std::vector<int64> prevs_trans_cid1_list;
      std::vector<int64> prevs_trans_cid2_list;
      std::vector<int64> prevs_trans_cid3_list;
      prevs_trans_cid1_list.reserve(cid1_len);
      prevs_trans_cid2_list.reserve(cid1_len);
      prevs_trans_cid3_list.reserve(cid1_len);
      for (int64 i = 0; i < cid1_len; ++i) {
        auto c1 = (*prevs_cid1_list)[i];
        auto c2 = (*prevs_cid2_list)[i];
        auto c3 = (*prevs_cid3_list)[i];
        if (!(m.empty())) {
          if (c1 > 0 && c2 > 0 && c3 > 0) {
            auto iter = m.find(std::make_tuple(c1, c2, c3));
            if (iter != m.end()) {
              c1 = std::get<0>(iter->second);
              c2 = std::get<1>(iter->second);
              c3 = std::get<2>(iter->second);
            }
          }
        }
        prevs_trans_cid1_list.emplace_back(c1);
        prevs_trans_cid2_list.emplace_back(c2);
        prevs_trans_cid3_list.emplace_back(c3);
      }
      context.SetIntListCommonAttr("prevs_trans_cid1_list", std::move(prevs_trans_cid1_list));
      context.SetIntListCommonAttr("prevs_trans_cid2_list", std::move(prevs_trans_cid2_list));
      context.SetIntListCommonAttr("prevs_trans_cid3_list", std::move(prevs_trans_cid3_list));
    }
    return true;
  }

  static bool MixVariantAfterGenerator(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto cat_map_list = context.GetStringListCommonAttr("cat_map_list");
    std::unordered_map<std::tuple<int64, int64, int64>, std::tuple<int64, int64, int64>> m;
    if (cat_map_list) {
      for (const auto &s : (*cat_map_list)) {
        std::vector<absl::string_view> cids = absl::StrSplit(s, ':');
        if (cids.size() == 2) {
          std::vector<absl::string_view> cid_source = absl::StrSplit(cids[0], '_');
          std::vector<absl::string_view> cid_target = absl::StrSplit(cids[1], '_');
          if (cid_source.size() == 3 && cid_target.size() == 3) {
            int64 c1_s = 0;
            int64 c2_s = 0;
            int64 c3_s = 0;
            int64 c1_t = 0;
            int64 c2_t = 0;
            int64 c3_t = 0;
            if (absl::SimpleAtoi(cid_source[0], &c1_s) && absl::SimpleAtoi(cid_source[1], &c2_s) &&
                absl::SimpleAtoi(cid_source[2], &c3_s) && absl::SimpleAtoi(cid_target[0], &c1_t) &&
                absl::SimpleAtoi(cid_target[1], &c2_t) && absl::SimpleAtoi(cid_target[2], &c3_t)) {
              m.emplace(std::make_tuple(c1_s, c2_s, c3_s), std::make_tuple(c1_t, c2_t, c3_t));
            }
          }
        }
      }
    }
    auto mmu_cid1_threshold_list = context.GetStringListCommonAttr("mmu_cid1_threshold_list");
    auto mmu_embedding_size = context.GetIntCommonAttr("mmu_embedding_size").value_or(64);
    auto get_item_type = context.GetIntItemAttr("item_type");
    auto get_item_mmu_embedding = context.GetDoubleListItemAttr("item_mmu_embedding");
    std::unordered_map<int64, double> mmu_cid1_threshold_map;
    std::unordered_map<int64, std::vector<double>> item_id_mmu_embedding_map;
    // 本次新增 mmu 相似度打散功能是否开启实验，默认不开启
    auto mmu_exp_enable = context.GetIntCommonAttr("mmu_exp_enable").value_or(0);
    if (mmu_exp_enable) {
      if (mmu_cid1_threshold_list) {
        for (const auto & s : (*mmu_cid1_threshold_list)) {
          std::vector<absl::string_view> cid1_threshold = absl::StrSplit(s, ':');
          if (cid1_threshold.size() == 2) {
            int64 cid1 = 0;
            double threshold = 0.0;
            if (absl::SimpleAtoi(cid1_threshold[0], &cid1) &&
                absl::SimpleAtod(cid1_threshold[1], &threshold)) {
              mmu_cid1_threshold_map[cid1] = threshold;
            }
          }
        }
      }
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto item_type_tmp = get_item_type(result).value_or(2);
        // 序列类型跳过，只保存单值类型 item 的 mmu embedding
        if (item_type_tmp == 32 || item_type_tmp == 99) {
          auto mmu_embedding = get_item_mmu_embedding(result);
          if (mmu_embedding && (*mmu_embedding).size() == mmu_embedding_size) {
            item_id_mmu_embedding_map[result.item_key].assign((*mmu_embedding).begin(),
                                                              (*mmu_embedding).end());
          }
        }
      });
    }

    // mmu soft check
    auto mmu_soft_check_enable = context.GetIntCommonAttr("mmu_soft_check_enable").value_or(0);
    std::unordered_map<int64, SoftDiversityWeightInfos> soft_key_to_weight_map;
    if (mmu_soft_check_enable) {
      auto soft_weight_list = context.GetStringCommonAttr("soft_weight_infos");
      if (soft_weight_list) {
        std::vector<absl::string_view> key_weights = absl::StrSplit(*soft_weight_list, ':');
        if (key_weights.size() == 2) {
          std::vector<absl::string_view> weights_struct = absl::StrSplit(key_weights[1], '_');
          if (weights_struct.size() == 4) {
            int64 key = 0;
            double rule_weight_w1 = 0;
            double pos_gap_weight_w2 = 0;
            double bias_b1 = 0;
            double bias_b2 = 0;
            if (absl::SimpleAtoi(key_weights[0], &key) &&
                absl::SimpleAtod(weights_struct[0], &rule_weight_w1) &&
                absl::SimpleAtod(weights_struct[1], &pos_gap_weight_w2) &&
                absl::SimpleAtod(weights_struct[2], &bias_b1) &&
                absl::SimpleAtod(weights_struct[3], &bias_b2)) {
              SoftDiversityWeightInfos weight_infos;
              weight_infos.rule_weight_w1 = rule_weight_w1;
              weight_infos.pos_gap_weight_w2 = pos_gap_weight_w2;
              weight_infos.bias_b1 = bias_b1;
              weight_infos.bias_b2 = bias_b2;
              soft_key_to_weight_map.emplace(key, weight_infos);
            }
          }
        }
      }
    }
    // 优先级 1: 滑动窗口, 商品三级类目 4 个最多出 2 个, 跨屏
    // 优先级 2: 滑动窗口, 商品二级类目 10 个最多出 4 个, 跨屏
    // 优先级 3: 滑动窗口, 商品店铺 5 个最多出 1 个, 跨屏
    // 优先级 4: top 窗口, top10 最多出 4 个直播
    // 优先级 5: 滑动窗口, 直播 4 个最多出 2 个, 跨屏
    // 优先级 6: 滑动窗口，mmu 计算相似度， 3 个最多出 1 个, 跨屏
    auto cid3_variant_enable = context.GetIntCommonAttr("cid3_variant_enable").value_or(1);
    auto cid3_variant_win_size = context.GetIntCommonAttr("cid3_variant_win_size").value_or(4) - 1;
    auto cid3_variant_max_num = context.GetIntCommonAttr("cid3_variant_max_num").value_or(2);
    auto cid2_variant_enable = context.GetIntCommonAttr("cid2_variant_enable").value_or(1);
    auto cid2_variant_win_size = context.GetIntCommonAttr("cid2_variant_win_size").value_or(10) - 1;
    auto cid2_variant_max_num = context.GetIntCommonAttr("cid2_variant_max_num").value_or(4);
    auto aid_variant_enable = context.GetIntCommonAttr("aid_variant_enable").value_or(1);
    auto aid_variant_win_size = context.GetIntCommonAttr("aid_variant_win_size").value_or(5) - 1;
    auto aid_variant_max_num = context.GetIntCommonAttr("aid_variant_max_num").value_or(1);
    auto live_variant_enable = context.GetIntCommonAttr("live_variant_enable").value_or(1);
    auto live_variant_win_size = context.GetIntCommonAttr("live_variant_win_size").value_or(4) - 1;
    auto live_variant_max_num = context.GetIntCommonAttr("live_variant_max_num").value_or(2);
    // 默认不开启 mmu 打散逻辑
    auto mmu_variant_enable = context.GetIntCommonAttr("mmu_variant_enable").value_or(0);
    auto mmu_variant_win_size = context.GetIntCommonAttr("mmu_variant_win_size").value_or(3) - 1;
    auto mmu_variant_max_num = context.GetIntCommonAttr("mmu_variant_max_num").value_or(1);
    // 新增页内打散修复开关
    auto cur_sess_variant_enable = context.GetIntCommonAttr("cur_sess_variant_enable").value_or(0);
    // 新增广告打散开关
    auto ad_variant_enable = context.GetIntCommonAttr("ad_variant_enable").value_or(0);
    auto leaf_variant_enable = context.GetIntCommonAttr("leaf_variant_enable").value_or(0);
    auto leaf_variant_win_size = context.GetIntCommonAttr("leaf_variant_win_size").value_or(4) - 1;
    auto leaf_variant_max_num = context.GetIntCommonAttr("leaf_variant_max_num").value_or(1);
    // spu 打散
    auto spu_variant_enable = context.GetIntCommonAttr("spu_variant_enable").value_or(0);
    auto spu_variant_win_size = context.GetIntCommonAttr("spu_variant_win_size").value_or(50) - 1;
    auto spu_variant_max_num = context.GetIntCommonAttr("spu_variant_max_num").value_or(1);

    SlideWindowCounter prev_goods_cid3_counter(cid3_variant_win_size);
    SlideWindowCounter prev_goods_cid2_counter(cid2_variant_win_size);
    SlideWindowCounter prev_goods_aid_counter(aid_variant_win_size);
    SlideWindowCounter prev_live_counter(live_variant_win_size);
    SlideWindowCounter prev_leaf_counter(leaf_variant_win_size);
    SlideWindowListCalCounter pre_goods_mmu_list_counter(mmu_variant_win_size);
    auto pre_item_key_list = context.GetIntListCommonAttr("prev_item_key_list");
    auto prev_item_cid1_list = context.GetIntListCommonAttr("prev_item_cid1_list");
    auto prev_item_cid2_list = context.GetIntListCommonAttr("prev_item_cid2_list");
    auto prev_item_cid3_list = context.GetIntListCommonAttr("prev_item_cid3_list");
    auto prev_item_aid_list = context.GetIntListCommonAttr("prev_item_aid_list");
    auto pre_item_mmu_list = context.GetDoubleListCommonAttr("prev_item_mmu_list");
    auto pre_item_leaf_list = context.GetIntListCommonAttr(
                                  "pageshow_current_session_item_trans_leaf_list");

    if (mmu_exp_enable) {
      if (pre_item_mmu_list && pre_item_key_list &&
        (*pre_item_mmu_list).size() % mmu_embedding_size == 0 &&
        (*pre_item_mmu_list).size() / mmu_embedding_size == (*pre_item_key_list).size()) {
        int pre_mmu_item_num = (*pre_item_mmu_list).size() / mmu_embedding_size;
        for (int i = 0; i < pre_mmu_item_num; i++) {
          int64 pre_item_key = (*pre_item_key_list)[i];
          item_id_mmu_embedding_map[pre_item_key].assign(
          (*pre_item_mmu_list).begin() +i * mmu_embedding_size,
          (*pre_item_mmu_list).begin() + (i + 1) * mmu_embedding_size);
        }
      }
    }
    int64 cid1_len = prev_item_cid1_list ? (*prev_item_cid1_list).size() : 0;
    int64 cid2_len = prev_item_cid2_list ? (*prev_item_cid2_list).size() : 0;
    int64 cid3_len = prev_item_cid3_list ? (*prev_item_cid3_list).size() : 0;
    int64 aid_len = prev_item_aid_list ? (*prev_item_aid_list).size() : 0;
    int64 key_len = pre_item_key_list ? (*pre_item_key_list).size() : 0;
    int64 leaf_len = pre_item_leaf_list ? (*pre_item_leaf_list).size() : 0;
    if (cid1_len > 0 && cid1_len == cid2_len && cid1_len == cid3_len && cid1_len == aid_len) {
      int64 max_win_size = 0;
      if (cid3_variant_enable > 0 && cid3_variant_win_size > max_win_size) {
        max_win_size = cid3_variant_win_size;
      }
      if (cid2_variant_enable > 0 && cid2_variant_win_size > max_win_size) {
        max_win_size = cid2_variant_win_size;
      }
      if (aid_variant_enable > 0 && aid_variant_win_size > max_win_size) {
        max_win_size = aid_variant_win_size;
      }
      for (int64 i = max_win_size; i > 0; --i) {
        auto index = cid1_len - i;
        auto item_key_index = key_len - i;
        int64 leaf_key_index = leaf_len - i;
        if (index >= 0 && index < cid1_len) {
          auto aid = (*prev_item_aid_list)[index];
          auto cid1 = (*prev_item_cid1_list)[index];
          auto cid2 = (*prev_item_cid2_list)[index];
          auto cid3 = (*prev_item_cid3_list)[index];
          if (!(m.empty()) && cid1 > 0 && cid2 > 0 && cid3 > 0) {
            auto iter = m.find(std::make_tuple(cid1, cid2, cid3));
            if (iter != m.end()) {
              cid1 = std::get<0>(iter->second);
              cid2 = std::get<1>(iter->second);
              cid3 = std::get<2>(iter->second);
            }
          }
          if (cid3_variant_enable > 0 && i <= cid3_variant_win_size) {
            if (cid3 > 0) {
              prev_goods_cid3_counter.Insert(cid3);
            } else {
              if (cur_sess_variant_enable > 0) {
                prev_goods_cid3_counter.Insert(0);
              }
            }
          }
          if (cid2_variant_enable > 0 && i <= cid2_variant_win_size) {
            if (cid2 > 0) {
              prev_goods_cid2_counter.Insert(cid2);
            } else {
              if (cur_sess_variant_enable > 0) {
                prev_goods_cid2_counter.Insert(0);
              }
            }
          }
          if (aid_variant_enable > 0 && i <= aid_variant_win_size) {
            if (aid > 0) {
              prev_goods_aid_counter.Insert(aid);
            } else {
              if (cur_sess_variant_enable > 0) {
                prev_goods_aid_counter.Insert(0);
              }
            }
          }
          if (mmu_variant_enable > 0  && item_key_index >= 0 &&
              i <= mmu_variant_win_size && cid1 > 0 && item_key_index < key_len) {
            pre_goods_mmu_list_counter.Insert(cid1, (*pre_item_key_list)[item_key_index]);
          }
        }
        if (leaf_variant_enable > 0 && leaf_key_index >= 0 &&
            leaf_key_index < leaf_len && i <= leaf_variant_max_num) {
          prev_leaf_counter.Insert((*pre_item_leaf_list)[leaf_key_index]);
        } else {
          prev_leaf_counter.Insert(0);
        }
      }
    }
    auto prev_item_type_list = context.GetIntListCommonAttr("prev_item_type_list");
    if (prev_item_type_list) {
      int64 item_type_len = (*prev_item_type_list).size();
      if (live_variant_enable > 0) {
        for (int64 i = live_variant_win_size; i > 0; --i) {
          auto index = item_type_len - i;
          if (index >= 0 && index < item_type_len) {
            auto item_type = (*prev_item_type_list)[index];
            if (item_type == 1) {
              prev_live_counter.Insert(item_type);
            } else {
              if (cur_sess_variant_enable > 0) {
                prev_live_counter.Insert(0);
              }
            }
          }
        }
      }
    }
    auto get_item_id_list_origin = context.GetIntListItemAttr("item_id_list_origin");
    auto get_item_type_list_origin = context.GetIntListItemAttr("item_type_list_origin");
    auto get_item_cid2_list_origin = context.GetIntListItemAttr("item_cid2_list_origin");
    auto get_item_cid3_list_origin = context.GetIntListItemAttr("item_cid3_list_origin");
    auto get_item_aid_list_origin = context.GetIntListItemAttr("item_aid_list_origin");
    auto get_item_ctr_list_origin = context.GetDoubleListItemAttr("item_ctr_list_origin");
    auto get_item_cvr_list_origin = context.GetDoubleListItemAttr("item_cvr_list_origin");
    auto get_item_price_list_origin = context.GetDoubleListItemAttr("item_price_list_origin");
    auto get_item_bonus_list_origin = context.GetDoubleListItemAttr("item_bonus_list_origin");
    auto get_item_ad_list_origin = context.GetIntListItemAttr("item_ad_list_origin");
    auto set_item_id_list_variant = context.SetIntListItemAttr("item_id_list_variant");
    auto set_item_type_list_variant = context.SetIntListItemAttr("item_type_list_variant");
    auto set_item_ctr_list_variant = context.SetDoubleListItemAttr("item_ctr_list_variant");
    auto set_item_cvr_list_variant = context.SetDoubleListItemAttr("item_cvr_list_variant");
    auto set_item_price_list_variant = context.SetDoubleListItemAttr("item_price_list_variant");
    auto set_item_bonus_list_variant = context.SetDoubleListItemAttr("item_bonus_list_variant");
    auto get_item_cid1_list_origin = context.GetIntListItemAttr("item_cid1_list_origin");
    auto get_item_leaf_list_origin = context.GetIntListItemAttr("item_leaf_list_origin");
    auto get_item_spu_list_origin = context.GetIntListItemAttr("item_spu_list_origin");
    double default_mmu_cid1_threshold = context.GetDoubleCommonAttr(
        "default_mmu_cid1_threshold").value_or(0.5);
    int pos_gap_offset = context.GetDoubleCommonAttr("pos_gap_offset").value_or(2);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_type_tmp = get_item_type(result).value_or(0);
      // 只处理序列类型物料
      if (item_type_tmp != 2) {
        return;
      }
      SlideWindowCounter temp_prev_cid3_counter(prev_goods_cid3_counter);
      SlideWindowCounter temp_prev_cid2_counter(prev_goods_cid2_counter);
      SlideWindowCounter temp_prev_aid_counter(prev_goods_aid_counter);
      int temp_top_live_num = 0;
      SlideWindowCounter temp_prev_live_counter(prev_live_counter);
      SlideWindowListCalCounter temp_goods_mmu_list_counter(pre_goods_mmu_list_counter);
      SlideWindowCounter temp_prev_leaf_counter(prev_leaf_counter);
      SlideWindowCounter temp_prev_spu_counter(spu_variant_win_size);
      if (mmu_soft_check_enable) {
        // 设置多样性软性打分公式权重
        auto iter_mmu = soft_key_to_weight_map.find(6);
        if (iter_mmu != soft_key_to_weight_map.end()) {
          temp_goods_mmu_list_counter.SetSoftWeightInfos(iter_mmu->second);
        }
      }
      auto item_id_list_origin = get_item_id_list_origin(result);
      auto item_type_list_origin = get_item_type_list_origin(result);
      auto item_cid1_list_origin = get_item_cid1_list_origin(result);
      auto item_cid2_list_origin = get_item_cid2_list_origin(result);
      auto item_cid3_list_origin = get_item_cid3_list_origin(result);
      auto item_aid_list_origin = get_item_aid_list_origin(result);
      auto item_ctr_list_origin = get_item_ctr_list_origin(result);
      auto item_cvr_list_origin = get_item_cvr_list_origin(result);
      auto item_price_list_origin = get_item_price_list_origin(result);
      auto item_bonus_list_origin = get_item_bonus_list_origin(result);
      auto item_ad_list_origin = get_item_ad_list_origin(result);
      auto item_leaf_list_origin = get_item_leaf_list_origin(result);
      auto item_spu_list_origin = get_item_spu_list_origin(result);
      auto len_item_id = item_id_list_origin ? (*item_id_list_origin).size() : 0;
      auto len_item_type = item_type_list_origin ? (*item_type_list_origin).size() : 0;
      auto len_item_cid1 = item_cid1_list_origin ? (*item_cid1_list_origin).size() : 0;
      auto len_item_cid2 = item_cid2_list_origin ? (*item_cid2_list_origin).size() : 0;
      auto len_item_cid3 = item_cid3_list_origin ? (*item_cid3_list_origin).size() : 0;
      auto len_item_aid = item_aid_list_origin ? (*item_aid_list_origin).size() : 0;
      auto len_item_ctr = item_ctr_list_origin ? (*item_ctr_list_origin).size() : 0;
      auto len_item_cvr = item_cvr_list_origin ? (*item_cvr_list_origin).size() : 0;
      auto len_item_price = item_price_list_origin ? (*item_price_list_origin).size() : 0;
      auto len_item_bonus = item_bonus_list_origin ? (*item_bonus_list_origin).size() : 0;
      auto len_item_ad = item_ad_list_origin ? (*item_ad_list_origin).size() : 0;
      auto len_item_leaf = item_leaf_list_origin ? (*item_leaf_list_origin).size() : 0;
      auto len_item_spu = item_spu_list_origin ? (*item_spu_list_origin).size() : 0;
      if (len_item_id > 0 && len_item_id == len_item_type && len_item_id == len_item_cid2 &&
          len_item_id == len_item_cid3 && len_item_id == len_item_aid && len_item_id == len_item_ctr &&
          len_item_id == len_item_cvr && len_item_id == len_item_price && len_item_id == len_item_bonus) {
        std::vector<int64> item_id_list_variant;
        std::vector<int64> item_type_list_variant;
        std::vector<double> item_ctr_list_variant;
        std::vector<double> item_cvr_list_variant;
        std::vector<double> item_price_list_variant;
        std::vector<double> item_bonus_list_variant;
        item_id_list_variant.reserve(10);
        item_type_list_variant.reserve(10);
        item_ctr_list_variant.reserve(10);
        item_cvr_list_variant.reserve(10);
        item_price_list_variant.reserve(10);
        item_bonus_list_variant.reserve(10);
        std::vector<int> selected_flag_list(len_item_id, 0);
        if (ad_variant_enable > 0 && len_item_ad == len_item_id) {
          for (int i = 0; i < len_item_id; ++i) {
            if ((*item_ad_list_origin)[i] == 1) {
              selected_flag_list[i] = 1;
            }
          }
        }
        std::vector<int> flag_list(len_item_id, 0);
        while (item_id_list_variant.size() < 10) {
          // 如果待插入的下一个素材在原始序列中是广告
          if (ad_variant_enable > 0 && len_item_ad == len_item_id) {
            size_t cur_len = item_id_list_variant.size();
            if (cur_len < len_item_ad && (*item_ad_list_origin)[cur_len] == 1) {
              item_id_list_variant.emplace_back((*item_id_list_origin)[cur_len]);
              item_type_list_variant.emplace_back((*item_type_list_origin)[cur_len]);
              item_ctr_list_variant.emplace_back((*item_ctr_list_origin)[cur_len]);
              item_cvr_list_variant.emplace_back((*item_cvr_list_origin)[cur_len]);
              item_price_list_variant.emplace_back((*item_price_list_origin)[cur_len]);
              item_bonus_list_variant.emplace_back((*item_bonus_list_origin)[cur_len]);
              continue;
            }
          }
          std::fill(flag_list.begin(), flag_list.end(), 0);
          // 找到第一个未被选择的候选结果
          int selected_index = 0;
          for (int i = 0; i < len_item_id; ++i) {
            if (selected_flag_list[i] == 0) {
              selected_index = i;
              break;
            }
          }
          if (selected_index == len_item_id - 1) {
            item_id_list_variant.emplace_back((*item_id_list_origin)[selected_index]);
            item_type_list_variant.emplace_back((*item_type_list_origin)[selected_index]);
            item_ctr_list_variant.emplace_back((*item_ctr_list_origin)[selected_index]);
            item_cvr_list_variant.emplace_back((*item_cvr_list_origin)[selected_index]);
            item_price_list_variant.emplace_back((*item_price_list_origin)[selected_index]);
            item_bonus_list_variant.emplace_back((*item_bonus_list_origin)[selected_index]);
            break;
          }
          for (int i = selected_index; i < len_item_id; ++i) {
            if (selected_flag_list[i] == 1) {
              continue;
            }
            auto item_type = (*item_type_list_origin)[i];
            auto cid2 = (*item_cid2_list_origin)[i];
            auto cid3 = (*item_cid3_list_origin)[i];
            auto aid = (*item_aid_list_origin)[i];
            auto item_key = (*item_id_list_origin)[i];
            int64 cid1 = 0;
            double cid1_mmu_threshold = default_mmu_cid1_threshold;
            if (len_item_cid1 == len_item_id) {
              cid1 = (*item_cid1_list_origin)[i];
            }
            int64 spu = 0;
            if (len_item_spu == len_item_id) {
              spu = (*item_spu_list_origin)[i];
            }
            if (mmu_cid1_threshold_map.find(cid1) != mmu_cid1_threshold_map.end()) {
              cid1_mmu_threshold = mmu_cid1_threshold_map[cid1];
            }
            if (cid3_variant_enable == 0 || item_type == 1 || cid3 <= 0 ||
                temp_prev_cid3_counter.Count(cid3) < cid3_variant_max_num) {
              flag_list[i] |= 0x80;
            }
            if (cid2_variant_enable == 0 || item_type == 1 || cid2 <= 0 ||
                temp_prev_cid2_counter.Count(cid2) < cid2_variant_max_num) {
              flag_list[i] |= 0x40;
            }
            if (aid_variant_enable == 0 || item_type == 1 || aid <= 0 ||
                temp_prev_aid_counter.Count(aid) < aid_variant_max_num) {
              flag_list[i] |= 0x20;
            }
            if (item_type != 1 || temp_top_live_num < 4) {
              flag_list[i] |= 0x10;
            }
            if (live_variant_enable == 0 || item_type != 1 ||
                temp_prev_live_counter.Count(item_type) < live_variant_max_num) {
              flag_list[i] |= 0x8;
            }
            if (leaf_variant_enable == 0 || item_type == 1 ||
                len_item_leaf != len_item_id || temp_prev_leaf_counter.Count(
                  (*item_leaf_list_origin)[i]) < leaf_variant_max_num) {
              flag_list[i] |= 0x4;
            }
            if (spu_variant_enable == 0 || item_type == 1 || spu <= 0 ||
                temp_prev_spu_counter.Count(spu) < spu_variant_max_num) {
              flag_list[i] |= 0x2;
            }
            if (mmu_soft_check_enable) {
              int curr_pos_index = item_id_list_variant.size();
              if (mmu_variant_enable == 0 || item_type == 1 ||
                  item_id_mmu_embedding_map.find(item_key) == item_id_mmu_embedding_map.end() ||
                  temp_goods_mmu_list_counter.SoftCount(cid1, item_key, curr_pos_index,
                  item_id_mmu_embedding_map, cid1_mmu_threshold, pos_gap_offset) < mmu_variant_max_num) {
                flag_list[i] |= 0x1;
              }
            } else {
              if (mmu_variant_enable == 0 || item_type == 1 ||
                  item_id_mmu_embedding_map.find(item_key) == item_id_mmu_embedding_map.end() ||
                  temp_goods_mmu_list_counter.Count(cid1, item_key,
                  item_id_mmu_embedding_map, cid1_mmu_threshold) < mmu_variant_max_num) {
                flag_list[i] |= 0x1;
              }
            }
          }
          int max_valid_num = 0;
          for (int i = selected_index; i < len_item_id; ++i) {
            if (selected_flag_list[i] == 1) {
              continue;
            }
            if ((flag_list[i] & (flag_list[i] + 1)) == 0) {
              int valid_num = Log2(flag_list[i]);
              if (valid_num > max_valid_num) {
                max_valid_num = valid_num;
                selected_index = i;
              }
            }
          }
          auto item_type = (*item_type_list_origin)[selected_index];
          auto cid2 = (*item_cid2_list_origin)[selected_index];
          auto cid3 = (*item_cid3_list_origin)[selected_index];
          auto aid = (*item_aid_list_origin)[selected_index];
          auto item_key = (*item_id_list_origin)[selected_index];
          int64 cid1 = 0;
          if (len_item_cid1 == len_item_id) {
            cid1 = (*item_cid1_list_origin)[selected_index];
          }
          int64 leaf_cate_id = 0;
          if (len_item_id == len_item_leaf) {
            leaf_cate_id = (*item_leaf_list_origin)[selected_index];
          }
          int64 spu = 0;
          if (len_item_id == len_item_spu) {
            spu = (*item_spu_list_origin)[selected_index];
          }
          if (cid3_variant_enable > 0) {
            if (item_type == 1 || cid3 <= 0) {
              if (cur_sess_variant_enable > 0) {
                temp_prev_cid3_counter.Insert(0);
              } else {
                temp_prev_cid3_counter.Pop();
              }
            } else {
              temp_prev_cid3_counter.Insert(cid3);
            }
          }
          if (cid2_variant_enable > 0) {
            if (item_type == 1 || cid2 <= 0) {
              if (cur_sess_variant_enable > 0) {
                temp_prev_cid2_counter.Insert(0);
              } else {
                temp_prev_cid2_counter.Pop();
              }
            } else {
              temp_prev_cid2_counter.Insert(cid2);
            }
          }
          if (aid_variant_enable > 0) {
            if (item_type == 1 || aid <= 0) {
              if (cur_sess_variant_enable > 0) {
                temp_prev_aid_counter.Insert(0);
              } else {
                temp_prev_aid_counter.Pop();
              }
            } else {
              temp_prev_aid_counter.Insert(aid);
            }
          }
          if (item_type == 1) {
            temp_top_live_num += 1;
          }
          if (live_variant_enable > 0) {
            if (item_type != 1) {
              if (cur_sess_variant_enable > 0) {
                temp_prev_live_counter.Insert(0);
              } else {
                temp_prev_live_counter.Pop();
              }
            } else {
              temp_prev_live_counter.Insert(item_type);
            }
          }
          if (mmu_variant_enable > 0) {
            if (item_type == 1 || cid1 <= 0) {
              if (cur_sess_variant_enable > 0) {
                temp_goods_mmu_list_counter.Insert(0, item_key);;
              } else {
                temp_goods_mmu_list_counter.Pop();
              }
            } else {
              temp_goods_mmu_list_counter.Insert(cid1, item_key);
            }
          }
          if (leaf_variant_enable > 0) {
            if (item_type == 1 || leaf_cate_id <= 0) {
              if (cur_sess_variant_enable > 0) {
                temp_prev_leaf_counter.Insert(0);;
              } else {
                temp_prev_leaf_counter.Pop();
              }
            } else {
              temp_prev_leaf_counter.Insert(leaf_cate_id);
            }
          }
          if (spu_variant_enable > 0) {
            if (item_type == 1 || spu <= 0) {
              temp_prev_spu_counter.Insert(0);;
            } else {
              temp_prev_spu_counter.Insert(spu);
            }
          }
          if (selected_index < len_item_id) {
            selected_flag_list[selected_index] = 1;
          }
          item_id_list_variant.emplace_back((*item_id_list_origin)[selected_index]);
          item_type_list_variant.emplace_back((*item_type_list_origin)[selected_index]);
          item_ctr_list_variant.emplace_back((*item_ctr_list_origin)[selected_index]);
          item_cvr_list_variant.emplace_back((*item_cvr_list_origin)[selected_index]);
          item_price_list_variant.emplace_back((*item_price_list_origin)[selected_index]);
          item_bonus_list_variant.emplace_back((*item_bonus_list_origin)[selected_index]);
        }
        set_item_id_list_variant(result, item_id_list_variant);
        set_item_type_list_variant(result, item_type_list_variant);
        set_item_ctr_list_variant(result, item_ctr_list_variant);
        set_item_cvr_list_variant(result, item_cvr_list_variant);
        set_item_price_list_variant(result, item_price_list_variant);
        set_item_bonus_list_variant(result, item_bonus_list_variant);
      }
    });
    return true;
  }

  static bool ItemsWithAttrMatch(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto source_item_list = context.GetIntListCommonAttr("source_item_list");
    auto source_attr_list = context.GetIntListCommonAttr("source_attr_list");
    auto len_item = source_item_list ? (*source_item_list).size() : 0;
    auto len_attr = source_attr_list ? (*source_attr_list).size() : 0;
    folly::F14FastMap<int64, std::vector<int64>> attr_to_items;
    if (len_item > 0 && len_item == len_attr) {
      for (int i = 0; i < len_item; ++i) {
        if ((*source_attr_list)[i] > 0 && (*source_item_list)[i] > 0) {
          attr_to_items[(*source_attr_list)[i]].emplace_back((*source_item_list)[i]);
        }
      }
    }
    auto get_item_attr = context.GetIntItemAttr("item_attr");
    auto set_items_attr_match = context.SetIntListItemAttr("items_attr_match");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (!attr_to_items.empty()) {
        auto item_attr = get_item_attr(result).value_or(0);
        auto iter = attr_to_items.find(item_attr);
        if (iter != attr_to_items.end()) {
          if (iter->second.size() > 64) {
            std::vector<int64> tmp(iter->second.begin(), iter->second.begin() + 64);
            set_items_attr_match(result, tmp);
          } else {
            std::vector<int64> tmp(iter->second);
            set_items_attr_match(result, tmp);
          }
        }
      }
    });
    return true;
  }

  static bool GenerateCateFilterFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto tab_first_cate_black_list = context.GetIntListCommonAttr("tab_first_cate_black_list");
    auto tab_most_cate_black_list = context.GetIntListCommonAttr("tab_most_cate_black_list");
    auto first_list_size = tab_first_cate_black_list ? (*tab_first_cate_black_list).size() : 0;
    auto most_list_size = tab_most_cate_black_list ? (*tab_most_cate_black_list).size() : 0;

    auto cart_first_item_cate1_id_attr = context.GetIntItemAttr("sCartFirstItemCate1Id");
    auto cart_first_item_cate2_id_attr = context.GetIntItemAttr("sCartFirstItemCate2Id");
    auto cart_first_item_cate3_id_attr = context.GetIntItemAttr("sCartFirstItemCate3Id");
    auto cart_most_item_cate1_id_attr = context.GetIntItemAttr("sCartMostItemCate1Id");
    auto cart_most_item_cate2_id_attr = context.GetIntItemAttr("sCartMostItemCate2Id");
    auto cart_most_item_cate3_id_attr = context.GetIntItemAttr("sCartMostItemCate3Id");

    std::unordered_set<int> tab_first_cate_black_set;
    std::unordered_set<int> tab_most_cate_black_set;
    if (tab_first_cate_black_list) {
      for (auto t : *tab_first_cate_black_list) {
        tab_first_cate_black_set.insert(t);
      }
    }
    if (tab_most_cate_black_list) {
      for (auto t : *tab_most_cate_black_list) {
        tab_most_cate_black_set.insert(t);
      }
    }

    int tab_first_cate_filter_flag = 0;
    int tab_most_cate_filter_flag = 0;

    auto tab_first_cate_filter_flag_attr = context.SetIntItemAttr("tab_first_cate_filter_flag");
    auto tab_most_cate_filter_flag_attr = context.SetIntItemAttr("tab_most_cate_filter_flag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto sCartFirstItemCate1Id = cart_first_item_cate1_id_attr(result).value_or(0);
      auto sCartFirstItemCate2Id = cart_first_item_cate2_id_attr(result).value_or(0);
      auto sCartFirstItemCate3Id = cart_first_item_cate3_id_attr(result).value_or(0);
      auto sCartMostItemCate1Id = cart_most_item_cate1_id_attr(result).value_or(0);
      auto sCartMostItemCate2Id = cart_most_item_cate2_id_attr(result).value_or(0);
      auto sCartMostItemCate3Id = cart_most_item_cate3_id_attr(result).value_or(0);

      std::vector<int64> live_first_cate_list = {sCartFirstItemCate1Id, sCartFirstItemCate2Id,
                                                 sCartFirstItemCate3Id};
      std::vector<int64> live_most_cate_list = {sCartMostItemCate1Id, sCartMostItemCate2Id,
                                                sCartMostItemCate3Id};

      for (int live_cate : live_first_cate_list) {
        if (tab_first_cate_black_set.find(live_cate) != tab_first_cate_black_set.end()) {
          tab_first_cate_filter_flag = 1;
          break;
        }
      }
      for (int live_cate : live_most_cate_list) {
        if (tab_most_cate_black_set.find(live_cate) != tab_most_cate_black_set.end()) {
          tab_most_cate_filter_flag = 1;
          break;
        }
      }
      tab_first_cate_filter_flag_attr(result, tab_first_cate_filter_flag);
      tab_most_cate_filter_flag_attr(result, tab_most_cate_filter_flag);
    });
    return true;
  }

  static bool GenPotentialBoostRedisAuthorKey(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto bh_seller_growth_potential_boost_bid_server_strategy_version_str =
        context.GetStringCommonAttr("bh_seller_growth_potential_boost_bid_server_strategy_version");
    std::string bh_seller_growth_potential_boost_bid_server_strategy_version =
        bh_seller_growth_potential_boost_bid_server_strategy_version_str
            ? std::string(*bh_seller_growth_potential_boost_bid_server_strategy_version_str)
            : "potential_no_strategy";
    auto aId_attr = context.GetIntItemAttr("aId");
    auto potential_boost_redis_author_key_attr =
        context.SetStringItemAttr("potential_boost_redis_author_key");

    time_t now = time(0);
    tm *timeinfo = new tm;
    char day[9] = {};
    if (localtime_r(&now, timeinfo) != nullptr) {
      strftime(day, 9, "%Y%m%d", timeinfo);

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::string potential_boost_redis_author_key = "";
        auto aId = aId_attr(result).value_or(0);
        if (aId > 0) {
          potential_boost_redis_author_key = "bid_server_boost2_pid_author_result_" + std::string(day) +
                                             "_author_" + std::to_string(aId) + "_" +
                                             bh_seller_growth_potential_boost_bid_server_strategy_version;
        }
        potential_boost_redis_author_key_attr(result, potential_boost_redis_author_key);
      });
    }
    return true;
  }

  static bool WelfareBoostBonusEnrichRedisKeys(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto bh_seller_growth_welfare_boost_redis_key_prefix_str =
        context.GetStringCommonAttr("bh_seller_growth_welfare_boost_redis_key_prefix");
    std::string bh_seller_growth_welfare_boost_redis_key_prefix =
        bh_seller_growth_welfare_boost_redis_key_prefix_str
            ? std::string(*bh_seller_growth_welfare_boost_redis_key_prefix_str)
            : "apply_wlf_bonus_flag_";
    auto live_id_attr = context.GetIntItemAttr("live_id");
    auto bh_seller_growth_welfare_boost_redis_key_attr =
        context.SetStringItemAttr("bh_seller_growth_welfare_boost_redis_key");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto live_id = live_id_attr(result).value_or(0);
      std::string bh_seller_growth_welfare_boost_redis_key =
          bh_seller_growth_welfare_boost_redis_key_prefix + std::to_string(live_id);
      bh_seller_growth_welfare_boost_redis_key_attr(result, bh_seller_growth_welfare_boost_redis_key);
    });
    return true;
  }

  static bool ParseAdOwnerInfo(const CommonRecoLightFunctionContext &context,
                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto ad_rio_mdata =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<uint64>>>("ad_roi_mdata");
    if (ad_rio_mdata == nullptr) {
      return true;
    }
    auto get_item_id_attr = context.GetIntItemAttr("item_id");
    auto get_aid_attr = context.GetIntItemAttr("iAuthorId");
    auto set_ad_owner_put_status = context.SetIntItemAttr("ad_owner_put_status");
    auto set_ad_owner_roi_ratio = context.SetDoubleItemAttr("ad_owner_roi_ratio");
    auto set_ad_owner_creative_id = context.SetIntItemAttr("ad_owner_creative_id");
    auto set_ad_owner_unit_id = context.SetIntItemAttr("ad_owner_unit_id");
    auto set_ad_owner_campaign_id = context.SetIntItemAttr("ad_owner_campaign_id");
    auto set_ad_owner_account_id = context.SetIntItemAttr("ad_owner_account_id");
    auto set_ad_owner_budget_put_status = context.SetIntItemAttr("ad_owner_budget_put_status");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {  // 遍历每一个 item_attr
      auto item_id = get_item_id_attr(result).value_or(0);
      auto aid = get_aid_attr(result).value_or(0);
      if (item_id > 0 && aid > 0) {
        auto iter = ad_rio_mdata->find(absl::StrFormat("%d#%d", item_id, aid));
        if (iter != ad_rio_mdata->end() && iter->second.size() == 6) {
          const auto& res = iter->second;
          set_ad_owner_put_status(result, (int64)res[0]);
          set_ad_owner_roi_ratio(result, (res[1] + 0.0) / 1000.0);
          set_ad_owner_creative_id(result, (int64)res[2]);
          set_ad_owner_unit_id(result, (int64)res[3]);
          set_ad_owner_campaign_id(result, (int64)res[4]);
          set_ad_owner_account_id(result, (int64)res[5]);
          set_ad_owner_budget_put_status(result, (int64)1);
        }
      }
    });

    return true;
  }

  static bool TransMixRankPrevSessionEntityList(const CommonRecoLightFunctionContext &context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto prev_session_entity_list = context.GetIntListCommonAttr("prev_session_entity_list");
    auto kconf_prev_session_entity_list = context.GetIntListCommonAttr("kconf_prev_session_entity_list");
    int len = prev_session_entity_list ? (*prev_session_entity_list).size() : 0;
    int kconf_len = kconf_prev_session_entity_list ? (*kconf_prev_session_entity_list).size() : 0;
    if (prev_session_entity_list &&
        kconf_prev_session_entity_list &&
        len > 0 &&
        len == kconf_len) {
      std::vector<int64> trans_prev_session_entity_list;
      trans_prev_session_entity_list.reserve(len);
      for (int i = 0; i < len; ++i) {
        if ((*kconf_prev_session_entity_list)[i] > 0) {
          trans_prev_session_entity_list.emplace_back((*kconf_prev_session_entity_list)[i]);
        } else {
          trans_prev_session_entity_list.emplace_back((*prev_session_entity_list)[i]);
        }
      }
      context.SetIntListCommonAttr("trans_prev_session_entity_list",
                                   std::move(trans_prev_session_entity_list));
    }

    return true;
  }

 private:
  class SlideWindowCounter {
   public:
    explicit SlideWindowCounter(size_t window_size) : window_size_(window_size) {}

    SlideWindowCounter(const SlideWindowCounter &s)
        : data_(s.data_), counter_(s.counter_), window_size_(s.window_size_) {}

    inline void Insert(int64 n) {
      if (data_.size() == window_size_) {
        this->Pop();
      }
      data_.push_back(n);
      counter_[n] += 1;
    }

    inline void Pop() {
      if (!(data_.empty())) {
        auto iter = counter_.find(data_.front());
        if (iter != counter_.end()) {
          if (iter->second == 1) {
            counter_.erase(iter);
          } else {
            iter->second -= 1;
          }
        }
        data_.pop_front();
      }
    }

    inline int Count(int64 n) {
      auto iter = counter_.find(n);
      if (iter == counter_.end()) {
        return 0;
      } else {
        return iter->second;
      }
    }

   private:
    std::deque<int64> data_;
    std::unordered_map<int64, int> counter_;
    const size_t window_size_;
  };

  // 多样性软性打分公式，对相似度和坑位距离做映射
  // score_weight = w1 * {1 / (exp^(w2 * (pos_gap - bias_b1) + 1) + bias_b2}
  // w1 设置多样性规则强弱, w1 越大，则该多样性规则力度就越大
  // w2 设置坑位 gap 影响不同 gap 的差异，gap 越小，则在 bias_w1 两侧坑位影响就越平滑，
  // bias_w1 坑位距离影响阶跃点
  // bias_b 权重最小值
  // 默认值输出最终权重 1，近似对应硬规则的 3 出 1
  struct SoftDiversityWeightInfos {
    double rule_weight_w1 = 1.0;
    double pos_gap_weight_w2 = 20.0;
    double bias_b1 = 3.5;
    double bias_b2 = 0;
  };

  class SlideWindowListCalCounter {
   public:
    explicit SlideWindowListCalCounter(size_t window_size) : window_size_(window_size) {}

    SlideWindowListCalCounter(const SlideWindowListCalCounter &s)
        : data_(s.data_), data_value_list_(s.data_value_list_), window_size_(s.window_size_) {}

    inline void Insert(int64 n, int64 item_key) {
      if (data_.size() == window_size_) {
        this->Pop();
      }
      data_.push_back(n);
      if (data_value_list_.find(n) == data_value_list_.end()) {
        data_value_list_[n] = {};
      }
      data_value_list_[n].push_back(item_key);
    }

    inline void Pop() {
      if (!(data_.empty())) {
        auto iter = data_value_list_.find(data_.front());
        if (iter != data_value_list_.end()) {
          if (iter->second.size() == 1) {
            data_value_list_.erase(iter);
          } else {
            iter->second.pop_front();
          }
        }
        data_.pop_front();
      }
    }

    inline int Count(int64 n, int64 item_key, const std::unordered_map<int64, std::vector<double>>&
                    item_key_2_embedding_map, double threshold) {
      auto iter1 = item_key_2_embedding_map.find(item_key);
      if (iter1 == item_key_2_embedding_map.end() ||
          data_value_list_.find(n) == data_value_list_.end()) {
        return 0;
      }

      int num = 0;
      const std::vector<double>& this_item_embedding = iter1->second;
      for (const auto& pre_item_key : data_value_list_[n]) {
        auto iter2 = item_key_2_embedding_map.find(pre_item_key);
        if (iter2 != item_key_2_embedding_map.end() &&
            this_item_embedding.size() == iter2->second.size()) {
            double inner_value = 0.0;
            const std::vector<double>& pre_item_embedding = iter2->second;
            for (size_t j = 0; j < pre_item_embedding.size(); ++j) {
                inner_value += this_item_embedding[j] * pre_item_embedding[j];
            }
            if (inner_value > threshold) {
                ++num;
            }
        }
      }
      return num;
    }

    inline void SetSoftWeightInfos(const SoftDiversityWeightInfos& soft_weight_infos) {
      soft_weight_infos_ = soft_weight_infos;
    }

    inline int GetGapMpa(const int pos_index, const int pos_gap, const int offset) {
      if (pos_index % 2 == 0 && pos_gap % 2 == 1) {
        return pos_gap + offset;
      }
      return pos_gap;
    }
    // score_weight = w1 * {1 / (exp^(w2 * (pos_gap - bias_b1) + 1) + bias_b2}
    inline double GetGapWeight(const int pos_gap) {
      double gap_w = soft_weight_infos_.pos_gap_weight_w2 * (pos_gap - soft_weight_infos_.bias_b1);
      gap_w = std::max(-10.0, gap_w);
      gap_w = std::min(10.0, gap_w);
      return soft_weight_infos_.rule_weight_w1 * (1.0 / (std::exp(gap_w) + 1 ) + soft_weight_infos_.bias_b2);
    }

    inline int SoftCount(int64 n, int64 item_key, const int pos_index,
                         const std::unordered_map<int64, std::vector<double>>& item_key_2_embedding_map,
                         const double threshold, const int offset) {
      const auto iter1 = item_key_2_embedding_map.find(item_key);
      const auto counter_key_iter = data_value_list_.find(n);
      if (iter1 == item_key_2_embedding_map.end() || counter_key_iter == data_value_list_.end()) {
        return 0;
      }
      int num = 0;
      const int max_size = data_.size();
      std::vector<int> key_select_index;
      key_select_index.reserve(counter_key_iter->second.size());
      for (int index = 0; index < max_size; ++index) {
        if (n == data_[index]) {
          key_select_index.emplace_back(index);
        }
      }
      if (key_select_index.size() != counter_key_iter->second.size()) {
        return 0;
      }

      const std::vector<double>& this_item_embedding = iter1->second;
      for (size_t i = 0; i < counter_key_iter->second.size(); ++i) {
        int pos_gap = max_size - key_select_index[i];
        const auto& pre_item_key = counter_key_iter->second[i];
        double inner_value = 0.0;
        auto pre_item_emb_iter = item_key_2_embedding_map.find(pre_item_key);

        if (pre_item_emb_iter != item_key_2_embedding_map.end() &&
            this_item_embedding.size() == pre_item_emb_iter->second.size()) {
          const std::vector<double>& pre_item_embedding = pre_item_emb_iter->second;
          for (size_t j = 0; j < pre_item_embedding.size(); ++j) {
            inner_value += this_item_embedding[j] * pre_item_embedding[j];
          }
        }
        pos_gap = GetGapMpa(pos_index, pos_gap, offset);
        const double this_score = GetGapWeight(pos_gap) * inner_value;
        if (this_score > threshold) {
          ++num;
        }
      }
      return num;
    }

   private:
    std::deque<int64> data_;
    std::unordered_map<int64, std::deque<int64>> data_value_list_;
    const size_t window_size_;
    SoftDiversityWeightInfos soft_weight_infos_;
  };

  static int Log2(int x) {
    int res = 0;
    while (x != 0) {
      x >>= 1;
      res += 1;
    }
    return res;
  }
};

}  // namespace platform
}  // namespace ks
