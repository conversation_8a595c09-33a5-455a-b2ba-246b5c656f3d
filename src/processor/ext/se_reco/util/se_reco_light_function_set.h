#pragma once

#include <math.h>
#include <stdlib.h>
#include <sys/types.h>
#include <time.h>
#include <algorithm>
#include <codecvt>
#include <cstddef>
#include <iterator>
#include <locale>
#include <map>
#include <memory>
#include <queue>
#include <regex>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/encoding/base64.h"
#include "base/encoding/url_encode.h"
#include "base/strings/utf_char_iterator.h"
#include "base/time/timestamp.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/processor/ext/se_reco/util/se_reco_common_util.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "ks/reco/plateco-dsalog/ics/shop_customer_service/kess/dialog_nlg_service/model_serving.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "redis_proxy_client/redis_pipeline.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "redis_proxy_client/redis_reply.h"
#include "redis_proxy_client/redis_response.h"
#include "se/online_service/protos/common_embedding_service.kess.grpc.pb.h"
#include "se/online_service/protos/flow_operation_query.kess.grpc.pb.h"
#include "se/online_service/protos/shiva_strategy.kess.grpc.pb.h"
#include "se/txt2vid_se/base/singleton.h"
#include "se/txt2vid_se/combo_search/search/proto/combo_search_component.pb.h"
#include "se/txt2vid_se/combo_search/search/proto/combo_search_log.pb.h"
#include "se/txt2vid_se/combo_search/search/proto/combo_search_service.pb.h"
#include "se/txt2vid_se/jubao/proto/jubao_data_source.pb.h"
#include "se/txt2vid_se/model/kfs/src/proto/search_item.pb.h"
#include "se/txt2vid_se/query/proto/query_rewriter.pb.h"
#include "se/txt2vid_se/search/util/filter_manager.h"
#include "se/txt2vid_se/search/util/operator_data.h"
#include "se/txt2vid_se/splatform/proto/search_api.kess.grpc.pb.h"
#include "se/txt2vid_se/util/data_client/data_client.h"
#include "se/txt2vid_se/util/debug_logger/debug_logger.h"
#include "se/txt2vid_se/util/dict_manager/dict_manager.h"
#include "se/txt2vid_se/util/dict_manager/hashmap_data.h"
#include "se/txt2vid_se/util/dict_manager/hashset_data.h"
#include "se/txt2vid_se/ziya_sort/proto/3rd_proto/mmu_vcg_model.kess.grpc.pb.h"
#include "se/txt2vid_se/ziya_sort/proto/gys_placeholder.pb.h"
#include "se/txt2vid_se/ziya_sort/proto/model_serving_stream_service.grpc.pb.h"
#include "se/txt2vid_se/ziya_sort/proto/user_cache_service.kess.grpc.pb.h"
#include "se/txt2vid_se/ziya_sort/src/base/resource_manager.h"
#include "se/txt2vid_se/ziya_sort/src/executor/recall/guess_you_search/newhot_realtime_insert_recall_executor.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/aladdin_platform/aladdin_intervene_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/author_live_time_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/first_view_force_show_multi_pv_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/first_view_force_show_options_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/first_view_force_show_pv_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/flow_operation_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/guess_you_search/operation_insert_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/guess_you_search/operation_refer_data_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/icon_label_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/operation_query2icon_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/kconf_timer_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/newhot_query_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/platform_intervention/intervention_black_data_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/platform_intervention/intervention_white_data_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/sugg/author_live_info_reloader.h"
#include "se/txt2vid_se/ziya_sort/src/utils/markdown_util.h"
#include "se/txt2vid_se/ziya_sort/src/utils/proto_tool_by_brpc.h"
#include "se/txt2vid_se/ziya_sort/src/utils/sensitive_util.h"
#include "infra/perfutil/src/perfutil/perfutil.h"
#include "se/txt2vid_se/ziya_sort/src/reloader/chinese_stroke_reloader.h"
#include "se/online_service/protos/search_feedback_inner.pb.h"
#include "se/txt2vid_se/ziya_sort/src/utils/sugg_utils.h"

using ks::search::ziya_sort::JsonValuePtr;
using RetrievalInfo = ::ks::search::recall_profile::RetrievalInfo;

DEFINE_string(pymk_proxy_grpc_service, "grpc_searchShivaFeatureService", "");

namespace ks {
namespace platform {

namespace pre_push_type {
enum PRE_PUSH_TYPE {
  INVALID_QUERY = -10000,
  RETRY_MESSAGE = -10001,
  WHITE_ANSWER = -10002,
  RICH_ANSWER = -10003,
  WEATHER_KBOX = -10004,
  SENSITIVE_WHITE = -10005,
  USER_PARENT = -10006,
  PHOTO_PARENT = -10007,
  FRESH_INTENT_PHOTO_PARENT = -10008,
  HP_INDEX = -10009,
  KBQA_ANSWER = -10010,
  QUERY_REJECT = -10011,
  TRAVEL_INDEX = -10012,
  GENERAL_KBOX = -10013,
  CALCULATOR_ANSWER = -10014,
  WENAN_PROMPT_ANSWER = -10016,
  IP_SEXY_QUERY_REJECT = -10017,
  TEXT_GENERATE_IMAGE = -10018,
  EMPTY = 0
};
}  // namespace pre_push_type

struct ZiyaItem {
  std::string item_id;
  double score;
  int64_t author;
  std::string source;
  std::string recall_reason;
  int64_t upload_timestamp;

  // 特殊字段
  std::string goods_live_a2a_info;
};

using ItemPtr = std::shared_ptr<ZiyaItem>;
struct item_cmp {
  bool operator()(const ItemPtr &a, const ItemPtr &b) {
    if (a->score != b->score) {
      return a->score > b->score;
    } else {
      return a->upload_timestamp > b->upload_timestamp;
    }
  }
};
using PriorityQueue = std::priority_queue<ItemPtr, std::vector<ItemPtr>, item_cmp>;

class SeRecoLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  SeRecoLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(TestLane);
    REGISTER_LIGHT_FUNCTION(BuildStrategyInfo);
    REGISTER_LIGHT_FUNCTION(GetUserIdsFromUserFollowList);
    REGISTER_LIGHT_FUNCTION(GetUserIdsFromUserRealtimeFollowList);
    REGISTER_LIGHT_FUNCTION(GetUserIdsFromUserClickList);
    REGISTER_LIGHT_FUNCTION(GetHighConsumeBigVUserIdList);
    REGISTER_LIGHT_FUNCTION(CalcScoreBySearchedQueries);
    REGISTER_LIGHT_FUNCTION(CalcScoreByMultiFromTriggerQueries);
    REGISTER_LIGHT_FUNCTION(ParseJsonArrayToCommonAttr);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdList);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdListV2);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdListV3);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdListV4);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdListV5);
    REGISTER_LIGHT_FUNCTION(GetUnwatchedAuthorIdListV6);
    REGISTER_LIGHT_FUNCTION(GoodsLiveAuthor2Author);
    REGISTER_LIGHT_FUNCTION(GetPrefixStroke);
    REGISTER_LIGHT_FUNCTION(GetAdsLiveRecallQueryFromKconf);
    REGISTER_LIGHT_FUNCTION(GetPhotoAndScoreForP2Q);
    REGISTER_LIGHT_FUNCTION(GetNewHotLowQueryList);
    REGISTER_LIGHT_FUNCTION(ParseLiveStreamFeatureBatchResponse);
    REGISTER_LIGHT_FUNCTION(GetReRankResultScore);
    REGISTER_LIGHT_FUNCTION(CountItemsIdAndSource);
    REGISTER_LIGHT_FUNCTION(CompleteTrendingDataSource);
    REGISTER_LIGHT_FUNCTION(GetFirstTwoPrefixQuery);
    REGISTER_LIGHT_FUNCTION(SourceCntByLimit);
    REGISTER_LIGHT_FUNCTION(SourceCntByLimitNew);
    REGISTER_LIGHT_FUNCTION(SimilarityDeduplicate);
    REGISTER_LIGHT_FUNCTION(ItemIsABABPattern);
    REGISTER_LIGHT_FUNCTION(CompleteRankTrendingDataSource);
    REGISTER_LIGHT_FUNCTION(GetBoostReferV3Candidates);
    REGISTER_LIGHT_FUNCTION(CalcCommerceMixRankScore);
    REGISTER_LIGHT_FUNCTION(EnrichCommerceItemLabel);
    REGISTER_LIGHT_FUNCTION(MergeCommerceAndSearchItems);
    REGISTER_LIGHT_FUNCTION(GetOperationFVFSInfo);
    REGISTER_LIGHT_FUNCTION(OperationFVFSSetPV);
    REGISTER_LIGHT_FUNCTION(OperationFVFSGetPV);
    REGISTER_LIGHT_FUNCTION(GetFVFSUserViewedData);
    REGISTER_LIGHT_FUNCTION(SetFVFSUserViewedData);
    REGISTER_LIGHT_FUNCTION(GetReferOperationStatus);
    REGISTER_LIGHT_FUNCTION(GetFlowOperationInfo);
    REGISTER_LIGHT_FUNCTION(GetOperationInsertInfo);
    REGISTER_LIGHT_FUNCTION(HitSourceAttr);
    REGISTER_LIGHT_FUNCTION(HitSensitiveAttr);
    REGISTER_LIGHT_FUNCTION(AntispamSessionHistoryParams);
    REGISTER_LIGHT_FUNCTION(EnrichUserMetaByUserId);
    REGISTER_LIGHT_FUNCTION(EnrichPymkRecallFromShiva);
    REGISTER_LIGHT_FUNCTION(SetIconNameForOperationQueries);
    REGISTER_LIGHT_FUNCTION(EnrichIconLabelAttrs);
    REGISTER_LIGHT_FUNCTION(OverwriteIconTextWithKconfOperation);
    REGISTER_LIGHT_FUNCTION(SetIconNameForYellowCartQueries);
    REGISTER_LIGHT_FUNCTION(ParseSearchQueryEmbeddingProtobufList);
    REGISTER_LIGHT_FUNCTION(ParseSearchQueryEmbeddingProtobufListNew);
    REGISTER_LIGHT_FUNCTION(ParseSearchQueryEmbeddingProtobufListGeneral);
    REGISTER_LIGHT_FUNCTION(ParseSearchQueryEmbeddingProtobufListNewHot);
    REGISTER_LIGHT_FUNCTION(ParseRecAfterSearchQueryEmbeddingProtobufList);
    REGISTER_LIGHT_FUNCTION(StringHashCodeLikeJava);
    REGISTER_LIGHT_FUNCTION(ModifyReferIDAndLiveId);
    REGISTER_LIGHT_FUNCTION(ParseClientRealActions);
    REGISTER_LIGHT_FUNCTION(ShapedQueryProcess);
    REGISTER_LIGHT_FUNCTION(BoostGnnRecallPosition);
    REGISTER_LIGHT_FUNCTION(AuthorNotLiveFilter);
    REGISTER_LIGHT_FUNCTION(EncodeUrlOfAdQuerySourceTrace);
    REGISTER_LIGHT_FUNCTION(PackSignalParams);
    REGISTER_LIGHT_FUNCTION(SetIconForCoupon);
    REGISTER_LIGHT_FUNCTION(CountSourceItems);
    REGISTER_LIGHT_FUNCTION(CountSourcePrerankZeroScoreItems);
    REGISTER_LIGHT_FUNCTION(GlobalBackupRecall);
    REGISTER_LIGHT_FUNCTION(BoostCommerceQuery);
    REGISTER_LIGHT_FUNCTION(GetItemUTF8CharNum);
    REGISTER_LIGHT_FUNCTION(GetItemHashID);
    REGISTER_LIGHT_FUNCTION(GetUserFollowHashID);
    REGISTER_LIGHT_FUNCTION(GetUserFollowHashID2);
    REGISTER_LIGHT_FUNCTION(EnrichFromInterventionBlackData);
    REGISTER_LIGHT_FUNCTION(EnrichStressLabel);
    REGISTER_LIGHT_FUNCTION(CalcRSRelevanceScore);
    REGISTER_LIGHT_FUNCTION(SelectedPositionOperationData);
    REGISTER_LIGHT_FUNCTION(InsertOperationData);
    REGISTER_LIGHT_FUNCTION(FirstViewForceShowOperationData);
    REGISTER_LIGHT_FUNCTION(GetVisionSearchDirtyData);
    REGISTER_LIGHT_FUNCTION(GetKboxProtoSerializeString);
    REGISTER_LIGHT_FUNCTION(EnrichNormalizedQuery);
    REGISTER_LIGHT_FUNCTION(EnrichPinYinQuery);
    REGISTER_LIGHT_FUNCTION(EnrichPinYinItem);
    REGISTER_LIGHT_FUNCTION(EnrichItemPrefix2);
    REGISTER_LIGHT_FUNCTION(EnrichAdjacentPinYinQueryList);
    REGISTER_LIGHT_FUNCTION(EnrichMatchedSearchQuery);
    REGISTER_LIGHT_FUNCTION(EnrichUser2GameMatchedQuery);
    REGISTER_LIGHT_FUNCTION(EnrichUser2GamePlayMatchedQuery);
    REGISTER_LIGHT_FUNCTION(EnrichAuthorLiveInfo);
    REGISTER_LIGHT_FUNCTION(ParseSEsResponseForSug);
    REGISTER_LIGHT_FUNCTION(SplitCorrectionValue);
    REGISTER_LIGHT_FUNCTION(EnrichPbDataSourceSet);
    REGISTER_LIGHT_FUNCTION(EnrichPoiDistance);
    REGISTER_LIGHT_FUNCTION(EnrichPoiBrandName);
    REGISTER_LIGHT_FUNCTION(SugEnrichSearcherFilterFlag);
    REGISTER_LIGHT_FUNCTION(ParseDocBasicInfo);
    REGISTER_LIGHT_FUNCTION(SplitPhotoEmbedingList);
    REGISTER_LIGHT_FUNCTION(EnrichPrefixInfoItemAttr);
    REGISTER_LIGHT_FUNCTION(EnrichSugSubQuery);
    REGISTER_LIGHT_FUNCTION(EnrichAdjustGoodsWeight);
    REGISTER_LIGHT_FUNCTION(EnrichBendiWeight);
    REGISTER_LIGHT_FUNCTION(EnrichBoostReferSrc);
    REGISTER_LIGHT_FUNCTION(EnrichBoostCommerceItemsV2);
    REGISTER_LIGHT_FUNCTION(EnrichDuplicateSupress);
    REGISTER_LIGHT_FUNCTION(ActivityGoodsEnrichFromInterventionBlackData);
    REGISTER_LIGHT_FUNCTION(EnrichLongNormQueryAndReferQuery);
    REGISTER_LIGHT_FUNCTION(EnrichQAReferJson);
    REGISTER_LIGHT_FUNCTION(SplitMultiReferText);
    REGISTER_LIGHT_FUNCTION(HitAladdinIntervenePlatform);
    REGISTER_LIGHT_FUNCTION(CheckFullMarkdown);
    REGISTER_LIGHT_FUNCTION(BoostNewQueryScore);
    REGISTER_LIGHT_FUNCTION(HandleMultiQAComboSearchResponse);
    REGISTER_LIGHT_FUNCTION(HandleMultiQAQueryRewriterResponse);
    REGISTER_LIGHT_FUNCTION(MultiQATransferWeatherString);
    REGISTER_LIGHT_FUNCTION(QueryAnswerRSKeywordNormalize);
    REGISTER_LIGHT_FUNCTION(GetSubWstr);
    REGISTER_LIGHT_FUNCTION(EnrichSuggSegmentDedup);
    REGISTER_LIGHT_FUNCTION(IsEndByChinese);
    REGISTER_LIGHT_FUNCTION(QaUserParentKeywordNormalizeExtrachUsername);
    REGISTER_LIGHT_FUNCTION(NormalizeLongSearchedHistory);
    REGISTER_LIGHT_FUNCTION(SuggFormatQueryEmbedding);
    REGISTER_LIGHT_FUNCTION(SuggFormatPhotoEmbedding);
    REGISTER_LIGHT_FUNCTION(NormalizeRequestQuery);
    REGISTER_LIGHT_FUNCTION(GetQueryInfoCnt);
    REGISTER_LIGHT_FUNCTION(EnrichQueryFilterInfo);
    REGISTER_LIGHT_FUNCTION(ParseExpressionKey);
    REGISTER_LIGHT_FUNCTION(AdjustSugPrerankInput);
    REGISTER_LIGHT_FUNCTION(AdjustSugPrerankResultScore);
    REGISTER_LIGHT_FUNCTION(QaTextGenerateImage);
    REGISTER_LIGHT_FUNCTION(CheckUnProcessedAnswerHitFlag);
    REGISTER_LIGHT_FUNCTION(QueryAnswerTruncateHistory);
    REGISTER_LIGHT_FUNCTION(QueryAnswerIPRs);
    REGISTER_LIGHT_FUNCTION(EnrichMatchedHistoryQuery);
    REGISTER_LIGHT_FUNCTION(EnrichHotUserGenderAge);
    REGISTER_LIGHT_FUNCTION(SuggJubaoMultiScoreAdjust);
    REGISTER_LIGHT_FUNCTION(EmptyLightFunction);
    REGISTER_LIGHT_FUNCTION(SuggJubaoMultiScoreHungarianAdjust);
    REGISTER_LIGHT_FUNCTION(SuggJubaoMultiFinalScoreAdjust);
    REGISTER_LIGHT_FUNCTION(SuggJubaoMultiFinalScoreHungarianAdjust);
    REGISTER_LIGHT_FUNCTION(CalcItemQueryRelevanceScore);
    REGISTER_LIGHT_FUNCTION(CalcItemPhotoRelevanceScore);
    REGISTER_LIGHT_FUNCTION(CalcQuerisItemsRelevanceScore);
    REGISTER_LIGHT_FUNCTION(CrossSessionInfoParseFromString);
    REGISTER_LIGHT_FUNCTION(SearchUnifiedRecallPerf);
    REGISTER_LIGHT_FUNCTION(GetSugReferQueryAdvanceFromRedis);
    REGISTER_LIGHT_FUNCTION(ParseFromSugNegativeFeedback);
    REGISTER_LIGHT_FUNCTION(GetPlatformDebugHandlePtr);
    REGISTER_LIGHT_FUNCTION(Refer2QueryListParseString);
    REGISTER_LIGHT_FUNCTION(MultiRefer2QueryListParseString);
    REGISTER_LIGHT_FUNCTION(Refer2QueryListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(MultiRefer2QueryListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(Refer2NextQueryListParseString);
    REGISTER_LIGHT_FUNCTION(Refer2NextQueryListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(P2QRecallOutputMerge);
    REGISTER_LIGHT_FUNCTION(MultiP2QRecallOutputMerge);
    REGISTER_LIGHT_FUNCTION(SugMultiRefer2Query);
    REGISTER_LIGHT_FUNCTION(His2NextQueryListParseString);
    REGISTER_LIGHT_FUNCTION(His2NextQueryListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(SugLongSearchedQueryList);
    REGISTER_LIGHT_FUNCTION(SugHisPrefixMatch);
    REGISTER_LIGHT_FUNCTION(SugRefer2QueryPrefixMatch);
    REGISTER_LIGHT_FUNCTION(SugRefer2Query2QueryPrefixMatch);
    REGISTER_LIGHT_FUNCTION(Refer2NextHashtagListParseString);
    REGISTER_LIGHT_FUNCTION(Refer2NextHashtagListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(SugRefer2Query2HashtagPrefixMatch);
    REGISTER_LIGHT_FUNCTION(His2HashtagListParseString);
    REGISTER_LIGHT_FUNCTION(His2HashtagListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(SugHis2HashtagPrefixMatch);
    REGISTER_LIGHT_FUNCTION(His2Hashtag2HashtagListParseString);
    REGISTER_LIGHT_FUNCTION(His2TopkHashtagMerge);
    REGISTER_LIGHT_FUNCTION(His2Hashtag2HashtagListWriteToRedis);
    REGISTER_LIGHT_FUNCTION(SugHis2Hashtag2HashtagPrefixMatch);
    REGISTER_LIGHT_FUNCTION(MultiP2HashtagRecallOutputMerge);
  }

  static bool TestLane(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    std::string rpc_name = context.GetStringCommonAttr("rpc_name").value_or("").data();
    auto *request = const_cast<::ks::platform::CommonRecoRequest *>(
      context.GetProtoMessagePtrCommonAttr<::ks::platform::CommonRecoRequest>(
          "pusher_push_request"));

    auto current_lane = ::ks::infra::kenv::ServiceMeta::GetLane();
    CL_LOG(INFO) << "testLane rpc_name: " << rpc_name;
    CL_LOG(INFO) << "testLane lane: " << current_lane;

    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client_old =
        ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("search", "search-kchat-pusher-rpc"));
    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client_new =
        ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("search", "search-kchat-pusher-v2-rpc"));
    // push 操作 - 泳道已自动传入
    ks::kess::rpc::grpc::Options options;
    // 查看压测标识
    auto stress_test_biz_name =
      ::ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName();
    CL_LOG(INFO) << "testLane stress_test_biz_name: " << stress_test_biz_name;

    ::ks::infra::kenv::ServiceMeta::SetLane(current_lane);
    ::ks::platform::CommonRecoResponse response;
    options.SetTimeout(std::chrono::milliseconds(25000));
    if (rpc_name == "search-kchat-pusher-rpc") {
      auto status = client_old->All()->SelectOne()->Stub<ks::platform::kess::CommonRecoLeafService::Stub>()
        .Recommend(options, *request, &response);
    } else {
      auto status = client_new->All()->SelectOne()->Stub<ks::platform::kess::CommonRecoLeafService::Stub>()
        .Recommend(options, *request, &response);
    }

    std::string serialized_msg;
    response.SerializeToString(&serialized_msg);
    context.SetStringCommonAttr("serialized_msg", serialized_msg);
    return true;
  }

  static bool BuildStrategyInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    ::Json::FastWriter jw;
    auto pre_type = context.GetIntCommonAttr("pre_push_type").value_or(0);
    auto weather_kbox_city = context.GetStringCommonAttr("weather_kbox_city").value_or("").data();
    auto city = context.GetStringCommonAttr("city").value_or("").data();
    auto prompt_type = context.GetStringCommonAttr("promptType").value_or("").data();
    auto prompt_word = context.GetStringCommonAttr("promptWord").value_or("").data();
    auto kbox_content = context.GetStringCommonAttr("kbox_content").value_or("").data();

    ::Json::Value strategy_info_node;
    if (pre_push_type::WEATHER_KBOX == pre_type) {
      // 天气 KBOX 结果
      ::Json::Value weather_node;
      weather_node["city"] = std::string(weather_kbox_city) == "本地" ? city : weather_kbox_city;
      strategy_info_node["weather"] = weather_node;
      strategy_info_node["kbox_type"] = "WEATHER_KBOX";
    } else if (pre_push_type::GENERAL_KBOX == pre_type) {
      // 通用 KBOX 结果
      ::Json::Reader reader;
      ::Json::Value general_kbox_json;
      reader.parse(kbox_content, general_kbox_json);
      std::string kbox_type = "";
      if (general_kbox_json.isObject() && general_kbox_json.isMember("item_name") &&
        general_kbox_json["item_name"].isString()) {
        kbox_type = general_kbox_json["item_name"].asString();
      }
      strategy_info_node["kbox_type"] = kbox_type;
    } else if (pre_push_type::TEXT_GENERATE_IMAGE == pre_type) {
      strategy_info_node["promptType"] = prompt_type;
      strategy_info_node["promptWord"] = prompt_word;
    }
    if (!strategy_info_node.isNull()) {
      context.SetStringCommonAttr("strategy_info_str", jw.write(strategy_info_node));
    } else {
      context.SetStringCommonAttr("strategy_info_str", "");
    }
    return true;
  }

  static bool ParseExpressionKey(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto un_processed_answer = std::string(context.GetStringCommonAttr("un_processed_answer").value_or(""));
    auto large_key = std::string(context.GetStringCommonAttr("large_expression_key").value_or(""));
    auto cur_offset = context.GetIntCommonAttr("current_offset").value_or(0);
    auto max_expression_offset = context.GetIntCommonAttr("max_expression_offset").value_or(10);
    int64_t large_pos = 0;

    std::regex expression_reg("\\[[\u4e00-\u9fa5a-zA-Z0-9]{1,12}\\]");
    if (large_key.size() > 0) {
      un_processed_answer = std::regex_replace(un_processed_answer, expression_reg, "");
      context.SetIntCommonAttr("need_push_expression", 0);
      context.SetStringCommonAttr("un_processed_answer", un_processed_answer);
      return true;
    }
    std::shared_ptr<::Json::Value> emoji_mapping_ptr =
        ks::search::ziya_sort::GlobalKconf::get_kconf_value<std::shared_ptr<::Json::Value>>(
            "se.query_answer.chat_ip_emoji_mapping", std::make_shared<::Json::Value>());
    std::string::const_iterator iter = un_processed_answer.begin();
    std::string::const_iterator iter_end = un_processed_answer.end();
    std::smatch result;
    if (std::regex_search(iter, iter_end, result, expression_reg)) {
      std::string match_text = result.str();
      if (!(emoji_mapping_ptr->isObject() && emoji_mapping_ptr->isMember(match_text))) {
        un_processed_answer = std::regex_replace(un_processed_answer, expression_reg, "");
        context.SetIntCommonAttr("need_push_expression", 0);
        context.SetStringCommonAttr("un_processed_answer", un_processed_answer);
        return true;
      }
      if (match_text.size() > 2 && '[' == match_text.front() && ']' == match_text.back()) {
        int64_t pattern_offset = cur_offset - ((un_processed_answer.size() + 4) / 3) + result.position() + 0;
        if (pattern_offset < max_expression_offset) {
          large_key = match_text.substr(1, match_text.size() - 2);
          large_pos = un_processed_answer.find(match_text);
        }
      }
    }
    un_processed_answer = std::regex_replace(un_processed_answer, expression_reg, "");
    context.SetIntCommonAttr("need_push_expression", large_key.size() > 0 ? 1 : 0);
    context.SetStringCommonAttr("un_processed_answer", un_processed_answer);
    context.SetStringCommonAttr("large_expression_key", large_key);
    context.SetIntCommonAttr("large_expression_pos", large_pos);
    return true;
  }

  static bool GetUserIdsFromUserFollowList(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (user_info == nullptr) {
      CL_LOG(WARNING) << "user info ptr is nullptr";
      return true;
    }

    std::unordered_set<int64_t> dedup;
    std::vector<std::string> str_aids;
    str_aids.reserve(user_info->follow_list_size());

    for (int i = 0; i < user_info->follow_list_size(); ++i) {
      const auto &aid = user_info->follow_list(i).user().id();
      if (aid > 0 && dedup.count(aid) == 0) {
        dedup.emplace(aid);
        str_aids.emplace_back(::base::Int64ToString(aid));
      }
    }

    context.SetStringListCommonAttr("user_follow_uid_list", std::move(str_aids));
    return true;
  }

  static bool GetUserIdsFromUserRealtimeFollowList(const CommonRecoLightFunctionContext &context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (user_info == nullptr) {
      CL_LOG(WARNING) << "user info ptr is nullptr";
      return true;
    }

    std::unordered_set<int64_t> dedup;
    std::vector<std::string> str_aids;
    str_aids.reserve(user_info->realtime_follow_list_size());

    for (int i = 0; i < user_info->realtime_follow_list_size(); ++i) {
      const auto &aid = user_info->realtime_follow_list(i);
      if (aid > 0 && dedup.count(aid) == 0) {
        dedup.emplace(aid);
        str_aids.emplace_back(::base::Int64ToString(aid));
      }
    }

    context.SetStringListCommonAttr("user_realtime_follow_uid_list", std::move(str_aids));
    return true;
  }

  static bool GetUserIdsFromUserClickList(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (user_info == nullptr) {
      CL_LOG(WARNING) << "user info ptr is nullptr";
      return true;
    }
    if (!user_info->has_user_profile_v1()) {
      CL_LOG(WARNING) << "no user profile v1";
      return true;
    }

    std::unordered_set<int64_t> dedup;
    std::vector<std::string> str_aids;
    const auto &user_profile_v1 = user_info->user_profile_v1();
    str_aids.reserve(user_profile_v1.click_list_size());

    for (int i = 0; i < user_profile_v1.click_list_size(); ++i) {
      const auto &click_item = user_profile_v1.click_list(i);
      const int64_t aid = click_item.author_id();
      if (aid > 0 && dedup.count(aid) == 0) {
        dedup.emplace(aid);
        str_aids.emplace_back(::base::Int64ToString(aid));
      }
    }

    context.SetStringListCommonAttr("user_click_uid_list", std::move(str_aids));
    return true;
  }

  static bool GetHighConsumeBigVUserIdList(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto follow_uids = context.GetStringListCommonAttr("user_follow_uid_list");
    auto realtime_follow_uids = context.GetStringListCommonAttr("user_realtime_follow_uid_list");
    auto item_id_str_attr = context.GetStringItemAttr("item_id");

    using DedupMapType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupMapType follow_uids_dedup;
    DedupMapType realtime_follow_uids_dedup;

    if (follow_uids) {
      follow_uids_dedup.insert(follow_uids->begin(), follow_uids->end());
    }
    if (realtime_follow_uids) {
      realtime_follow_uids_dedup.insert(realtime_follow_uids->begin(), realtime_follow_uids->end());
    }
    if (follow_uids_dedup.size() == 0 && realtime_follow_uids_dedup.size() == 0) {
      CL_LOG(INFO) << "user follow is empty";
      return true;
    }

    std::vector<std::string> high_consume_uid_list;
    std::vector<double> high_consume_score_list;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_str = item_id_str_attr(result).value_or("");
      VLOG(10) << "item_id: " << std::string(item_id_str.data(), item_id_str.size())
               << " score: " << result.score;
      if (item_id_str.empty()) return;
      if (realtime_follow_uids_dedup.find(item_id_str) != realtime_follow_uids_dedup.end()) {
        high_consume_score_list.push_back(result.score * 2);
        high_consume_uid_list.push_back(std::string(item_id_str.data(), item_id_str.size()));
      } else if (follow_uids_dedup.find(item_id_str) != follow_uids_dedup.end()) {
        high_consume_score_list.push_back(result.score);
        high_consume_uid_list.push_back(std::string(item_id_str.data(), item_id_str.size()));
      }
    });

    if (high_consume_uid_list.size() != high_consume_score_list.size()) {
      CL_LOG(ERROR) << "size miss match";
      return false;
    }

    context.SetStringListCommonAttr("user_high_consume_uid_list", std::move(high_consume_uid_list));
    context.SetDoubleListCommonAttr("user_high_consume_score_list", std::move(high_consume_score_list));
    return true;
  }

  static bool CalcScoreByQueryAndScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end,
                                       const std::string &query_list_name, const std::string &score_list_name,
                                       bool is_update_default_score) {
    auto searched_queries_query =
        context.GetStringListCommonAttr(query_list_name).value_or(std::vector<absl::string_view>());
    auto searched_queries_his_score =
        context.GetDoubleListCommonAttr(score_list_name).value_or(absl::Span<const double>());
    if (searched_queries_query.size() != searched_queries_his_score.size() ||
        searched_queries_his_score.empty()) {
      return false;
    }
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> query_his_score_map;

    for (size_t i = 0; i < searched_queries_query.size(); ++i) {
      query_his_score_map[searched_queries_query.at(i)] = searched_queries_his_score.at(i);
    }

    auto recall_reason_str_attr = context.GetStringItemAttr("item_recall_reason");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_set_func = context.SetDoubleItemAttr("item_score");
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto recall_reason_str = recall_reason_str_attr(result).value_or("");
      auto item_id_attr_str = item_id_attr(result).value_or("");
      if (recall_reason_str.empty()) return;
      auto item_score = item_score_attr(result).value_or(0.0);
      double score1 = 1;
      double score2 = item_score;
      double new_item_score = 0;
      if (query_his_score_map.find(recall_reason_str) != query_his_score_map.end()) {
        if (is_update_default_score) {
          score1 = query_his_score_map[recall_reason_str] + 1;
        } else {
          score1 = query_his_score_map[recall_reason_str];
        }
        new_item_score = score1 * score2;
      } else {
        new_item_score = score2;
      }
      item_score_set_func(result, new_item_score);
      VLOG(10) << "item_id:" << std::string(item_id_attr_str.data(), item_id_attr_str.size())
               << ",recall_reason: " << std::string(recall_reason_str.data(), recall_reason_str.size())
               << ",score1: " << std::to_string(score1) << ",score2: " << std::to_string(score2)
               << ",new_item_score:" << std::to_string(new_item_score);
    });
    return true;
  }

  static bool CalcScoreByMultiFromTriggerQueries(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    return CalcScoreByQueryAndScore(context, begin, end, "trigger_queries_ori", "trigger_queries_score",
                                    true);
  }

  static bool CalcScoreBySearchedQueries(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    return CalcScoreByQueryAndScore(context, begin, end, "searched_queries_query",
                                    "searched_queries_his_score", false);
  }

  static bool ParseJsonArrayToCommonAttr(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto json_value_attr = context.GetStringListCommonAttr("user_goods_query_score_json")
                               .value_or(std::vector<absl::string_view>());
    auto user_click_str_list_attr = context.GetStringListCommonAttr("user_click_goods_str_list")
                                        .value_or(std::vector<absl::string_view>());
    if (json_value_attr.size() != user_click_str_list_attr.size()) {
      CL_LOG(ERROR) << "size miss match";
      return false;
    }
    std::vector<std::string> json_query;
    std::vector<double> json_rank_score;
    std::vector<std::string> recall_reason;
    for (size_t i = 0; i < json_value_attr.size(); ++i) {
      if (json_value_attr[i].empty()) continue;
      ::Json::Reader json_reader;
      ::Json::Value json_array;
      try {
        if (json_reader.parse(std::string(json_value_attr[i].data(), json_value_attr[i].size()),
                              json_array) &&
            json_array.isArray()) {
          for (int j = 0; j < json_array.size() && j < 3; j++) {
            std::string query = json_array[j].isMember("query") ? json_array[j]["query"].asString() : "";
            double score =
                json_array[j].isMember("rank_score") ? json_array[j]["rank_score"].asDouble() : 0.0;
            json_query.emplace_back(query);
            json_rank_score.emplace_back(score);
            recall_reason.emplace_back(user_click_str_list_attr[i]);
          }
        }
      } catch (std::exception &e) {
        CL_LOG(ERROR) << "parse json_value_attr[i] error:" << e.what();
      }
    }
    context.SetStringListCommonAttr("user_goods_json_query", std::move(json_query));
    context.SetDoubleListCommonAttr("user_goods_json_rank_score", std::move(json_rank_score));
    context.SetStringListCommonAttr("user_goods_json_reason", std::move(recall_reason));
    return true;
  }

  static bool GetUnwatchedAuthorIdList(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      std::vector<std::string> photo_ids;
      std::vector<double> scores;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool GetUnwatchedAuthorIdListV2(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_v2_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_v2_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (author_id_list_ptr.size() == 0) {
      return true;
    }
    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      std::vector<std::string> photo_ids;
      std::vector<double> scores;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_v2_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_v2_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool GetUnwatchedAuthorIdListV3(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_v3_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_v3_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (author_id_list_ptr.size() == 0) {
      return true;
    }
    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      // std::vector<std::string> photo_ids;
      // std::vector<double> scores;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_v3_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_v3_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool GetUnwatchedAuthorIdListV4(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_v4_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_v4_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (author_id_list_ptr.size() == 0) {
      return true;
    }
    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      // std::vector<std::string> photo_ids;
      // std::vector<double> scores;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_v4_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_v4_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool GetUnwatchedAuthorIdListV5(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_v5_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_v5_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (author_id_list_ptr.size() == 0) {
      return true;
    }
    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      // std::vector<std::string> photo_ids;
      // std::vector<double> scores;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_v5_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_v5_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool GetUnwatchedAuthorIdListV6(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_list_ptr = context.GetStringListCommonAttr("photo_update_v6_author_id_list")
                                  .value_or(std::vector<absl::string_view>());
    auto author_score_list_ptr = context.GetDoubleListCommonAttr("photo_update_v6_author_score_list")
                                     .value_or(absl::Span<const double>());
    auto user_click_pids_ptr = context.GetStringListCommonAttr("reco_click_list_photo_id_str");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> user_click_pids_dup;

    if (author_id_list_ptr.size() == 0) {
      return true;
    }
    if (user_click_pids_ptr) {
      user_click_pids_dup.insert(user_click_pids_ptr->begin(), user_click_pids_ptr->end());
    }

    auto redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc("searchRs", 20);
    if (!redis_client) {
      return true;
    }
    ::ks::infra::RedisPipeline pipeline;
    std::vector<std::vector<std::string>> vals;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size(); i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      // std::vector<std::string> photo_ids;
      // std::vector<double> scores ;
      std::string key = absl::StrFormat("apl_%s", author_id);
      pipeline.ZGetMembersRevByIndex(key, 0, 10)
          .Then([&vals](::ks::infra::RedisErrorCode err_code, const std::vector<std::string> &val) {
            vals.push_back(val);
          });
    }
    auto code = redis_client->Exec(&pipeline);
    if (code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      CL_LOG(ERROR) << "execute pipeline command failed, code: " << code;
      return false;
    }

    std::vector<std::string> unwatch_author_ids;
    std::vector<double> unwatch_author_scores;
    for (int i = 0; i < author_id_list_ptr.size() && i < author_score_list_ptr.size() && i < vals.size();
         i++) {
      std::string author_id = absl::StrFormat("%s", author_id_list_ptr.at(i));
      double author_score = author_score_list_ptr.at(i);
      std::vector<std::string> photo_list = vals[i];
      bool un_watch_flag = true;
      for (const auto &photo_id : photo_list) {
        if (user_click_pids_dup.find(photo_id) != user_click_pids_dup.end()) {
          un_watch_flag = false;
          break;
        }
      }
      if (un_watch_flag && photo_list.size() > 0) {
        unwatch_author_ids.emplace_back(author_id);
        unwatch_author_scores.emplace_back(author_score);
      }
    }
    context.SetStringListCommonAttr("photo_update_v6_author_id_list_filtered", std::move(unwatch_author_ids));
    context.SetDoubleListCommonAttr("photo_update_v6_author_score_list_filtered",
                                    std::move(unwatch_author_scores));
    return true;
  }

  static bool reader_redis(const std::vector<std::string> &keys, std::vector<std::string> *values) {
    if (keys.size() == 0) return false;
    static const std::string cluster("searchRs");  // TODO(caohongjin): 从 attr 获取
    static const int redis_timeout_ms = 20;
    mmu::util::DataClientWrapper data_client(cluster);
    if (nullptr == data_client) {
      CL_LOG(ERROR) << "failed to init data_client:[" << cluster << "],"
                    << "timeout:[" << redis_timeout_ms << "]";
      return false;
    }
    data_client->SetUseLocalCache(true);
    if (!data_client->BatchGet(keys, *values, redis_timeout_ms)) {
      CL_LOG(ERROR) << "failed to BatchGet:" << keys[0];
      return false;
    }
    if (keys.size() != values->size()) {
      CL_LOG(ERROR) << "size is wrong:" << keys.size() << " != " << values->size();
      return false;
    }
    return true;
  }

  static inline ItemPtr create_search_item(const std::string &item_id, uint64_t author_id, float score,
                                           const std::string &source, uint64_t upload_timestamp,
                                           const std::string &recall_reason) {
    auto result = std::make_shared<ZiyaItem>();
    result->item_id = item_id;
    result->author = author_id;
    result->score = score;
    result->upload_timestamp = upload_timestamp;
    result->source = source;
    result->recall_reason = recall_reason;
    return result;
  }

  static void push_to_queue(const ItemPtr &item, const int result_num, PriorityQueue *item_queue) {
    if (!result_num || item_queue->size() < result_num) {
      item_queue->push(item);
    } else {
      if (item->score > item_queue->top()->score) {
        item_queue->pop();
        item_queue->push(item);
      } else if (item->score == item_queue->top()->score &&
                 item->upload_timestamp > item_queue->top()->upload_timestamp) {
        item_queue->pop();
        item_queue->push(item);
      }
    }
  }

  static bool GoodsLiveAuthor2Author(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    // TODO(caohongjin): 改成从 attr 获取 recall_num 和 single_recall_num
    int recall_num = 200;
    int single_recall_num = 50;
    std::string recall_name("gys_goods_live_a2a_swing_recall");
    auto user_click_live_author_id_list =
        context.GetStringListCommonAttr("user_click_live_author_id_str_list");
    std::unordered_map<std::string, int> author_trigger_map;
    if (user_click_live_author_id_list) {
      for (int i = 0; i < user_click_live_author_id_list->size(); ++i) {
        auto aid_sv = user_click_live_author_id_list->at(i);
        std::string aid(aid_sv.data(), aid_sv.size());
        author_trigger_map[aid] = i;
        VLOG(10) << "author_id: " << aid;
      }
    }

    std::vector<uint64_t> author_ids;
    std::unordered_map<std::string, std::pair<double, double>> author_map;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_recall_reason_attr = context.GetStringItemAttr("item_recall_reason");
    auto item_inner_index_attr = context.GetIntItemAttr("item_inner_index");
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id = item_id_attr(result).value_or("");
      auto item_score = item_score_attr(result).value_or(0);
      auto item_recall_reason = item_recall_reason_attr(result).value_or("");
      auto item_inner_index = item_inner_index_attr(result).value_or(0);
      std::string author1(item_recall_reason.data(), item_recall_reason.size());
      std::string author2(item_id.data(), item_id.size());
      double score = single_recall_num * recall_num - author_trigger_map[author1] * single_recall_num -
                     item_inner_index + 1;
      VLOG(10) << "author1: " << author1 << " author2: " << author2 << " score: " << score;

      try {
        uint64_t author_id = std::strtoull(author2.c_str(), nullptr, 10);
        author_ids.push_back(author_id);
        author_map[author2] = std::pair<double, double>(score, item_score);
      } catch (const std::exception &ex) {
        CL_LOG(ERROR) << "author score to double error:author_id=" << author2 << ", score=" << score;
      }
    }

    // 判断是否正在直播
    std::unordered_map<uint64_t, uint64_t> author_live_results;
    auto live_data_loader =
        Singleton<
            ::ks::search::ziya_sort::KconfTimerReloader<::ks::search::ziya_sort::AuthorLiveTimeData>>::get()
            ->get();
    if (live_data_loader == nullptr) {
      CL_LOG(ERROR) << "null live_data_loader";
      return false;
    }
    live_data_loader->get_author_time(author_ids, author_live_results);
    if (author_live_results.size() == 0) {
      CL_LOG(ERROR) << "author_live_results.size() == 0";
      return false;
    }

    // 从 redis 里获取 author 的名字
    std::vector<std::string> keys, values;
    std::vector<std::string> author_id_vec;
    for (const auto &author_live_result : author_live_results) {
      static const std::string key_prefix("uname_");  // TODO(caohongjin): 从 attr 获取
      keys.push_back(key_prefix + std::to_string(author_live_result.first));
      author_id_vec.push_back(std::to_string(author_live_result.first));
    }

    if (!reader_redis(keys, &values)) {
      CL_LOG(ERROR) << "reader_redis fail";
      return false;
    }

    PriorityQueue item_queue;
    for (int i = 0; i < values.size() && i < author_id_vec.size(); i++) {
      if (values[i].empty()) {
        continue;
      }
      std::string recall_query = "「" + values[i] + "」" + "正在直播中";
      if (author_map.find(author_id_vec[i]) != author_map.end()) {
        auto &p_score = author_map[author_id_vec[i]];
        auto search_item =
            create_search_item(recall_query, 0, p_score.first, recall_name, 0, author_id_vec[i]);
        push_to_queue(search_item, recall_num, &item_queue);

        // 落日志至 strategy_log
        std::string goods_live_a2a_info = std::to_string(p_score.second);
        // ctx->set_item_extra_param("goods_live_a2a_info", goods_live_a2a_info, search_item);
        search_item->goods_live_a2a_info = goods_live_a2a_info;
      }
    }
    std::vector<int64_t> item_id_int_list;
    std::vector<std::string> item_id_list;
    std::vector<double> item_score_list;
    std::vector<int64_t> item_author_list;
    std::vector<std::string> item_source_list;
    std::vector<std::string> item_recall_reason_list;
    std::vector<std::string> item_goods_live_a2a_info_list;

    item_id_int_list.reserve(item_queue.size());
    item_id_list.reserve(item_queue.size());
    item_score_list.reserve(item_queue.size());
    item_author_list.reserve(item_queue.size());
    item_source_list.reserve(item_queue.size());
    item_recall_reason_list.reserve(item_queue.size());
    item_goods_live_a2a_info_list.reserve(item_queue.size());

    while (!item_queue.empty()) {
      auto item = item_queue.top();
      item_queue.pop();
      uint64_t item_key = se_reco::GenerateItemKey(item->item_id, recall_name, std::to_string(item->author));
      item_id_int_list.push_back(item_key);
      item_id_list.push_back(item->item_id);
      item_score_list.push_back(item->score);
      item_author_list.push_back(item->author);
      item_source_list.push_back(item->source);
      item_recall_reason_list.push_back(item->recall_reason);
      item_goods_live_a2a_info_list.push_back(item->goods_live_a2a_info);
      VLOG(10) << "item_id: " << item->item_id << " score: " << item->score << " author: " << item->author
               << " source: " << item->source << " recall_reason: " << item->recall_reason
               << " goods_live_a2a_info: " << item->goods_live_a2a_info;
    }

    context.SetIntListCommonAttr("goods_live_a2a_item_id_int_list", std::move(item_id_int_list));
    context.SetStringListCommonAttr("goods_live_a2a_item_id_list", std::move(item_id_list));
    context.SetDoubleListCommonAttr("goods_live_a2a_item_score_list", std::move(item_score_list));
    context.SetIntListCommonAttr("goods_live_a2a_item_author_list", std::move(item_author_list));
    context.SetStringListCommonAttr("goods_live_a2a_item_source_list", std::move(item_source_list));
    context.SetStringListCommonAttr("goods_live_a2a_item_recall_reason_list",
                                    std::move(item_recall_reason_list));
    context.SetStringListCommonAttr("goods_live_a2a_item_goods_live_a2a_info_list",
                                    std::move(item_goods_live_a2a_info_list));

    return true;
  }

  static bool GetPrefixStroke(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto keyword = context.GetStringCommonAttr("query").value_or("");
    std::string keyword_str(keyword.data(), keyword.size());

    std::string keyword_stroke = "";
    int is_stroke = 0;
    auto stroke_data_loader =
        Singleton<
            ::ks::search::ziya_sort::KconfTimerReloader<::ks::search::ziya_sort::ChinenseStrokeData>>::get()
            ->get();
    if (stroke_data_loader == nullptr) {
      CL_LOG(ERROR) << "null stroke_data_loader";
      return false;
    }
    stroke_data_loader->get_prefix_stroke(keyword_str, keyword_stroke, is_stroke);

    context.SetIntCommonAttr("is_stroke", is_stroke);
    context.SetStringCommonAttr("query_stroke", keyword_stroke);

    return true;
  }

  static bool GetAdsLiveRecallQueryFromKconf(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_follow_uid_list_limit = context.GetStringListCommonAttr("user_follow_uid_list_limit")
                                          .value_or(std::vector<absl::string_view>());
    auto user_recent_live_author_ids = context.GetStringListCommonAttr("user_recent_live_author_ids")
                                           .value_or(std::vector<absl::string_view>());
    std::unordered_set<std::string> author_id_set, author_id_set_part1, author_id_set_part2;

    for (int i = 0; i < user_follow_uid_list_limit.size(); i++) {
      const std::string author_id(user_follow_uid_list_limit[i]);
      author_id_set.insert(author_id);
      author_id_set_part1.insert(author_id);
    }

    for (int i = 0; i < user_recent_live_author_ids.size(); i++) {
      const std::string author_id(user_recent_live_author_ids[i]);
      author_id_set.insert(author_id);
      author_id_set_part2.insert(author_id);
    }

    std::vector<int64_t> query_id_list;
    std::vector<std::string> query_list;
    std::vector<std::string> author_type_list;
    std::vector<std::string> reason_list;
    query_id_list.reserve(author_id_set.size());
    query_list.reserve(author_id_set.size());
    author_type_list.reserve(author_id_set.size());
    reason_list.reserve(author_id_set.size());

    auto parser = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };
    auto default_str_map = std::make_shared<std::map<std::string, std::string>>();
    auto ads_aid2name_ptr = ks::infra::KConf()
                                .GetMap<std::string, std::string>("ad.target_search.searchAdsAuthorIdToName",
                                                                  default_str_map, parser)
                                ->Get();
    if (ads_aid2name_ptr == nullptr) {
      CL_LOG(ERROR) << "ads_aid2name_ptr is nullptr";
      return false;
    }

    for (auto author_id : author_id_set) {
      if ((*ads_aid2name_ptr).find(author_id) == (*ads_aid2name_ptr).end()) {
        continue;
      }
      std::string author_name = (*ads_aid2name_ptr)[author_id];
      std::string recall_query = "「" + author_name + "」" + "正在直播中";
      int64_t item_id = ::base::CityHash64(recall_query.c_str(), recall_query.size());
      query_id_list.emplace_back(item_id);
      query_list.emplace_back(recall_query);
      reason_list.emplace_back(author_id);
      std::string author_type = "-1";
      if (author_id_set_part1.count(author_id) && !author_id_set_part2.count(author_id)) {
        author_type = "1";
      } else if (!author_id_set_part1.count(author_id) && author_id_set_part2.count(author_id)) {
        author_type = "2";
      } else if (author_id_set_part1.count(author_id) && author_id_set_part2.count(author_id)) {
        author_type = "3";
      }
      author_type_list.emplace_back(author_type);
    }
    context.SetIntListCommonAttr("ads_live_recall_id_list", std::move(query_id_list));
    context.SetStringListCommonAttr("ads_live_recall_query_list", std::move(query_list));
    context.SetStringListCommonAttr("ads_live_recall_reason_list", std::move(reason_list));
    context.SetStringListCommonAttr("ads_live_recall_author_type_list", std::move(author_type_list));
    return true;
  }

  static bool GetPhotoAndScoreForP2Q(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    // get common attr
    // realtime_click_list
    auto realtime_click_list_str =
        context.GetStringListCommonAttr("realtime_click_list_str").value_or(std::vector<absl::string_view>());
    if (realtime_click_list_str.empty()) {
      return false;
    }
    folly::F14FastMap<absl::string_view, int32_t, absl::Hash<absl::string_view>> photo_trigger_map;

    for (size_t i = 0; i < realtime_click_list_str.size(); ++i) {
      photo_trigger_map[realtime_click_list_str.at(i)] = i;
    }

    int recall_num = 100;
    int single_recall_num = 10;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_recall_reason_attr = context.GetStringItemAttr("item_recall_reason");
    auto item_inner_index_attr = context.GetIntItemAttr("item_inner_index");
    std::vector<std::string> photo_list_vec;
    std::vector<double> score_list_vec;
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id = item_id_attr(result).value_or("");
      auto item_score = item_score_attr(result).value_or(0);
      auto item_recall_reason = item_recall_reason_attr(result).value_or("");
      auto item_inner_index = item_inner_index_attr(result).value_or(0);
      std::string photo1(item_recall_reason.data(), item_recall_reason.size());
      std::string photo2(item_id.data(), item_id.size());
      double score = single_recall_num * recall_num - photo_trigger_map[photo1] * single_recall_num -
                     item_inner_index + 1;
      photo_list_vec.push_back(photo2);
      score_list_vec.push_back(score);
      VLOG(10) << "photo1: " << photo1 << " photo2: " << photo2 << " score: " << score;
    }
    context.SetStringListCommonAttr("p2q_trigger_photo_pid_list", std::move(photo_list_vec));
    context.SetDoubleListCommonAttr("p2q_trigger_photo_score_list", std::move(score_list_vec));
    return true;
  }

  static bool GetNewHotLowQueryList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto newhot_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::NewHotRealtimeInsertRecallExecutor>>::get()
                               ->get();
    if (newhot_reloader == nullptr) {
      CL_LOG(ERROR) << "newhot_reloader is nullptr.";
      return false;
    }
    const std::unordered_set<std::string> &newhot_low_query_set = newhot_reloader->get_newhot_low_query_set();
    std::vector<std::string> low_query_list_vec;
    low_query_list_vec.insert(low_query_list_vec.end(), newhot_low_query_set.begin(),
                              newhot_low_query_set.end());
    context.SetStringListCommonAttr("newhot_low_query_list", std::move(low_query_list_vec));
    return true;
  }

  static bool ParseLiveStreamFeatureBatchResponse(const CommonRecoLightFunctionContext &context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *live_response =
        context.GetProtoMessagePtrCommonAttr<ks::search::ziya_sort::dragon::LivestreamFeatureBatchResponse>(
            "livestream_fasttext_live_response");
    if (live_response == nullptr) {
      CL_LOG(WARNING) << "live_response ptr is nullptr";
      return true;
    }
    if (std::distance(begin, end) != live_response->responses_size()) {
      CL_LOG(ERROR) << "live_response doesn't match item's size.";
      return false;
    }

    auto item_recommend_type_set_func = context.SetStringItemAttr("item_recommend_type");
    auto item_recommend_reason_set_func = context.SetStringItemAttr("item_recommend_reason");
    auto item_filter_score_set_func = context.SetDoubleItemAttr("item_filter_score");
    auto item_live_query_type_set_func = context.SetIntItemAttr("item_live_query_type");
    int idx = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto &resp = live_response->responses(idx);
      item_filter_score_set_func(result, resp.score());
      item_live_query_type_set_func(result, resp.query_attr());
      if (resp.score() > 0.0) {
        const auto &reco_result = se_reco::GetRecommendationReasons(resp);
        item_recommend_type_set_func(result, reco_result.first);
        item_recommend_reason_set_func(result, reco_result.second);
      }
      idx++;
    });

    return true;
  }

  static bool GetReRankResultScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto rerank_query =
        context.GetStringListCommonAttr("rerank_query").value_or(std::vector<absl::string_view>());
    auto rerank_ctr = context.GetDoubleListCommonAttr("rerank_ctr").value_or(absl::Span<const double>());
    auto rerank_res_ctr =
        context.GetDoubleListCommonAttr("rerank_res_ctr").value_or(absl::Span<const double>());
    auto age_segment = context.GetIntCommonAttr("age_enum").value_or(0);
    if (rerank_query.size() != rerank_ctr.size() || rerank_query.size() != rerank_ctr.size()) {
      CL_LOG(ERROR) << "rerank_query and rerank_res_ctr size not equal";
      return false;
    }
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> vulgar_query_map;
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> other_query_map;
    double ctr_threshold = 0.008;
    double res_ctr_threshold = 0.3;
    for (size_t i = 0; i < rerank_query.size(); ++i) {
      auto &query = rerank_query[i];
      if (rerank_ctr.at(i) > ctr_threshold) {
        vulgar_query_map[query] = rerank_ctr.at(i);
      } else if (rerank_res_ctr.at(i) < res_ctr_threshold) {
        other_query_map[query] = rerank_res_ctr.at(i);
      }
    }
    if (vulgar_query_map.size() <= 0 && other_query_map.size() <= 0) {
      VLOG(2) << "vulgar_query_map or other_query_map is empty";
      return false;
    }

    int is_adult = (age_segment != 0 && age_segment != 1);
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_set_func = context.SetDoubleItemAttr("item_score");
    for (auto it = begin; it != end; ++it) {
      auto item_id = item_id_attr(*it).value_or("");
      auto score = score_attr(*it).value_or(0.0);
      float query_res_ctr = 0.0;
      if (!is_adult && vulgar_query_map.find(item_id) != vulgar_query_map.end()) {
        query_res_ctr = vulgar_query_map[item_id];
      }
      if (other_query_map.find(item_id) != other_query_map.end()) {
        query_res_ctr = other_query_map[item_id];
      }
      if (query_res_ctr == 0.0) {
        continue;
      }
      double rerank_score = score * query_res_ctr;
      item_score_set_func(*it, rerank_score);
      VLOG(2) << "query:" << item_id << "score:" << std::to_string(score)
              << ", query_res_ctr:" << std::to_string(query_res_ctr)
              << ", rerank_score:" << std::to_string(rerank_score);
    }
    return true;
  }

  static bool CountItemsIdAndSource(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    int recall_total_num_no_dedup = 0;
    std::unordered_set<std::string> recall_item_set;
    auto set_cur_recall_item_cnt_func = context.SetIntItemAttr("cur_recall_item_cnt");
    auto set_repeat_recall_cnt_func = context.SetIntItemAttr("repeat_recall_cnt");
    auto get_item_id_func = context.GetStringItemAttr("item_id");
    auto get_item_source_func = context.GetStringItemAttr("item_source");
    std::unordered_map<std::string, int64> recall_source_item_cnt_map;
    std::unordered_map<std::string, int64> query_repeat_recall_cnt_map;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_view = get_item_id_func(result).value_or("");
      auto item_source_view = get_item_source_func(result).value_or("");
      std::string item_id(item_id_view.begin(), item_id_view.size());
      std::string item_source(item_source_view.begin(), item_source_view.size());
      recall_source_item_cnt_map[item_source]++;
      query_repeat_recall_cnt_map[item_id]++;
      recall_item_set.insert(item_id);
      recall_total_num_no_dedup++;
    });

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_view = get_item_id_func(result).value_or("");
      auto item_source_view = get_item_source_func(result).value_or("");
      std::string item_id(item_id_view.begin(), item_id_view.size());
      std::string item_source(item_source_view.begin(), item_source_view.size());
      int64 cur_recall_item_cnt = recall_source_item_cnt_map[item_source];
      int64 repeat_recall_cnt = query_repeat_recall_cnt_map[item_id];
      set_cur_recall_item_cnt_func(result, cur_recall_item_cnt);
      set_repeat_recall_cnt_func(result, repeat_recall_cnt);
    });

    context.SetIntCommonAttr("recall_total_num_no_dedup", recall_total_num_no_dedup);
    context.SetIntCommonAttr("recall_total_num_dedup", recall_item_set.size());
    return true;
  }

  static bool CountSourceItems(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto get_item_source_func = context.GetStringItemAttr("item_source");
    std::unordered_map<std::string, int64> recall_source_item_cnt_map;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string item_source(get_item_source_func(result).value_or(""));
      recall_source_item_cnt_map[item_source]++;
    });

    std::vector<std::string> recall_sources_after_merge;
    std::vector<int64> recall_sources_item_count_after_merge;
    for (auto &pair : recall_source_item_cnt_map) {
      auto source = pair.first;
      auto count = pair.second;
      recall_sources_after_merge.push_back(source);
      recall_sources_item_count_after_merge.push_back(count);
    }
    context.SetStringListCommonAttr("recall_sources_after_merge", std::move(recall_sources_after_merge));
    context.SetIntListCommonAttr("recall_sources_item_count_after_merge",
                                 std::move(recall_sources_item_count_after_merge));
    return true;
  }

  static bool CountSourcePrerankZeroScoreItems(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto get_item_source_func = context.GetStringItemAttr("item_source");
    auto get_item_prerank_score_func = context.GetDoubleItemAttr("item_prerank_score");
    std::unordered_map<std::string, int> zero_score_sources_cnt_map;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string item_source(get_item_source_func(result).value_or(""));
      double item_prerank_score = get_item_prerank_score_func(result).value_or(0);
      if (item_prerank_score != 0) {
        return;
      }
      if (zero_score_sources_cnt_map.find(item_source) != zero_score_sources_cnt_map.end()) {
        zero_score_sources_cnt_map[item_source] = zero_score_sources_cnt_map[item_source] + 1;
      } else {
        zero_score_sources_cnt_map[item_source] = 1;
      }
    });

    std::string zero_score_source_info = "";
    for (const auto &it : zero_score_sources_cnt_map) {
      zero_score_source_info += it.first + ":" + std::to_string(it.second) + ", ";
    }
    context.SetStringCommonAttr("prerank_zero_score_source_cnt", zero_score_source_info);

    return true;
  }

  static bool CompleteTrendingDataSource(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    const ks::kfs::data_source::TrendingDataSource *data_source_ptr =
        context.GetProtoMessagePtrCommonAttr<ks::kfs::data_source::TrendingDataSource>(
            "trending_data_source");
    if (data_source_ptr == nullptr) {
      CL_LOG(WARNING) << "trending data source ptr is nullptr";
      return false;
    }
    ks::kfs::data_source::TrendingDataSource data_source;
    data_source.CopyFrom(*data_source_ptr);
    auto click_photo_embedding_list = context.GetDoubleListCommonAttr("reco_click_photo_embedding_list")
                                          .value_or(absl::Span<const double>());
    if (click_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < click_photo_embedding_list.size(); i = i + 128) {
        auto emb_value = data_source.add_click_photo_embeddings()->mutable_value();
        std::copy(click_photo_embedding_list.begin() + i, click_photo_embedding_list.begin() + i + 128,
                  ::google::protobuf::RepeatedFieldBackInserter(emb_value));
      }
    }
    auto search_query_embedding_list =
        context.GetDoubleListCommonAttr("search_query_embedding_list").value_or(absl::Span<const double>());
    if (search_query_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < search_query_embedding_list.size(); i = i + 128) {
        auto emb_value = data_source.add_search_history_query_embeddings()->mutable_value();
        std::copy(search_query_embedding_list.begin() + i, search_query_embedding_list.begin() + i + 128,
                  ::google::protobuf::RepeatedFieldBackInserter(emb_value));
      }
    }

    std::string data_source_serial_str;
    if (data_source.SerializeToString(&data_source_serial_str)) {
      context.SetStringCommonAttr("trending_data_source_serial_str", data_source_serial_str);
      return true;
    }
    return false;
  }

  static bool GetFirstTwoPrefixQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    // is_item_prefix_dedup_func 为 1 代表删除
    auto is_item_prefix_dedup_func = context.SetIntItemAttr("is_item_prefix_dedup");
    int limit = 20;
    std::unordered_set<std::string> sub_query_set;
    for (auto iter = begin; iter != end && limit-- > 0; ++iter) {
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
      std::wstring tmp_text = wcv.from_bytes(std::string(item_id_attr_str.data(), item_id_attr_str.size()));
      std::wstring sub_tmp_text = tmp_text.substr(0, 2);
      std::string sub_str(wcv.to_bytes(sub_tmp_text));
      int flags = 0;
      if (sub_query_set.find(sub_str) != sub_query_set.end()) {
        flags = 1;
        VLOG(2) << "item_id:" << std::string(item_id_attr_str.data(), item_id_attr_str.size())
                << "sub_str:" << sub_str;
      } else {
        sub_query_set.insert(sub_str);
      }
      is_item_prefix_dedup_func(*iter, flags);
    }
    return true;
  }

  static bool SourceCntByLimit(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_source_attr = context.GetStringItemAttr("item_source");
    // is_item_prefix_dedup_func 为 1 代表删除
    auto remove_flag_func = context.SetIntItemAttr("remove_flag");
    int total_cnt = std::distance(begin, end);
    int max_delete_op = total_cnt - 6;
    if (max_delete_op > 100) {
      max_delete_op = 100;
    }

    auto item_source_common_list = context.GetStringListCommonAttr("target_source_common_list");
    using DedupSetType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupSetType item_source_dedup;
    if (item_source_common_list) {
      item_source_dedup.insert(item_source_common_list->begin(), item_source_common_list->end());
    } else {
      CL_LOG(ERROR) << "item_source_common_list is empty";
      return false;
    }
    int delete_cnt = 0;
    int total = 0;
    int max_dup_count = 2;  // TODO(fuyanzhuo): 后续可以使用 attr
    for (auto iter = begin; iter != end; ++iter) {
      remove_flag_func(*iter, 0);
      if (delete_cnt >= max_delete_op) {
        break;
      }
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      auto item_source_attr_str = item_source_attr(*iter).value_or("");
      if (item_source_dedup.find(item_source_attr_str) == item_source_dedup.end()) {
        continue;
      }
      if (total >= max_dup_count) {
        remove_flag_func(*iter, 1);
        ++delete_cnt;
        VLOG(2) << "remove item:" << std::string(item_id_attr_str.data(), item_id_attr_str.size());
      } else {
        ++total;
        remove_flag_func(*iter, 0);
      }
    }
    if (VLOG_IS_ON(2)) {
      VLOG(2) << "total_cnt:" << std::to_string(total_cnt) << ", delete_cnt:" << std::to_string(delete_cnt);
      for (auto &item : item_source_dedup) {
        VLOG(2) << "source_item:" << std::string(item.data(), item.size());
      }
    }
    return true;
  }

  static bool SourceCntByLimitNew(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_source_attr = context.GetStringItemAttr("item_source");
    // is_item_prefix_dedup_func 为 1 代表删除
    auto remove_flag_func = context.SetIntItemAttr("remove_flag");
    int total_cnt = std::distance(begin, end);
    int max_delete_op = total_cnt - 6;
    if (max_delete_op > 100) {
      max_delete_op = 100;
    }

    auto item_source_common_list = context.GetStringListCommonAttr("target_source_common_list");
    using DedupSetType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupSetType item_source_dedup;
    if (item_source_common_list) {
      item_source_dedup.insert(item_source_common_list->begin(), item_source_common_list->end());
    } else {
      CL_LOG(ERROR) << "item_source_common_list is empty";
      return false;
    }
    int delete_cnt = 0;
    int total = 0;
    auto max_dup_count = context.GetIntCommonAttr("max_dup_count").value_or(2);
    for (auto iter = begin; iter != end; ++iter) {
      remove_flag_func(*iter, 0);
      if (delete_cnt >= max_delete_op) {
        break;
      }
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      auto item_source_attr_str = item_source_attr(*iter).value_or("");
      if (item_source_dedup.find(item_source_attr_str) == item_source_dedup.end()) {
        continue;
      }
      if (total >= max_dup_count) {
        remove_flag_func(*iter, 1);
        ++delete_cnt;
        VLOG(2) << "remove item:" << std::string(item_id_attr_str.data(), item_id_attr_str.size());
      } else {
        ++total;
        remove_flag_func(*iter, 0);
      }
    }
    if (VLOG_IS_ON(2)) {
      VLOG(2) << "total_cnt:" << std::to_string(total_cnt) << ", delete_cnt:" << std::to_string(delete_cnt);
      for (auto &item : item_source_dedup) {
        VLOG(2) << "source_item:" << std::string(item.data(), item.size());
      }
    }
    return true;
  }

  static bool SimilarityDeduplicate(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    const double score_threshold_ab = 0.4;
    const int64_t min_item_count = 6;
    const int64_t filter_top_n = 60;

    std::vector<std::string> reserved_items;
    int remaining_query_cnt = std::distance(begin, end);
    CL_LOG(INFO) << "remaining_query_cnt: " << remaining_query_cnt;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_sim_set_func = context.SetDoubleItemAttr("item_sim");
    auto item_is_dedup_filter_set_func = context.SetIntItemAttr("item_is_dedup_filter");
    auto cal_max_sim_score = [&](const std::string &cur_item_id) -> float {
      float max_score = 0.0;
      for (const auto &item_id : reserved_items) {
        float score = se_reco::CalculateSimilarityScore(item_id, cur_item_id);
        max_score = score > max_score ? score : max_score;
      }
      return max_score;
    };

    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str = std::string(item_id.data(), item_id.size());
      if (item_id.empty()) {
        continue;
      }
      float cur_item_sim = cal_max_sim_score(item_id_str);
      item_sim_set_func(result, cur_item_sim);
      CL_LOG(INFO) << "item_id: " << item_id << ", sim score: " << cur_item_sim
                   << ", sim size: " << reserved_items.size();
      if (reserved_items.size() == 0 || cur_item_sim < score_threshold_ab) {
        reserved_items.emplace_back(item_id_str);
        item_is_dedup_filter_set_func(result, 0);
      } else {
        remaining_query_cnt--;
        item_is_dedup_filter_set_func(result, 1);
      }
      if (remaining_query_cnt <= min_item_count || reserved_items.size() >= filter_top_n) {
        break;
      }
    }

    return true;
  }

  static bool ItemIsABABPattern(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    const int64_t min_item_count = 6;
    const int64_t filter_top_n = 50;

    int remaining_query_cnt = std::distance(begin, end);
    CL_LOG(INFO) << "remaining_query_cnt: " << remaining_query_cnt;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_is_abab_filter_set_func = context.SetIntItemAttr("item_is_abab_filter");

    int cnt = 0;
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str = std::string(item_id.data(), item_id.size());
      if (item_id.empty()) {
        continue;
      }

      bool isABAB = false;
      std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
      std::wstring wstr = wcv.from_bytes(item_id_str);
      if (wstr.length() < 2) {
        isABAB = false;
      } else {
        std::unordered_map<std::wstring, int> ngram_map;
        std::wstring pre_ngram = wcv.from_bytes("");
        int pre_cnt = 0;
        for (int i = 0; i < wstr.length()-1; ++i) {
          std::wstring ngram_str = wstr.substr(i, 2);
          if (ngram_str == pre_ngram && pre_cnt == 0) {
            pre_cnt = 1;
            continue;
          }
          ++ngram_map[ngram_str];
          if (ngram_map[ngram_str] > 1) {
            isABAB = true;
            break;
          }
          pre_cnt = 0;
          pre_ngram = ngram_str;
        }
      }
      ++cnt;
      CL_LOG(INFO) << "item_id: " << item_id << ", cnt: " << cnt
                   << ", filter_top_n: " << filter_top_n
                   << ", remaining_query_cnt: " << remaining_query_cnt;
      if (isABAB == false) {
        item_is_abab_filter_set_func(result, 0);
      } else {
        remaining_query_cnt--;
        item_is_abab_filter_set_func(result, 1);
      }
      if (cnt >= filter_top_n || remaining_query_cnt <= min_item_count) {
        break;
      }
    }

    return true;
  }

  static bool CompleteRankTrendingDataSource(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    const ks::kfs::data_source::TrendingDataSource *data_source_ptr =
        context.GetProtoMessagePtrCommonAttr<ks::kfs::data_source::TrendingDataSource>(
            "rank_trending_data_source");
    if (data_source_ptr == nullptr) {
      CL_LOG(WARNING) << "trending data source ptr is nullptr";
      return false;
    }
    ks::kfs::data_source::TrendingDataSource data_source;
    data_source.CopyFrom(*data_source_ptr);
    auto click_photo_ids = context.GetIntListCommonAttr("reco_click_list_int_photo_id_within_a_day_limit_30")
                               .value_or(absl::Span<const int64>());
    auto click_photo_embedding_list = context.GetDoubleListCommonAttr("reco_click_photo_embedding_list")
                                          .value_or(absl::Span<const double>());
    if (click_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < click_photo_ids.size() && i * 128 < click_photo_embedding_list.size(); i++) {
        if (std::any_of(click_photo_embedding_list.begin() + i * 128,
                        click_photo_embedding_list.begin() + (i + 1) * 128,
                        [](double x) { return x > 0.00001; })) {
          data_source.add_click_photo_ids(click_photo_ids[i]);
          auto emb_value = data_source.add_click_photo_embeddings()->mutable_value();
          std::copy(click_photo_embedding_list.begin() + i * 128,
                    click_photo_embedding_list.begin() + (i + 1) * 128,
                    ::google::protobuf::RepeatedFieldBackInserter(emb_value));
        }
      }
    }

    auto rec_after_search_photo_ids = context.GetIntListCommonAttr("rec_after_search_photo_list_feat")
                               .value_or(absl::Span<const int64>());
    auto rec_after_search_photo_embedding_list =
              context.GetDoubleListCommonAttr("rec_after_search_photo_embedding_list_feat")
                                          .value_or(absl::Span<const double>());
    if (rec_after_search_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < rec_after_search_photo_ids.size() &&
                                i * 128 < rec_after_search_photo_embedding_list.size(); i++) {
        if (std::any_of(rec_after_search_photo_embedding_list.begin() + i * 128,
                        rec_after_search_photo_embedding_list.begin() + (i + 1) * 128,
                        [](double x) { return x > 0.00001; })) {
            auto add_photo_var = data_source.add_rec_after_search_photo_embeddings();
            if (add_photo_var != nullptr) {
              auto rec_after_search_photo_emb_value = add_photo_var->mutable_value();
              std::copy(rec_after_search_photo_embedding_list.begin() + i * 128,
                        rec_after_search_photo_embedding_list.begin() + (i + 1) * 128,
                        ::google::protobuf::RepeatedFieldBackInserter(rec_after_search_photo_emb_value));
            }
        }
      }
    }

    auto like_photo_ids =
        context.GetIntListCommonAttr("reco_like_list_photo_id_limit_12").value_or(absl::Span<const int64>());
    auto like_photo_embedding_list = context.GetDoubleListCommonAttr("reco_like_photo_embedding_list")
                                         .value_or(absl::Span<const double>());
    if (like_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < like_photo_ids.size() && i * 128 < like_photo_embedding_list.size(); i++) {
        if (std::any_of(like_photo_embedding_list.begin() + i * 128,
                        like_photo_embedding_list.begin() + (i + 1) * 128,
                        [](double x) { return x > 0.00001; })) {
          data_source.add_like_photo_ids(like_photo_ids[i]);
          auto emb_value = data_source.add_like_photo_embeddings()->mutable_value();
          std::copy(like_photo_embedding_list.begin() + i * 128,
                    like_photo_embedding_list.begin() + (i + 1) * 128,
                    ::google::protobuf::RepeatedFieldBackInserter(emb_value));
        }
      }
    }
    auto search_query_embedding_list =
        context.GetDoubleListCommonAttr("search_query_embedding_list").value_or(absl::Span<const double>());
    if (search_query_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < search_query_embedding_list.size(); i = i + 128) {
        auto emb_value = data_source.add_search_history_query_embeddings()->mutable_value();
        std::copy(search_query_embedding_list.begin() + i, search_query_embedding_list.begin() + i + 128,
                  ::google::protobuf::RepeatedFieldBackInserter(emb_value));
      }
    }

    auto rec_after_search_query_embedding_list =
      context.GetDoubleListCommonAttr("rec_after_search_keyword_embedding_list_feat")
                              .value_or(absl::Span<const double>());
    if (rec_after_search_query_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < rec_after_search_query_embedding_list.size(); i = i + 128) {
        auto add_query_var = data_source.add_rec_after_search_query_embeddings();
        if (add_query_var != nullptr) {
          auto rec_after_search_query_emb_value = add_query_var->mutable_value();
          std::copy(rec_after_search_query_embedding_list.begin() + i,
                    rec_after_search_query_embedding_list.begin() + i + 128,
                    ::google::protobuf::RepeatedFieldBackInserter(rec_after_search_query_emb_value));
        }
      }
    }

    std::string data_source_serial_str;
    if (data_source.SerializeToString(&data_source_serial_str)) {
      context.SetStringCommonAttr("rank_trending_data_source_serial_str", data_source_serial_str);
      return true;
    }
    return false;
  }

  static bool GetBoostReferV3Candidates(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    int gys_boost_refer_dedup_exp = context.GetIntCommonAttr("gys_boost_refer_dedup_exp").value_or(0);
    double gys_boost_refer_dedup_thres =
        context.GetDoubleCommonAttr("gys_boost_refer_dedup_thres").value_or(2.0);

    std::vector<std::pair<std::string, float>> class_1_items;
    std::vector<std::pair<std::string, float>> class_2_items;
    std::vector<std::pair<std::string, float>> class_3_items;
    std::unordered_map<std::string, int64_t> query2key;

    auto item_id_list_attr = context.GetStringListCommonAttr("gys_bottombar_recall_v2_item_id_list")
                                 .value_or(std::vector<absl::string_view>());
    auto item_key_list_attr = context.GetIntListCommonAttr("gys_bottombar_recall_v2_item_key_list")
                                  .value_or(absl::Span<const int64>());
    auto item_p2q_score_list_attr = context.GetDoubleListCommonAttr("gys_bottombar_recall_v2_p2q_score_list")
                                        .value_or(absl::Span<const double>());

    if (item_id_list_attr.size() != item_key_list_attr.size() ||
        item_id_list_attr.size() != item_p2q_score_list_attr.size()) {
      return false;
    }
    std::unordered_map<std::string, float> rank_score_map;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto rank_score_attr = context.GetDoubleItemAttr("rank_score");
    for (auto iter = begin; iter != end; ++iter) {
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      std::string item_id_str(std::string(item_id_attr_str.data(), item_id_attr_str.size()));
      float rank_score = rank_score_attr(*iter).value_or(0.0);
      rank_score_map[item_id_str] = rank_score;
    }

    for (int i = 0; i < item_id_list_attr.size(); i++) {
      auto item_id_attr_str = item_id_list_attr.at(i);
      std::string item_id_str(std::string(item_id_attr_str.data(), item_id_attr_str.size()));
      float rank_score = 0.0;
      if (rank_score_map.find(item_id_str) != rank_score_map.end()) {
        rank_score = rank_score_map[item_id_str];
      }
      auto item_p2q_score = item_p2q_score_list_attr.at(i);
      query2key[item_id_str] = item_key_list_attr.at(i);

      if (item_p2q_score > 3.0) {
        // 第一档，对于特定垂类的 refer 召回来源，3 < p2q 分数，最优先强插
        class_1_items.emplace_back(std::pair(item_id_str, rank_score));
      } else if (1.0 < item_p2q_score && item_p2q_score <= 3.0) {
        // 第二档，对于优质的 refer 召回来源，1 < p2q 分数 <= 3 ，次优先强插
        class_2_items.emplace_back(std::pair(item_id_str, rank_score));
      } else {
        // 第三档，对于非特定垂类且非优质的 refer 召回来源，p2q 分数 <= 1 ，最后强插
        class_3_items.emplace_back(std::pair(item_id_str, rank_score));
      }
    }

    std::vector<std::string> boost_condidates;
    std::vector<std::vector<std::pair<std::string, float>>> class_items = {class_1_items, class_2_items,
                                                                           class_3_items};
    for (auto &items_list : class_items) {
      if (items_list.size() <= 0) {
        continue;
      }
      std::sort(items_list.begin(), items_list.end(),
                [](const std::pair<std::string, float> &a, const std::pair<std::string, float> &b) -> bool {
                  return a.second > b.second;
                });
      for (auto &item : items_list) {
        boost_condidates.emplace_back(item.first);
      }
    }

    if (boost_condidates.size() == 0 || gys_boost_refer_dedup_exp <= 0) {
      context.SetStringListCommonAttr("boost_refer_related_query_v3_candidates", std::move(boost_condidates));
      return true;
    }

    std::vector<std::string> boost_condidates_deduped;
    std::vector<int64_t> boost_condidates_deduped_id;
    auto item_sim_func = [&](const std::string &item_id) -> float {
      float max_score = 0.0;
      for (auto &item_id_dedup : boost_condidates_deduped) {
        float score = se_reco::CalculateSimilarityScore(item_id_dedup, item_id);
        if (score > max_score) {
          max_score = score;
        }
      }
      return max_score;
    };

    for (auto &candidate : boost_condidates) {
      if (boost_condidates_deduped.size() == 0 || item_sim_func(candidate) < gys_boost_refer_dedup_thres) {
        boost_condidates_deduped.emplace_back(candidate);
        boost_condidates_deduped_id.emplace_back(query2key[candidate]);
      }
    }

    context.SetStringListCommonAttr("boost_refer_related_query_v3_candidates",
                                    std::move(boost_condidates_deduped));
    context.SetIntListCommonAttr("boost_refer_related_query_v3_candidate_ids",
                                 std::move(boost_condidates_deduped_id));
    return true;
  }

  static bool CalcCommerceMixRankScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto default_val = std::make_shared<std::vector<double>>();
    auto search_score_bucket =
        ks::infra::KConf()
            .GetList("se.ziyasort.commerce_mixrank_gys_search_score_bucket", default_val)
            ->Get();

    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto rank_score_attr = context.GetDoubleItemAttr("rank_score");
    auto item_score_set_func = context.SetDoubleItemAttr("item_score");
    auto commerce_score_set_func = context.SetDoubleItemAttr("commerce_score");
    auto ad_ecpm_set_func = context.SetDoubleItemAttr("ad_ecpm");
    auto ad_model_id_set_func = context.SetStringItemAttr("ad_model_id");
    auto query_list =
        context.GetStringListCommonAttr("commerce_ad_query_list").value_or(std::vector<absl::string_view>());
    auto query_score_list =
        context.GetDoubleListCommonAttr("commerce_ad_query_score_list").value_or(absl::Span<const double>());
    auto query_ecpm_model_list = context.GetDoubleListCommonAttr("commerce_ad_query_ecpm_model_list")
                                     .value_or(absl::Span<const double>());
    auto query_model_id_list =
        context.GetIntListCommonAttr("commerce_ad_query_model_id_list").value_or(absl::Span<const int64>());
    double gys_commerce_mixrank_score_coef =
        context.GetDoubleCommonAttr("gys_commerce_mixrank_score_coef").value_or(0.0);
    folly::F14FastMap<absl::string_view, int64_t, absl::Hash<absl::string_view>> query_commerce_info_map;
    if (query_list.size() == query_score_list.size() && query_list.size() == query_ecpm_model_list.size() &&
        query_list.size() == query_model_id_list.size()) {
      for (int i = 0; i < query_list.size(); i++) {
        query_commerce_info_map[query_list[i]] = i;
      }
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      float rank_score = rank_score_attr(result).value_or(0.0);
      float search_score = rank_score;
      auto upper = std::upper_bound(search_score_bucket->begin(), search_score_bucket->end(), search_score);
      if (upper != search_score_bucket->end()) {
        int32_t bucket_id = std::distance(search_score_bucket->begin(), upper);
        search_score = bucket_id / (float)search_score_bucket->size();
      } else {
        search_score = 0.0f;
      }
      float commerce_score = 0.0;
      if (query_commerce_info_map.find(item_id) != query_commerce_info_map.end()) {
        int index = query_commerce_info_map[item_id];
        if (index < query_score_list.size() && index < query_ecpm_model_list.size() &&
            index < query_model_id_list.size()) {
          commerce_score = query_score_list[index];
          ad_ecpm_set_func(result, query_ecpm_model_list[index]);
          ad_model_id_set_func(result, std::to_string(query_model_id_list[index]));
        }
      }
      commerce_score_set_func(result, commerce_score);
      float mixrank_score = search_score + gys_commerce_mixrank_score_coef * commerce_score;
      item_score_set_func(result, mixrank_score);
    });

    return true;
  }

  static bool EnrichCommerceItemLabel(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto commerce_source_name = context.GetStringCommonAttr("commerce_source_name").value_or("");
    int judge_ad_item_use_score_only = context.GetIntCommonAttr("judge_ad_item_use_score_only").value_or(0);
    int gys_commerce_mixrank_size = context.GetIntCommonAttr("gys_commerce_mixrank_size").value_or(0);
    double gys_commerce_mixrank_score_thd =
        context.GetDoubleCommonAttr("gys_commerce_mixrank_score_thd").value_or(1.0);

    auto item_source_attr = context.GetStringItemAttr("item_source");
    auto commerce_score_attr = context.GetDoubleItemAttr("commerce_score");

    auto commerce_mixrank_info_set_func = context.SetStringItemAttr("commerce_mixrank_info");
    auto is_commerce_rerank_set_func = context.SetIntItemAttr("is_commerce_rerank");

    int commerce_item_count = 0;
    int index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_source = item_source_attr(result).value_or("");
      double commerce_score = commerce_score_attr(result).value_or(0.0);
      std::string item_source_str(std::string(item_source.data(), item_source.size()));
      std::string commerce_mixrank_info = "";
      bool is_set_commerce_params = false;
      bool is_commerce = item_source == commerce_source_name;
      if (commerce_item_count < gys_commerce_mixrank_size &&
          ((judge_ad_item_use_score_only == 0 && item_source == commerce_source_name) ||
           commerce_score > gys_commerce_mixrank_score_thd)) {
        is_commerce_rerank_set_func(result, 1);
        is_set_commerce_params = true;
        commerce_item_count++;
        commerce_mixrank_info += "1," + std::to_string(index) + "," + std::to_string(is_commerce) + "," +
                                 std::to_string(commerce_score) + ";";
      } else {
        is_commerce_rerank_set_func(result, 0);
      }
      commerce_mixrank_info_set_func(result, commerce_mixrank_info);
      index++;
    });

    return true;
  }

  static bool MergeCommerceAndSearchItems(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int gys_commerce_mixrank_interval = context.GetIntCommonAttr("gys_commerce_mixrank_interval").value_or(0);

    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto is_commerce_attr = context.GetIntItemAttr("is_commerce_rerank");

    auto boost_postion_set_func = context.SetIntItemAttr("commerce_boost_position");

    int item_index = 0;
    std::vector<std::pair<int, double>> search_item_infos;
    std::vector<std::pair<int, double>> commerce_item_infos;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double item_score = item_score_attr(result).value_or(0.0);
      int is_commerce = is_commerce_attr(result).value_or(0);
      if (is_commerce == 1) {
        commerce_item_infos.emplace_back(std::pair(item_index, item_score));
      } else {
        search_item_infos.emplace_back(std::pair(item_index, item_score));
      }
      item_index++;
    });

    std::unordered_map<int, int> index2position;
    int i = 0;
    int j = 0;
    int pos = 0;
    int last_commerce_pos = -1 * gys_commerce_mixrank_interval;
    while (i < search_item_infos.size() && j < commerce_item_infos.size()) {
      if (search_item_infos[i].second <= commerce_item_infos[j].second &&
          (pos - last_commerce_pos) >= gys_commerce_mixrank_interval) {
        index2position[commerce_item_infos[j].first] = pos;
        last_commerce_pos = pos + 1;
        j++;
      } else {
        index2position[search_item_infos[i].first] = pos;
        i++;
      }
      pos++;
    }
    while (i < search_item_infos.size()) {
      index2position[search_item_infos[i].first] = pos;
      i++;
      pos++;
    }
    while (j < commerce_item_infos.size()) {
      index2position[commerce_item_infos[j].first] = pos;
      j++;
      pos++;
    }

    item_index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (index2position.find(item_index) != index2position.end()) {
        boost_postion_set_func(result, index2position[item_index]);
      } else {
        boost_postion_set_func(result, index2position.size());
      }
      item_index++;
    });

    return true;
  }

  static bool HitSourceAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    auto item_source_attr = context.GetStringItemAttr("item_source");
    auto target_source_flag_func = context.SetIntItemAttr("target_source_flag");

    auto target_source_common_list = context.GetStringListCommonAttr("target_source_common_list");
    auto not_target_source_common_list = context.GetStringListCommonAttr("not_target_source_common_list");
    using DedupSetType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupSetType item_source_dedup, not_item_source_dedup;
    if (target_source_common_list) {
      item_source_dedup.insert(target_source_common_list->begin(), target_source_common_list->end());
    }
    if (not_target_source_common_list) {
      not_item_source_dedup.insert(not_target_source_common_list->begin(),
                                   not_target_source_common_list->end());
    }
    if (target_source_common_list->empty() && not_target_source_common_list->empty()) {
      return false;
    }
    int32_t target_source_flag_num = 0;
    for (auto iter = begin; iter != end; ++iter) {
      target_source_flag_func(*iter, 0);
      auto item_source_attr_str = item_source_attr(*iter).value_or("");
      if (!not_item_source_dedup.empty()) {
        if (not_item_source_dedup.find(item_source_attr_str) == not_item_source_dedup.end()) {
          target_source_flag_num++;
          target_source_flag_func(*iter, 1);
          VLOG(2) << "source:" << std::string(item_source_attr_str);
        }
        continue;
      } else if (!item_source_dedup.empty()) {
        if (item_source_dedup.find(item_source_attr_str) != item_source_dedup.end()) {
          target_source_flag_num++;
          target_source_flag_func(*iter, 1);
          VLOG(2) << "source:" << std::string(item_source_attr_str);
        }
        continue;
      }
      // 没有配置 target_source_common_list 和对应的 not 则都默认为 1
      target_source_flag_func(*iter, 1);
    }
    context.SetIntCommonAttr("target_source_flag_num", target_source_flag_num);
    if (VLOG_IS_ON(2)) {
      for (auto &item : item_source_dedup) {
        VLOG(2) << "source_item:" << std::string(item.data(), item.size());
      }
    }
    return true;
  }

  static bool HitSensitiveAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto remove_flag_flag_func = context.SetIntItemAttr("remove_flag");
    auto sensitive_action_list =
        context.GetIntListCommonAttr("sensitive_action").value_or(absl::Span<const int64>());
    auto sensitive_input_list =
        context.GetStringListCommonAttr("sensitive_input").value_or(std::vector<absl::string_view>());

    using DedupSetType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupSetType item_action_set;

    if (sensitive_input_list.size() != sensitive_action_list.size()) {
      return false;
    }
    for (size_t i = 0; i < sensitive_action_list.size(); ++i) {
      if (sensitive_action_list[i] == 1 || sensitive_action_list[i] == 2 || sensitive_action_list[i] >= 21) {
        item_action_set.insert(sensitive_input_list[i]);
      }
      VLOG(2) << "input:" << std::string(sensitive_input_list[i])
              << ", action:" << std::to_string(sensitive_action_list[i]);
    }
    for (auto iter = begin; iter != end; ++iter) {
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      if (item_action_set.find(item_id_attr_str) != item_action_set.end()) {
        remove_flag_flag_func(*iter, 1);
        VLOG(2) << "sensitive remove item_id:"
                << std::string(item_id_attr_str.data(), item_id_attr_str.size());
      } else {
        remove_flag_flag_func(*iter, 0);
        VLOG(2) << "sensitive input:" << std::string(item_id_attr_str.data(), item_id_attr_str.size());
      }
    }
    return true;
  }

  static bool AntispamSessionHistoryParams(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto session_his_list =
        context.GetStringListCommonAttr("session_history").value_or(std::vector<absl::string_view>());
    auto memory_query_list =
        context.GetStringListCommonAttr("memory_query_list").value_or(std::vector<absl::string_view>());
    if (session_his_list.empty()) {
      context.SetIntCommonAttr("sessionRounds", 0);
      ::Json::Value root(::Json::arrayValue);
      ::Json::FastWriter jw;
      context.SetStringCommonAttr("sessionHistory", jw.write(root));  // json
      return true;
    }

    int recent_invalid_query_round = 0;
    for (int i = 0; i < memory_query_list.size(); ++i) {
      std::string query_str(memory_query_list[i].data());
      bool is_invalid_query = true;
      for (auto c : query_str) {
        if (!std::isdigit(c) && !std::ispunct(c)) {
          is_invalid_query = false;
          break;
        }
      }
      if (is_invalid_query) {
        recent_invalid_query_round += 1;
      } else {
        recent_invalid_query_round = 0;
      }
    }

    const size_t his_lens = session_his_list.size() / 2;
    context.SetIntCommonAttr("sessionRounds", his_lens);

    ::Json::Value root(::Json::arrayValue);
    size_t idx = 0;
    if (his_lens > 10) {
      idx = his_lens - 10;
    }
    for (; idx < his_lens; ++idx) {
      const auto &q = session_his_list[idx * 2];
      const auto &a = session_his_list[idx * 2 + 1];

      ::Json::Value jv, sub_jv;
      sub_jv["type"] = "text";
      sub_jv["content"] = q.data();
      jv["query"] = sub_jv;

      sub_jv["content"] = a.data();
      jv["answer"] = sub_jv;
      root.append(jv);
    }

    ::Json::FastWriter jw;
    context.SetIntCommonAttr("CharacterRound", recent_invalid_query_round);
    context.SetStringCommonAttr("sessionHistory", jw.write(root));  // json

    return true;
  }

  static bool EnrichUserMetaByUserId(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    kuaishou::user::cache::GetByIdsRequest request;
    kuaishou::user::cache::GetByIdsResponse response;
    auto get_author_id_func = context.GetIntItemAttr("item_author_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto author_id = get_author_id_func(result);
      if (author_id.has_value()) {
        request.add_user_id(author_id.value());
      }
    });

    ks::kess::rpc::grpc::Options options;
    options.SetTimeout(std::chrono::milliseconds(20));
    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client = ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("se_reco_gyk", "grpc_apiCoreUserCacheService"));
    grpc::Status status =
        client->All()->SelectOne()->Stub<kuaishou::user::cache::kess::UserCacheRpc::Stub>().GetByIds(
            options, request, &response);

    if (!status.ok()) {
      LOG(ERROR) << "GetByIds Failed, rpc_name=grpc_apiCoreUserCacheService"
                 << ", err_msg=" << status.error_message() << ", err_code=" << status.error_code()
                 << ", uid=";
      return false;
    }

    auto set_author_name_func = context.SetStringItemAttr("item_author_name");
    auto set_author_head_func = context.SetStringItemAttr("item_author_head");
    auto set_author_verified_func = context.SetIntItemAttr("item_author_verified");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto author_id = get_author_id_func(result);
      if (!author_id.has_value()) {
        return;
      }
      if (response.result().find(author_id.value()) == response.result().end()) {
        LOG(ERROR) << "get user name response is not corresponding to request";
        return;
      }
      auto &user_info = response.result().at(author_id.value());
      set_author_name_func(result, user_info.user_name());
      set_author_head_func(result, user_info.user_head());
      set_author_verified_func(result, user_info.verified_dto().verified_type());
    });

    return true;
  }

  static bool SetIconNameForOperationQueries(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto icon_loader =
        Singleton<
            ::ks::search::ziya_sort::KconfTimerReloader<::ks::search::ziya_sort::IconLabelReloader>>::get()
            ->get();
    if (icon_loader == nullptr) {
      CL_LOG(ERROR) << "SetIconNameForOperationQueries icon_loader is nullptr.";
      return false;
    }
    ::mmu::video::InterfereGysStyle interfere_style;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto icon_name_set_func = context.SetStringItemAttr("icon_name");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(std::string(item_id_attr_str.data(), item_id_attr_str.size()));
      if (icon_loader->get_icon_style(item_id_str, &interfere_style)) {
        icon_name_set_func(result, "operation");
      }
    });
    return true;
  }

  static bool SetIconNameForYellowCartQueries(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto gys_max_yellow_cart_icon_num_ab_exp =
        context.GetIntCommonAttr("gys_max_yellow_cart_icon_num_ab_exp").value_or(0);
    auto gys_yellow_cart_use_new_icon = context.GetIntCommonAttr("gys_yellow_cart_use_new_icon").value_or(0);
    auto gys_shop_url_type_exp_attr = context.GetStringCommonAttr("gys_shop_url_type_exp").value_or("vert");
    std::string gys_shop_url_type_exp(gys_shop_url_type_exp_attr.data(), gys_shop_url_type_exp_attr.size());
    auto from_page_attr = context.GetStringCommonAttr("from_page").value_or("5");
    std::string from_page(from_page_attr.data(), from_page_attr.size());
    auto request_id_attr = context.GetStringCommonAttr("request_id").value_or("");
    std::string request_id(request_id_attr.data(), request_id_attr.size());
    auto kwai_source_0_attr = context.GetStringCommonAttr("kwai_source_0").value_or("");
    std::string kwai_source_0(kwai_source_0_attr.data(), kwai_source_0_attr.size());
    auto yellow_cart_dict_name = context.GetStringCommonAttr("yellow_cart_dict_name").value_or("vert");
    auto icon_label_set_upper_limit = context.GetIntCommonAttr("icon_label_set_upper_limit").value_or(0);
    auto cur_icon_num = context.GetIntCommonAttr("cur_icon_num").value_or(0);

    std::shared_ptr<::mmu::util::HashMapData> hash_map_ptr =
        ::mmu::util::DictManager::GetDict<::mmu::util::HashMapData>(
            "video", std::string(yellow_cart_dict_name.data(), yellow_cart_dict_name.size()));
    if (hash_map_ptr == nullptr) {
      CL_LOG(ERROR) << "SetIconNameForYellowCartQueries failed to get dict:"
                    << std::string(yellow_cart_dict_name.data(), yellow_cart_dict_name.size());
      return false;
    }
    std::vector<std::pair<std::string, double>> query_intent_score_vec;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(item_id_attr_str.data(), item_id_attr_str.size());
      std::string shop_intent_score_str = hash_map_ptr->GetValue(item_id_str);
      CL_LOG(INFO) << "item_id_str: " << item_id_str << ", shop_intent_score_str: " << shop_intent_score_str;
      if (shop_intent_score_str != "") {
        double shop_intent_score = -1;
        if (base::StringToDouble(shop_intent_score_str, &shop_intent_score)) {
          if (shop_intent_score >= 0) {
            query_intent_score_vec.emplace_back(std::pair(item_id_str, shop_intent_score));
          }
        }
      }
    });
    std::sort(query_intent_score_vec.begin(), query_intent_score_vec.end(),
              [](const std::pair<std::string, double> &a, const std::pair<std::string, double> &b) -> bool {
                return a.second > b.second;
              });
    int64_t yellow_cart_icon_limit = icon_label_set_upper_limit - cur_icon_num;
    yellow_cart_icon_limit = yellow_cart_icon_limit > gys_max_yellow_cart_icon_num_ab_exp
                                 ? gys_max_yellow_cart_icon_num_ab_exp
                                 : yellow_cart_icon_limit;
    std::unordered_set<std::string> yellow_cart_icon_query_set;
    for (int i = 0; i < query_intent_score_vec.size() && i < yellow_cart_icon_limit; i++) {
      yellow_cart_icon_query_set.insert(query_intent_score_vec[i].first);
      CL_LOG(INFO) << "top intent query: " << query_intent_score_vec[i].first;
    }
    std::string icon_name = "yellow_cart_url";
    if (gys_yellow_cart_use_new_icon == 1) {
      icon_name = "new_yellow_cart_url";
    }
    static char shop_format[] =
        "kwai://search?keyword=%s&sessionId=%s&fromPage=%s&selectedTabType=goods&source=%s";
    static char k_box_format[] =
        "kwai://"
        "search?keyword=%s&sessionId=%s&fromPage=%s&selectedTabType=new&source=%s&extParams={\"p2q_src\":\"%"
        "s\"}&alwaysUploadExtParams=true";
    auto icon_name_set_func = context.SetStringItemAttr("icon_name");
    auto url_set_func = context.SetStringItemAttr("url");
    auto item_is_refer_source_attr = context.GetIntItemAttr("item_is_refer_source");
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(item_id_attr_str.data(), item_id_attr_str.size());
      if (yellow_cart_icon_query_set.find(item_id_str) == yellow_cart_icon_query_set.end()) {
        CL_LOG(INFO) << "not yellow cart icon query";
        continue;
      }
      auto item_is_refer_source = item_is_refer_source_attr(result).value_or(0);
      if (item_is_refer_source == 1 && it == begin) {
        CL_LOG(INFO) << "is first and refer source";
        continue;
      }
      icon_name_set_func(result, icon_name);
      if (gys_shop_url_type_exp == "vert") {
        std::string url = ::base::StringPrintf(shop_format, item_id_str.c_str(), request_id.c_str(),
                                               from_page.c_str(), kwai_source_0.c_str());
        url_set_func(result, url);
      } else if (gys_shop_url_type_exp == "k_box") {
        std::string url = ::base::StringPrintf(k_box_format, item_id_str.c_str(), request_id.c_str(),
                                               from_page.c_str(), kwai_source_0.c_str(), "GOODS");
        url_set_func(result, url);
      }
    }

    return true;
  }

  static bool SetIconForCoupon(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto gys_max_coupon_icon_num_ab_exp =
        context.GetIntCommonAttr("gys_max_coupon_icon_num_ab_exp").value_or(0);
    auto icon_label_set_upper_limit = context.GetIntCommonAttr("icon_label_set_upper_limit").value_or(0);
    auto cur_icon_num = context.GetIntCommonAttr("cur_icon_num").value_or(0);

    std::shared_ptr<::mmu::util::HashMapData> hash_map_ptr =
        ::mmu::util::DictManager::GetDict<::mmu::util::HashMapData>("video", "gys_shop_icon_query_score");
    if (hash_map_ptr == nullptr) {
      CL_LOG(ERROR) << "gys_shop_icon_query_score failed to get dict";
      return false;
    }
    std::vector<std::pair<std::string, double>> query_intent_score_vec;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(item_id_attr_str.data(), item_id_attr_str.size());
      std::string shop_intent_score_str = hash_map_ptr->GetValue(item_id_str);
      if (shop_intent_score_str != "") {
        VLOG(2) << "item_id_str: " << item_id_str << ", shop_intent_score_str: " << shop_intent_score_str;
        double shop_intent_score = -1;
        if (base::StringToDouble(shop_intent_score_str, &shop_intent_score)) {
          if (shop_intent_score >= 0) {
            query_intent_score_vec.emplace_back(std::pair(item_id_str, shop_intent_score));
          }
        }
      }
    });
    std::sort(query_intent_score_vec.begin(), query_intent_score_vec.end(),
              [](const std::pair<std::string, double> &a, const std::pair<std::string, double> &b) -> bool {
                return a.second > b.second;
              });
    int64_t coupon_icon_limit = icon_label_set_upper_limit - cur_icon_num;
    coupon_icon_limit = coupon_icon_limit > gys_max_coupon_icon_num_ab_exp ? gys_max_coupon_icon_num_ab_exp
                                                                           : coupon_icon_limit;
    VLOG(2) << "coupon_icon_limit:" << std::to_string(coupon_icon_limit);
    std::unordered_set<std::string> coupon_icon_query_set;
    for (int i = 0; i < query_intent_score_vec.size() && i < coupon_icon_limit; i++) {
      coupon_icon_query_set.insert(query_intent_score_vec[i].first);
      CL_LOG(INFO) << "top intent query: " << query_intent_score_vec[i].first;
    }
    std::string icon_name = "coupon";
    auto icon_name_set_func = context.SetStringItemAttr("icon_name");
    auto item_is_refer_source_attr = context.GetIntItemAttr("item_is_refer_source");
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(item_id_attr_str.data(), item_id_attr_str.size());
      if (coupon_icon_query_set.find(item_id_str) == coupon_icon_query_set.end()) {
        continue;
      }
      auto item_is_refer_source = item_is_refer_source_attr(result).value_or(0);
      if (item_is_refer_source == 1 && it == begin) {
        CL_LOG(INFO) << "is first and refer source";
        continue;
      }
      VLOG(2) << "set coupon icon item:" << item_id_str;
      icon_name_set_func(result, icon_name);
    }
    return true;
  }

  static bool EnrichIconLabelAttrs(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto icon_loader =
        Singleton<
            ::ks::search::ziya_sort::KconfTimerReloader<::ks::search::ziya_sort::IconLabelReloader>>::get()
            ->get();
    if (icon_loader == nullptr) {
      CL_LOG(ERROR) << "EnrichIconLabelAttrs icon_loader is nullptr.";
      return false;
    }

    auto icon_upper_limit = context.GetIntCommonAttr("icon_label_set_upper_limit").value_or(0);
    CL_LOG(INFO) << "icon_upper_limit: " << icon_upper_limit;
    if (icon_upper_limit <= 0) {
      return false;
    }

    std::vector<std::pair<std::string, int64_t>> query_and_icon_level_vec;
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto icon_level_attr = context.GetIntItemAttr("icon_level");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_attr_str = item_id_attr(result).value_or("");
      int64_t icon_level = icon_level_attr(result).value_or(-1);
      if (icon_level > 0 && item_id_attr_str != "") {
        CL_LOG(INFO) << "icon_level: " << icon_level
                     << ", item_id: " << std::string(item_id_attr_str.data(), item_id_attr_str.size());
        query_and_icon_level_vec.emplace_back(
            std::pair(std::string(item_id_attr_str.data(), item_id_attr_str.size()), icon_level));
      }
    });

    std::sort(query_and_icon_level_vec.begin(), query_and_icon_level_vec.end(),
              [](const std::pair<std::string, int64_t> &a, const std::pair<std::string, int64_t> &b) -> bool {
                return a.second < b.second;
              });

    std::unordered_set<std::string> icon_query_set;
    for (int i = 0; i < query_and_icon_level_vec.size() && i < icon_upper_limit; i++) {
      icon_query_set.insert(query_and_icon_level_vec[i].first);
      CL_LOG(INFO) << "target_query: " << query_and_icon_level_vec[i].first;
    }
    auto icon_name_attr = context.GetStringItemAttr("icon_name");
    auto icon_text_set_func = context.SetStringItemAttr("icon_text");
    auto icon_color_set_func = context.SetStringItemAttr("icon_color");
    auto icon_text_color_set_func = context.SetStringItemAttr("icon_text_color");
    auto icon_shadow_color_set_func = context.SetStringItemAttr("icon_shadow_color");
    auto icon_url_set_func = context.SetStringItemAttr("icon_url");
    auto url_set_func = context.SetStringItemAttr("url");
    auto icon_width_set_func = context.SetIntItemAttr("icon_width");
    auto icon_height_set_func = context.SetIntItemAttr("icon_height");
    for (auto it = begin; it != end; ++it) {
      const auto &result = *it;
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string item_id_str(std::string(item_id_attr_str.data(), item_id_attr_str.size()));
      if (icon_query_set.find(item_id_str) == icon_query_set.end()) {
        continue;
      }
      auto icon_name_attr_str = icon_name_attr(result).value_or("");
      std::string icon_name(std::string(icon_name_attr_str.data(), icon_name_attr_str.size()));
      if (icon_name == "operation") {  //  运营干预 icon
        ::mmu::video::InterfereGysStyle interfere_style;
        if (icon_loader->get_icon_style(item_id_str, &interfere_style)) {
          switch (interfere_style.gys_icon_style_case()) {
            case ::mmu::video::InterfereGysStyle::kTextLabel:
              icon_name = interfere_style.text_label();  //  文本类型 icon
              break;
            case ::mmu::video::InterfereGysStyle::kIconUrl:  // url 类型 icon
              icon_url_set_func(result, interfere_style.icon_url());
              icon_width_set_func(result, interfere_style.icon_image_width());
              icon_height_set_func(result, interfere_style.icon_image_height());
              break;
            default:
              break;
          }
          if (!interfere_style.query_jump_url().empty()) {
            url_set_func(result, interfere_style.query_jump_url());
          }
          if (icon_name == "operation") {
            continue;
          }
        } else {
          CL_LOG(WARNING) << "load style false, item_id: " << item_id_str;
          continue;
        }
      }
      ::ks::search::ziya_sort::IconStyle is;
      if (icon_loader->get_icon_conf(icon_name, &is)) {
        VLOG(2) << icon_name << ", " << is.url << ", " << is.width << ", " << is.height << ", " << is.text
                << ", " << is.color << ", " << is.text_color << ", " << is.shadow_color;
        if (is.url != "") {
          icon_url_set_func(result, is.url);
        }
        if (is.text != "") {
          icon_text_set_func(result, is.text);
        }
        if (is.color != "") {
          icon_color_set_func(result, is.color);
        }
        if (is.text_color != "") {
          icon_text_color_set_func(result, is.text_color);
        }
        if (is.shadow_color != "") {
          icon_shadow_color_set_func(result, is.shadow_color);
        }
        icon_width_set_func(result, is.width);
        icon_height_set_func(result, is.height);
      } else {
        CL_LOG(INFO) << "load icon false, icon_name: " << icon_name;
      }
    }
    return true;
  }

  static bool OverwriteIconTextWithKconfOperation(const CommonRecoLightFunctionContext &context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto icon_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::OperationQuery2IconReloader>>::get()
                           ->get();
    if (icon_loader == nullptr) {
      CL_LOG(ERROR) << "OverwriteIconTextWithKconfOperation icon_loader is nullptr.";
      return false;
    }
    auto kwai_source_attr = context.GetStringCommonAttr("kwaiSource").value_or("");
    std::string kwai_source(kwai_source_attr.data(), kwai_source_attr.size());
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto icon_text_set_func = context.SetStringItemAttr("icon_text");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_attr_str = item_id_attr(result).value_or("");
      std::string query(item_id_attr_str.data(), item_id_attr_str.size());

      std::string icon_text = icon_loader->get_operation_icon_name(query, kwai_source);
      if (!icon_text.empty()) {
        icon_text_set_func(result, icon_text);
      }
    });
    return true;
  }

  static bool GetOperationFVFSInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto opeation_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::FirstViewForceShowOptionsReloader>>::get()
                                 ->get();
    if (opeation_reloader == nullptr) {
      CL_LOG(ERROR) << "GetOperationFVFSInfo opeation_reloader is nullptr.";
      return false;
    }

    auto user_id = context.GetStringCommonAttr("user_id_str").value_or("");
    std::string user_id_str(user_id.data(), user_id.size());

    int fvfs_response_status = opeation_reloader->get_response_status();
    int64_t fvfs_total_threshold_pv = opeation_reloader->get_threshold_pv();
    int fvfs_is_white_user = opeation_reloader->is_white_user(user_id_str);
    int fvfs_size_of_white_users = opeation_reloader->get_size_of_white_users();
    std::string fvfs_limited_users_prefix = opeation_reloader->get_specified_users_prefix();
    int fvfs_user_threshold_pv = opeation_reloader->get_user_threshold_pv();
    std::vector<std::string> fvfs_show_query_list = opeation_reloader->get_first_view_force_show_queries();
    int fvfs_is_allow_dup = opeation_reloader->get_allow_dup();
    int fvfs_insert_pos = opeation_reloader->get_insert_pos();
    int fvfs_expire_timestamp = opeation_reloader->get_expire_timestamp();

    context.SetIntCommonAttr("fvfs_response_status", fvfs_response_status);
    context.SetIntCommonAttr("fvfs_total_threshold_pv", fvfs_total_threshold_pv);
    context.SetIntCommonAttr("fvfs_is_white_user", fvfs_is_white_user);
    context.SetIntCommonAttr("fvfs_size_of_white_users", fvfs_size_of_white_users);
    context.SetStringCommonAttr("fvfs_limited_users_prefix", fvfs_limited_users_prefix);
    context.SetIntCommonAttr("fvfs_user_threshold_pv", fvfs_user_threshold_pv);
    context.SetStringListCommonAttr("fvfs_show_query_list", std::move(fvfs_show_query_list));
    context.SetIntCommonAttr("fvfs_is_allow_dup", fvfs_is_allow_dup);
    context.SetIntCommonAttr("fvfs_insert_pos", fvfs_insert_pos);
    context.SetIntCommonAttr("fvfs_expire_timestamp", fvfs_expire_timestamp);
    return true;
  }

  static bool OperationFVFSSetPV(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto opeation_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::FirstViewForceShowPvReloader>>::get()
                                 ->get();
    if (opeation_reloader == nullptr) {
      CL_LOG(ERROR) << "opeation_reloader is nullptr.";
      return false;
    }
    bool status = opeation_reloader->set_pv();
    return status;
  }

  static bool OperationFVFSGetPV(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto opeation_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::FirstViewForceShowPvReloader>>::get()
                                 ->get();
    if (opeation_reloader == nullptr) {
      CL_LOG(ERROR) << "OperationFVFSGetPV opeation_reloader is nullptr.";
      return false;
    }
    int64_t fvfs_cur_pv = opeation_reloader->get_cur_pv();
    context.SetIntCommonAttr("fvfs_cur_pv", fvfs_cur_pv);
    return true;
  }

  static bool GetFVFSUserViewedData(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto fvfs_user_view_key = context.GetStringCommonAttr("fvfs_user_view_key").value_or("");
    auto fvfs_user_view_fields =
        context.GetStringListCommonAttr("fvfs_user_view_fields").value_or(std::vector<absl::string_view>());
    auto fvfs_is_allow_dup = context.GetIntCommonAttr("fvfs_is_allow_dup").value_or(0);
    auto fvfs_show_query_list =
        context.GetStringListCommonAttr("fvfs_show_query_list").value_or(std::vector<absl::string_view>());
    auto data_client = ks::infra::RedisProxyClient::GetRedisClientByKcc("searchRecoOperationUser", 20);
    if (data_client == nullptr) {
      CL_LOG(ERROR) << "failed to init data_client:[searchRecoOperationUser]";
      return false;
    }
    std::string key(fvfs_user_view_key.data(), fvfs_user_view_key.size());
    std::vector<std::string> fields;
    for (size_t i = 0; i < fvfs_user_view_fields.size(); ++i) {
      fields.emplace_back(std::string(fvfs_user_view_fields[i].data(), fvfs_user_view_fields[i].size()));
    }
    std::vector<std::string> values;
    data_client->MultiHashGet(key, fields, &values, 20);
    if (fields.size() != values.size()) {
      CL_LOG(ERROR) << "size of fields and values is not equal.";
      return false;
    }
    std::string fvfs_is_first_request_today = values[0];
    std::string fvfs_cur_user_pv_str = values[1];
    int64_t fvfs_cur_user_pv = 0;
    ::base::StringToInt64(fvfs_cur_user_pv_str, &fvfs_cur_user_pv);
    std::string fvfs_viewed_queries = values[2];
    std::string fvfs_insert_query;
    if (fvfs_is_allow_dup == 1) {
      uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
      int64 target_pos = rand_r(&seed) % fvfs_show_query_list.size();
      if (fvfs_show_query_list.size() != 0) {
        fvfs_insert_query =
            std::string(fvfs_show_query_list[target_pos].data(), fvfs_show_query_list[target_pos].size());
        context.SetStringCommonAttr("fvfs_insert_query", fvfs_insert_query);
      }
    } else {
      std::vector<std::string> viewed_queries_vec;
      ::base::SplitString(fvfs_viewed_queries, "|", &viewed_queries_vec);
      std::unordered_set<std::string> viewed_queries_set(viewed_queries_vec.begin(),
                                                         viewed_queries_vec.end());
      std::vector<std::string> unwatched_show_queries_vec;
      for (int i = 0; i < fvfs_show_query_list.size(); i++) {
        std::string show_query(fvfs_show_query_list[i].data(), fvfs_show_query_list[i].size());
        if (viewed_queries_set.find(show_query) != viewed_queries_set.end()) {
          continue;
        }
        unwatched_show_queries_vec.emplace_back(show_query);
      }
      if (unwatched_show_queries_vec.size() != 0) {
        uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
        int64 target_pos = rand_r(&seed) % unwatched_show_queries_vec.size();
        context.SetStringCommonAttr("fvfs_insert_query", unwatched_show_queries_vec[target_pos]);
      }
    }

    context.SetStringCommonAttr("fvfs_is_first_request_today", fvfs_is_first_request_today);
    context.SetIntCommonAttr("fvfs_cur_user_pv", fvfs_cur_user_pv);
    context.SetStringCommonAttr("fvfs_viewed_queries", fvfs_viewed_queries);

    return true;
  }

  static bool SetFVFSUserViewedData(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto fvfs_user_view_key = context.GetStringCommonAttr("fvfs_user_view_key").value_or("");
    auto fvfs_user_view_fields =
        context.GetStringListCommonAttr("fvfs_user_view_fields").value_or(std::vector<absl::string_view>());
    auto fvfs_cur_user_pv = context.GetIntCommonAttr("fvfs_cur_user_pv").value_or(0);
    auto fvfs_viewed_queries = context.GetStringCommonAttr("fvfs_viewed_queries").value_or("");
    auto fvfs_insert_query = context.GetStringCommonAttr("fvfs_insert_query").value_or("");
    auto cur_timestamp = context.GetIntCommonAttr("cur_timestamp").value_or(0);
    auto fvfs_expire_timestamp = context.GetIntCommonAttr("fvfs_expire_timestamp").value_or(0);

    auto data_client = ks::infra::RedisProxyClient::GetRedisClientByKcc("searchRecoOperationUser", 20);
    if (data_client == nullptr) {
      CL_LOG(ERROR) << "failed to init data_client:[searchRecoOperationUser]";
      return false;
    }
    std::string key(fvfs_user_view_key.data(), fvfs_user_view_key.size());
    std::vector<std::string> fields;
    for (size_t i = 0; i < fvfs_user_view_fields.size(); ++i) {
      fields.emplace_back(std::string(fvfs_user_view_fields[i].data(), fvfs_user_view_fields[i].size()));
    }
    std::vector<std::string> new_values = {"1"};
    std::string fvfs_viewed_queries_str(fvfs_viewed_queries.data(), fvfs_viewed_queries.size());
    fvfs_viewed_queries_str += "|" + std::string(fvfs_insert_query.data(), fvfs_insert_query.size());
    new_values.emplace_back(std::to_string(fvfs_cur_user_pv + 1));
    new_values.emplace_back(fvfs_viewed_queries_str);
    int64 life_in_seconds = fvfs_expire_timestamp - cur_timestamp;
    life_in_seconds = life_in_seconds > 0 ? life_in_seconds : 0;
    data_client->MultiHashSetEx(key, fields, new_values, life_in_seconds, 20);
    return true;
  }

  static bool GetReferOperationStatus(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto opeation_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::OperationReferDataReloader>>::get()
                                 ->get();
    if (opeation_reloader == nullptr) {
      CL_LOG(ERROR) << "GetReferOperationStatus opeation_reloader is nullptr.";
      return false;
    }
    int refer_op_reponse_status = opeation_reloader->get_response_status();
    if (refer_op_reponse_status == 0) {
      return true;
    }
    auto refer_photo_id = context.GetStringCommonAttr("referVideoId").value_or("");
    std::string refer_photo_id_str(refer_photo_id.data(), refer_photo_id.end());

    std::vector<std::string> refer_op_refer_query_vec;
    std::vector<int64> insert_locations;
    std::string refer_op_limited_user_prefix;
    if (!opeation_reloader->get_pid_operation_info(refer_photo_id_str, refer_op_refer_query_vec,
                                                   insert_locations, refer_op_limited_user_prefix)) {
      return true;
    }
    std::vector<int64> refer_op_insert_position_vec(
        insert_locations.begin(), insert_locations.begin() + refer_op_refer_query_vec.size());
    std::vector<int64> refer_op_backup_insert_position_vec(
        insert_locations.begin() + refer_op_refer_query_vec.size(), insert_locations.end());

    context.SetIntCommonAttr("refer_op_reponse_status", 1);
    context.SetStringListCommonAttr("refer_op_refer_query_vec", std::move(refer_op_refer_query_vec));
    context.SetIntListCommonAttr("refer_op_insert_position_vec", std::move(refer_op_insert_position_vec));
    context.SetIntListCommonAttr("refer_op_backup_insert_position_vec",
                                 std::move(refer_op_backup_insert_position_vec));
    context.SetStringCommonAttr("refer_op_limited_user_prefix", refer_op_limited_user_prefix);
    return true;
  }

  static bool GetFlowOperationInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto options_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::FlowOperationReloader>>::get()
                                ->get();
    if (options_reloader == nullptr) {
      CL_LOG(ERROR) << "options_reloader is nullptr.";
      return false;
    }

    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (user_info == nullptr) {
      CL_LOG(WARNING) << "user info ptr is nullptr";
      return false;
    }
    auto data_client = ks::infra::RedisProxyClient::GetRedisClientByKcc("searchRecoOperationUser", 20);
    if (data_client == nullptr) {
      CL_LOG(ERROR) << "failed to init data_client:[searchRecoOperationUser]";
      return false;
    }

    // 配置暂时写死了
    std::vector<int64_t> effective_way_list = {2, 3};

    std::unordered_set<std::string> insert_query_candidates;
    std::vector<std::string> flow_operation_insert_query_list;
    std::vector<int64_t> flow_operation_insert_position_list;
    std::vector<std::string> flow_operation_trace_id_list;
    std::vector<std::string> flow_operation_nebula_trace_id_list;
    const auto &flow_collections = options_reloader->get_valid_collections();
    int64_t cur_timestamp = base::GetTimestamp() / 1000;
    auto user_id = context.GetStringCommonAttr("user_id_str").value_or("");
    auto biz_prefix = context.GetStringCommonAttr("biz_prefix").value_or("");
    auto photo_id = context.GetStringCommonAttr("referVideoId").value_or("");
    std::string photo_id_str(photo_id.data(), photo_id.size());
    auto city = context.GetStringCommonAttr("city").value_or("");
    std::string city_str(city.data(), city.size());
    auto product_enum = context.GetIntCommonAttr("client_info_product_enum").value_or(0);

    auto age_num = context.GetIntCommonAttr("age_enum").value_or(0);

    auto gender_enum = context.GetIntCommonAttr("basic_info_gender_num").value_or(0);
    auto min_insert_position = context.GetIntCommonAttr("flow_min_insert_pos").value_or(1);
    auto max_insert_position = context.GetIntCommonAttr("flow_max_insert_pos").value_or(1);

    std::unordered_set<::kuaishou::flow::resource::EffectiveWay> effective_way_set;
    for (const auto effective_way : effective_way_list) {
      effective_way_set.insert(::kuaishou::flow::resource::EffectiveWay(effective_way));
    }
    std::string user_id_str(user_id.data(), user_id.size());
    std::string biz_prefix_str(biz_prefix.data(), biz_prefix.size());
    for (const auto &collection : flow_collections) {
      if (collection.long_time_enabled() == false &&
          (cur_timestamp < collection.start_time() || cur_timestamp > collection.end_time())) {
        continue;
      }
      if (effective_way_set.find(collection.effective_way()) == effective_way_set.end()) {
        continue;
      }

      // 人群包 check
      if (!se_reco::FlowOperationCheckProduct(collection, product_enum) ||
          !se_reco::FlowOperationCheckCrowName(collection, user_id_str, "searchRecoOperationUser") ||
          !se_reco::FlowOperationCheckCity(collection, city_str) ||
          !se_reco::FlowOperationCheckGender(collection, gender_enum) ||
          !se_reco::FlowOperationCheckAge(collection, age_num)) {
        continue;
      }

      if (collection.effective_way() == ::kuaishou::flow::resource::BROADCAST ||
          (collection.effective_way() == ::kuaishou::flow::resource::STRONG_INTERVENTION &&
           collection.collection_type() == ::kuaishou::flow::resource::GENERAL_VERTICAL)) {
        std::string key = biz_prefix_str + "_" + std::to_string(collection.id()) + "_" + user_id_str;
        std::string value;
        data_client->Get(key, &value, 20);
        if ("1" == value) {
          continue;
        } else {
          value = "1";
          int64 life_seconds = (collection.end_time() - cur_timestamp) / 1000;
          if (collection.long_time_enabled() == true) {
            // 过期时间设置为一个月
            life_seconds = 30 * 24 * 60 * 60;
          }
          data_client->SetEx(key, value, life_seconds, 20);
        }
      }

      std::string insert_query;
      if (collection.collection_type() == ::kuaishou::flow::resource::GENERAL_VERTICAL) {
        const auto operation_data_ptr = options_reloader->get_operation_data(collection.id());
        if (nullptr == operation_data_ptr) {
          continue;
        }
        if (false == se_reco::GetRandomQuery(*operation_data_ptr, &insert_query)) {
          continue;
        }
      } else if (collection.collection_type() == ::kuaishou::flow::resource::AFTER_VIDEO_VIEW) {
        // refer 强插每次都出
        if (photo_id_str == "0") {
          continue;
        }
        const auto refer_operation_data_ptr = options_reloader->get_refer_operation_data(collection.id());
        if (nullptr == refer_operation_data_ptr) {
          continue;
        }

        auto refer_iter = refer_operation_data_ptr->find(photo_id_str);
        if (refer_operation_data_ptr->end() == refer_iter) {
          continue;
        }
        const auto &queries = refer_iter->second;
        if (false == se_reco::GetRandomQuery(queries, &insert_query)) {
          continue;
        }
      } else {
        continue;
      }

      auto query_iter = insert_query_candidates.find(insert_query);
      if (query_iter != insert_query_candidates.end()) {
        continue;
      }

      int64 insert_location;
      if (collection.effective_way() == ::kuaishou::flow::resource::STRONG_INTERVENTION) {
        // 干预强插有插入位置
        insert_location = collection.effective_position();
        insert_location--;
      } else {
        // 全网广播随机产生一个插入位置
        uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
        insert_location =
            (rand_r(&seed) % (max_insert_position - min_insert_position + 1)) + (min_insert_position - 1);
      }

      if (insert_location < 0 || insert_location >= max_insert_position) {
        continue;
      }

      flow_operation_insert_query_list.emplace_back(insert_query);
      flow_operation_insert_position_list.emplace_back(insert_location);
      flow_operation_trace_id_list.emplace_back(collection.kuaishou_trace_id());
      flow_operation_nebula_trace_id_list.emplace_back(collection.kuaishou_nebula_trace_id());
      insert_query_candidates.insert(insert_query);
    }

    context.SetStringListCommonAttr("flow_operation_insert_query_list",
                                    std::move(flow_operation_insert_query_list));
    context.SetIntListCommonAttr("flow_operation_insert_position_list",
                                 std::move(flow_operation_insert_position_list));
    context.SetStringListCommonAttr("flow_operation_trace_id_list", std::move(flow_operation_trace_id_list));
    context.SetStringListCommonAttr("flow_operation_nebula_trace_id_list",
                                    std::move(flow_operation_nebula_trace_id_list));
    return true;
  }

  static bool GetOperationInsertInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto opeation_reloader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::OperationInsertReloader>>::get()
                                 ->get();
    if (opeation_reloader == nullptr) {
      CL_LOG(ERROR) << "GetOperationInsertInfo opeation_reloader is nullptr.";
      return false;
    }
    std::vector<std::string> operation_insert_word_list = opeation_reloader->get_insert_query_vec();
    std::vector<int64_t> operation_insert_position_list = opeation_reloader->get_insert_postion_vec();

    context.SetStringListCommonAttr("operation_insert_word_list", std::move(operation_insert_word_list));
    context.SetIntListCommonAttr("operation_insert_position_list", std::move(operation_insert_position_list));
    return true;
  }

  static bool EnrichPymkRecallFromShiva(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client = ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("se_reco_gyk", FLAGS_pymk_proxy_grpc_service));
    ::mmu::dup::ShivaFeaturesRequest request_feature;
    request_feature.set_biz("se_reco_gyk");
    request_feature.set_features_key("pymkRecoUsers");
    auto session_id_attr = context.GetStringCommonAttr("request_id").value_or("");
    std::string session_id(session_id_attr.begin(), session_id_attr.end());
    auto user_id = context.GetIntCommonAttr("user_id").value_or(0);
    request_feature.mutable_context()->set_id(session_id);
    auto session_feature = request_feature.mutable_context()->add_features();
    session_feature->set_name("sessionId");
    session_feature->set_string(session_id);
    auto user_feature = request_feature.mutable_context()->add_features();
    user_feature->set_name("userId");
    user_feature->set_int64(user_id);
    ::mmu::dup::ShivaFeaturesResponse response_feature;
    ks::kess::rpc::grpc::Options request_options;
    request_options.SetTimeout(std::chrono::milliseconds(1000));
    auto status = client->All()->SelectOne()->Stub<::mmu::dup::kess::ShivaFeatureService::Stub>().GetFeature(
        request_options, request_feature, &response_feature);
    if (!status.ok()) {
      LOG(WARNING) << "rpc failed, code: " + std::to_string(status.error_code()) +
                          ", msg: " + status.error_message();
      return false;
    }
    for (const auto &ctx : response_feature.contexts()) {
      for (const auto &f : ctx.features()) {
        if (f.name() == "scores") {
          std::vector<double> score_vec(f.float_list().value().begin(), f.float_list().value().end());
          context.SetDoubleListCommonAttr("pymk_author_scores", std::move(score_vec));
        }
        if (f.name() == "users") {
          std::vector<int64> user_vec(f.int64_list().value().begin(), f.int64_list().value().end());
          context.SetIntListCommonAttr("pymk_author_users", std::move(user_vec));
        }
        if (f.name() == "reasons") {
          std::vector<std::string> reason_vec(f.string_list().value().begin(), f.string_list().value().end());
          context.SetStringListCommonAttr("pymk_author_reasons", std::move(reason_vec));
        }
      }
    }
    return true;
  }

  static bool ParseSearchQueryEmbeddingProtobufList(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto searched_query_embedding_str_list =
        context.GetStringListCommonAttr("searched_query_embedding_str_list")
            .value_or(std::vector<absl::string_view>());

    std::vector<double> query_embedding_list;
    int64_t query_cnt = 0;
    int64_t max_query_cnt = 10;
    for (const auto &embedding_attr : searched_query_embedding_str_list) {
      std::string embedding_str(embedding_attr.data(), embedding_attr.size());
      if (embedding_str.empty()) {
        continue;
      }
      std::string embedding_str_bytes;
      if (!::base::Base64Decode(embedding_str, &embedding_str_bytes)) {
        continue;
      }
      ::ks::search::ziya_sort::RecoQueryFeature query_info;
      if (!query_info.ParseFromString(embedding_str_bytes)) {
        continue;
      }
      for (uint32_t i = 0; i < query_info.embedding_size(); ++i) {
        query_embedding_list.push_back(query_info.embedding(i));
      }
      if (++query_cnt >= max_query_cnt) {
        break;
      }
    }

    context.SetDoubleListCommonAttr("search_query_embedding_list", std::move(query_embedding_list));
    return true;
  }

  static bool ParseRecAfterSearchQueryEmbeddingProtobufList(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto rec_after_search_query_embedding_str_list =
        context.GetStringListCommonAttr("rec_after_search_keyword_embedding_str_list")
            .value_or(std::vector<absl::string_view>());

    std::vector<double> query_embedding_list;
    int64_t query_cnt = 0;
    int64_t max_query_cnt = 30;
    for (const auto &embedding_attr : rec_after_search_query_embedding_str_list) {
      std::string embedding_str(embedding_attr.data(), embedding_attr.size());
      if (embedding_str.empty()) {
        continue;
      }
      std::string embedding_str_bytes;
      if (!::base::Base64Decode(embedding_str, &embedding_str_bytes)) {
        continue;
      }
      ::ks::search::ziya_sort::RecoQueryFeature query_info;
      if (!query_info.ParseFromString(embedding_str_bytes)) {
        continue;
      }
      for (uint32_t i = 0; i < query_info.embedding_size(); ++i) {
        query_embedding_list.push_back(query_info.embedding(i));
      }
      if (++query_cnt >= max_query_cnt) {
        break;
      }
    }

    context.SetDoubleListCommonAttr("rec_after_search_keyword_embedding_list_feat",
                                std::move(query_embedding_list));
    return true;
  }

  static bool ParseSearchQueryEmbeddingProtobufListNew(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto searched_query_embedding_str_list =
        context.GetStringListCommonAttr("searched_query_embedding_str_list_q2q")
            .value_or(std::vector<absl::string_view>());

    std::vector<double> query_embedding_list;
    int64_t query_cnt = 0;
    int64_t max_query_cnt = 10;
    for (const auto &embedding_attr : searched_query_embedding_str_list) {
      std::string embedding_str(embedding_attr.data(), embedding_attr.size());
      if (embedding_str.empty()) {
        continue;
      }
      std::string embedding_str_bytes;
      if (!::base::Base64Decode(embedding_str, &embedding_str_bytes)) {
        continue;
      }
      ::ks::search::ziya_sort::RecoQueryFeature query_info;
      if (!query_info.ParseFromString(embedding_str_bytes)) {
        continue;
      }
      for (uint32_t i = 0; i < query_info.embedding_size(); ++i) {
        query_embedding_list.push_back(query_info.embedding(i));
      }
      if (++query_cnt >= max_query_cnt) {
        break;
      }
    }

    context.SetDoubleListCommonAttr("search_query_embedding_list_q2q", std::move(query_embedding_list));
    return true;
  }

  static bool ParseSearchQueryEmbeddingProtobufListNewHot(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto searched_query_embedding_str_list =
        context.GetStringListCommonAttr("searched_query_embedding_str_list_new")
            .value_or(std::vector<absl::string_view>());
    auto searched_query_list =
        context.GetStringListCommonAttr("searched_queries_query_key_new")
            .value_or(std::vector<absl::string_view>());

    std::vector<double> query_embedding_list;
    std::vector<std::string> query_list;
    int64_t query_cnt = 0;
    int64_t max_query_cnt = 10;
    int idx = -1;
    for (const auto &embedding_attr : searched_query_embedding_str_list) {
      ++idx;

      std::string embedding_str(embedding_attr.data(), embedding_attr.size());
      if (embedding_str.empty()) {
        continue;
      }
      std::string embedding_str_bytes;
      if (!::base::Base64Decode(embedding_str, &embedding_str_bytes)) {
        continue;
      }
      ::ks::search::ziya_sort::RecoQueryFeature query_info;
      if (!query_info.ParseFromString(embedding_str_bytes)) {
        continue;
      }
      for (uint32_t i = 0; i < query_info.embedding_size(); ++i) {
        query_embedding_list.push_back(query_info.embedding(i));
      }
      if (idx < searched_query_list.size()) {
        std::string o_query(searched_query_list[idx].data(), searched_query_list[idx].size());
        query_list.push_back(o_query);
      }
      if (++query_cnt >= max_query_cnt) {
        break;
      }
    }

    context.SetDoubleListCommonAttr("search_query_embedding_list_newhot", std::move(query_embedding_list));
    context.SetStringListCommonAttr("search_query_list_q2q_newhot", std::move(query_list));
    return true;
  }

  static bool ParseSearchQueryEmbeddingProtobufListGeneral(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto searched_query_embedding_str_list =
        context.GetStringListCommonAttr("searched_query_embedding_str_list_q2q")
            .value_or(std::vector<absl::string_view>());
    auto searched_query_list =
        context.GetStringListCommonAttr("searched_queries_query_key_for_q2q")
            .value_or(std::vector<absl::string_view>());
    auto output_attr_name1 = context.GetStringCommonAttr("my_light_function_output_attr_name1")
            .value_or("search_query_embedding_list_q2q");
    auto output_attr_name2 = context.GetStringCommonAttr("my_light_function_output_attr_name2")
            .value_or("search_query_list_q2q");

    std::vector<double> query_embedding_list;
    std::vector<std::string> query_list;
    int64_t query_cnt = 0;
    int64_t max_query_cnt = 10;
    int idx = -1;
    for (const auto &embedding_attr : searched_query_embedding_str_list) {
      ++idx;

      std::string embedding_str(embedding_attr.data(), embedding_attr.size());
      if (embedding_str.empty()) {
        continue;
      }
      std::string embedding_str_bytes;
      if (!::base::Base64Decode(embedding_str, &embedding_str_bytes)) {
        continue;
      }
      ::ks::search::ziya_sort::RecoQueryFeature query_info;
      if (!query_info.ParseFromString(embedding_str_bytes)) {
        continue;
      }
      for (uint32_t i = 0; i < query_info.embedding_size(); ++i) {
        query_embedding_list.push_back(query_info.embedding(i));
      }
      if (idx < searched_query_list.size()) {
        std::string o_query(searched_query_list[idx].data(), searched_query_list[idx].size());
        query_list.push_back(o_query);
      }
      if (++query_cnt >= max_query_cnt) {
        break;
      }
    }

    context.SetDoubleListCommonAttr(output_attr_name1, std::move(query_embedding_list));
    context.SetStringListCommonAttr(output_attr_name2, std::move(query_list));
    return true;
  }

  static bool StringHashCodeLikeJava(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto input_string_view = context.GetStringCommonAttr("hashcode_input_string_attr").value_or("");
    std::string input_string(input_string_view);
    int32 result = 0;
    if (result == 0 && input_string.size() > 0) {
      for (int i = 0; i < input_string.size(); i++) {
        result = 31 * result + input_string.at(i);
      }
    }
    context.SetIntCommonAttr("hashcode_output_int_attr", std::move(result));
    return true;
  }

  static bool ModifyReferIDAndLiveId(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    // input common attr: referVideoId:string, live_id:int, user_info
    // output common attr: referVideoId:string, live_id:int, photo_ids_del_list, user_info
    auto referVideoId_attr = context.GetStringCommonAttr("referVideoId").value_or("0");
    auto live_id = context.GetIntCommonAttr("live_id").value_or(0);
    std::string referVideoId_str(referVideoId_attr);
    std::string live_id_str = std::to_string(live_id);
    std::vector<std::string> photo_keys;
    std::vector<std::string> redis_keys;
    if (referVideoId_str != "0") {
      photo_keys.emplace_back(referVideoId_str);
      redis_keys.emplace_back("spf_" + referVideoId_str);
    }
    if (live_id_str != "0") {
      photo_keys.emplace_back(live_id_str);
      redis_keys.emplace_back("spf_" + live_id_str);
    }
    if (redis_keys.empty()) {
      return true;
    }
    mmu::util::DataClientWrapper data_client("searchPhotoFilter");
    if (!data_client) {
      CL_LOG(WARNING) << "searchPhotoFilter redis is nullptr";
      return true;
    }
    std::vector<std::string> filter_fields = {"1"};
    std::vector<std::vector<std::string>> values;
    if (!data_client->BatchAsyncHMGet(redis_keys, filter_fields, values, 20)) {
      values.clear();
      for (const auto &key : redis_keys) {
        values.push_back(std::vector<std::string>{"1"});
      }
    }
    std::unordered_set<std::string> photo_ids_del;
    for (size_t i = 0; i < values.size() && i < photo_keys.size(); ++i) {
      if (std::any_of(values[i].begin(), values[i].end(), [](const std::string &s) { return s == "1"; })) {
        photo_ids_del.emplace(photo_keys[i]);
      }
    }
    std::string refer_photo_id_stat = photo_ids_del.count(referVideoId_str) ? "1" : "0";
    std::string live_id_stat = photo_ids_del.count(live_id_str) ? "1" : "0";
    auto default_set = std::make_shared<std::set<std::string>>();
    auto black_refer_photo_ids_set =
        ks::infra::KConf().GetSet<std::string>("se.ziyasort.black_refer_photo_ids", default_set)->Get();

    if (refer_photo_id_stat == "1" ||
        (black_refer_photo_ids_set && black_refer_photo_ids_set->count(referVideoId_str))) {  // 清空 refer
      context.SetStringCommonAttr("referVideoId", "0");
      photo_ids_del.insert(referVideoId_str);
    }
    if (live_id_stat == "1" ||
        (black_refer_photo_ids_set && black_refer_photo_ids_set->count(live_id_str))) {  // 清空 live id
      context.SetIntCommonAttr("live_id", 0);
      photo_ids_del.insert(live_id_str);
    }
    if (photo_ids_del.empty()) {
      return true;
    }
    std::vector<std::string> photo_ids_vec{photo_ids_del.begin(), photo_ids_del.end()};
    context.SetStringListCommonAttr("photo_ids_del", std::move(photo_ids_vec));
    const auto *const_user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (const_user_info == nullptr) {
      CL_LOG(WARNING) << "user info ptr is nullptr";
      return true;
    }
    auto *user_info = const_cast<ks::reco::UserInfo *>(const_user_info);

#define DEL_SE_USER_INFO_PHOTO(photoType)                                          \
  {                                                                                \
    auto photo_list = user_info->mutable_user_profile_v1()->mutable_##photoType(); \
    auto it = photo_list->begin();                                                 \
    while (it != photo_list->end()) {                                              \
      std::string photo_id_str = std::to_string(it->photo_id());                   \
      if (photo_ids_del.count(std::to_string(it->photo_id()))) {                   \
        VLOG(2) << "erase_photo:" << std::to_string(it->photo_id());               \
        it = photo_list->erase(it);                                                \
      } else {                                                                     \
        ++it;                                                                      \
      }                                                                            \
    }                                                                              \
  }

    DEL_SE_USER_INFO_PHOTO(click_list);
    DEL_SE_USER_INFO_PHOTO(like_list);
    DEL_SE_USER_INFO_PHOTO(video_playing_stat);
#undef DEL_SE_USER_INFO_PHOTO

    return true;
  }

  static bool ParseClientRealActions(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto del_photo_ids =
        context.GetStringListCommonAttr("photo_ids_del").value_or(std::vector<absl::string_view>());
    using DedupSetType = folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>>;
    DedupSetType del_photo_ids_set;
    if (!del_photo_ids.empty()) {
      del_photo_ids_set.insert(del_photo_ids.begin(), del_photo_ids.end());
    }
    auto actions =
        context.GetStringListCommonAttr("client_real_actions").value_or(std::vector<absl::string_view>());
    if (actions.empty()) {
      CL_LOG(WARNING) << "client_real_actions is empty";
      return true;
    }
    uint64_t photo_id_v2 = 0, timestamp = 0, duration = 0;

    std::string source = "";
    mix::kuaishou::client::ClientRealActionFeed *last_action = nullptr;
    mix::kuaishou::client::ClientRealActionFeed action;
    for (size_t i = 0; i < actions.size(); ++i) {
      std::string action_str(actions.at(i));
      action.Clear();
      if (!action.ParseFromString(action_str)) {
        CL_LOG(ERROR) << "refer info, action parse failed";
        continue;
      }
      absl::string_view photo_id_str_view(action.feed_id());
      if (del_photo_ids_set.find(photo_id_str_view) != del_photo_ids_set.end()) {
        VLOG(2) << "del photo:" << action.feed_id();
        continue;
      }
      uint64_t photo_id = atoll(action.feed_id().c_str());

      VLOG(2) << "refer info, photo: " << photo_id << ", action: " << action.Utf8DebugString();
      std::string exp_tag = "";
      for (size_t j = 0; j < action.action_detail_size(); j++) {
        auto action_detail = action.action_detail(j);
        size_t found = action_detail.client_exp_tag().rfind('_');
        if (found != std::string::npos) {
          exp_tag = action_detail.client_exp_tag().substr(found + 1);
          break;
        }
      }
      bool isJingXuan = exp_tag.substr(0, 2) == "bs";
      for (size_t j = 0; j < action.action_detail_size(); j++) {
        auto action_detail = action.action_detail(j);
        if (action_detail.timestamp() > timestamp) {
          if (isJingXuan && (action_detail.action_type() == 1)) {
            photo_id_v2 = photo_id;
            timestamp = action_detail.timestamp();
            duration = 0;
            source = "refer_v1_bs";
            last_action = &action;
          } else if (!isJingXuan && (action_detail.action_type() == 7)) {
            photo_id_v2 = photo_id;
            timestamp = action_detail.timestamp();
            duration = action_detail.play_length();
            source = "refer_v1";
            last_action = &action;
          }
        }
      }
    }
    context.SetIntCommonAttr("latest_photo_id_v2", photo_id_v2);
    context.SetIntCommonAttr("latest_photo_id_v2_play_duration", duration);
    context.SetStringCommonAttr("latest_photo_id_v2_source", source);
    if (last_action) {
      std::string client_real_action_feed_base64;
      base::Base64Encode(last_action->SerializeAsString(), &client_real_action_feed_base64);
      context.SetStringCommonAttr("latest_photo_id_v2_action_base64", client_real_action_feed_base64);
    }
    return true;
  }

  static bool ShapedQueryProcess(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    /*
    import common att: gys_boost_shaped_query_max_rank, request_count
      shaped_item_id_list, shaped_item_author_id_list
    import item attr: item_id
    export item attr: item_shaped_query_pattern, item_shaped_query_bizname_for_signal, item_recommend_reason
    */
    auto shaped_item_id_list =
        context.GetStringListCommonAttr("shaped_item_id_list").value_or(std::vector<absl::string_view>());
    auto shaped_item_author_id_list =
        context.GetIntListCommonAttr("shaped_item_author_id_list").value_or(absl::Span<const int64>());
    folly::F14FastMap<absl::string_view, int64_t, absl::Hash<absl::string_view>> shaped_query_map;
    if (shaped_item_id_list.size() != shaped_item_author_id_list.size()) {
      CL_LOG(ERROR) << "shaped not match size";
      return true;
    }
    for (size_t i = 0; i < shaped_item_id_list.size(); ++i) {
      shaped_query_map[shaped_item_id_list[i]] = shaped_item_author_id_list[i];
    }

    auto boost_rank_max = context.GetIntCommonAttr("gys_boost_shaped_query_max_rank").value_or(0);
    auto request_count = context.GetIntCommonAttr("request_count").value_or(0);
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto force_insert_func = context.SetIntItemAttr("item_shaped_force_insert_pos");
    auto shaped_query_pattern_func = context.SetStringItemAttr("item_shaped_query_pattern");
    auto shaped_query_bizname_func = context.SetStringItemAttr("item_shaped_query_bizname_for_signal");
    auto recommend_reason_func = context.SetStringItemAttr("item_recommend_reason");
    auto icon_level_func = context.SetIntItemAttr("icon_level");
    auto icon_name_func = context.SetStringItemAttr("icon_name");

    std::string icon_name = "shaped";
    int index = 0;
    int hit_shaped_cnt = 0;
    int total_size = std::distance(begin, end);
    for (auto iter = begin; iter != end; ++iter) {
      auto item_id_attr_str = item_id_attr(*iter).value_or("");
      if (shaped_query_map.find(item_id_attr_str) != shaped_query_map.end()) {
        auto author_id = shaped_query_map[item_id_attr_str];
        // set item attr, force_insert
        std::string pattern_text = "", recommend_reason = "", bizname = "", icon_url_light = "",
                    icon_url_dark = "";
        if (author_id == 1) {
          pattern_text = "百科";
          recommend_reason = "学点小知识";
          bizname = "ALADDIN_MIXED_BAIKE";
          icon_url_light = "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_baike_light.png";
          icon_url_dark = "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_baike_dark.png";
        } else if (author_id == 2) {
          pattern_text = "看动漫";
          recommend_reason = "在线观看";
          bizname = "ALADDIN_TK_KPMOVIE,TK_ALADDIN_MOVIES,TK_ALADDIN_KPMOVIE";
          icon_url_light =
              "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kandongman_light.png";
          icon_url_dark = "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kandongman_dark.png";
        } else if (author_id == 3) {
          pattern_text = "看漫画";
          recommend_reason = "在线观看";
          bizname = "ALADDIN_TK_KPMOVIE,TK_ALADDIN_MOVIES,TK_ALADDIN_KPMOVIE";
          icon_url_light =
              "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kanmanhua_light.png";
          icon_url_dark = "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kanmanhua_dark.png";
        } else if (author_id == 4) {
          pattern_text = "看短剧";
          recommend_reason = "热播星芒短剧";
          bizname = "ALADDIN_LONGVIDEO_THEATER,ALADDIN_TK_THEATER";
          icon_url_light = "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kanduanju_light.png";
          icon_url_dark = "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kanduanju_dark.png";
        } else if (author_id == 5) {
          pattern_text = "玩游戏";
          recommend_reason = "小游戏在线入口";
          bizname = "ALADDIN_BASE_MINIGAMEMP,TK_ALADDIN_MINIGAMEMP,ALADDIN_TAB_MINIGAME";
          icon_url_light = "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_wanyouxi_light.png";
          icon_url_dark = "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_wanyouxi_dark.png";
        } else if (author_id == 6) {
          pattern_text = "看赛程";
          recommend_reason = "完整赛程";
          bizname = "ALADDIN_MIXED_SPORT_BATTLE,ALADDIN_MIXED_SPORT_MATCH";
          icon_url_light =
              "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kansaicheng_light.png";
          icon_url_dark =
              "https://tx2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_kansaicheng_light.png";
        } else if (author_id == 7) {
          pattern_text = "学学看";
          recommend_reason = "看点实用的";
          bizname = "";
          icon_url_light =
              "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_xuexuekan_light.png";
          icon_url_dark = "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_home/search_xuexuekan_dark.png";
        }
        ::Json::FastWriter jw;
        ::Json::Value query_pattern;
        query_pattern["text"] = pattern_text;
        query_pattern["icon_url_light"] = icon_url_light;
        query_pattern["icon_url_dark"] = icon_url_dark;
        query_pattern["icon_height"] = "16";
        query_pattern["icon_width"] = "47";
        if (pattern_text == "百科") {
          query_pattern["icon_width"] = "30";
        }
        shaped_query_pattern_func(*iter, jw.write(query_pattern));
        shaped_query_bizname_func(*iter, bizname);
        recommend_reason_func(*iter, recommend_reason);
        icon_level_func(*iter, 5);
        icon_name_func(*iter, icon_name);

        if (total_size > request_count - 2) {
          force_insert_func(*iter, request_count - 2 + hit_shaped_cnt);
          VLOG(2) << "shaped_insert, item_id:" << std::string(item_id_attr_str)
                  << ", pos:" << std::to_string(request_count - 2 + hit_shaped_cnt);
        }
        ++hit_shaped_cnt;
      }
      if (++index >= boost_rank_max || hit_shaped_cnt >= 2) {
        break;
      }
    }
    return true;
  }

  static bool BoostGnnRecallPosition(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto start_index = context.GetIntCommonAttr("u2q_gnn_query_recall_start_index").value_or(-1);
    auto end_index = context.GetIntCommonAttr("u2q_gnn_query_recall_end_index").value_or(-1);
    auto max_items_count = context.GetIntCommonAttr("u2q_gnn_query_recall_max_items_count").value_or(50);
    if (start_index < 0 || end_index < 0) {
      return true;
    }
    auto skip_regex = context.GetStringCommonAttr("u2q_gnn_query_recall_skip_regex");
    std::regex txt_regex;
    if (skip_regex) {
      txt_regex = std::regex(std::string(skip_regex.value()));
    }

    int index = 0;
    int item_count = std::distance(begin, end);
    auto item_source_attr = context.GetStringItemAttr("item_source");
    std::vector<int> index_vec;
    // 先获取原始位置
    for (auto it = begin; it != end; ++it) {
      CommonRecoResult result = *it;
      auto source = std::string(item_source_attr(result).value_or(""));
      if (source == "u2q_gnn_query_recall" && index_vec.size() < 6 && index < max_items_count) {
        index_vec.emplace_back(index);
      }
      index++;
    }
    auto insert_index_attr = context.SetIntItemAttr("u2q_gnn_query_recall_insert_index");
    // 直接设置到末尾
    for (int i = 0; i < index_vec.size(); i++) {
      int target_item_index = index_vec[i];
      RecoResultConstIter target = begin;
      std::advance(target, target_item_index);
      int64 last_index = item_count - index_vec.size() + i;
      insert_index_attr(*target, last_index);
    }
    index = 0;
    int insert_index = 0;
    auto request_id_attr = context.GetStringCommonAttr("request_id").value_or("");
    std::string request_id(request_id_attr.data(), request_id_attr.size());
    // 设置 boost 的位置
    for (auto it = begin; it != end; ++it) {
      // 找到起始插入位置
      CommonRecoResult result = *it;
      auto source = std::string(item_source_attr(result).value_or(""));
      if (source == "u2q_gnn_query_recall") {
        // 如果是目标 source, 说明已插入到末尾, 直接跳过
        continue;
      }
      if (index < start_index) {
        index++;
        continue;
      }
      // 已超过插入范围直接跳过
      if (index > end_index || insert_index >= index_vec.size()) {
        break;
      }
      if (skip_regex && std::regex_search(source, txt_regex)) {
        // 不强插的 source
        insert_index_attr(result, index);
        index++;
      } else {
        // 把 insert_index 位置的目标数据, 强插到当前位置
        int target_item_index = index_vec[insert_index];
        RecoResultConstIter target = begin;
        std::advance(target, target_item_index);
        insert_index_attr(*target, index);
        insert_index++;
        index++;
      }
    }
    return true;
  }

  static bool AuthorNotLiveFilter(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto target_sources_attr = context.GetStringListCommonAttr("author_not_live_filter_sources");
    if (!target_sources_attr) {
      CL_LOG(ERROR) << "null author_not_live_filter_sources";
      return false;
    }
    std::unordered_set<std::string> target_sources;
    for (auto target_source : target_sources_attr.value()) {
      target_sources.emplace(target_source);
    }
    auto live_data_loader =
        Singleton<
            ::ks::search::ziya_sort::KconfTimerReloader<::ks::search::ziya_sort::AuthorLiveTimeData>>::get()
            ->get();
    if (live_data_loader == nullptr) {
      CL_LOG(ERROR) << "null live_data_loader";
      return false;
    }
    std::vector<uint64_t> author_ids;
    std::unordered_map<uint64_t, uint64_t> author_liveid_map;
    auto author_id_attr = context.GetIntItemAttr("item_author_id");
    auto recall_source_attr = context.GetStringItemAttr("item_source");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string recall_source(recall_source_attr(result).value_or(""));
      if (target_sources.find(recall_source) == target_sources.end()) {
        return;
      }
      auto author_id = author_id_attr(result).value_or(0);
      if (author_id == 0) {
        return;
      }
      author_ids.emplace_back(author_id);
    });
    live_data_loader->get_author_time(author_ids, author_liveid_map);
    if (author_liveid_map.size() == 0) {
      CL_LOG(ERROR) << "author_live_results.size() == 0";
    }
    auto author_not_live_filter_attr = context.SetIntItemAttr("author_not_live_filter");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string recall_source(recall_source_attr(result).value_or(""));
      if (target_sources.find(recall_source) == target_sources.end()) {
        return;
      }
      auto author_id = author_id_attr(result).value_or(0);
      if (author_liveid_map.find(author_id) == author_liveid_map.end()) {
        author_not_live_filter_attr(result, 1);
      }
    });
    return true;
  }

  static bool EncodeUrlOfAdQuerySourceTrace(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto ori_query_source_attr = context.GetStringItemAttr("ad_query_source_trace_str_base64");
    auto encode_query_source_func = context.SetStringItemAttr("ad_query_source_for_signal");

    for (auto it = begin; it != end; ++it) {
      CommonRecoResult result = *it;
      auto ori_query_source = ori_query_source_attr(result).value_or("");
      std::string ori_query_source_str = std::string(ori_query_source.data(), ori_query_source.size());
      if (ori_query_source_str.empty()) {
        continue;
      }
      std::string url_encode_str;
      ::base::FastEncodeUrlComponent(ori_query_source_str.c_str(), &url_encode_str);
      encode_query_source_func(result, url_encode_str);
    }
    return true;
  }

  static bool PackSignalParams(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto ad_query_source_attr = context.GetStringItemAttr("ad_query_source_for_signal");
    auto shaped_query_bizname_attr = context.GetStringItemAttr("item_shaped_query_bizname_for_signal");
    auto author_id_attr = context.GetStringItemAttr("author_id_for_signal");
    auto signal_params_func = context.SetStringItemAttr("item_signal_params");

    for (auto it = begin; it != end; ++it) {
      CommonRecoResult result = *it;
      ::Json::FastWriter jw;
      ::Json::Value signal_params;
      auto ad_query_source = ad_query_source_attr(result).value_or("");
      std::string ad_query_source_str = std::string(ad_query_source.data(), ad_query_source.size());
      if (!ad_query_source_str.empty()) {
        signal_params["adQuerySource"] = ad_query_source_str;
      }
      auto shaped_query_bizname = shaped_query_bizname_attr(result).value_or("");
      std::string shaped_query_bizname_str =
          std::string(shaped_query_bizname.data(), shaped_query_bizname.size());
      if (!shaped_query_bizname_str.empty()) {
        signal_params["shaped_query_bizname"] = shaped_query_bizname_str;
      }
      auto author_id = author_id_attr(result).value_or("");
      std::string author_id_str = std::string(author_id.data(), author_id.size());
      if (!author_id_str.empty()) {
        signal_params["author_id"] = author_id_str;
      }
      if (ad_query_source_str.empty() && shaped_query_bizname_str.empty() && author_id_str.empty()) {
        continue;
      }
      signal_params_func(result, jw.write(signal_params));
    }
    return true;
  }

  static bool GlobalBackupRecall(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // import common attr: referVideoId, kwai_source_0, request_count
    // is_need_flags_and_recommend_list ( 默认为 0 )
    auto referVideoId = context.GetStringCommonAttr("referVideoId").value_or("");
    std::string referVideoId_str(referVideoId);
    auto kwai_source = context.GetStringCommonAttr("kwai_source_0").value_or("");
    auto is_need_flags_and_recommend_list =
        context.GetIntCommonAttr("is_need_flags_and_recommend_list").value_or(1);
    std::string kwai_source_str(kwai_source);
    auto gys_degrade =
        ks::infra::KConf().Get("se.ziyasort.gys_activity_degrade", std::make_shared<::Json::Value>())->Get();
    std::unordered_map<std::string, float> kwai_source_map;
    std::unordered_set<std::string> refer_photos_set;
    std::vector<std::string> candidate_query_vec;

    bool is_degrade = false;
    std::string recommend_reason = "猜你想搜";
    static unsigned int seed = time(nullptr);
    if (gys_degrade->isObject() && gys_degrade->isMember("refer_photo_config")) {
      auto refer_photo_config = (*gys_degrade)["refer_photo_config"];
      for (auto refer_photo_item : refer_photo_config) {
        if (!refer_photo_item.isMember("photo_ids")) continue;
        for (auto each_photo : refer_photo_item["photo_ids"]) {
          if (each_photo == referVideoId_str) {
            VLOG(2) << "hit refer photo:" << each_photo.asString();
            is_degrade = true;
            break;
          }
        }
        if (!is_degrade) continue;
        int32_t degrade_percentage = 100;
        if (refer_photo_item.isMember("degrade_percentage")) {
          degrade_percentage = refer_photo_item["degrade_percentage"].asInt();
        }
        if (degrade_percentage < 100 && (rand_r(&seed) % 100 + 1) > degrade_percentage) {
          continue;
        }
        if (refer_photo_item.isMember("recommend_reason")) {
          recommend_reason = refer_photo_item["recommend_reason"].asString();
        }
        if (refer_photo_item.isMember("queries")) {
          for (auto each_query : refer_photo_item["queries"]) {
            candidate_query_vec.push_back(each_query.asString());
            VLOG(2) << "candidate_query:" << each_query.asString();
          }
        }
        if (is_degrade) break;
      }
    }
    if (!is_degrade && gys_degrade->isObject() && gys_degrade->isMember("kwai_source_config")) {
      auto kwai_source_config = (*gys_degrade)["kwai_source_config"];
      for (auto kwai_config_item : kwai_source_config) {
        int32_t degrade_percentage = 100;
        if (!kwai_config_item.isMember("kwai_sources")) continue;
        if (kwai_config_item.isMember("degrade_percentage")) {
          degrade_percentage = kwai_config_item["degrade_percentage"].asInt();
        }
        if (degrade_percentage < 100 && (rand_r(&seed) % 100 + 1) > degrade_percentage) {
          continue;
        }
        for (auto each_kwai_source : kwai_config_item["kwai_sources"]) {
          if (kwai_source_str == each_kwai_source.asString() || each_kwai_source == "ALL") {
            VLOG(2) << "hit_kwai_source:" << each_kwai_source.asString();
            is_degrade = true;
            break;
          }
        }
        if (!is_degrade) continue;
        if (kwai_config_item.isMember("queries")) {
          for (auto each_query : kwai_config_item["queries"]) {
            candidate_query_vec.push_back(each_query.asString());
          }
        }
        if (kwai_config_item.isMember("recommend_reason")) {
          recommend_reason = kwai_config_item["recommend_reason"].asString();
        }
        if (is_degrade) break;
      }
    }
    VLOG(2) << "is_degrade:" << std::to_string(is_degrade);
    if (!is_degrade) {
      return true;
    }

    auto request_count = context.GetIntCommonAttr("request_count").value_or(1);
    static thread_local std::random_device rd;
    static thread_local std::mt19937 g(rd());
    std::shuffle(candidate_query_vec.begin(), candidate_query_vec.end(), g);
    if (candidate_query_vec.size() > (request_count / 2)) {
      candidate_query_vec.resize(request_count / 2);
    }
    auto newhot_reloader =
        Singleton<
            ks::search::ziya_sort::KconfTimerReloader<ks::search::ziya_sort::NewHotQueryReloader>>::get()
            ->get();
    if (newhot_reloader == nullptr) {
      CL_LOG(ERROR) << "newhot_reloader nullptr";
      return false;
    }
    std::vector<std::string> newhot_queries = newhot_reloader->get_newhot_queries();
    std::shuffle(newhot_queries.begin(), newhot_queries.end(), g);
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> query_set;
    for (const auto &candidate : candidate_query_vec) {
      query_set.insert(candidate);
    }
    std::vector<int64_t> candidate_new_hot_flag_vec(request_count + 1, 0);
    std::vector<std::string> candidate_recommend_vec(request_count + 1, recommend_reason);
    int new_hot_icon_upper = 0;
    if (gys_degrade->isMember("new_hot_icon_upper")) {
      new_hot_icon_upper = (*gys_degrade)["new_hot_icon_upper"].asInt();
    }
    int icon_cnt = 0;
    for (size_t i = 0; i < newhot_queries.size() && candidate_query_vec.size() < request_count; ++i) {
      if (query_set.find(newhot_queries[i]) != query_set.end()) continue;
      candidate_query_vec.push_back(newhot_queries[i]);
      if (++icon_cnt <= new_hot_icon_upper) {
        candidate_new_hot_flag_vec[candidate_query_vec.size() - 1] = 1;
      }
      candidate_recommend_vec[candidate_query_vec.size() - 1] = "猜你想搜";
      query_set.insert(newhot_queries[i]);
      VLOG(2) << "new_hot:" << newhot_queries[i];
    }
    candidate_new_hot_flag_vec.resize(candidate_query_vec.size());
    candidate_recommend_vec.resize(candidate_query_vec.size());
    context.SetStringListCommonAttr("global_candidates", std::move(candidate_query_vec));
    if (is_need_flags_and_recommend_list) {
      context.SetIntListCommonAttr("global_candidates_flags", std::move(candidate_new_hot_flag_vec));
      context.SetStringListCommonAttr("candidate_recommend_list", std::move(candidate_recommend_vec));
    }
    return true;
  }

  static bool BoostCommerceQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto gys_boost_commerce_query_weight =
        context.GetDoubleCommonAttr("gys_boost_commerce_query_weight").value_or(0.0);
    auto gys_boost_commerce_score_threshold =
        context.GetDoubleCommonAttr("gys_boost_commerce_score_threshold").value_or(0.0);
    auto commerce_ad_query_list =
        context.GetStringListCommonAttr("commerce_ad_query_list").value_or(std::vector<absl::string_view>());
    auto commerce_ad_query_score_list =
        context.GetDoubleListCommonAttr("commerce_ad_query_score_list").value_or(absl::Span<const double>());

    if (commerce_ad_query_list.size() != commerce_ad_query_score_list.size()) {
      CL_LOG(WARNING) << "error, mismatch size";
      return false;
    }
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> commerce_query_score;
    for (size_t i = 0; i < commerce_ad_query_list.size(); ++i) {
      commerce_query_score[commerce_ad_query_list.at(i)] = commerce_ad_query_score_list.at(i);
    }

    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    for (auto iter = begin; iter != end; ++iter) {
      auto item_id = item_id_attr(*iter).value_or("");
      auto item_score = item_score_attr(*iter).value_or(0.0);

      double commerce_score = 0.0;
      if (commerce_query_score.find(item_id) != commerce_query_score.end()) {
        commerce_score = commerce_query_score[item_id];
      }
      if (commerce_score < gys_boost_commerce_score_threshold) {
        continue;
      }
      double score = item_score * (1 + (commerce_score - 0.7) * gys_boost_commerce_query_weight);
      VLOG(2) << "commerce_item_id:" << std::string(item_id.data(), item_id.size())
              << ", score:" << std::to_string(score);
      item_score_func(*iter, score);
    }
    return true;
  }

  static bool GetItemUTF8CharNum(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_len_func = context.SetIntItemAttr("item_id_len");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str = std::string(item_id);
      int word_size = 0;
      if (!::base::GetUTF8CharNum(item_id_str, &word_size)) {  // 原有失败会删除 本次置为 0 后续也会被过滤
        word_size = 0;
      }
      item_len_func(result, word_size);
    });
    return true;
  }

  static bool GetItemHashID(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_hash_id_func = context.SetIntItemAttr("item_rough_rank_hash_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_hash = std::to_string(::base::CityHash64(item_id.data(), item_id.size()));
      uint64 item_hash_trunc = static_cast<uint64_t>(std::stoull(item_hash.substr(0, 15)));
      item_hash_id_func(result, item_hash_trunc);
    });
    return true;
  }

  static bool GetUserFollowHashID(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto follow_substr_results =
        context.GetIntListCommonAttr("sug_person_uids_with_nick_temp_").value_or(absl::Span<const int64>());
    std::vector<int64_t> uids;
    uids.reserve(follow_substr_results.size());
    for (const auto &item : follow_substr_results) {
      std::string str_tmp = "user_follow" + std::to_string(item);
      uids.emplace_back(::base::CityHash64(str_tmp.c_str(), str_tmp.size()));
    }
    context.SetIntListCommonAttr("sug_user_follow_keys_", std::move(uids));
    return true;
  }

  static bool GetUserFollowHashID2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto follow_substr_results =
        context.GetIntListCommonAttr("user_follow_substr_results_").value_or(absl::Span<const int64>());
    std::vector<int64_t> uids;
    uids.reserve(follow_substr_results.size());
    for (const auto &item : follow_substr_results) {
      std::string str_tmp = "user_follow" + std::to_string(item);
      uids.emplace_back(::base::CityHash64(str_tmp.c_str(), str_tmp.size()));
    }
    context.SetIntListCommonAttr("sug_user_follow_substr_keys_", std::move(uids));
    return true;
  }

  static bool EnrichFromInterventionBlackData(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    uint64_t photo_id = context.GetIntCommonAttr("refer_video_id_int").value_or(0);
    uint64_t author_id = context.GetIntCommonAttr("author_id").value_or(0);
    auto scene_type = context.GetStringCommonAttr("intervention_scene_type").value_or("");
    std::string scene_type_str(scene_type);
    auto black_data_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::InterventionBlackDataReloader>>::get()
                                 ->get();
    if (black_data_loader == nullptr) {
      CL_LOG(ERROR) << "null black_data_loader";
      return false;
    }
    const auto &infer_data_map = black_data_loader->get_inter_data();
    auto it = infer_data_map.find(scene_type_str);
    if (it == infer_data_map.end()) {
      CL_LOG(INFO) << "The scene type [" << scene_type_str << "] isn't intervened !!!";
      return true;
    }

    const auto &infer_data = it->second;
    int is_close_reco = 0;

    if (photo_id != 0 && infer_data.photo_set_.count(photo_id)) {  // check black photo
      is_close_reco = 1;
      CL_LOG(INFO) << "photo_id: " << photo_id << " hit black list.";
    } else if (author_id != 0 && infer_data.author_set_.count(author_id)) {  // check black author
      is_close_reco = 1;
      CL_LOG(INFO) << "author_id: " << author_id << " hit black list.";
    }

    std::vector<std::string> black_query_vec;
    black_query_vec.insert(black_query_vec.end(), infer_data.query_set_.begin(), infer_data.query_set_.end());

    if (is_close_reco == 1) {
      context.SetIntCommonAttr("is_close_reco", is_close_reco);
    }
    context.SetStringListCommonAttr("operation_black_query_list", std::move(black_query_vec));
    return true;
  }

  static bool EnrichStressLabel(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    const std::string &stress_test_biz_name =
        ::ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName();
    context.SetStringCommonAttr("stress_label", stress_test_biz_name);
    return true;
  }

  static bool CalcRSRelevanceScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto response = context.GetProtoMessagePtrCommonAttr<::mmu::query_parser::QueryRewriterResponse>(
        "query_rewriter_response");
    if (response == nullptr) {
      return true;
    }
    auto item_id_str_attr = context.GetStringItemAttr("item_id");
    auto item_relevance_score_func = context.SetDoubleItemAttr("item_relevance_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id_str = item_id_str_attr(result).value_or("");
      if (item_id_str.empty()) return;
      std::string raw_query(item_id_str);
      float relevance_score = 0.0;
      if (nullptr != response) {
        auto &query_terms = response->query_terms();
        for (auto iter = query_terms.begin(); iter != query_terms.end(); ++iter) {
          const std::string &term_ori = iter->term();
          std::string term = term_ori;
          std::transform(term.begin(), term.end(), term.begin(), ::tolower);
          const float &qimp = iter->qimp();
          if (raw_query.find(term) != std::string::npos) {
            relevance_score += qimp;
          }
        }
      }
      item_relevance_score_func(result, relevance_score);
    });
    return true;
  }

  static bool SelectedPositionOperationData(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto white_data_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::InterventionWhiteDataReloader>>::get()
                                 ->get();
    if (white_data_loader == nullptr) {
      CL_LOG(ERROR) << "null white_data_loader";
      return false;
    }
    std::vector<std::string> operation_query_vec;
    std::vector<int64_t> operation_timestamp_vec;
    const auto &inter_data_map = white_data_loader->get_inter_data();
    for (const auto &inter_datas : inter_data_map) {
      for (const auto &inter_data : inter_datas.second) {
        JsonValuePtr content = std::make_shared<ks::search::Json>(inter_data->extra_content);
        // 指定位置入场： 5
        if (content->Get<int64_t>("interStrategy", 0) != 5) {
          continue;
        }
        for (const auto &inter_item : inter_data->inter_items) {
          operation_query_vec.emplace_back(inter_item.item_id);
          operation_timestamp_vec.emplace_back(inter_data->start_time);
        }
      }
    }
    context.SetStringListCommonAttr("operate_word_enter_at_selected_position_query",
                                    std::move(operation_query_vec));
    context.SetIntListCommonAttr("operate_word_enter_at_selected_position_timestamp",
                                 std::move(operation_timestamp_vec));
    return true;
  }

  static bool InsertOperationData(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto refer_photo_id = context.GetStringCommonAttr("referVideoId").value_or("");
    std::string refer_photo_id_str(refer_photo_id);
    auto white_data_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::InterventionWhiteDataReloader>>::get()
                                 ->get();
    if (white_data_loader == nullptr) {
      CL_LOG(ERROR) << "null white_data_loader";
      return false;
    }
    std::vector<std::string> operation_query_vec;
    std::vector<int64_t> operation_postion_vec;
    const auto &inter_data_map = white_data_loader->get_inter_data();
    for (const auto &inter_datas : inter_data_map) {
      for (const auto &inter_data : inter_datas.second) {
        JsonValuePtr content = std::make_shared<ks::search::Json>(inter_data->extra_content);
        // 指定位置强插： 6
        if (content->Get<int64_t>("interStrategy", 0) != 6) {
          continue;
        }
        if (!inter_data->enter_items.empty() &&
            inter_data->enter_items.find(refer_photo_id_str) == inter_data->enter_items.end()) {
          continue;
        }
        for (const auto &inter_item : inter_data->inter_items) {
          operation_query_vec.emplace_back(inter_item.item_id);
          operation_postion_vec.emplace_back(inter_item.position - 1);
        }
      }
    }
    context.SetStringListCommonAttr("operation_insert_word_list", std::move(operation_query_vec));
    context.SetIntListCommonAttr("operation_insert_position_list", std::move(operation_postion_vec));
    return true;
  }

  static bool FirstViewForceShowOperationData(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    int64_t user_id = context.GetIntCommonAttr("user_id").value_or(0);
    std::string biz_prefix(context.GetStringCommonAttr("biz_prefix").value_or(""));

    auto white_data_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::InterventionWhiteDataReloader>>::get()
                                 ->get();
    if (white_data_loader == nullptr) {
      LOG(ERROR) << "null white_data_loader";
      return false;
    }

    auto pv_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::FirstViewForceShowMultiPvReloader>>::get()
                         ->get();
    if (pv_loader == nullptr) {
      LOG(ERROR) << "null pv_loader";
      return false;
    }

    auto client = ks::infra::RedisProxyClient::GetRedisClientByKcc("searchRecoOperationUser", 20);
    if (client == nullptr) {
      CL_LOG(ERROR) << "failed to init client:[searchRecoOperationUser]";
      return false;
    }

    auto is_target_user = [](int64_t user_id, const std::string &user_prefix,
                             const std::unordered_set<std::string> &enter_users,
                             const std::unordered_set<uint64_t> &test_users) -> bool {
      std::string user_id_str = std::to_string(user_id);
      if (user_prefix.empty() && enter_users.empty() && test_users.empty()) {
        return true;
      } else if (!user_prefix.empty() &&
                 se_reco::CheckUserRedisValue(user_prefix, user_id_str, "searchRecoOperationUser")) {
        return true;
      } else if (!enter_users.empty() && enter_users.find(user_id_str) != enter_users.end()) {
        return true;
      } else if (!test_users.empty() && test_users.find(user_id) != test_users.end()) {
        return true;
      }
      return false;
    };

    const auto &inter_data_map = white_data_loader->get_inter_data();
    int64 cur_timestamp = base::GetTimestamp() / 1000000;
    int64 cur_date = (cur_timestamp + 8 * 3600) / (24 * 3600);
    std::vector<std::string> fields;
    fields.emplace_back(std::to_string(cur_date));
    fields.emplace_back("cur_u_pv");
    fields.emplace_back("u_his_qs");
    std::vector<std::string> fvfs_operation_query_vec;
    std::vector<int64_t> fvfs_operation_postion_vec;
    uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
    for (const auto &inter_datas : inter_data_map) {
      for (const auto &inter_data : inter_datas.second) {
        JsonValuePtr content = std::make_shared<ks::search::Json>(inter_data->extra_content);
        // 首刷必出： 3
        if (content->Get<int64_t>("interStrategy", 0) != 3) {
          continue;
        }

        // 每日总 pv 上限
        int64_t id = inter_data->id;
        int64_t max_show_count = content->Get<int64_t>("totalShowCount", 0);
        int64_t cur_show_count = pv_loader->get_cur_pv(id);
        if (max_show_count != 0 && cur_show_count >= max_show_count) {
          CL_LOG(INFO) << "cur_show_count is more than max_show_count. cur_show_count: " << cur_show_count
                       << ", max_show_count: " << max_show_count;
          continue;
        }

        // 指定用户
        std::string user_prefix = content->Get<std::string>("userGroupKey", "");
        if (!is_target_user(user_id, user_prefix, inter_data->enter_items, inter_data->test_user_id)) {
          CL_LOG(INFO) << user_id << " is not target user.";
          continue;
        }
        std::string key = "fvfs_" + biz_prefix + "_" + std::to_string(user_id) + "_" + std::to_string(id);
        std::vector<std::string> values;
        client->MultiHashGet(key, fields, &values, 20);
        if (fields.size() != values.size()) {
          LOG(ERROR) << "size is not equal.";
          continue;
        }

        // 判定用户每日首次请求
        if (!values[0].empty()) {
          CL_LOG(INFO) << user_id << " is not first request.";
          continue;
        }

        // 用户曝光次数上限
        int64_t max_user_pv = content->Get<int64_t>("userShowCount", 0);
        int64_t cur_user_pv = 0;
        base::StringToInt64(values[1], &cur_user_pv);
        if (max_user_pv != 0 && cur_user_pv >= max_user_pv) {
          CL_LOG(INFO) << "cur_user_pv is more than max_user_pv. cur_user_pv: " << cur_user_pv
                       << ", max_user_pv: " << max_user_pv;
          continue;
        }
        int64 query_size = inter_data->inter_items.size();
        int64 target_pos = rand_r(&seed) % query_size;
        fvfs_operation_query_vec.emplace_back(inter_data->inter_items[target_pos].item_id);
        fvfs_operation_postion_vec.emplace_back(inter_data->inter_items[target_pos].position - 1);

        // 更新每日总 pv
        if (!pv_loader->set_pv(id)) {
          LOG(ERROR) << "set pv false!";
        }

        // 更新用户数据
        std::vector<std::string> update_values;
        update_values.emplace_back("1");
        update_values.emplace_back(std::to_string(cur_user_pv + 1));
        update_values.emplace_back(values[2] + "|" + inter_data->inter_items[target_pos].item_id);
        int64 life_in_seconds = inter_data->end_time - cur_timestamp;
        life_in_seconds = life_in_seconds > 0 ? life_in_seconds : 0;
        auto errcode = client->MultiHashSetEx(key, fields, update_values, life_in_seconds, 20);
      }
    }
    context.SetStringListCommonAttr("fvfs_insert_query", std::move(fvfs_operation_query_vec));
    context.SetIntListCommonAttr("fvfs_insert_pos_from_zero", std::move(fvfs_operation_postion_vec));
    return true;
  }

  static bool GetVisionSearchDirtyData(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto dict_ns = context.GetStringCommonAttr("dict_ns").value_or("photo");
    auto dict_name = context.GetStringCommonAttr("dict_name").value_or("");
    int item_number = context.GetIntCommonAttr("item_number").value_or(0);
    int is_dict_inited = context.GetIntCommonAttr("is_dict_inited").value_or(0);

    if (is_dict_inited == 0) {
      CL_LOG(ERROR) << "dict is not init.";
      return true;
    }

    auto dict = mmu::util::DictManager::GetDict<mmu::util::HashSetData>(dict_ns.data(), dict_name.data());
    if (dict == nullptr) {
      CL_LOG(ERROR) << "Fail to get dict ns:" << dict_ns.data() << " name:" << dict_name.data();
      return true;
    }
    std::vector<std::string> dirty_data_str = dict->RandomSample(item_number);
    std::vector<int64_t> dirty_data_uint;
    dirty_data_uint.resize(item_number);
    for (int i = 0; i < item_number; ++i) {
      dirty_data_uint[i] = std::stoull(dirty_data_str[i]);
    }

    context.SetIntListCommonAttr("dirty_data", std::move(dirty_data_uint));
    return true;
  }

  static bool GetKboxProtoSerializeString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto goods_ids =
        context.GetIntListCommonAttr("item_id_common_for_kbox").value_or(absl::Span<const int64>());
    auto slice_ids = context.GetIntListCommonAttr("slice_id_common").value_or(absl::Span<const int64>());
    auto video_ids = context.GetIntListCommonAttr("photo_id_common").value_or(absl::Span<const int64>());
    auto live_ids = context.GetIntListCommonAttr("live_id_common").value_or(absl::Span<const int64>());
    auto explain_live_ids =
        context.GetIntListCommonAttr("explain_live_id_common").value_or(absl::Span<const int64>());
    auto seller_ids =
        context.GetIntListCommonAttr("arch_seller_id_common").value_or(absl::Span<const int64>());
    auto owner_ids = context.GetIntListCommonAttr("seller_id_common").value_or(absl::Span<const int64>());

    auto pb_ptr = std::make_shared<::kuaishou::ds::search::ComboSearchItem>();
    pb_ptr->set_item_name("ALADDIN_MIXED_GOODS_LIVE");
    pb_ptr->mutable_attribute_params()->set_tk_template_id("StrongGoodsCard");
    auto extra_param_map_ptr = pb_ptr->mutable_attribute_params()->mutable_extra_param();
    (*extra_param_map_ptr)["feedbackWeak"] =
        "{\"headList\":[\"search_kbox_not_interested\"],\"contentList\":[\"search_kbox_commodity_"
        "irrelevant\",\"search_kbox_dislike_style\",\"search_kbox_inappropriate_price\",\"search_kbox_"
        "product_quality\",\"search_kbox_buy_liveproduct\"]}";
    (*extra_param_map_ptr)["feedbackStrong"] =
        "{\"headList\":[\"search_kbox_not_interested\"],\"contentList\":[\"search_kbox_commodity_"
        "irrelevant\",\"search_kbox_dislike_style\",\"search_kbox_inappropriate_price\",\"search_kbox_"
        "product_quality\",\"search_kbox_buy_liveproduct\"],\"otherContent\":\"search_kbox_other\"}";

    auto item_component_title = pb_ptr->mutable_mixed_item()->add_mixed_component();
    item_component_title->mutable_mixed_tk_new()->set_tk_type(
        ::kuaishou::ds::search::TkComponentNew::TK_TITLE);
    item_component_title->mutable_mixed_tk_new()->mutable_tk_title()->set_title("商品");
    item_component_title->mutable_mixed_tk_new()->mutable_tk_title()->set_more_link(
        "search://gotab?tab=goods");
    item_component_title->mutable_mixed_tk_new()->mutable_tk_title()->set_type(
        ::kuaishou::ds::search::TkTitle::QUERY_TITLE);

    auto item_component_goods = pb_ptr->mutable_mixed_item()->add_mixed_component();
    item_component_goods->mutable_mixed_tk_new()->set_tk_type(
        ::kuaishou::ds::search::TkComponentNew::TK_SLIDE_LIP);
    item_component_goods->mutable_mixed_tk_new()->set_tk_style_type(
        ::kuaishou::ds::search::TkStyleType::STRONG);
    item_component_goods->mutable_mixed_tk_new()->mutable_tk_slide_lip()->set_left_slide_url(
        "search://gotab?tab=goods");

    auto *common_attr =
        item_component_goods->mutable_mixed_tk_new()->mutable_tk_slide_lip()->mutable_common_attr();
    (*common_attr)["bottom_link"] = "search://gotab?tab=goods";

    for (int i = 0; i < goods_ids.size(); ++i) {
      auto tk_slide_card_ptr =
          item_component_goods->mutable_mixed_tk_new()->mutable_tk_slide_lip()->add_card();
      tk_slide_card_ptr->mutable_id()->set_id(std::to_string(goods_ids[i]));
      tk_slide_card_ptr->mutable_id()->set_type(::kuaishou::ds::search::TkId::GOOD);
      if (seller_ids.size() > i && seller_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_seller_id(seller_ids[i]);
      }
      if (owner_ids.size() > i && owner_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_owner_id(owner_ids[i]);
      }
      if (live_ids.size() > i && live_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_live_id(live_ids[i]);
      }
      if (video_ids.size() > i && video_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_video_id(video_ids[i]);
      }
      if (slice_ids.size() > i && slice_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_slice_id(slice_ids[i]);
      }
      if (live_ids.size() > i && live_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_type(
            ::kuaishou::ds::search::GoodsStatusType::GOODS_LIVE_STATUS);
      } else if (slice_ids.size() > i && slice_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_type(
            ::kuaishou::ds::search::GoodsStatusType::GOODS_SLICE_STATUS);
      } else if (video_ids.size() > i && video_ids[i] > 0) {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_type(
            ::kuaishou::ds::search::GoodsStatusType::GOODS_VIDEO_STATUS);
      } else {
        tk_slide_card_ptr->mutable_goods_info()->mutable_goods_status_info()->set_type(
            ::kuaishou::ds::search::GoodsStatusType::GOODS_PROFILE_STATUS);
      }
    }
    std::string serialized_msg;
    pb_ptr->SerializeToString(&serialized_msg);
    context.SetStringCommonAttr("kbox_str", serialized_msg);
    return true;
  }

  static bool EnrichNormalizedQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto searched_query_list =
        context.GetStringListCommonAttr("searched_query_list").value_or(std::vector<absl::string_view>());
    std::vector<std::string> normalized_searched_query_list;
    for (size_t i = 0; i < searched_query_list.size(); ++i) {
      std::string search_query = std::string(searched_query_list[i].data(), searched_query_list[i].size());
      normalized_searched_query_list.emplace_back(se_reco::QueryNormalize(search_query));
    }
    context.SetStringListCommonAttr("normalized_searched_query_list",
                                    std::move(normalized_searched_query_list));
    return true;
  }

  static bool EnrichPinYinQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::string pinyin_query;
    se_reco::Query2Pinyin(query, &pinyin_query);
    context.SetStringCommonAttr("pinyin_query", pinyin_query);
    return true;
  }

  static bool EnrichPinYinItem(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str(item_id);

      std::string item_py;
      se_reco::Query2Pinyin(item_id_str, &item_py);

      auto item_item_py_func = context.SetStringItemAttr("item_py");
      item_item_py_func(result, item_py);
    });
    return true;
  }

  static bool EnrichItemPrefix2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str(item_id);

      // 转换为宽字符字符串
      std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
      std::wstring wide_item = converter.from_bytes(item_id_str);

      // 获取前两个汉字
      std::wstring firstTwoChars = wide_item.substr(0, 2);

      // 将宽字符字符串转换回 UTF-8
      std::string item_prefix2 = converter.to_bytes(firstTwoChars);

      auto item_item_prefix2_func = context.SetStringItemAttr("item_prefix2");
      item_item_prefix2_func(result, item_prefix2);
    });
    return true;
  }

  static bool EnrichAdjacentPinYinQueryList(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::vector<std::string> pinyin_result;
    se_reco::FollowedUsernamePinyin(query, &pinyin_result);
    context.SetStringListCommonAttr("adjacent_pinyin_query_list", std::move(pinyin_result));
    return true;
  }

  static bool EnrichMatchedSearchQuery(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::string pinyin_query(context.GetStringCommonAttr("pinyin_query").value_or(""));
    auto selected_searched_query_list = context.GetStringListCommonAttr("selected_searched_query_list")
                                            .value_or(std::vector<absl::string_view>());
    std::vector<std::string> matched_searched_query;
    for (size_t i = 0; i < selected_searched_query_list.size(); ++i) {
      std::string searched_query =
          std::string(selected_searched_query_list[i].data(), selected_searched_query_list[i].size());
      // 前缀匹配
      if (!searched_query.empty() && se_reco::PrefixMatch(searched_query, query)) {
        matched_searched_query.emplace_back(searched_query);
        continue;
      }
      // 拼音前缀匹配
      if (!se_reco::IsAlnumStr(query)) {
        continue;
      }
      std::string pinyin_searched_query = "";
      se_reco::Query2Pinyin(searched_query, &pinyin_searched_query);
      if (!pinyin_searched_query.empty() && se_reco::PrefixMatch(pinyin_searched_query, pinyin_query)) {
        matched_searched_query.emplace_back(searched_query);
      }
    }
    context.SetStringListCommonAttr("matched_searched_query", std::move(matched_searched_query));
    return true;
  }

  static bool EnrichUser2GameMatchedQuery(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    int64_t sug_game_recall_midmatch = context.GetIntCommonAttr("sug_game_recall_midmatch").value_or(0);
    auto final_sogame_common_playlist =
        context.GetStringListCommonAttr("sogame_common_playlist").value_or(std::vector<absl::string_view>());
    std::vector<std::string> candidate_query_vec;
    for (size_t i = 0; i < final_sogame_common_playlist.size(); ++i) {
      candidate_query_vec.emplace_back(final_sogame_common_playlist[i].data(),
                                       final_sogame_common_playlist[i].size());
    }
    std::vector<std::string> game_query_vec;
    se_reco::GenerateGameQueryList(query, candidate_query_vec, sug_game_recall_midmatch, &game_query_vec);
    context.SetStringListCommonAttr("profile_matched_user2game_common_query_list", std::move(game_query_vec));
    return true;
  }

  static bool EnrichUser2GamePlayMatchedQuery(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    int64_t sug_game_recall_midmatch = context.GetIntCommonAttr("sug_game_recall_midmatch").value_or(0);
    auto sogame_user_consume_30d =
        context.GetStringListCommonAttr("sogame_user_consume_30d").value_or(std::vector<absl::string_view>());
    std::vector<std::string> candidate_query_vec;
    for (size_t i = 0; i < sogame_user_consume_30d.size(); ++i) {
      candidate_query_vec.emplace_back(sogame_user_consume_30d[i].data(), sogame_user_consume_30d[i].size());
    }
    std::vector<std::string> game_query_vec;
    se_reco::GenerateGameQueryList(query, candidate_query_vec, sug_game_recall_midmatch, &game_query_vec);
    context.SetStringListCommonAttr("matched_user2game_play_query_list", std::move(game_query_vec));
    return true;
  }

  static bool EnrichAuthorLiveInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto author_info_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::AuthorLiveInfoReloader>>::get()
                                  ->get();
    if (author_info_loader == nullptr) {
      LOG(ERROR) << "null author_info_loader";
      return false;
    }
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    int64_t num_limit = context.GetIntCommonAttr("live_author_num_limit").value_or(0);

    const std::vector<::ks::search::ziya_sort::LiveAuthorInfo> &live_author_info_vec =
        author_info_loader->get_live_author_info_vec();
    int cnt = 0;
    std::vector<std::string> live_author_name_list;
    std::vector<double> live_author_score_list;
    std::vector<int64_t> live_author_id_list;
    std::vector<std::string> merchant_live_author_name_list;
    std::vector<std::string> ads_live_author_name_list;
    for (const auto &live_author_info : live_author_info_vec) {
      if (cnt >= num_limit) {
        break;
      }
      if (!::base::StartsWith(live_author_info.author_name, query, false)) {
        continue;
      }
      live_author_name_list.emplace_back(live_author_info.author_name);
      live_author_score_list.emplace_back(live_author_info.score);
      live_author_id_list.emplace_back(live_author_info.author_id);
      if (live_author_info.is_merchant) {
        merchant_live_author_name_list.emplace_back(live_author_info.author_name);
      }
      if (live_author_info.is_ads) {
        ads_live_author_name_list.emplace_back(live_author_info.author_name);
      }
      cnt++;
    }
    context.SetStringListCommonAttr("matched_live_author_name_list", std::move(live_author_name_list));
    context.SetDoubleListCommonAttr("matched_live_author_score_list", std::move(live_author_score_list));
    context.SetIntListCommonAttr("matched_live_author_id_list", std::move(live_author_id_list));
    context.SetStringListCommonAttr("matched_merchant_live_author_name_list",
                                    std::move(merchant_live_author_name_list));
    context.SetStringListCommonAttr("matched_ads_live_author_name_list",
                                    std::move(ads_live_author_name_list));

    return true;
  }

  static bool IsEndByChinese(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto query_attr = context.GetStringCommonAttr("query").value_or("");
    auto end_pos = se_reco::ChineseSplit(std::string(query_attr));
    int pos = (end_pos == query_attr.size() ? 0 : -1);
    context.SetIntCommonAttr("end_chiness_pos", pos);
    return true;
  }

  static bool ParseSEsResponseForSug(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto sug_fuzzy_es_response =
        context.GetProtoMessagePtrCommonAttr<ks::search::splatform::SearchResponse>("sug_fuzzy_es_response");
    if (sug_fuzzy_es_response == nullptr) {
      CL_LOG(WARNING) << "sug_fuzzy_es_response ptr is nullptr";
      return true;
    }
    const auto &suggest_response =
        sug_fuzzy_es_response->suggest_response().at("FuzzyCompletionSuggester_-1");  // 可用 attr 代替
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::string norm_prefix("");
    for (const auto &c : query) {
      if (!isalnum(c)) {
        norm_prefix.push_back(c);
      }
    }
    // 对召回结果去重
    std::unordered_set<std::string> user_sug_item_dedup_set;
    std::unordered_set<std::string> query_sug_item_dedup_set;
    std::vector<std::string> result_item_id_vec;
    std::vector<double> result_score_vec;
    std::vector<int64_t> result_user_id_vec;
    std::vector<std::string> result_item_reason_vec;
    std::vector<std::string> result_item_source_vec;
    std::vector<int64_t> result_item_key_vec;
    result_item_id_vec.reserve(suggest_response.items_size());
    result_score_vec.reserve(suggest_response.items_size());
    result_user_id_vec.reserve(suggest_response.items_size());
    result_item_source_vec.reserve(suggest_response.items_size());
    result_item_reason_vec.reserve(suggest_response.items_size());
    result_item_key_vec.reserve(suggest_response.items_size());
    for (size_t i = 0; i < suggest_response.items_size(); ++i) {
      const auto &item = suggest_response.items(i);
      const auto &keyword = item.key();
      const auto &data = item.data();
      if (data.find("source") == data.end() || data.find("user_id") == data.end()) {
        continue;
      }
      const std::string &source = data.find("source")->second.text();
      const std::string &user_id = data.find("user_id")->second.text();
      int64_t score = 0;
      if (data.find("weight") != data.end()) {
        score = data.find("weight")->second.int_num();
      }
      uint64_t item_key = se_reco::GenerateItemKey(keyword + std::to_string(i), source, user_id);
      if (source == "main_index") {
        int overlap_num = se_reco::CharOverlapNum(norm_prefix, keyword);
        if (query_sug_item_dedup_set.find(keyword) == query_sug_item_dedup_set.end() &&
            (norm_prefix.size() == 0 || overlap_num >= 1)) {
          query_sug_item_dedup_set.insert(keyword);
          result_item_id_vec.emplace_back(keyword);
          result_score_vec.emplace_back(score);
          result_user_id_vec.emplace_back(0);
          result_item_source_vec.emplace_back("PINYIN_FUZZY_RECALL");
          result_item_reason_vec.emplace_back("PINYIN_FUZZY_RECALL");
          result_item_key_vec.emplace_back(item_key);
          VLOG(2) << "keyword:" << keyword << ", score:" << score;
        }
      } else {
        if (user_sug_item_dedup_set.find(user_id) == user_sug_item_dedup_set.end()) {
          user_sug_item_dedup_set.insert(user_id);
          int64_t uid = 0;
          ::base::StringToInt64(user_id, &uid);
          if (uid == 0) continue;
          std::string recall_reason_tmp = "USER_ID";
          if (source == "kwai_id") {
            recall_reason_tmp = "KUAI_ID";
          }
          result_item_id_vec.emplace_back("");
          result_score_vec.emplace_back(score);
          result_user_id_vec.emplace_back(uid);
          result_item_source_vec.emplace_back("UID_FUZZY_RECALL");
          result_item_reason_vec.emplace_back(recall_reason_tmp);
          result_item_key_vec.emplace_back(item_key);
        }
      }
    }
    context.SetIntListCommonAttr("sug_es_item_key_list", std::move(result_item_key_vec));
    context.SetStringListCommonAttr("sug_es_item_id_list", std::move(result_item_id_vec));
    context.SetIntListCommonAttr("sug_es_user_id_list", std::move(result_user_id_vec));
    context.SetDoubleListCommonAttr("sug_es_score_list", std::move(result_score_vec));
    context.SetStringListCommonAttr("sug_es_item_source_list", std::move(result_item_source_vec));
    context.SetStringListCommonAttr("sug_es_item_reason_list", std::move(result_item_reason_vec));
    return true;
  }

  static bool SplitCorrectionValue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    std::string correct_name(context.GetStringCommonAttr("spelling_correction_from_redis_name").value_or(""));
    if (correct_name == "") {
      return false;
    }
    auto correct_value_attr = context.GetStringItemAttr(correct_name + "_correction_value");
    auto item_ec_quey_func = context.SetStringItemAttr(correct_name + "_correction_ec_query");
    auto item_type_func = context.SetStringItemAttr(correct_name + "_correction_type");
    auto item_score_func = context.SetDoubleItemAttr(correct_name + "_correction_score");
    for (auto iter = begin; iter != end; ++iter) {
      std::string correct_value(correct_value_attr(*iter).value_or(""));
      if (correct_value == "") {
        continue;
      }
      std::vector<std::string> correct_value_split;
      ::base::SplitString(correct_value, "_", &correct_value_split);
      if (correct_value_split.size() != 3) {
        continue;
      }
      double correct_score = 0.0;
      if (!absl::SimpleAtod(correct_value_split[2], &correct_score)) {
        LOG(WARNING) << "SimpleAtod error! str:" << correct_value_split[2];
      }

      item_ec_quey_func(*iter, correct_value_split[0]);
      item_type_func(*iter, correct_value_split[1]);
      item_score_func(*iter, correct_score);
    }
    return true;
  }
  static void AdaptProfile(const ks::reco::UserInfo *profile, ks::reco::UserInfo *kfs_profile) {
    if (nullptr == profile || nullptr == kfs_profile) return;
    kfs_profile->set_id(profile->id());
    kfs_profile->set_gender(profile->gender());
    kfs_profile->set_user_base_score(profile->user_base_score());
    kfs_profile->set_fans_count(profile->fans_count());
    kfs_profile->set_recent_liked_count(profile->recent_liked_count());
    kfs_profile->set_reg_time(profile->reg_time());
    kfs_profile->set_active_days(profile->active_days());
    kfs_profile->set_user_upload_preference(profile->user_upload_preference());
    kfs_profile->set_mod_screen(profile->mod_screen());
    kfs_profile->set_upload_rate(profile->upload_rate());
    kfs_profile->set_social_type(profile->social_type());
    kfs_profile->set_pure_consumer_low_app(profile->pure_consumer_low_app());
    kfs_profile->set_user_active_level(profile->user_active_level());
    kfs_profile->set_mod_price(profile->mod_price());
    kfs_profile->set_is_douyin(profile->is_douyin());
    kfs_profile->set_visit_channel(profile->visit_channel());
    kfs_profile->set_visit_mod(profile->visit_mod());
    kfs_profile->set_mod_brand(profile->mod_brand());
    kfs_profile->set_important_user_level(profile->important_user_level());
    kfs_profile->set_dnn_cluster_id(profile->dnn_cluster_id());
    kfs_profile->set_risk_level(profile->risk_level());

    if (profile->has_basic_info()) {
      kfs_profile->mutable_basic_info()->CopyFrom(profile->basic_info());
    }
    if (profile->has_location()) {
      kfs_profile->mutable_location()->CopyFrom(profile->location());
    }
  }

  static bool EnrichPbDataSourceSet(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    // input common attr:
    // trending_data_source
    // searched_query_list, string list
    const ks::kfs::data_source::TrendingDataSource *data_source_ptr =
        context.GetProtoMessagePtrCommonAttr<ks::kfs::data_source::TrendingDataSource>(
            "trending_data_source");
    if (data_source_ptr == nullptr) {
      CL_LOG(WARNING) << "trending data source ptr is nullptr";
      return false;
    }
    ks::search::jubao::PbDataSourceSet pb_data_source_set;
    ks::kfs::data_source::TrendingDataSource *data_source = pb_data_source_set.mutable_trending();
    data_source->CopyFrom(*data_source_ptr);

#define SET_SUG_EMBEDDING_LIST(ATTR_NAME, SET_PB_FUNC, IS_CLEAR_ZERO)                                      \
  {                                                                                                        \
    auto embedding_list = context.GetDoubleListCommonAttr(ATTR_NAME).value_or(absl::Span<const double>()); \
    if (embedding_list.size() % 128 == 0) {                                                                \
      for (size_t i = 0; i < embedding_list.size(); i = i + 128) {                                         \
        if (IS_CLEAR_ZERO) {                                                                               \
          bool is_all_of_zero = std::all_of(embedding_list.begin() + i, embedding_list.begin() + i + 128,  \
                                            [](float element) { return element == 0.0f; });                \
          if (is_all_of_zero) continue;                                                                    \
        }                                                                                                  \
        auto emb_value = data_source->SET_PB_FUNC()->mutable_value();                                      \
        std::copy(embedding_list.begin() + i, embedding_list.begin() + i + 128,                            \
                  ::google::protobuf::RepeatedFieldBackInserter(emb_value));                               \
      }                                                                                                    \
    }                                                                                                      \
  }

    SET_SUG_EMBEDDING_LIST("reco_click_photo_embedding_list", add_click_photo_embeddings, true);
    SET_SUG_EMBEDDING_LIST("reco_like_photo_embedding_list", add_like_photo_embeddings, true);
    SET_SUG_EMBEDDING_LIST("search_query_embedding_list", add_search_history_query_embeddings, false);
#undef SET_SUG_EMBEDDING_LIST

    const ks::kfs::data_source::ReferInfo *refer_info_ptr =
        context.GetProtoMessagePtrCommonAttr<ks::kfs::data_source::ReferInfo>("refer_info");
    if (refer_info_ptr != nullptr) {
      data_source->mutable_refer_info()->CopyFrom(*(refer_info_ptr));
    }

    // build_reco_user_info
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("reco_user_info");
    if (user_info != nullptr) {
      auto reco_user_info = pb_data_source_set.mutable_reco_user_info();
      AdaptProfile(user_info, reco_user_info);
    }
    VLOG(2) << "data_source:" << data_source->Utf8DebugString();

    // build_sug_data_source
    auto sug_data_source_ptr = pb_data_source_set.add_pb_data_source()->mutable_sug();

    auto searched_query_list =
        context.GetStringListCommonAttr("searched_query_list").value_or(std::vector<absl::string_view>());
    auto searched_query_timestamp_list =
        context.GetIntListCommonAttr("searched_query_timestamp_list").value_or(absl::Span<const int64>());
    std::vector<std::string> searched_query_list_vec;
    std::vector<int64_t> searched_query_timestamp_list_vec;
    searched_query_list_vec.reserve(searched_query_list.size());
    searched_query_timestamp_list_vec.reserve(searched_query_list.size());
    for (size_t i = 0; i < searched_query_list.size() && i < searched_query_timestamp_list.size(); ++i) {
      std::string norm_query = ks::platform::se_reco::QueryNomalize2(searched_query_list[i]);
      if (!norm_query.empty()) {
        searched_query_list_vec.push_back(std::move(norm_query));
        searched_query_timestamp_list_vec.push_back(searched_query_timestamp_list[i]);
      }
    }
    std::copy(
        searched_query_list_vec.begin(), searched_query_list_vec.end(),
        ::google::protobuf::RepeatedFieldBackInserter(sug_data_source_ptr->mutable_searched_queries_bytes()));
    std::copy(searched_query_timestamp_list_vec.begin(), searched_query_timestamp_list_vec.end(),
              ::google::protobuf::RepeatedFieldBackInserter(
                  sug_data_source_ptr->mutable_searched_queries_timestamp()));
    if (!searched_query_timestamp_list_vec.empty()) {
      sug_data_source_ptr->set_searched_queries_front_timestamp(searched_query_timestamp_list_vec[0]);
    }

    // 中长期序列
    auto long_searched_history =
        context.GetStringListCommonAttr("long_searched_history").value_or(std::vector<absl::string_view>());
    auto long_searched_history_score =
        context.GetDoubleListCommonAttr("long_searched_history_score").value_or(absl::Span<const double>());
    std::vector<std::pair<absl::string_view, double>> long_searched_query_score;
    if (!long_searched_history.empty() &&
        long_searched_history.size() == long_searched_history_score.size()) {
      for (size_t i = 0; i < long_searched_history.size(); ++i) {
        long_searched_query_score.push_back({long_searched_history.at(i), long_searched_history_score.at(i)});
      }
      std::stable_sort(long_searched_query_score.begin(), long_searched_query_score.end(),
                       [&](const std::pair<absl::string_view, double> &l,
                           const std::pair<absl::string_view, double> &r) { return l.second > r.second; });
      sug_data_source_ptr->mutable_long_searched_queries()->Reserve(long_searched_query_score.size());
      sug_data_source_ptr->mutable_long_searched_scores()->Reserve(long_searched_query_score.size());
      std::vector<std::string> normalized_long_searched_query_vec;
      for (size_t i = 0; i < long_searched_query_score.size(); ++i) {
        std::string long_search_query =
            ks::platform::se_reco::QueryNomalize2(long_searched_query_score[i].first);
        sug_data_source_ptr->add_long_searched_queries(long_search_query);
        sug_data_source_ptr->add_long_searched_scores(long_searched_query_score[i].second);
        normalized_long_searched_query_vec.emplace_back(long_search_query);
      }
      context.SetStringListCommonAttr("normalized_long_search_queries",
                                      std::move(normalized_long_searched_query_vec));
    }
    // refer query
    auto refer_query =
        context.GetStringListCommonAttr("refer_query").value_or(std::vector<absl::string_view>());
    if (!refer_query.empty()) {
      std::vector<std::string> refer_query_vec;
      sug_data_source_ptr->mutable_refer_queries()->Reserve(refer_query.size());
      for (size_t i = 0; i < refer_query.size(); ++i) {
        std::string norm_refer_query = ks::platform::se_reco::QueryNomalize2(refer_query.at(i));
        refer_query_vec.emplace_back(norm_refer_query);
        sug_data_source_ptr->add_refer_queries(std::move(norm_refer_query));
      }
      context.SetStringListCommonAttr("refer_query_list", std::move(refer_query_vec));
    }
    // referVideoId
    auto referVideoId = context.GetStringCommonAttr("referVideoId").value_or("");
    if (!referVideoId.empty()) {
      sug_data_source_ptr->add_refer_photo_ids(std::string(referVideoId.data(), referVideoId.size()));
    }
    // sug_cross_session_id
    auto sug_cross_session_id = context.GetStringCommonAttr("sug_cross_session_id").value_or("");
    sug_data_source_ptr->set_sug_cross_session_id(
        std::string(sug_cross_session_id.data(), sug_cross_session_id.size()));
    // platform pb 的原始形式
    int64_t platform = context.GetIntCommonAttr("platform_enum").value_or(0);
    // ksib::reco::Platform platform_tmp = ksib::reco::Platform.Parse(platform);
    sug_data_source_ptr->set_platform(static_cast<ksib::reco::Platform>(platform));
    // merchant_buyer_type
    std::map<std::string, int> buyer_type_map = {{"U0-流失", 1}, {"U1", 2}, {"U2", 3},
                                                 {"U3", 4},      {"U4", 5}, {"U4+", 6}};
    auto merchant_buyer_type = context.GetStringCommonAttr("merchant_buyer_type").value_or("");
    if (!merchant_buyer_type.empty()) {
      auto buyer_type_iter =
          buyer_type_map.find(std::string(merchant_buyer_type.data(), merchant_buyer_type.size()));
      if (buyer_type_iter != buyer_type_map.end()) {
        sug_data_source_ptr->set_buyer_type(buyer_type_iter->second);
      }
    }
    VLOG(2) << "sug_data_source_ptr:" << sug_data_source_ptr->Utf8DebugString();
    VLOG(3) << "pb_data_source_set:" << pb_data_source_set.Utf8DebugString();
    std::string pb_data_source_set_serialized_str;
    if (pb_data_source_set.SerializeToString(&pb_data_source_set_serialized_str)) {
      context.SetStringCommonAttr("pb_data_source_set_serialized_str", pb_data_source_set_serialized_str);
      return true;
    }
    return false;
  }

  static std::string GetPoiDistance(const double &poi_distance) {
    std::string distance_inf = "";
    if ((float)poi_distance < 0.1) {
      distance_inf = "<100m";
    } else if ((float)poi_distance < 0.2) {
      distance_inf = "<200m";
    } else if ((float)poi_distance < 0.3) {
      distance_inf = "<300m";
    } else if ((float)poi_distance < 0.4) {
      distance_inf = "<400m";
    } else if ((float)poi_distance < 0.5) {
      distance_inf = "<500m";
    } else if ((float)poi_distance < 0.7) {
      distance_inf = "<700m";
    } else if ((float)poi_distance < 1) {
      distance_inf = "<1km";
    } else {
      distance_inf = std::to_string(poi_distance);
      distance_inf = distance_inf.substr(0, distance_inf.find(".") + 2);
      distance_inf = distance_inf + "km";
    }
    return distance_inf;
  }

  static bool EnrichPoiDistance(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto geoParams_str = context.GetStringCommonAttr("geoParams").value_or("");
    mmu::video::GeoParams geo_params;
    if (geoParams_str.empty() || !geo_params.ParseFromString(std::string(geoParams_str))) {
      CL_LOG(WARNING) << "geoParams is empty or parse error";
      return true;
    }
    double lat2 = geo_params.lat();
    double lon2 = geo_params.lng();

    auto group_buy_poi_recall_distance =
        context.GetIntCommonAttr("group_buy_poi_recall_distance").value_or(0);
    // 只选取距离小于该值的 Item
    double distance_value_limit = static_cast<double>(group_buy_poi_recall_distance);
    double poi_distance = 0.0;
    double min_distance = 1000000.0;

    auto item_consumption_attr = context.GetStringItemAttr("consumption");
    auto item_district_attr = context.GetStringItemAttr("district");
    auto item_latitude_attr = context.GetDoubleItemAttr("latitude");
    auto item_longitude_attr = context.GetDoubleItemAttr("longitude");
    auto poi_reco_info_func = context.SetStringItemAttr("poi_reco_info");
    auto poi_distance_func = context.SetDoubleItemAttr("poi_distance");

    const CommonRecoResult *target_result = nullptr;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double item_lat = item_latitude_attr(result).value_or(0.0);
      double item_lng = item_longitude_attr(result).value_or(0.0);
      poi_distance = se_reco::GetDistanceByGeo(item_lat, item_lng, lat2, lon2);
      if (poi_distance > distance_value_limit) return;
      if (poi_distance > min_distance) return;
      min_distance = poi_distance;
      target_result = &result;
    });

    if (min_distance < 1000000 && target_result != nullptr) {
      ::Json::FastWriter jw;
      ::Json::Value poi_jv_info(::Json::arrayValue);
      poi_distance_func(*target_result, min_distance);
      std::string distance_inf = GetPoiDistance(min_distance);
      if (!distance_inf.empty()) {
        ::Json::Value distance_pattern;
        distance_pattern["type"] = 1;
        distance_pattern["text"] = std::move(distance_inf);
        poi_jv_info.append(distance_pattern);
      }
      auto item_district = item_district_attr(*target_result).value_or("");
      std::string district(item_district);
      if (district != "" && district != "empty") {
        ::Json::Value district_pattern;
        district_pattern["type"] = 2;
        district_pattern["text"] = std::move(district);
        poi_jv_info.append(district_pattern);
      }
      auto item_consumption = item_consumption_attr(*target_result).value_or("");
      std::string consumption(item_consumption);
      if (consumption != "empty" && consumption != "") {
        ::Json::Value consumption_pattern;
        consumption_pattern["type"] = 3;
        consumption_pattern["text"] = std::move(consumption);
        poi_jv_info.append(consumption_pattern);
      }
      poi_reco_info_func(*target_result, jw.write(poi_jv_info));
      VLOG(2) << "poi_jv_info:" << jw.write(poi_jv_info);
    }
    return true;
  }

  static bool EnrichPoiBrandName(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto extend_brand = context.GetIntCommonAttr("bendi_extend_brand").value_or(0);
    auto item_id_attr = context.GetStringItemAttr("item_id");
    std::array<std::string, 5> extend_brand_text_list_1 = {"团购", "套餐", "团购套餐", "套餐团购", "优惠券"};
    std::array<std::string, 22> extend_brand_text_list_2 = {"团购",
                                                            "套餐",
                                                            "团购套餐",
                                                            "套餐团购",
                                                            "优惠券",
                                                            "全国通用套餐",
                                                            "优惠",
                                                            "附近",
                                                            "全国优惠券",
                                                            "官方团购",
                                                            "团购官方",
                                                            "团购附近",
                                                            "优惠团购",
                                                            "官方旗舰店",
                                                            "官方团购",
                                                            "聚惠场",
                                                            "官方账号",
                                                            "奶茶团购",
                                                            "25块9十件套全国通用",
                                                            "团购直播",
                                                            "直播",
                                                            "团购直播间"};

    std::unordered_set<std::string> brand_name_set;
    brand_name_set.reserve(std::distance(begin, end) * 2);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str(item_id);
      brand_name_set.emplace(item_id_str);
      if (1 == extend_brand) {
        for (const std::string &extend_brand_text : extend_brand_text_list_1) {
          brand_name_set.emplace(item_id_str + extend_brand_text);
        }
      } else if (2 == extend_brand) {
        for (const std::string &extend_brand_text : extend_brand_text_list_2) {
          brand_name_set.emplace(item_id_str + extend_brand_text);
        }
      }
    });
    std::vector<std::string> brand_vec = {brand_name_set.begin(), brand_name_set.end()};
    auto brand_name_list = context.SetStringListCommonAttr("brand_name_list", std::move(brand_vec));
    return true;
  }

  static bool SugEnrichSearcherFilterFlag(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto operator_data = Singleton<::mmu::search::OperatorDataManager>::get()->GetPointer();
    if (!operator_data) {
      return true;
    }
    auto &black_user_id_list_tmp = operator_data->GetSearchGlobalBlackUser();
    if (black_user_id_list_tmp.empty()) {
      return true;
    }
    auto item_author_id_attr = context.GetIntItemAttr("item_author_id");
    auto item_set_rm_item_flag_func = context.SetIntItemAttr("item_rm_item_flag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto author_id = item_author_id_attr(result);
      if (!author_id.has_value() || author_id.value() == 0) {
        return;
      }
      if (black_user_id_list_tmp.find(author_id.value()) != black_user_id_list_tmp.end()) {
        VLOG(2) << "rm author id:" << author_id.value();
        item_set_rm_item_flag_func(result, 1);
      }
      return;
    });
    return true;
  }

  static bool ParseDocBasicInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    const auto *basic_info =
        context.GetProtoMessagePtrCommonAttr<::mmu::ranking::DocBasicInfo>("simple_basic_info_pb");
    if (basic_info == nullptr) {
      CL_LOG(WARNING) << "basic_info ptr is nullptr";
      return true;
    }
    const auto &simple_basic_info = *basic_info;
    std::string refer_maintag;
    std::string refer_cover_ocr;
    std::string refer_caption;

    const auto &section_info_list = simple_basic_info.section_info_list();
    const auto &term_str = simple_basic_info.term_str();
    const size_t term_str_size = term_str.size();

    for (const auto &section_info : section_info_list) {
      std::string photo_text;
      const auto &width_size = section_info.width_size();
      for (size_t j = 0; j < section_info.term_index_size(); ++j) {
        const auto &index = section_info.term_index(j);
        if (width_size > j && term_str_size > index) {
          photo_text += term_str.Get(index);
          if (section_info.width(j) > 1) {
            j += section_info.width(j);
          }
        }
      }
      switch (section_info.section_type()) {
        case 1:
          refer_maintag = photo_text;
          break;
        case 10:
          refer_cover_ocr = photo_text;
          break;
        case 22:
          refer_caption = photo_text;
          break;
        default:
          continue;
      }
    }

    context.SetStringCommonAttr("basic_info_refer_maintag", refer_maintag);
    context.SetStringCommonAttr("basic_info_refer_cover_ocr", refer_cover_ocr);
    context.SetStringCommonAttr("basic_info_refer_caption", refer_caption);

    return true;
  }

  static bool SplitPhotoEmbedingList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto click_photo_embedding_list = context.GetDoubleListCommonAttr("reco_click_photo_embedding_list")
                                          .value_or(absl::Span<const double>());
    auto like_photo_embedding_list = context.GetDoubleListCommonAttr("reco_like_photo_embedding_list")
                                         .value_or(absl::Span<const double>());
    int64_t index = 0;
    if (!click_photo_embedding_list.empty() && click_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < click_photo_embedding_list.size(); i = i + 128) {
        std::vector<double> single_embeding_vec;
        int32_t zero_value = 0;
        for (size_t j = i; j < i + 128; j++) {
          if (std::abs(click_photo_embedding_list.at(j)) < 0.000001) {
            zero_value++;
          }
          single_embeding_vec.emplace_back(click_photo_embedding_list.at(j));
        }
        if (zero_value < 128 && single_embeding_vec.size() == 128) {
          context.SetDoubleListCommonAttr("p_click_emb_" + std::to_string(index++),
                                          std::move(single_embeding_vec));
        }
        if (index > 20) {
          break;
        }
      }
    }
    context.SetIntCommonAttr("photo_click_emb_num", index);

    index = 0;
    if (!like_photo_embedding_list.empty() && like_photo_embedding_list.size() % 128 == 0) {
      for (size_t i = 0; i < like_photo_embedding_list.size(); i = i + 128) {
        std::vector<double> single_embeding_vec;
        int32_t zero_value = 0;
        for (size_t j = i; j < i + 128; j++) {
          if (std::abs(like_photo_embedding_list.at(j)) < 0.000001) {
            zero_value++;
          }
          single_embeding_vec.emplace_back(like_photo_embedding_list.at(j));
        }
        if (zero_value < 128 && single_embeding_vec.size() == 128) {
          context.SetDoubleListCommonAttr("p_like_emb_" + std::to_string(index++),
                                          std::move(single_embeding_vec));
        }
        if (index > 11) {
          break;
        }
      }
    }
    context.SetIntCommonAttr("photo_like_emb_num", index);
    return true;
  }

  static bool EnrichPrefixInfoItemAttr(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto query = context.GetStringCommonAttr("query").value_or("");
    std::string query_str(query);
    int64_t query_length = se_reco::WstringFormat(query).size();
    if (query_length == 0) {
      return true;
    }
    auto item_id_attr = context.GetStringItemAttr("item_id");

    auto item_is_contain_prefix_func = context.SetIntItemAttr("item_is_contain_prefix");
    auto item_prefix_same_ratio_func = context.SetDoubleItemAttr("item_prefix_same_ratio");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      std::string item_id_str(item_id);
      int overlap_num = se_reco::CharOverlapNum(item_id_str, query_str);
      item_is_contain_prefix_func(result, ::base::StartsWith(item_id_str, query_str, true));
      item_prefix_same_ratio_func(result, overlap_num * 1.0 / query_length);
    });
    return true;
  }

  static bool EnrichSugSubQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto sub_query_list_attr = context.GetStringItemAttr("sub_querylist");
    auto sub_score_list_attr = context.GetStringItemAttr("sub_scorelist");
    auto query_sub_query_flag_attr = context.GetIntItemAttr("query_sub_query_flag");

    std::unordered_set<std::string> sug_querys_set;
    std::unordered_map<std::string, double> sug_sub_query_map;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      if (item_id.empty()) return;
      sug_querys_set.emplace(std::string(item_id));
    });
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_id = item_id_attr(result).value_or("");
      auto sub_query_list_str = sub_query_list_attr(result).value_or("");
      auto sub_score_list_str = sub_score_list_attr(result).value_or("");
      auto query_sub_query_flag = query_sub_query_flag_attr(result).value_or(0);
      if (sub_query_list_str.empty() || 0 != query_sub_query_flag) return;

      std::vector<std::string> sub_query_list;
      std::vector<std::string> sub_score_list;
      base::SplitString(std::string(sub_query_list_str), "\x01", &sub_query_list);
      base::SplitString(std::string(sub_score_list_str), "\x01", &sub_score_list);
      int size = std::min(sub_query_list.size(), sub_score_list.size());
      for (int j = 0; j < size; j++) {
        std::string sub_query = sub_query_list[j];
        double sub_score = 0.0;
        if (!absl::SimpleAtod(sub_score_list[j], &sub_score)) {
          CL_LOG(WARNING) << "SimpleAtod error, str:" << sub_score_list[j];
        }
        if (sug_querys_set.count(sub_query) > 0) {
          std::string map_key = std::string(item_id) + "_" + sub_query;
          if (sug_sub_query_map.find(map_key) == sug_sub_query_map.end()) {
            sug_sub_query_map[map_key] = sub_score;
          } else {
            sug_sub_query_map[map_key] = std::max(sub_score, sug_sub_query_map[map_key]);
          }
        }
      }
    });
    std::vector<std::string> sug_sub_query_result;
    std::vector<double> sub_sub_score_result;
    sug_sub_query_result.reserve(sug_sub_query_map.size());
    sub_sub_score_result.reserve(sug_sub_query_map.size());
    for (auto &[key, value] : sug_sub_query_map) {
      sug_sub_query_result.emplace_back(key);
      sub_sub_score_result.emplace_back(value);
    }
    context.SetStringListCommonAttr("sug_sub_query_list", std::move(sug_sub_query_result));
    context.SetDoubleListCommonAttr("sug_sub_query_score_list", std::move(sub_sub_score_result));
    return true;
  }

  static bool EnrichAdjustGoodsWeight(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_source_attr = context.GetStringItemAttr("item_source");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    auto sug_pos_diff = context.GetDoubleCommonAttr("sug_pos_diff").value_or(0.0);
    auto sug_boost_factor = context.GetDoubleCommonAttr("sug_boost_factor").value_or(0.0);
    auto need_boost_cnt = context.GetIntCommonAttr("sug_need_boost_cnt").value_or(0);

    std::string target_source = "GOODS_RECALL";
    int total_size = std::distance(begin, end);
    int boost_pos = need_boost_cnt + static_cast<int>(sug_pos_diff);
    int base_pos = std::min(boost_pos, static_cast<int>(total_size / 2));
    auto iter = begin;
    std::advance(iter, base_pos);
    double base_score = item_score_attr(*iter).value_or(0.0);

    int current_index = 0;
    int boost_cnt = 0;
    for (iter = begin; iter != end; ++iter) {
      auto item_source = item_source_attr(*iter).value_or("");
      auto item_score = item_score_attr(*iter).value_or(0.0);
      std::string item_source_str(item_source);
      if (item_source.empty()) continue;
      if (item_source_str == target_source && boost_cnt < need_boost_cnt) {
        double factor = sug_boost_factor + 1 / (1 + std::exp(0.5 * (current_index - base_pos)));
        double score = std::max(base_score, item_score) * factor;
        boost_cnt++;
        item_score_func(*iter, score);
      }
      current_index++;
    }
    return true;
  }

  static bool EnrichBendiWeight(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    auto bendi_brand_down_score_rate =
        context.GetDoubleCommonAttr("bendi_brand_down_score_rate").value_or(0.0);
    auto brand_name_list_attr =
        context.GetStringListCommonAttr("brand_name_list").value_or(std::vector<absl::string_view>());
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> brand_name_set;
    if (!brand_name_list_attr.empty()) {
      brand_name_set = {brand_name_list_attr.begin(), brand_name_list_attr.end()};
    }
    bool bendi_brand_flag = false;
    absl::string_view target_item("");
    auto iter = begin;
    for (; iter != end; ++iter) {
      auto item_id = item_id_attr(*iter).value_or("");
      auto item_score = item_score_attr(*iter).value_or(0.0);
      double score = item_score;
      if (!bendi_brand_flag && brand_name_set.find(item_id) != brand_name_set.end()) {
        bendi_brand_flag = true;
        target_item = item_id;
      } else if (bendi_brand_flag && brand_name_set.find(item_id) != brand_name_set.end()) {
        score = item_score * bendi_brand_down_score_rate;
        item_score_func(*iter, score);
      }
    }
    if (bendi_brand_flag) {
      for (iter = begin; iter != end; ++iter) {
        auto item_id = item_id_attr(*iter).value_or("");
        auto item_score = item_score_attr(*iter).value_or(0.0);
        std::vector<absl::string_view> extend_brand_text_list = {"团购", "套餐", "优惠券", "自助"};
        if (target_item != item_id) {
          for (const auto &extend_brand_text : extend_brand_text_list) {
            if (item_id.find(extend_brand_text) != -1) {
              item_score_func(*iter, item_score * bendi_brand_down_score_rate);
            }
          }
        }
      }
    }

    return true;
  }

  static bool EnrichBoostSource(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end, const std::string &source, const int &cnt,
                                const double &factor) {
    auto item_source_attr = context.GetStringItemAttr("item_source");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    int num = 0;
    for (auto iter = begin; iter != end; ++iter) {
      auto item_source = item_source_attr(*iter).value_or("");
      auto item_score = item_score_attr(*iter).value_or(0.0);
      if (item_source == absl::string_view(source)) {
        item_score_func(*iter, item_score * factor);
        num++;
      }
      if (cnt > 0 && num >= cnt) break;
    }
    return true;
  }
  static bool EnrichBoostReferSrc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto sug_refer_fast = context.GetIntCommonAttr("sug_refer_fast").value_or(0);
    auto se_sug_refer_boost_factor = context.GetDoubleCommonAttr("se_sug_refer_boost_factor").value_or(0.0);
    int boost_cnt = sug_refer_fast == 0 ? 0 : 2;
    return EnrichBoostSource(context, begin, end, "REFER", boost_cnt, se_sug_refer_boost_factor);
  }

  static bool EnrichBoostCommerceItemsV2(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto sug_boost_commerce_query_exp = context.GetIntCommonAttr("sug_boost_commerce_query_exp").value_or(0);
    auto sug_boost_commerce_query_weight =
        context.GetDoubleCommonAttr("sug_boost_commerce_query_weight").value_or(0.0);
    auto sug_boost_commerce_score_threshold =
        context.GetDoubleCommonAttr("sug_boost_commerce_score_threshold").value_or(0.0);
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    auto commerce_score_attr = context.GetDoubleItemAttr("boost_commerce_score");
    for (auto iter = begin; iter != end; ++iter) {
      auto commerce_score = commerce_score_attr(*iter).value_or(0.0);
      if (commerce_score < sug_boost_commerce_score_threshold) continue;
      auto item_score = item_score_attr(*iter).value_or(0.0);
      if (sug_boost_commerce_query_exp == 1) {
        item_score_func(*iter, item_score * (1 + commerce_score * sug_boost_commerce_query_weight));
      } else if (sug_boost_commerce_query_exp == 2) {
        item_score_func(*iter, item_score * (1 + (commerce_score - 0.8) * sug_boost_commerce_query_weight));
      } else if (sug_boost_commerce_query_exp == 3) {
        item_score_func(*iter, item_score * (1 + (commerce_score - 0.6) * sug_boost_commerce_query_weight));
      }
    }
    return true;
  }

  static bool EnrichDuplicateSupress(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto query = context.GetStringCommonAttr("query").value_or("");
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    if (std::distance(begin, end) <= 2) {
      return true;
    }

    std::vector<std::wstring> norm_wstr;
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
    int i = 0;
    for (auto iter = begin; iter != end; ++iter, ++i) {
      auto item_id = item_id_attr(*iter).value_or("");
      std::wstring wstr = wcv.from_bytes(std::string(item_id));
      std::stable_sort(wstr.begin(), wstr.end());
      norm_wstr.emplace_back(wstr);

      if (item_id == query) continue;
      auto iter2 = begin;
      int j = 0;
      double lcs_pecent_threshold = 0.5;
      double supress_factor = lcs_pecent_threshold;
      absl::string_view new_sug_str;
      for (; iter2 != iter; ++iter2, ++j) {
        new_sug_str = item_id_attr(*iter2).value_or("");
        if (new_sug_str == query) continue;
        auto lcs_len = se_reco::LcsSeq(norm_wstr[i], norm_wstr[j]);
        auto wstr_len_min = std::min(norm_wstr[i].length(), norm_wstr[j].length());
        auto wstr_len_max = std::max(norm_wstr[i].length(), norm_wstr[j].length());
        supress_factor = static_cast<float>(wstr_len_min) / (static_cast<float>(wstr_len_max) + 1e-5);
        if ((lcs_len >= std::max(wstr_len_min, 5LU)) && (supress_factor >= lcs_pecent_threshold)) {
          break;
        }
      }
      if (iter != iter2 && j != i) {
        auto item_score = item_score_attr(*iter).value_or(0.0);
        double score = item_score * (1 + 2 * (lcs_pecent_threshold - supress_factor));
        item_score_func(*iter, score);
      }
    }
    return true;
  }

  static bool ActivityGoodsEnrichFromInterventionBlackData(const CommonRecoLightFunctionContext &context,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) {
    auto scene_type = context.GetStringCommonAttr("intervention_scene_type").value_or("");
    std::string scene_type_str(scene_type);
    auto black_data_loader = Singleton<::ks::search::ziya_sort::KconfTimerReloader<
        ::ks::search::ziya_sort::InterventionBlackDataReloader>>::get()
                                 ->get();
    if (black_data_loader == nullptr) {
      CL_LOG(ERROR) << "null black_data_loader";
      return false;
    }
    const auto &infer_data_map = black_data_loader->get_inter_data();
    auto it = infer_data_map.find(scene_type_str);
    if (it == infer_data_map.end()) {
      CL_LOG(INFO) << "The scene type [" << scene_type_str << "] isn't intervened !!!";
      return true;
    }

    const auto &infer_data = it->second;
    std::vector<std::string> black_query_vec;
    black_query_vec.insert(black_query_vec.end(), infer_data.query_set_.begin(), infer_data.query_set_.end());

    std::vector<std::string> black_goods_id_vec;
    black_goods_id_vec.insert(black_goods_id_vec.end(), infer_data.commodity_id_set_.begin(),
                              infer_data.commodity_id_set_.end());

    context.SetStringListCommonAttr("operation_black_query_list", std::move(black_query_vec));
    context.SetStringListCommonAttr("operation_black_goods_id_list", std::move(black_goods_id_vec));
    return true;
  }

  static bool EnrichLongNormQueryAndReferQuery(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    // 中长期序列
    auto long_searched_history =
        context.GetStringListCommonAttr("long_searched_history").value_or(std::vector<absl::string_view>());
    auto long_searched_history_score =
        context.GetDoubleListCommonAttr("long_searched_history_score").value_or(absl::Span<const double>());
    std::vector<std::pair<absl::string_view, double>> long_searched_query_score;
    if (!long_searched_history.empty() &&
        long_searched_history.size() == long_searched_history_score.size()) {
      for (size_t i = 0; i < long_searched_history.size(); ++i) {
        long_searched_query_score.push_back({long_searched_history.at(i), long_searched_history_score.at(i)});
      }
      std::stable_sort(long_searched_query_score.begin(), long_searched_query_score.end(),
                       [&](const std::pair<absl::string_view, double> &l,
                           const std::pair<absl::string_view, double> &r) { return l.second > r.second; });
      std::vector<std::string> normalized_long_searched_query_vec;
      for (size_t i = 0; i < long_searched_query_score.size(); ++i) {
        std::string long_search_query =
            ks::platform::se_reco::QueryNomalize2(long_searched_query_score[i].first);
        normalized_long_searched_query_vec.emplace_back(long_search_query);
      }
      context.SetStringListCommonAttr("normalized_long_search_queries",
                                      std::move(normalized_long_searched_query_vec));
    }
    // refer query
    auto refer_query =
        context.GetStringListCommonAttr("refer_query").value_or(std::vector<absl::string_view>());
    if (!refer_query.empty()) {
      std::vector<std::string> refer_query_vec;
      for (size_t i = 0; i < refer_query.size(); ++i) {
        std::string norm_refer_query = ks::platform::se_reco::QueryNomalize2(refer_query.at(i));
        refer_query_vec.emplace_back(norm_refer_query);
      }
      context.SetStringListCommonAttr("refer_query_list", std::move(refer_query_vec));
    }
    return true;
  }

  static bool EnrichQAReferJson(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto refer_info = context.GetStringItemAttr("refer_info");
    auto refer_type_func = context.SetIntListItemAttr("refer_type_list");
    auto link_type_func = context.SetIntListItemAttr("link_type_list");
    auto photo_id_func = context.SetIntListItemAttr("photo_id_list");
    auto title_func = context.SetStringListItemAttr("title_list");
    auto source_func = context.SetStringListItemAttr("source_list");
    auto site_func = context.SetStringListItemAttr("site_list");
    auto url_func = context.SetStringListItemAttr("url_list");
    auto refer_count_func = context.SetIntItemAttr("refer_count");
    ::Json::Reader reader;
    for (auto iter = begin; iter != end; iter++) {
      ::Json::Value root;
      if (!reader.parse(std::string(refer_info(*iter).value_or("{}")), root)) {
        continue;
      }
      if (!root.isArray()) {
        continue;
      }
      std::vector<int64_t> refer_types, photo_ids, link_types;
      std::vector<std::string> titles, sites, sources, urls;
      for (const auto &config : root) {
        refer_types.emplace_back(config.isMember("photoId") ? 1 : 0);
        photo_ids.emplace_back(config.isMember("photoId") ? config["photoId"].asInt64() : 0);
        link_types.emplace_back(config.isMember("linkType") ? config["linkType"].asInt64() : 0);
        titles.emplace_back(config.isMember("title") ? config["title"].asString() : "");
        sites.emplace_back(config.isMember("site") ? config["site"].asString() : "");
        sources.emplace_back(config.isMember("source") ? config["source"].asString() : "");
        urls.emplace_back(config.isMember("h5Url") ? config["h5Url"].asString() : "");
      }
      refer_count_func(*iter, refer_types.size());
      refer_type_func(*iter, refer_types);
      link_type_func(*iter, link_types);
      photo_id_func(*iter, photo_ids);
      title_func(*iter, titles);
      source_func(*iter, sources);
      site_func(*iter, sites);
      url_func(*iter, urls);
    }
    return true;
  }

  static bool SplitMultiReferText(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto un_split_text_view = context.GetStringCommonAttr("un_split_text_attr").value_or("");
    std::string un_split_text(un_split_text_view);
    std::regex refer_reg("[[0-9]\\d*]");
    std::vector<int64_t> pos_vec, len_vec, refer_vec;
    std::string::const_iterator iter = un_split_text.begin();
    std::string::const_iterator iter_end = un_split_text.end();
    std::smatch result;
    int64_t iter_pos = 0;
    while (std::regex_search(iter, iter_end, result, refer_reg)) {
      std::string match_text = result.str();
      if (match_text.size() > 2 && '[' == match_text.front() && ']' == match_text.back()) {
        auto str_val = match_text.substr(1, match_text.size() - 2);
        int64_t int_val;
        base::StringToInt64(str_val, &int_val);
        int64_t match_refer_index = int_val - 1;
        pos_vec.emplace_back(result.position() + iter_pos);
        len_vec.emplace_back(result.length());
        refer_vec.emplace_back(match_refer_index);
      }
      iter = result[0].second;
      iter_pos = std::distance((std::string::const_iterator)(un_split_text.begin()), iter);
    }
    context.SetIntListCommonAttr("refer_reg_pos_vec", std::move(pos_vec));
    context.SetIntListCommonAttr("refer_reg_len_vec", std::move(len_vec));
    context.SetIntListCommonAttr("refer_reg_id_vec", std::move(refer_vec));
    return true;
  }

  static bool HitAladdinIntervenePlatform(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_view = context.GetStringCommonAttr("query").value_or("");
    auto city_view = context.GetStringCommonAttr("city").value_or("");
    auto uid_val = context.GetIntCommonAttr("uid").value_or(0);
    std::string query_val(query_view);
    std::string city_val(city_view);
    ks::search::ziya_sort::WhiteAnswerConfig white_answer;
    int64_t hit_whitelist = 0;
    auto reloader =
        Singleton<
            ks::search::ziya_sort::KconfTimerReloader<ks::search::ziya_sort::AladdinInterveneReloader>>::get()
            ->get();
    if (nullptr != reloader && reloader->hit(query_val, uid_val, city_val, &white_answer)) {
      hit_whitelist = 1;
      context.SetIntCommonAttr("white_answer_type", white_answer.type);
      context.SetStringCommonAttr("white_answer_title", white_answer.title);
      context.SetStringCommonAttr("white_answer_site", white_answer.site);
      context.SetStringCommonAttr("white_answer_source", white_answer.source);
      context.SetStringCommonAttr("white_answer_url", white_answer.url);
      context.SetStringCommonAttr("white_answer_photo_id", white_answer.photo_id);
      context.SetStringCommonAttr("white_answer_content", white_answer.content);
    }
    context.SetIntCommonAttr("hit_whitelist", hit_whitelist);
    return true;
  }

  static bool CheckFullMarkdown(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto content_view = context.GetStringCommonAttr("content_unchecked_markdown").value_or("");
    std::string content(content_view);
    ks::search::ziya_sort::MarkdownParser md_parser;
    if (md_parser.check_full_markdown(content)) {
      context.SetIntCommonAttr("content_is_full_markdown", 1);
    } else {
      context.SetIntCommonAttr("content_is_full_markdown", 0);
    }
    return true;
  }

  static bool BoostNewQueryScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_score_attr = context.GetDoubleItemAttr("item_score");
    auto sug_boost_factor_attr = context.GetDoubleItemAttr("sug_boost_factor");
    auto item_id_attr = context.GetStringItemAttr("item_id");
    auto item_icon_type_attr = context.GetStringItemAttr("icon_type");
    auto item_text_type_attr = context.GetStringItemAttr("text");

    auto item_icon_type_func = context.SetStringItemAttr("icon_type");
    auto shaped_query_bizname_func = context.SetStringItemAttr("shaped_query_bizname");
    auto item_score_func = context.SetDoubleItemAttr("item_score");
    auto sug_hot_icon_func = context.SetIntItemAttr("sug_hot_icon");

    auto boost_whitelist_list =
        context.GetStringListCommonAttr("boost_whitelist_list").value_or(std::vector<absl::string_view>());
    auto sug_factor_max_score = context.GetDoubleCommonAttr("sug_factor_max_score").value_or(0.0);
    auto max_boost_num = context.GetDoubleCommonAttr("max_boost_num").value_or(0.0);
    auto icon_factor_threshold = context.GetDoubleCommonAttr("icon_factor_threshold").value_or(0.0);
    int max_boost_num_int = static_cast<int>(max_boost_num);

    std::vector<std::pair<uint64_t, double>> set_new_score_item;
    for (auto iter = begin; iter != end; ++iter) {
      auto item_socre = item_score_attr(*iter).value_or(0.0);
      auto sug_boost_factor = sug_boost_factor_attr(*iter).value_or(0.0);
      set_new_score_item.emplace_back(std::make_pair(iter->item_key, sug_boost_factor));
    }
    if (set_new_score_item.empty()) {
      return true;
    }
    std::stable_sort(set_new_score_item.begin(), set_new_score_item.end(),
                     [&](const std::pair<uint64_t, double> &l, const std::pair<uint64_t, double> &r) {
                       return l.second > r.second;
                     });
    set_new_score_item.resize(max_boost_num_int);
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> boost_whitelist_list_set;
    if (!boost_whitelist_list.empty()) {
      boost_whitelist_list_set.insert(boost_whitelist_list.begin(), boost_whitelist_list.end());
    }
    std::unordered_map<uint64_t, double> item_key_factor_score;
    for (auto &item : set_new_score_item) {
      item_key_factor_score[item.first] = item.second;
    }
    int boost_cnt = 0;
    int icon_cnt = 0;
    for (auto iter = begin; iter != end; ++iter) {
      if (item_key_factor_score.find(iter->item_key) == item_key_factor_score.end()) {
        continue;
      }
      if (boost_cnt >= max_boost_num_int) break;
      auto item_score = item_score_attr(*iter).value_or(0.0);
      auto &factor = item_key_factor_score[iter->item_key];
      double new_score = std::min(sug_factor_max_score, item_score * factor);
      if (item_score < new_score) {
        item_score_func(*iter, new_score);
      }
      auto item_id = item_id_attr(*iter).value_or("");
      auto item_icon_type = item_icon_type_attr(*iter).value_or("");
      auto item_text_type = item_text_type_attr(*iter).value_or("");
      if (boost_whitelist_list_set.count(item_id) > 0 && factor > icon_factor_threshold && icon_cnt < 1 &&
          item_icon_type.empty() && item_text_type.empty()) {
        item_icon_type_func(*iter, "hot");
        sug_hot_icon_func(*iter, 1);
        shaped_query_bizname_func(
            *iter,
            "ALADDIN_AGGR_HOTWORDAP,ALADDIN_AGGR_CHALLENGE,ALADDIN_HOTWORD_CONTEXT,ALADDIN_AGGR_HEADQUERY");
        icon_cnt++;
      }
      boost_cnt++;
    }
    return true;
  }

  static bool HandleMultiQAComboSearchResponse(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *response_info =
        context.GetProtoMessagePtrCommonAttr<::kuaishou::ds::search::ComboSearchResponse>(
            "multi_qa_combo_search_response_pb");
    if (response_info == nullptr) {
      CL_LOG(WARNING) << "response info ptr is nullptr";
      return true;
    }
    auto multi_qa_general_kbox_bizname_list =
        context.GetStringListCommonAttr("multi_qa_general_kbox_bizname_list")
            .value_or(std::vector<absl::string_view>());
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> multi_qa_general_kbox_bizname_set;
    if (!multi_qa_general_kbox_bizname_list.empty()) {
      multi_qa_general_kbox_bizname_set.insert(multi_qa_general_kbox_bizname_list.begin(),
                                               multi_qa_general_kbox_bizname_list.end());
    }
    std::string json_val = "";
    if (response_info->feed_item_size() > 0) {
      auto &item = response_info->feed_item(0);
      if (multi_qa_general_kbox_bizname_set.count(item.item_name())) {
        se::ziya_sort::util::proto_to_json_by_brpc(item, &json_val);
      }
    }

    context.SetStringCommonAttr("multi_qa_combo_search_response_item_json_val", json_val);
    return true;
  }

  static bool HandleMultiQAQueryRewriterResponse(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *response_info =
        context.GetProtoMessagePtrCommonAttr<::mmu::query_parser::QueryRewriterResponse>(
            "multi_qa_query_rewriter_response_pb");
    if (response_info == nullptr) {
      CL_LOG(WARNING) << "response info ptr is nullptr";
      return true;
    }

    auto query_ana_map = (response_info->qrtoparent_resp()).query_ana_map();
    auto query_ana_iter = query_ana_map.find("QA_COMMAND_MULTI_CAT");
    int fresh_degree_val = 0;
    if (query_ana_iter != query_ana_map.end()) {
      const auto &fresh_degree = query_ana_iter->second.fresh_degree();
      fresh_degree_val = fresh_degree.fresh_model_degree();
    }

    context.SetIntCommonAttr("multi_qa_query_rewriter_response_model_degree", fresh_degree_val);
    return true;
  }

  static bool MultiQATransferWeatherString(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto weather_json =
        context.GetStringCommonAttr("multi_qa_transfer_weather_string_weather_json").value_or("");
    auto city_view = context.GetStringCommonAttr("multi_qa_transfer_weather_string_city").value_or("");
    std::string city(city_view.data(), city_view.length());
    std::string weather_string = "";
    ::Json::Value root;
    ::Json::Reader reader;
    reader.parse(weather_json.data(), root);
    if (root.isObject()) {
      std::string cur_air, cur_temp;
      if (root.isMember("curAirQuality") && root["curAirQuality"].isString()) {
        cur_air = root["curAirQuality"].asString();
      }
      if (root.isMember("curTemperature") && root["curTemperature"].isString()) {
        cur_temp = root["curTemperature"].asString();
      }
      static char weather_head_format[] = "%s今天%s，现在温度%s度。";
      weather_string +=
          ::base::StringPrintf(weather_head_format, city.c_str(), cur_air.c_str(), cur_temp.c_str());
      if (root.isMember("temperatureInfo") && root["temperatureInfo"].isArray()) {
        std::vector<std::string> date_name_list = {"今天", "明天", "后天", "大后天"};
        int index = 0;
        for (auto &info : root["temperatureInfo"]) {
          std::string sky, air, date, temp, date_name;
          if (info.isMember("skycon") && info["skycon"].isMember("text") &&
              info["skycon"]["text"].isString()) {
            sky = info["skycon"]["text"].asString();
          }
          if (info.isMember("airQuality") && info["airQuality"].isString()) {
            air = info["airQuality"].asString();
          }
          if (info.isMember("time") && info["time"].isMember("week") && info["time"]["week"].isString()) {
            date = info["time"]["week"].asString();
          }
          if (info.isMember("temperatureRange") && info["temperatureRange"].isString()) {
            temp = info["temperatureRange"].asString();
          }
          if (index < date_name_list.size()) {
            date_name = date_name_list[index];
            static char info_format[] = "%s(%s)空气质量%s，天气状况：%s，温度%s。";
            weather_string += ::base::StringPrintf(info_format, date_name.c_str(), date.c_str(), air.c_str(),
                                                   sky.c_str(), temp.c_str());
          } else {
            static char info_format[] = "%s空气质量%s，天气状况：%s，温度%s。";
            weather_string +=
                ::base::StringPrintf(info_format, date.c_str(), air.c_str(), sky.c_str(), temp.c_str());
          }
          index++;
        }
      }
    }

    context.SetStringCommonAttr("multi_qa_transfer_weather_string_weather_string", weather_string);
    return true;
  }

  static bool QueryAnswerRSKeywordNormalize(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto ori_query_view = context.GetStringCommonAttr("query_answer_rs_ori_query").value_or("");
    std::string query(ori_query_view.data(), ori_query_view.length());
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
    std::wstring input = wcv.from_bytes(query);
    std::wregex pattern(L"[^a-zA-Z0-9\u2e80-\u2fd5\u3190-\u319f\u3400-\u4dbf\u4e00-\u9fcc\uf900-\ufaad]");
    std::wstring charset_range_fmt =
        regex_replace(input, pattern, std::wstring(L""), std::regex_constants::match_default);
    std::string norm_input = wcv.to_bytes(charset_range_fmt);
    std::transform(norm_input.begin(), norm_input.end(), norm_input.begin(), ::tolower);
    context.SetStringCommonAttr("query_answer_rs_regular_query", norm_input);
    return true;
  }

  static bool GetSubWstr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                         RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
    std::wstring tmp_text = wcv.from_bytes(query);
    if (tmp_text.size() <= 1) return true;
    std::wstring sub_tmp_text = tmp_text.substr(0, tmp_text.size() - 1);
    std::string sub_str(wcv.to_bytes(sub_tmp_text));
    context.SetStringCommonAttr("sub_prefix_recall_sub_query", std::move(sub_str));
    return true;
  }

  static bool EnrichSuggSegmentDedup(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto sug_sub_query_list =
        context.GetStringListCommonAttr("sug_sub_query_list").value_or(std::vector<absl::string_view>());
    auto sug_sub_query_score_list =
        context.GetDoubleListCommonAttr("sug_sub_query_score_list").value_or(absl::Span<const double>());
    auto sub_query_dedup_thresh = context.GetDoubleCommonAttr("sub_query_dedup_thresh").value_or(0.0);
    auto item_id_accessor = context.GetStringItemAttr("item_id");
    auto dedup_is_filter = context.GetIntItemAttr("sugg_segment_dedup_is_filter");
    auto sugg_segment_dedup_is_filter_accessor = context.SetIntItemAttr("sugg_segment_dedup_is_filter");

    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> sub_query_score_set;

    if (sug_sub_query_list.size() != sug_sub_query_score_list.size()) {
      LOG(ERROR) << "sug_sub_query_list's size is not equal to sug_sub_query_score_list's";
      return true;
    }
    for (size_t i = 0; i < sug_sub_query_list.size() && i < sug_sub_query_score_list.size(); ++i) {
      if (sug_sub_query_score_list.at(i) >= sub_query_dedup_thresh) {
        sub_query_score_set.insert(sug_sub_query_list.at(i));
      }
    }
    for (auto iterA = begin; iterA != end; ++iterA) {
      auto item_id_A = item_id_accessor(*iterA).value_or("");
      for (auto iterB = iterA; iterB != end; ++iterB) {
        const CommonRecoResult &result = *iterB;
        auto item_id_B = item_id_accessor(result).value_or("");
        auto is_filter = dedup_is_filter(result).value_or(0);
        if (is_filter == 1) {
          continue;
        }
        std::string pair_key = absl::StrFormat("%s_%s", item_id_A, item_id_B);
        std::string pair_key_revert = absl::StrFormat("%s_%s", item_id_B, item_id_A);
        if (sub_query_score_set.find(pair_key) != sub_query_score_set.end() ||
            sub_query_score_set.find(pair_key_revert) != sub_query_score_set.end()) {
          sugg_segment_dedup_is_filter_accessor(result, 1);
        }
      }
    }
    return true;
  }

  static bool QaUserParentKeywordNormalizeExtrachUsername(const CommonRecoLightFunctionContext &context,
                                                          RecoResultConstIter begin,
                                                          RecoResultConstIter end) {
    auto ori_query_view = context.GetStringCommonAttr("qa_user_parent_ori_query").value_or("");
    std::string query(ori_query_view.data(), ori_query_view.length());
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv_kn;
    std::wstring input_kn = wcv_kn.from_bytes(query);
    std::wregex pattern_kn(L"[^a-zA-Z0-9\u2e80-\u2fd5\u3190-\u319f\u3400-\u4dbf\u4e00-\u9fcc\uf900-\ufaad]");
    std::wstring charset_range_fmt_kn =
        regex_replace(input_kn, pattern_kn, std::wstring(L""), std::regex_constants::match_default);
    std::string norm_input_kn = wcv_kn.to_bytes(charset_range_fmt_kn);
    std::transform(norm_input_kn.begin(), norm_input_kn.end(), norm_input_kn.begin(), ::tolower);
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv_eu;
    std::wstring input_eu = wcv_eu.from_bytes(norm_input_kn);
    std::wregex pattern_eu(
        L"(^你了解|有了解吗$|^你所说的|是否是一个公众人物$|^我对|^我想深入了解一下|有什么了解能分享一下吗$|"
        L"是个怎样的人$|是哪个人$|是什么人吗$|^你认识|^你能介绍一下|^你能让我了解一下|的身份信息吗$|有认识吗$"
        L"|是指哪一个人呢$|是指哪个人$|是指哪位呢$|^你说的|^你能帮我详细介绍一下|的情况吗$|"
        L"你能给我一些帮助吗$|吗我对他很感兴趣$|^你能分享一下关于|^我想深入了解|^你能向我介绍一下|的资料吗$|"
        L"的身份吗$|这个人吗$|的了解有所兴趣$|^你能告诉我一些关于|是一个怎样的人物$|^你说的这个|"
        L"是哪个地方的人吗$|有何了解吗$|他是个什么样的人$|是怎样的一个人物$|是谁呢$|是谁$|你能帮我吗$|^"
        L"你知道|^你对这个|来自哪个国家$|^你熟悉|的身份有何了解$|^你能给我介绍一下|^你能告诉我这个|的知识吗$|"
        L"是哪个$|^你能详细解释一下|^你能解释一下这个|^我想探索一下|有什么样的了解$|^你能分享一些关于|^"
        L"你能协助我了解一些关于|他是何许人也$|^你能告诉我|是指谁$|的信息吗$|是谁吗$|的事情吗$|^你对|^"
        L"你提到的|是怎样的一个人$|么$|吗$|^介绍一下|^介绍下|^介绍)");
    std::wstring charset_range_fmt_eu =
        regex_replace(input_eu, pattern_eu, std::wstring(L""), std::regex_constants::match_default);
    std::string norm_input_ev = wcv_eu.to_bytes(charset_range_fmt_eu);
    context.SetStringCommonAttr("qa_user_parent_norm_query", norm_input_ev);
    return true;
  }

  static bool NormalizeLongSearchedHistory(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto long_searched_history =
        context.GetStringListCommonAttr("long_searched_history").value_or(std::vector<absl::string_view>());
    std::vector<std::string> normalized_long_searched_query;
    for (size_t i = 0; i < long_searched_history.size(); ++i) {
      auto &search_query = long_searched_history.at(i);
      std::string search_query_str = std::string(search_query.data(), search_query.size());
      std::string normalized_search_query = se_reco::QueryNormalize(search_query_str);
      normalized_long_searched_query.emplace_back(normalized_search_query);
      if (normalized_long_searched_query.size() >= 9) {
        break;
      }
    }
    context.SetStringListCommonAttr("normalized_long_searched_history_for_emb",
                                    std::move(normalized_long_searched_query));

    return true;
  }

  static bool SuggFormatQueryEmbedding(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *embedding_response =
        context.GetProtoMessagePtrCommonAttr<::mmu::search::CommonEmbeddingResponse>(
            "sugg_query_embedding_response");
    if (embedding_response == nullptr) {
      CL_LOG(WARNING) << "embedding response ptr is nullptr";
      return true;
    }

    auto query_list = context.GetStringListCommonAttr("normalized_long_searched_history_for_emb")
                          .value_or(std::vector<absl::string_view>());
    std::vector<std::string> query_vec;
    std::vector<double> embedding_vec;
    std::vector<float> default_query_embedding(64, 0.0f);
    for (int i = 0; i < query_list.size(); i++) {
      auto query_ori = query_list.at(i);
      std::string query(query_ori.data(), query_ori.length());
      std::vector<float> query_embedding(64, 0.0f);
      auto emb_iter = embedding_response->text_feature().find(query);
      if (emb_iter == embedding_response->text_feature().end()) {
        continue;
      }
      if (emb_iter->second.float_feature_size() == 64) {
        query_embedding.assign(emb_iter->second.float_feature().begin(),
                               emb_iter->second.float_feature().end());
        se_reco::NormalizeEmbedding(&query_embedding);
      }
      if (query_embedding.size() == 64) {
        query_vec.emplace_back(query);
        embedding_vec.insert(embedding_vec.end(), query_embedding.begin(), query_embedding.end());
      } else {
        query_vec.emplace_back("");
        embedding_vec.insert(embedding_vec.end(), default_query_embedding.begin(),
                             default_query_embedding.end());
      }
    }
    context.SetStringListCommonAttr("query_embedding_query_list", std::move(query_vec));
    context.SetDoubleListCommonAttr("query_embedding_vec_list", std::move(embedding_vec));
    return true;
  }

  static bool SuggFormatPhotoEmbedding(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_list_for_embedding =
        context.GetIntListCommonAttr("photo_list_for_embedding").value_or(absl::Span<const int64>());
    auto photo_embedding_list =
        context.GetDoubleListCommonAttr("photo_embedding_list").value_or(absl::Span<const double>());
    if (photo_list_for_embedding.size() * 64 != photo_embedding_list.size()) {
      CL_LOG(WARNING) << "Not the expected size! size of embedding: " << photo_list_for_embedding.size()
                      << ", size of photo: " << photo_embedding_list.size();
      return true;
    }
    std::vector<double> final_photo_embedding;
    std::vector<std::string> final_photo_list;
    for (size_t i = 0; i < photo_embedding_list.size(); i = i + 64) {
      std::vector<float> single_embeding_vec;
      int32_t zero_value = 0;
      for (size_t j = i; j < i + 64; j++) {
        if (std::abs(photo_embedding_list.at(j)) < 0.000001) {
          zero_value++;
        }
        single_embeding_vec.emplace_back(photo_embedding_list.at(j));
      }

      if (zero_value < 64 && single_embeding_vec.size() == 64) {
        se_reco::NormalizeEmbedding(&single_embeding_vec, false);
        final_photo_list.emplace_back(std::to_string(photo_list_for_embedding[i / 64]));
      } else {
        final_photo_list.emplace_back("");
      }
      final_photo_embedding.insert(final_photo_embedding.end(), single_embeding_vec.begin(),
                                   single_embeding_vec.end());
    }

    context.SetStringListCommonAttr("photo_embedding_query_list", std::move(final_photo_list));
    context.SetDoubleListCommonAttr("photo_embedding_vec_list", std::move(final_photo_embedding));
    return true;
  }

  static bool NormalizeRequestQuery(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto rawQuery = context.GetStringCommonAttr("rawQuery").value_or("");
    std::string raw_query_str(rawQuery.data(), rawQuery.size());
    std::string query = se_reco::QueryNormalize(raw_query_str);
    context.SetStringCommonAttr("query", query);
    return true;
  }

  static bool GetQueryInfoCnt(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto query = context.GetStringCommonAttr("query").value_or("");
    int length = query.size();
    int number_cnt = 0;
    int alpha_cnt = 0;
    for (size_t i = 0; i < length; ++i) {
      if (std::isdigit(query[i])) {
        number_cnt++;
      } else if (std::isalpha(query[i])) {
        alpha_cnt++;
      }
    }
    bool only_number = (length == number_cnt);
    bool only_alpha = (length == alpha_cnt);
    // 纯数字或纯短字母
    if (only_number || (only_alpha && alpha_cnt <= 3)) {
      context.SetIntCommonAttr("is_get_merchant_intent", 0);
    } else {
      context.SetIntCommonAttr("is_get_merchant_intent", 1);
    }
    return true;
  }

  static bool EnrichQueryFilterInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto query = context.GetStringCommonAttr("query").value_or("");
    auto session_id_attr = context.GetStringCommonAttr("request_id").value_or("");
    auto user_id = context.GetIntCommonAttr("user_id").value_or(0);
    std::string query_str(query);
    mmu::search::FilterRequest req;
    mmu::search::FilterResult res;
    req.query = query_str;
    Singleton<mmu::search::FilterManager2>::get()->FilterQuery(req, &res);
    if (res.reason.size() != 1) {
      context.SetIntCommonAttr("is_filter_request", 0);
      return false;
    }
    auto &filter_reason = res.reason[0];
    if (!Singleton<mmu::search::FilterManager2>::get()->IsFilter(filter_reason)) {
      return true;
    }

    // 请求被过滤 信息落表
    std::string filter_reason_str = "filter:";
    filter_reason_str += Singleton<mmu::search::FilterManager2>::get()->ToString(filter_reason);
    kuaishou::ds::search::ComboSearchQuerySugLog logger;
    logger.set_keyword(query_str);
    std::string session_id(session_id_attr);
    logger.set_session_id(session_id);
    logger.set_query_session_id(session_id);
    logger.set_timestamp(::base::GetTimestamp() / 1000);  // NOTE: 产出 Kafka 消息的时间，而非实际 sess 时间
    logger.set_user_id(user_id);
    logger.set_debug_info(filter_reason_str);
    std::string string_log;
    logger.SerializeToString(&string_log);
    context.SetStringCommonAttr("request_filter_info", std::move(string_log));
    context.SetIntCommonAttr("is_filter_request", 1);
    return true;
  }

  static bool AdjustSugPrerankInput(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    // input item attr: trend, item_source
    auto max_infer_num = context.GetIntCommonAttr("sug_PRERANK_MERGER_max_infer_num").value_or(0);
    auto keep_input_quota = context.GetIntCommonAttr("sug_PRERANK_MERGER_keep_input_quota").value_or(0);
    auto trend_attr = context.GetDoubleItemAttr("trend");

    std::vector<std::pair<uint64_t, double>> keep_list;
    int total_size = std::distance(begin, end);
    if (total_size > max_infer_num) {
      keep_list.reserve(total_size - max_infer_num + 1);
    } else {
      return true;
    }
    auto iter = begin;
    std::advance(iter, max_infer_num);
    for (; iter != end; ++iter) {
      const auto &result = *iter;
      double trend = trend_attr(result).value_or(0.0);
      if (trend > 0.0) {
        keep_list.emplace_back(std::make_pair(result.key(), trend));
      }
    }
    if (keep_list.empty()) {
      return true;
    }
    std::stable_sort(keep_list.begin(), keep_list.end(),
                     [](const std::pair<uint64_t, double> &l, const std::pair<uint64_t, double> &r) {
                       return l.second > r.second;
                     });
    int cnt = 0;
    std::vector<int64_t> keep_result;
    keep_result.reserve(keep_input_quota + 1);
    for (auto &[item_key, trend_item] : keep_list) {
      if (cnt++ >= keep_input_quota) break;
      keep_result.emplace_back(item_key);
    }
    context.SetIntListCommonAttr("keep_input_trend_item_key_list", std::move(keep_result));
    return true;
  }

  static bool AdjustSugPrerankResultScore(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto prerank_score_list =
        context.GetDoubleListCommonAttr("prerank_score_list").value_or(absl::Span<const double>());
    std::vector<double> result_score_list;
    result_score_list.reserve(prerank_score_list.size());
    for (int i = 0; i < prerank_score_list.size(); ++i) {
      result_score_list.emplace_back(100000000.0 * prerank_score_list.at(i));
    }
    context.SetDoubleListCommonAttr("prerank_score_list", std::move(result_score_list));
    return true;
  }

  static bool QaTextGenerateImage(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client = ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("search", "mmu-vcg-service"));
    ::mmu::vcg::VcgTextToImageRequest request;
    ::mmu::vcg::VcgTextToImageResponse response;

    auto session_id_attr = context.GetStringCommonAttr("request_id").value_or("");
    std::string session_id(session_id_attr.begin(), session_id_attr.end());

    auto query_attr = context.GetStringCommonAttr("query").value_or("");
    std::string query(query_attr.begin(), query_attr.end());

    int image_limit = context.GetIntCommonAttr("qa_text_generate_image_limit").value_or(3);
    int image_width = context.GetIntCommonAttr("qa_text_generate_image_width").value_or(1024);
    int image_height = context.GetIntCommonAttr("qa_text_generate_image_height").value_or(1024);
    int user_id = context.GetIntCommonAttr("user_id").value_or(0);
    auto device_id_attr = context.GetStringCommonAttr("device_id").value_or("");
    std::string device_id(device_id_attr.begin(), device_id_attr.end());
    auto ipv4_attr = context.GetStringCommonAttr("ipv4").value_or("");
    std::string ipv4(ipv4_attr.begin(), ipv4_attr.end());
    auto ipv6_attr = context.GetStringCommonAttr("ipv6").value_or("");
    std::string ipv6(ipv6_attr.begin(), ipv6_attr.end());
    auto query_id_attr = context.GetStringCommonAttr("queryId").value_or("");
    std::string query_id(query_id_attr.begin(), query_id_attr.end());

    auto vcg_common_request_ptr = request.mutable_vcg_common_request();
    vcg_common_request_ptr->set_biz("search");
    vcg_common_request_ptr->set_request_type(::mmu::vcg::RequestType::SYNC_TASK);
    vcg_common_request_ptr->set_bucket_name("upload-image-search-goods-photo");
    vcg_common_request_ptr->set_create_time(::base::GetTimestamp());
    auto params = vcg_common_request_ptr->mutable_params();
    (*params)["callBackKey"] = "callBackValue";
    vcg_common_request_ptr->set_request_id(session_id);
    request.set_prompt(query);
    request.set_image_count(image_limit);
    request.set_width(image_width);
    request.set_height(image_height);
    ks::kess::rpc::grpc::Options request_options;
    request_options.SetTimeout(std::chrono::milliseconds(10000));
    auto status = client->All()->SelectOne()->Stub<::mmu::vcg::kess::MmuVcgService::Stub>().TextToImage(
        request_options, request, &response);
    if (!status.ok()) {
      LOG(WARNING) << "rpc failed, code: " + std::to_string(status.error_code()) +
                          ", msg: " + status.error_message();
      return false;
    }
    std::vector<std::string> image_key_vec;
    std::vector<std::string> image_bucket_name_vec;
    std::vector<std::string> image_url_vec;
    std::vector<std::string> answer_id_vec;
    std::vector<bool> is_filter_vec;

    for (int i = 0; i < response.generate_image_infos_size(); i++) {
      auto item = response.generate_image_infos(i);
      image_key_vec.emplace_back(item.image().key());
      image_bucket_name_vec.emplace_back(item.image().bucket_name());
      std::string url = "https://ali2.a.kwimgs.com/bs2/upload-image-search-goods-photo/" + item.image().key();
      image_url_vec.emplace_back(url);
      std::string answer_id = std::to_string(::base::CityHash64(url.c_str(), url.size()));
      answer_id_vec.emplace_back(answer_id);
      is_filter_vec.emplace_back(false);
    }

    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client_antispam =
        ks::kess::rpc::RpcFacade::CreateGrpcClient2(
            ks::kess::rpc::grpc::OptionsForClient("search", "grpc_antispamRpcAigcMmuService"));
    for (int i = 0; i < image_key_vec.size(); ++i) {
      std::string json = "";

      ::Json::FastWriter root_jw;
      ::Json::Value root;
      root["prompt"] = query;
      root["imageKey"] = image_key_vec[i];
      root["imageBucket"] = image_bucket_name_vec[i];
      root["source"] = "search_chat";
      root["userId"] = std::to_string(user_id);
      root["deviceId"] = device_id;
      root["ipv4"] = ipv4;
      root["ipv6"] = ipv6;

      ::Json::FastWriter ext_params_jw;
      ::Json::Value ext_params;
      ext_params["sessionId"] = session_id;
      ext_params["queryMsgIdV2"] = query_id;
      ext_params["answerMsgIdV2"] = answer_id_vec[i];

      root["extParams"] = ext_params_jw.write(ext_params);

      json.assign(root_jw.write(root));

      ::kuaishou::antispam::RpcRequestParam request_antispam;
      ::kuaishou::antispam::RealTimeAntispamResponse response_antispam;
      request_antispam.set_event_type("AIGC_MMU_CG_PLATFORM_IMAGE");
      request_antispam.set_json(json);

      ks::kess::rpc::grpc::Options request_options_antispam;
      request_options_antispam.SetTimeout(std::chrono::milliseconds(500));
      auto status = client_antispam->All()
                        ->SelectOne()
                        ->Stub<::kuaishou::antispam::kess::AntispamRpcService::Stub>()
                        .Antispam(request_options_antispam, request_antispam, &response_antispam);
      int ret = 1;
      if (!status.ok()) {
        LOG(ERROR) << "get_antispam_errcode error:" << status.error_message();
        ret = 1;
      } else {
        int64_t error_code = response_antispam.error_code();
        ret = error_code > 1 ? 1 : -1;
      }

      if (ret > 0) {
        is_filter_vec[i] = true;
      } else {
        is_filter_vec[i] = false;
      }
    }

    std::string pic_url_list = "";
    int final_image_num = 0;
    for (int i = 0; i < is_filter_vec.size(); i++) {
      if (!is_filter_vec[i]) {
        pic_url_list += "{\"coverId\":\"" + answer_id_vec[i] + "\",";
        pic_url_list += "\"coverUrl\":\"" + image_url_vec[i] + "\"},";
        final_image_num++;
      }
    }
    if (pic_url_list != "") {
      pic_url_list.erase(pic_url_list.size() - 1);
    }
    std::string answer = "以下是AI为您生成的图片：";
    std::string answer_json =
        "{\"index\":0,\"type\":2,\"picUrlList\":[" + pic_url_list + "],\"content\": \"" + answer + "\"}";

    context.SetStringListCommonAttr("text_gen_image_key_vec", std::move(image_key_vec));
    context.SetStringListCommonAttr("text_gen_image_bucket_name_vec", std::move(image_bucket_name_vec));
    context.SetStringListCommonAttr("text_gen_image_url_vec", std::move(image_url_vec));

    context.SetIntCommonAttr("text_gen_image_num", final_image_num);
    context.SetStringCommonAttr("text_gen_image_json", std::move(answer_json));
    return true;
  }

  static bool CheckUnProcessedAnswerHitFlag(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    absl::string_view un_processed_answer_attr =
        context.GetStringCommonAttr("un_processed_answer").value_or("");
    std::string un_processed_answer(un_processed_answer_attr.begin(), un_processed_answer_attr.end());
    int un_processed_answer_len = un_processed_answer.length();
    int un_processed_answer_hit_flag = 0;

    if (un_processed_answer_len >= 1 && un_processed_answer.substr(un_processed_answer_len - 1, 1) == "\\") {
      un_processed_answer_hit_flag = 1;
    }

    context.SetIntCommonAttr("un_processed_answer_hit_flag", un_processed_answer_hit_flag);
    return true;
  }

  static bool QueryAnswerTruncateHistory(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    absl::string_view prompt_attr_name_view = context.GetStringCommonAttr("prompt_attr_name").value_or("");
    std::string prompt_attr_name(prompt_attr_name_view.begin(), prompt_attr_name_view.end());
    absl::string_view prompt_attr_view = context.GetStringCommonAttr(prompt_attr_name).value_or("");
    std::string prompt(prompt_attr_view.begin(), prompt_attr_view.end());
    absl::string_view ip_prompt_ai_role_view = context.GetStringCommonAttr("ip_prompt_ai_role").value_or("");
    std::string ip_prompt_ai_role(ip_prompt_ai_role_view.begin(), ip_prompt_ai_role_view.end());
    absl::string_view ip_prompt_user_role_view =
        context.GetStringCommonAttr("ip_prompt_user_role").value_or("");
    std::string ip_prompt_user_role(ip_prompt_user_role_view.begin(), ip_prompt_user_role_view.end());
    absl::string_view answer_end_label_view =
        context.GetStringCommonAttr("answer_end_label").value_or("</s>");
    std::string answer_end_label(answer_end_label_view.begin(), answer_end_label_view.end());
    auto session_history =
        context.GetStringListCommonAttr("session_history").value_or(std::vector<absl::string_view>());
    int max_length = context.GetIntCommonAttr("ip_prompt_max_length").value_or(1800);

    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
    std::wstring w_prompt = wcv.from_bytes(prompt);
    int length = w_prompt.length();

    std::vector<std::string> multi_qa_session_history_select;
    int session_history_len = session_history.size();
    for (int i = session_history_len - 1; i >= 0; i--) {
      std::string hist(session_history.at(i).begin(), session_history.at(i).end());
      std::string extra_hist_info = "";
      if (i % 2 == 1) {
        auto fixed_tips_pos = hist.find("我还为您在快手里找到了");
        if (fixed_tips_pos != std::string::npos) {
          hist.erase(fixed_tips_pos, hist.size() - fixed_tips_pos);
        }
        std::string answer_cut_tips{"（输出长度超过限制）"};
        auto cut_tips_pos = hist.find(answer_cut_tips);
        if (cut_tips_pos != std::string::npos) {
          hist.erase(cut_tips_pos, answer_cut_tips.size());
        }
        extra_hist_info = ip_prompt_ai_role + ": \\n " + answer_end_label;
      } else {
        extra_hist_info = ip_prompt_user_role + ":  \\n ";
      }
      std::wstring w_hist = wcv.from_bytes(hist);
      std::wstring w_extra_hist_info = wcv.from_bytes(extra_hist_info);
      length += w_hist.length();
      length += w_extra_hist_info.length();
      multi_qa_session_history_select.push_back(hist);
      // 截断最近对话历史，避免输入 token 长度超限制
      if (i % 2 == 0 && length >= max_length) {
        break;
      }
    }

    if (length > 2300 && multi_qa_session_history_select.size() >= 2) {
      multi_qa_session_history_select.pop_back();
      multi_qa_session_history_select.pop_back();
    }
    std::string final_history = "";
    std::string deep_seek_history = "";

    std::reverse(multi_qa_session_history_select.begin(), multi_qa_session_history_select.end());

    int index = 0;
    for (auto hist : multi_qa_session_history_select) {
      if (index % 2 == 0) {
        final_history = final_history + ip_prompt_user_role + ": " + hist + " \\n ";
        deep_seek_history = deep_seek_history + "<|im_start|>user\\n" + hist + "<|im_end|>\\n";
      } else {
        final_history = final_history + ip_prompt_ai_role + ":" + hist + " \\n " + answer_end_label;
        deep_seek_history = deep_seek_history + "<|im_start|>assistant\\n" + hist + "<|im_end|>\\n";
      }
      ++index;
    }
    context.SetStringCommonAttr("chat_history", final_history);
    context.SetStringCommonAttr("deep_seek_history", deep_seek_history);
    return true;
  }

  static bool QueryAnswerIPRs(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto session_history =
        context.GetStringListCommonAttr("session_history").value_or(std::vector<absl::string_view>());
    auto query_view = context.GetStringCommonAttr("query").value_or("");
    std::string query(query_view.begin(), query_view.end());
    auto character_id = context.GetIntCommonAttr("characterId").value_or(0);
    static std::shared_ptr<ks::kess::rpc::grpc::Client2> client = ks::kess::rpc::RpcFacade::CreateGrpcClient2(
        ks::kess::rpc::grpc::OptionsForClient("search", "multi-chat-query-sexy-sensitive-intent"));
    std::string prompt = "";
    std::tuple<std::string, std::string, std::string> prompt_config;
    std::shared_ptr<::Json::Value> ip_prompt_ptr =
        ks::search::ziya_sort::GlobalKconf::get_kconf_value<std::shared_ptr<::Json::Value>>(
            "se.query_answer.rs_character_config", std::make_shared<::Json::Value>());
    if (ip_prompt_ptr->isObject() && ip_prompt_ptr->isMember("prompt_config") &&
        (*ip_prompt_ptr)["prompt_config"].isMember(std::to_string(character_id))) {
      const auto &config = (*ip_prompt_ptr)["prompt_config"][std::to_string(character_id)];
      if (config.isMember("system") && config.isMember("user_role") && config.isMember("ai_role")) {
        std::get<0>(prompt_config) = config["system"].asString();
        std::get<1>(prompt_config) = config["user_role"].asString();
        std::get<2>(prompt_config) = config["ai_role"].asString();
      } else {
        LOG(ERROR) << "character_id don't have prompt_config";
        return false;
      }
    } else {
      return true;
    }
    prompt = "###\n" + std::get<0>(prompt_config);
    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
    std::wstring w_prompt = wcv.from_bytes(prompt);
    int length = w_prompt.length();
    std::vector<std::string> multi_qa_session_history_select;
    int session_history_len = session_history.size();
    for (int i = session_history_len - 1; i >= 0; i--) {
      std::string hist(session_history.at(i).begin(), session_history.at(i).end());
      if (i % 2 == 1) {
        auto fixed_tips_pos = hist.find("我还为您在快手里找到了");
        if (fixed_tips_pos != std::string::npos) {
          hist.erase(fixed_tips_pos, hist.size() - fixed_tips_pos);
        }
        std::string answer_cut_tips{"（输出长度超过限制）"};
        auto cut_tips_pos = hist.find(answer_cut_tips);
        if (cut_tips_pos != std::string::npos) {
          hist.erase(cut_tips_pos, answer_cut_tips.size());
        }
      }
      std::wstring w_hist = wcv.from_bytes(hist);
      length += w_hist.length();
      multi_qa_session_history_select.push_back(hist);
      // 截断最近对话历史，避免输入 token 长度超限制
      if (i % 2 == 0 && length >= 1800) {
        break;
      }
    }
    std::reverse(multi_qa_session_history_select.begin(), multi_qa_session_history_select.end());
    int index = 0;
    for (auto hist : multi_qa_session_history_select) {
      if (index % 2 == 0) {
        prompt += (std::get<1>(prompt_config) + ": " + hist + " \\n ");
      } else {
        prompt += (std::get<2>(prompt_config) + ":" + hist + " \\n </s>");
      }
      ++index;
    }
    prompt += std::get<1>(prompt_config) + ": " + query + " \\n " + std::get<2>(prompt_config) + ":";
    prompt += "\n###根据以上内容预测用户下一轮可能说的话:";

    std::vector<std::string> rs_list;
    ::mmu::serving::PredictRequest request;
    ::mmu::serving::PredictResult response;
    auto request_val_map = request.mutable_ex_feature();
    ::mmu::serving::ExFeature val1;
    val1.set_str_val(prompt);
    (*request_val_map)["text"] = val1;

    ks::kess::rpc::grpc::Options request_options;
    request_options.SetTimeout(std::chrono::milliseconds(1000));
    auto status = client->All()->SelectOne()->Stub<::mmu::serving::kess::ModelServing::Stub>().Predict(
        request_options, request, &response);

    if (!status.ok()) {
      LOG(ERROR) << "get_ip_rs error:" << status.error_message();
      return false;
    }
    auto response_val_map = response.mutable_ex_feature();
    if (response_val_map->count("rs_list")) {
      for (const auto &elem : (*response_val_map)["rs_list"].str_array().str_elems()) {
        rs_list.emplace_back(elem);
      }
    }
    context.SetStringListCommonAttr("query_answer_ip_rs", std::move(rs_list));
    return true;
  }

  static bool EnrichMatchedHistoryQuery(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::string query(context.GetStringCommonAttr("query").value_or(""));
    std::string pinyin_query(context.GetStringCommonAttr("pinyin_query").value_or(""));
    auto searched_query_list =
        context.GetStringListCommonAttr("searched_query_list").value_or(std::vector<absl::string_view>());
    auto searched_query_timestamp_list =
        context.GetIntListCommonAttr("searched_query_timestamp_list").value_or(absl::Span<const int64>());
    auto u_gsu_query_opt =
        context.GetStringListCommonAttr("u_gsu_query_opt").value_or(std::vector<absl::string_view>());
    auto u_gsu_timestamp_ms_opt =
        context.GetIntListCommonAttr("u_gsu_timestamp_ms_opt").value_or(absl::Span<const int64>());

    const int64_t result_num = 4;
    std::vector<std::string> matched_history_query_list;
    std::vector<int64_t> matched_history_query_timestamp_list;
    std::unordered_set<std::string> history_query_set;
    for (size_t i = 0; i < searched_query_list.size() && i < searched_query_timestamp_list.size(); ++i) {
      if (matched_history_query_list.size() >= result_num) {
        break;
      }
      std::string searched_query = std::string(searched_query_list[i].data(), searched_query_list[i].size());
      if (searched_query.size() > 48 || history_query_set.find(searched_query) != history_query_set.end()) {
        continue;
      }
      std::string norm_searched_query = ks::platform::se_reco::QueryNomalize2(searched_query_list[i]);
      if (norm_searched_query.size() < 2) {
        continue;
      }
      if (se_reco::IsMatch(query, pinyin_query, norm_searched_query)) {
        matched_history_query_list.emplace_back(searched_query);
        matched_history_query_timestamp_list.emplace_back(searched_query_timestamp_list[i]);
        history_query_set.insert(searched_query);
      }
    }
    for (size_t i = 0; i < u_gsu_query_opt.size() && i < u_gsu_timestamp_ms_opt.size(); ++i) {
      if (matched_history_query_list.size() >= result_num) {
        break;
      }
      std::string searched_query = std::string(u_gsu_query_opt[i].data(), u_gsu_query_opt[i].size());
      if (searched_query.size() > 48 || history_query_set.find(searched_query) != history_query_set.end()) {
        continue;
      }
      std::string norm_searched_query = ks::platform::se_reco::QueryNomalize2(u_gsu_query_opt[i]);
      if (norm_searched_query.size() < 2) {
        continue;
      }
      if (se_reco::IsMatch(query, pinyin_query, norm_searched_query)) {
        matched_history_query_list.emplace_back(searched_query);
        matched_history_query_timestamp_list.emplace_back(u_gsu_timestamp_ms_opt[i]);
        history_query_set.insert(searched_query);
      }
    }

    context.SetStringListCommonAttr("history_recall_query_list", std::move(matched_history_query_list));
    context.SetIntListCommonAttr("history_recall_timestamp_list",
                                 std::move(matched_history_query_timestamp_list));
    return true;
  }

  static bool EnrichHotUserGenderAge(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto query_str = std::string(context.GetStringCommonAttr("query").value_or(""));
    auto author_list_attr = context.GetStringListCommonAttr("hot_user_gender_author_list")
                                .value_or(std::vector<absl::string_view>());

    std::vector<int64_t> item_key_list;
    std::vector<int64_t> item_author_id_list;
    std::vector<std::string> item_author_name_list;
    for (int i = 0; i < author_list_attr.size(); ++i) {
      std::vector<std::string> info_list;
      std::string author_list_str = std::string(author_list_attr.at(i));
      base::SplitString(author_list_str, "_", &info_list);
      if (info_list.size() != 3 || info_list[0].empty()) {
        CL_LOG(WARNING) << "sug_gender_age split falied";
        continue;
      }
      int64_t author_id = 0;
      ::base::StringToInt64(info_list[0], &author_id);
      if (author_id == 0) continue;
      std::string author_name = info_list[2];
      if (author_name.size() < query_str.size() || author_name.compare(0, query_str.size(), query_str) != 0) {
        continue;
      }

      int64_t item_key = ::base::CityHash64(author_list_str.c_str(), author_list_str.size());
      item_key_list.emplace_back(item_key);
      item_author_id_list.emplace_back(author_id);
      item_author_name_list.emplace_back(author_name);
    }
    if (!item_key_list.empty()) {
      context.SetIntListCommonAttr("hot_user_gender_age_item_key_list", std::move(item_key_list));
      context.SetIntListCommonAttr("hot_user_gender_age_author_id_list", std::move(item_author_id_list));
      context.SetStringListCommonAttr("hot_user_gender_age_author_name_list",
                                      std::move(item_author_name_list));
    }
    return true;
  }

  static bool SuggJubaoMultiScoreAdjust(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> sug_ctr_scores;
    std::vector<double> res_ctr_scores;
    auto sug_ctr_score_attr = context.GetDoubleItemAttr("sug_ctr");
    auto res_ctr_score_attr = context.GetDoubleItemAttr("res_ctr");
    for (auto iter = begin; iter != end; ++iter) {
      double sug_ctr_score = sug_ctr_score_attr(*iter).value_or(0.0);
      double res_ctr_score = res_ctr_score_attr(*iter).value_or(0.0);
      sug_ctr_scores.emplace_back(sug_ctr_score);
      res_ctr_scores.emplace_back(res_ctr_score);
    }
    std::sort(sug_ctr_scores.begin(), sug_ctr_scores.end(),
              [](auto &left, auto &right) { return left > right; });
    std::sort(res_ctr_scores.begin(), res_ctr_scores.end(),
              [](auto &left, auto &right) { return left > right; });

    int max_rank_num = 8;
    const int total_item_num = std::distance(begin, end);
    std::unordered_map<int64_t, double> sug_ctr_key2score;
    std::unordered_map<int64_t, double> res_ctr_key2score;
    // 下面两个 attr name 和变量名相反，是为了对齐原来的策略
    auto sug_ctr_multi_score_attr = context.GetDoubleListItemAttr("res_ctr_multipos");
    auto res_ctr_multi_score_attr = context.GetDoubleListItemAttr("sug_ctr_multipos");
    std::vector<std::pair<int64_t, double>> sug_ctr_multipos_score_pairs;
    std::vector<std::pair<int64_t, double>> res_ctr_multipos_score_pairs;
    for (int i = 0; i < total_item_num; i++) {
      if (i < max_rank_num) {
        for (auto iter = begin; iter != end; ++iter) {
          const CommonRecoResult &result = *iter;
          if (sug_ctr_key2score.find(result.item_key) == sug_ctr_key2score.end()) {
            auto sug_ctr_multipos = sug_ctr_multi_score_attr(result).value_or(absl::Span<const double>());
            if (i < sug_ctr_multipos.size()) {
              auto ctr_score = sug_ctr_multipos.at(i);
              sug_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, ctr_score));
            }
          }
          if (res_ctr_key2score.find(result.item_key) == res_ctr_key2score.end()) {
            auto res_ctr_multipos = res_ctr_multi_score_attr(result).value_or(absl::Span<const double>());
            if (i < res_ctr_multipos.size()) {
              auto ctr_score = res_ctr_multipos.at(i);
              res_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, ctr_score));
            }
          }
        }
        std::sort(sug_ctr_multipos_score_pairs.begin(), sug_ctr_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        std::sort(res_ctr_multipos_score_pairs.begin(), res_ctr_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        if (sug_ctr_multipos_score_pairs.size() > 0) {
          sug_ctr_key2score[sug_ctr_multipos_score_pairs[0].first] = sug_ctr_scores[i];
        }
        if (res_ctr_multipos_score_pairs.size() > 0) {
          res_ctr_key2score[res_ctr_multipos_score_pairs[0].first] = res_ctr_scores[i];
        }
        sug_ctr_multipos_score_pairs.clear();
        res_ctr_multipos_score_pairs.clear();
      } else {
        for (auto iter = begin; iter != end; ++iter) {
          const CommonRecoResult &result = *iter;
          if (sug_ctr_key2score.find(result.item_key) == sug_ctr_key2score.end()) {
            double sug_ctr_score = sug_ctr_score_attr(*iter).value_or(0.0);
            sug_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, sug_ctr_score));
          }
          if (res_ctr_key2score.find(result.item_key) == res_ctr_key2score.end()) {
            double res_ctr_score = res_ctr_score_attr(*iter).value_or(0.0);
            res_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, res_ctr_score));
          }
        }
        std::sort(sug_ctr_multipos_score_pairs.begin(), sug_ctr_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        std::sort(res_ctr_multipos_score_pairs.begin(), res_ctr_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        for (size_t k = 0;
             k < sug_ctr_multipos_score_pairs.size() && (k + max_rank_num) < sug_ctr_scores.size(); ++k) {
          sug_ctr_key2score[sug_ctr_multipos_score_pairs[k].first] = sug_ctr_scores[k + max_rank_num];
        }
        for (size_t k = 0;
             k < res_ctr_multipos_score_pairs.size() && (k + max_rank_num) < res_ctr_scores.size(); ++k) {
          res_ctr_key2score[res_ctr_multipos_score_pairs[k].first] = res_ctr_scores[k + max_rank_num];
        }
        break;
      }
    }

    auto adjust_sug_ctr_set_func = context.SetDoubleItemAttr("sug_ctr");
    auto adjust_res_ctr_set_func = context.SetDoubleItemAttr("res_ctr");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (sug_ctr_key2score.find(result.item_key) != sug_ctr_key2score.end()) {
        adjust_sug_ctr_set_func(result, sug_ctr_key2score[result.item_key]);
      }
      if (res_ctr_key2score.find(result.item_key) != res_ctr_key2score.end()) {
        adjust_res_ctr_set_func(result, res_ctr_key2score[result.item_key]);
      }
    }
    return true;
  }

  static bool EmptyLightFunction(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    return true;
  }

  static bool SuggJubaoMultiScoreHungarianAdjust(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> sug_ctr_scores;
    std::vector<double> res_ctr_scores;
    std::vector<int64_t> keys;
    auto sug_ctr_score_attr = context.GetDoubleItemAttr("sug_ctr");
    auto res_ctr_score_attr = context.GetDoubleItemAttr("res_ctr");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      keys.emplace_back(result.item_key);
      double sug_ctr_score = sug_ctr_score_attr(*iter).value_or(0.0);
      double res_ctr_score = res_ctr_score_attr(*iter).value_or(0.0);
      sug_ctr_scores.emplace_back(sug_ctr_score);
      res_ctr_scores.emplace_back(res_ctr_score);
    }
    std::sort(sug_ctr_scores.begin(), sug_ctr_scores.end(),
              [](auto &left, auto &right) { return left > right; });
    std::sort(res_ctr_scores.begin(), res_ctr_scores.end(),
              [](auto &left, auto &right) { return left > right; });

    int max_rank_num = 8;
    std::unordered_map<int64_t, double> sug_ctr_key2score;
    std::unordered_map<int64_t, double> res_ctr_key2score;
    // 下面两个 attr name 和变量名相反，是为了对齐原来的策略
    auto sug_ctr_multi_score_attr = context.GetDoubleListItemAttr("res_ctr_multipos");
    auto res_ctr_multi_score_attr = context.GetDoubleListItemAttr("sug_ctr_multipos");
    std::vector<std::pair<int64_t, double>> sug_ctr_multipos_score_pairs;
    std::vector<std::pair<int64_t, double>> res_ctr_multipos_score_pairs;


    std::vector<std::vector<double> > costMatrix_sug;
    std::vector<std::vector<double> > costMatrix_res;

    for (int i = 0; i < max_rank_num; i++) {
      std::vector<double> costMatrix_sug_posn;
      std::vector<double> costMatrix_res_posn;
      for (auto iter = begin; iter != end; ++iter) {
        const CommonRecoResult &result = *iter;
        auto sug_ctr_multipos = sug_ctr_multi_score_attr(result).value_or(absl::Span<const double>());
        costMatrix_sug_posn.emplace_back(1-sug_ctr_multipos.at(i));
        auto res_ctr_multipos = res_ctr_multi_score_attr(result).value_or(absl::Span<const double>());
        costMatrix_res_posn.emplace_back(1-res_ctr_multipos.at(i));
      }
      costMatrix_sug.emplace_back(costMatrix_sug_posn);
      costMatrix_res.emplace_back(costMatrix_res_posn);
    }
    std::vector<int> assignment_sug;
    std::vector<int> assignment_res;

    double cost_sug = se_reco::HungarianSolve(costMatrix_sug, &assignment_sug);
    double cost_res = se_reco::HungarianSolve(costMatrix_res, &assignment_res);

    for (int i = 0; i < assignment_sug.size(); i++) {
      if (assignment_sug[i] < keys.size()) {
        sug_ctr_key2score[keys[assignment_sug[i]]] = sug_ctr_scores[i];
      }
    }
    for (int i = 0; i < assignment_res.size(); i++) {
      if (assignment_res[i] < keys.size()) {
        res_ctr_key2score[keys[assignment_res[i]]] = res_ctr_scores[i];
      }
    }
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (sug_ctr_key2score.find(result.item_key) == sug_ctr_key2score.end()) {
        double sug_ctr_score = sug_ctr_score_attr(*iter).value_or(0.0);
        sug_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, sug_ctr_score));
      }
      if (res_ctr_key2score.find(result.item_key) == res_ctr_key2score.end()) {
        double res_ctr_score = res_ctr_score_attr(*iter).value_or(0.0);
        res_ctr_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, res_ctr_score));
      }
    }
    std::sort(sug_ctr_multipos_score_pairs.begin(), sug_ctr_multipos_score_pairs.end(),
              [](auto &left, auto &right) { return left.second > right.second; });
    std::sort(res_ctr_multipos_score_pairs.begin(), res_ctr_multipos_score_pairs.end(),
              [](auto &left, auto &right) { return left.second > right.second; });
    for (size_t k = 0; k < sug_ctr_multipos_score_pairs.size(); ++k) {
      sug_ctr_key2score[sug_ctr_multipos_score_pairs[k].first] = sug_ctr_scores[k + max_rank_num];
    }
    for (size_t k = 0; k < res_ctr_multipos_score_pairs.size(); ++k) {
      res_ctr_key2score[res_ctr_multipos_score_pairs[k].first] = res_ctr_scores[k + max_rank_num];
    }
    auto adjust_sug_ctr_set_func = context.SetDoubleItemAttr("sug_ctr");
    auto adjust_res_ctr_set_func = context.SetDoubleItemAttr("res_ctr");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (sug_ctr_key2score.find(result.item_key) != sug_ctr_key2score.end()) {
        adjust_sug_ctr_set_func(result, sug_ctr_key2score[result.item_key]);
      }
      if (res_ctr_key2score.find(result.item_key) != res_ctr_key2score.end()) {
        adjust_res_ctr_set_func(result, res_ctr_key2score[result.item_key]);
      }
    }
    return true;
  }

  static bool SuggJubaoMultiFinalScoreAdjust(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> final_scores;
    auto final_score_attr = context.GetDoubleItemAttr("final_score_single");
    for (auto iter = begin; iter != end; ++iter) {
      double final_score = final_score_attr(*iter).value_or(0.0);
      final_scores.emplace_back(final_score);
    }
    std::sort(final_scores.begin(), final_scores.end(),
              [](auto &left, auto &right) { return left > right; });

    int max_rank_num = 8;
    const int total_item_num = std::distance(begin, end);
    std::unordered_map<int64_t, double> final_score_key2score;

    auto final_score_multi_score_attr = context.GetDoubleListItemAttr("final_score_multipos");
    std::vector<std::pair<int64_t, double>> final_score_multipos_score_pairs;
    for (int i = 0; i < total_item_num; i++) {
      if (i < max_rank_num) {
        for (auto iter = begin; iter != end; ++iter) {
          const CommonRecoResult &result = *iter;
          auto final_score_multipos =
          final_score_multi_score_attr(result).value_or(absl::Span<const double>());
          if (final_score_key2score.find(result.item_key) == final_score_key2score.end()) {
            if (i < final_score_multipos.size()) {
              auto final_score = final_score_multipos.at(i);
              final_score_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, final_score));
            }
          }
        }
        std::sort(final_score_multipos_score_pairs.begin(), final_score_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        if (final_score_multipos_score_pairs.size() > 0) {
          final_score_key2score[final_score_multipos_score_pairs[0].first] = final_scores[i];
        }
        final_score_multipos_score_pairs.clear();
      } else {
        for (auto iter = begin; iter != end; ++iter) {
          const CommonRecoResult &result = *iter;
          if (final_score_key2score.find(result.item_key) == final_score_key2score.end()) {
            double final_score = final_score_attr(*iter).value_or(0.0);
            final_score_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, final_score));
          }
        }
        std::sort(final_score_multipos_score_pairs.begin(), final_score_multipos_score_pairs.end(),
                  [](auto &left, auto &right) { return left.second > right.second; });
        for (size_t k = 0; k < final_score_multipos_score_pairs.size(); ++k) {
          final_score_key2score[final_score_multipos_score_pairs[k].first] = final_scores[k + max_rank_num];
        }
        break;
      }
    }

    auto adjust_sug_ctr_set_func = context.SetDoubleItemAttr("item_score");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (final_score_key2score.find(result.item_key) != final_score_key2score.end()) {
        adjust_sug_ctr_set_func(result, final_score_key2score[result.item_key]);
      }
    }
    return true;
  }

  static bool SuggJubaoMultiFinalScoreHungarianAdjust(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> final_scores;
    std::vector<int64_t> keys;
    auto final_score_attr = context.GetDoubleItemAttr("final_score_single");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      keys.emplace_back(result.item_key);
      double final_score = final_score_attr(*iter).value_or(0.0);
      final_scores.emplace_back(final_score);
    }
    std::sort(final_scores.begin(), final_scores.end(),
              [](auto &left, auto &right) { return left > right; });

    int max_rank_num = 8;
    std::unordered_map<int64_t, double> final_score_key2score;
    auto final_score_multipos_attr = context.GetDoubleListItemAttr("final_score_multipos");
    std::vector<std::pair<int64_t, double>> final_score_multipos_score_pairs;
    std::vector<std::vector<double> > costMatrix;

    for (int i = 0; i < max_rank_num; i++) {
      std::vector<double> costMatrix_posn;
      for (auto iter = begin; iter != end; ++iter) {
        const CommonRecoResult &result = *iter;
        auto final_score_multipos = final_score_multipos_attr(result).value_or(absl::Span<const double>());
        costMatrix_posn.emplace_back(20-final_score_multipos.at(i));
      }
      costMatrix.emplace_back(costMatrix_posn);
    }

    std::vector<int> assignment;
    double cost = se_reco::HungarianSolve(costMatrix, &assignment);
    for (int i = 0; i < assignment.size(); i++) {
      if (assignment[i] < keys.size()) {
        final_score_key2score[keys[assignment[i]]] = final_scores[i];
      }
    }
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (final_score_key2score.find(result.item_key) == final_score_key2score.end()) {
        double final_score = final_score_attr(*iter).value_or(0.0);
        final_score_multipos_score_pairs.emplace_back(std::make_pair(result.item_key, final_score));
      }
    }
    std::sort(final_score_multipos_score_pairs.begin(), final_score_multipos_score_pairs.end(),
              [](auto &left, auto &right) { return left.second > right.second; });
    for (size_t k = 0; k < final_score_multipos_score_pairs.size(); ++k) {
      final_score_key2score[final_score_multipos_score_pairs[k].first] = final_scores[k + max_rank_num];
    }
    auto adjust_final_score_set_func = context.SetDoubleItemAttr("item_score");
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      if (final_score_key2score.find(result.item_key) != final_score_key2score.end()) {
        adjust_final_score_set_func(result, final_score_key2score[result.item_key]);
      }
    }
    return true;
  }

  static bool CalcItemQueryRelevanceScore(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recent_searched_query_combo_ann_emb =
        context.GetDoubleListCommonAttr("recent_searched_query_combo_ann_emb")
            .value_or(absl::Span<const double>());
    auto recent_searched_query_combo_ann_emb_norm =
        context.GetDoubleCommonAttr("recent_searched_query_combo_ann_emb_norm").value_or(0.0);
    auto embedding_accessor = context.GetDoubleListItemAttr("embedding");
    auto recent_searched_query_relevance_score_accessor =
        context.SetDoubleItemAttr("recent_searched_query_relevance_score");
    bool is_common_emb_valid = true;
    if (recent_searched_query_combo_ann_emb.size() != 128) {
      is_common_emb_valid = false;
    }
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      auto embedding = embedding_accessor(result).value_or(absl::Span<const double>());
      if (is_common_emb_valid == false || embedding.size() != 128) {
        recent_searched_query_relevance_score_accessor(result, 0.0);
        continue;
      }
      double emb_sum = 0.0;
      double emb_product = 0.0;
      for (int i = 0; i < 128; i++) {
        emb_product += embedding.at(i) * recent_searched_query_combo_ann_emb.at(i);
        emb_sum += embedding.at(i) * embedding.at(i);
      }
      if (emb_product > 0) {
        emb_product =
            (emb_product / (std::sqrt(emb_sum) * recent_searched_query_combo_ann_emb_norm) + 1.0) / 2.0;
        recent_searched_query_relevance_score_accessor(result, emb_product);
      } else {
        recent_searched_query_relevance_score_accessor(result, 0.0);
      }
    }
    return true;
  }

  static bool CalcQuerisItemsRelevanceScore(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recent_searched_query_combo_ann_emb =
        context.GetDoubleListCommonAttr("recent_searched_query_combo_ann_emb")
            .value_or(absl::Span<const double>());
    auto embedding_accessor = context.GetDoubleListItemAttr("embedding");
    int64_t emb_dim = context.GetIntCommonAttr("emb_dim").value_or(0);;
    auto recent_searched_query_relevance_score_accessor =
        context.SetDoubleItemAttr("recent_searched_query_relevance_score");
    bool is_common_emb_valid = true;
    if (recent_searched_query_combo_ann_emb.size() == 0 ||
        recent_searched_query_combo_ann_emb.size() % emb_dim != 0) {
      is_common_emb_valid = false;
    }
    int recent_query_num = recent_searched_query_combo_ann_emb.size() / emb_dim;
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      auto embedding = embedding_accessor(result).value_or(absl::Span<const double>());
      if (is_common_emb_valid == false || embedding.size() != emb_dim) {
        recent_searched_query_relevance_score_accessor(result, 0.0);
        continue;
      }
      double sim = 0.0;
      double max_sim = 0.0;
      for (int i = 0; i < recent_query_num; i++) {
        sim = 0.0;
        for (int j = 0; j < emb_dim; j++) {
          sim += embedding.at(j) * recent_searched_query_combo_ann_emb.at(i*emb_dim+j);
        }
        if (max_sim < sim) {
          max_sim = sim;
        }
      }
      if (max_sim > 0) {
        max_sim = (max_sim + 1.0) / 2.0;
        recent_searched_query_relevance_score_accessor(result, max_sim);
      } else {
        recent_searched_query_relevance_score_accessor(result, 0.0);
      }
    }
    return true;
  }

  static bool CalcItemPhotoRelevanceScore(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recent_searched_query_combo_ann_emb =
        context.GetDoubleListCommonAttr("recent_combo_relevance_photo_emb")
            .value_or(absl::Span<const double>());
    auto recent_searched_query_combo_ann_emb_norm =
        context.GetDoubleCommonAttr("recent_combo_relevance_photo_emb_norm").value_or(0.0);
    auto embedding_accessor = context.GetDoubleListItemAttr("embedding_1");
    auto recent_searched_query_relevance_score_accessor =
        context.SetDoubleItemAttr("recent_refer_photo_relevance_score");
    bool is_common_emb_valid = true;
    if (recent_searched_query_combo_ann_emb.size() != 128) {
      is_common_emb_valid = false;
    }
    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      auto embedding = embedding_accessor(result).value_or(absl::Span<const double>());
      if (is_common_emb_valid == false || embedding.size() != 128) {
        recent_searched_query_relevance_score_accessor(result, 0.0);
        continue;
      }
      double emb_sum = 0.0;
      double emb_product = 0.0;
      for (int i = 0; i < 128; i++) {
        emb_product += embedding.at(i) * recent_searched_query_combo_ann_emb.at(i);
        emb_sum += embedding.at(i) * embedding.at(i);
      }
      if (emb_product > 0) {
        emb_product =
            (emb_product / (std::sqrt(emb_sum) * recent_searched_query_combo_ann_emb_norm) + 1.0) / 2.0;
        recent_searched_query_relevance_score_accessor(result, emb_product);
      } else {
        recent_searched_query_relevance_score_accessor(result, 0.0);
      }
    }
    return true;
  }

  static bool CrossSessionInfoParseFromString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto cross_ssid_info_from_redis_str =
      context.GetStringCommonAttr("cross_ssid_info_from_redis").value_or("");
    ks::search::ziya_sort::SugImplicitFeedback cross_ssid_info;
    auto cross_ssid_info_from_redis =
      std::string(cross_ssid_info_from_redis_str.data(), cross_ssid_info_from_redis_str.size());
    std::vector<std::string> cross_prefix_list;
    std::vector<int64_t> cross_timestamp_list;
    std::vector<std::string> cross_queries_list;
    std::vector<int64_t> cross_poses_list;
    std::vector<int64_t> remain_time_list;
    if (!cross_ssid_info_from_redis_str.empty() &&
      cross_ssid_info.ParseFromString(cross_ssid_info_from_redis)) {
      uint64_t cur_timestamp = 0;
      int keep_cnt_per_sugsession = 10;
      for (int i = 0; i < cross_ssid_info.feedback_items_size(); i++) {
        if (i != cross_ssid_info.feedback_items_size() - 1) {
          cur_timestamp = cross_ssid_info.feedback_items(i + 1).timestamp();
        } else {
          cur_timestamp = ::base::GetTimestamp() / 1000;
        }
        int64 previous_timestamp = cross_ssid_info.feedback_items(i).timestamp();
        int time_diff = cur_timestamp - previous_timestamp;
        cross_prefix_list.push_back(cross_ssid_info.feedback_items(i).prefix());
        cross_timestamp_list.push_back(previous_timestamp);
        remain_time_list.push_back(time_diff);
        int sug_session_num = std::min(cross_ssid_info.feedback_items(i).query_list_size(),
          cross_ssid_info.feedback_items(i).position_list_size());
        for (int j = 0; j < keep_cnt_per_sugsession; j++) {
          if (j < sug_session_num) {
            cross_queries_list.push_back(cross_ssid_info.feedback_items(i).query_list(j));
            cross_poses_list.push_back(cross_ssid_info.feedback_items(i).position_list(j));
          } else {
            cross_queries_list.push_back("");
            cross_poses_list.push_back(-1);
          }
        }
      }
    }
    context.SetStringListCommonAttr("cross_prefix_list", std::move(cross_prefix_list));
    context.SetIntListCommonAttr("cross_timestamp_list", std::move(cross_timestamp_list));
    context.SetStringListCommonAttr("cross_queries_list", std::move(cross_queries_list));
    context.SetIntListCommonAttr("cross_poses_list", std::move(cross_poses_list));
    context.SetIntListCommonAttr("remain_time_list", std::move(remain_time_list));
    return true;
  }

  static bool SearchUnifiedRecallPerf(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    std::string perf_server_name = context.GetStringCommonAttr("perf_server_name").value_or("").data();
    int64_t perf_ab_value = context.GetIntCommonAttr("perf_ab_value").value_or(0);
    auto item_source_list_attr = context.GetStringListItemAttr("item_source_list");
    int64_t enabel_growth_recall_source_perf =
        context.GetIntCommonAttr("enabel_growth_recall_source_perf").value_or(0);

    if (enabel_growth_recall_source_perf == 0) {
      return true;
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_source_view_list = item_source_list_attr(result).value_or(std::vector<absl::string_view>());
      std::unordered_set<std::string> recall_source;
      ks::infra::PerfUtil::IntervalLogStash(1, "segrowth", "unified_recall_source_perf", perf_server_name,
                                            std::to_string(perf_ab_value), "total_recall_num");
      for (auto const &item_source_view : item_source_view_list) {
        std::string item_source(item_source_view.data());
        if (recall_source.find(item_source) == recall_source.end()) {
          ks::infra::PerfUtil::IntervalLogStash(1, "segrowth", "unified_recall_source_perf", perf_server_name,
                                                std::to_string(perf_ab_value), item_source);
          recall_source.insert(item_source);
        }
      }
    });
    return true;
  }

  static bool GetSugReferQueryAdvanceFromRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto refer_query_list_str = context.GetStringListCommonAttr("refer_query_list_str")
                               .value_or(std::vector<absl::string_view>());
    std::vector<std::string> candidate_vec;
    for (size_t i = 0; i < refer_query_list_str.size(); ++i) {
      ks::search::ziya_sort::SugPhotoKeywords photo_2_queries;
      std::string refer_query_str(refer_query_list_str.at(i));
      photo_2_queries.ParseFromString(refer_query_str);
      const std::string& queries = photo_2_queries.keywords();
      std::vector<std::string> query_vec;
      ::base::SplitString(queries, "||", &query_vec);
      for (const auto& query : query_vec) {
        std::vector<std::string> tmp_split;
        ::base::SplitString(query, ":", &tmp_split);
        if (tmp_split.size() != 2 || tmp_split[0].empty()) {
          continue;
        }
        candidate_vec.emplace_back(tmp_split[0]);
      }
    }
    context.SetStringListCommonAttr("refer_query_list_real", std::move(candidate_vec));
    return true;
  }

  static bool ParseFromSugNegativeFeedback(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto negtive_feedback_str =
      context.GetStringCommonAttr("negtive_feedback_str").value_or("");
    ::com::kuaishou::search::search::SearchFeedbackInnerMessageList sug_feedback_inner_message;
    ::com::kuaishou::search::search::NegativeSceneType negative_scene;
    int word_num = 0;
    int negative_reason_num = 0;
    std::unordered_map<uint64, int> negative_reason_to_valid_sug;
    negative_reason_to_valid_sug[14] = 1;
    negative_reason_to_valid_sug[15] = 1;
    std::unordered_map<uint64, int> negative_reason_to_valid_else;
    negative_reason_to_valid_else[13] = 1;
    std::unordered_map<uint64, int> negative_reason_to_valid;
    auto negtive_feedback =
      std::string(negtive_feedback_str.data(), negtive_feedback_str.size());
    std::vector<std::string> negative_words_list;
    if (!negtive_feedback_str.empty() &&
      sug_feedback_inner_message.ParseFromString(negtive_feedback)) {
      for (int i = 0; i < sug_feedback_inner_message.search_feedback_inner_message_size(); i++) {
        negative_scene = sug_feedback_inner_message.search_feedback_inner_message(i).negative_scene();
        if (negative_scene == ::com::kuaishou::search::search::NegativeSceneType::SUG) {
          negative_reason_to_valid = negative_reason_to_valid_sug;
        } else if (negative_scene == ::com::kuaishou::search::search::NegativeSceneType::INTERSTS ||
          negative_scene == ::com::kuaishou::search::search::NegativeSceneType::RS) {
          negative_reason_to_valid = negative_reason_to_valid_else;
        }
        word_num = sug_feedback_inner_message.search_feedback_inner_message(i).negative_words_size();
        negative_reason_num =
          sug_feedback_inner_message.search_feedback_inner_message(i).negative_reason_size();
        for (int j = 0; j < word_num; j++) {
          for  (int k = 0; k < negative_reason_num; k++) {
            auto negative_reason =
              sug_feedback_inner_message.search_feedback_inner_message(i).negative_reason(k);
            if (negative_reason_to_valid.find(negative_reason) != negative_reason_to_valid.end()) {
              negative_words_list.push_back(
                sug_feedback_inner_message.search_feedback_inner_message(i).negative_words(j));
              break;
            }
          }
        }
      }
    }
    context.SetStringListCommonAttr("negative_words_list", std::move(negative_words_list));
    return true;
  }

  static bool GetPlatformDebugHandlePtr(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::shared_ptr<::mmu::DebugLogger> ctx;
    ctx = std::make_shared<::mmu::DebugLogger>();
    context.SetPtrCommonAttr("debug_log_ptr", ctx);
    return true;
  }

  static bool Refer2QueryListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_refer_to_query_list = 0;
    auto refer_to_query_list_str =
      context.GetStringCommonAttr("refer_to_query_list_str").value_or("");
    auto refer_to_query_list_string =
      std::string(refer_to_query_list_str.data(), refer_to_query_list_str.size());
    RetrievalInfo sug_refer_to_query_list;
    std::vector<std::string> refer_to_query_list;
    if (!refer_to_query_list_str.empty() &&
      sug_refer_to_query_list.ParseFromString(refer_to_query_list_string)) {
      for (int j = 0; j < sug_refer_to_query_list.item_size(); j++) {
        auto item_id = sug_refer_to_query_list.item(j).item_id();
        // double score = sug_refer_to_query_list.item(j).score();
        refer_to_query_list.push_back(item_id);
      }
      has_refer_to_query_list = 1;
    }
    context.SetStringListCommonAttr("refer_to_query_list", std::move(refer_to_query_list));
    context.SetIntCommonAttr("has_refer_to_query_list", std::move(has_refer_to_query_list));
    return true;
  }

  static bool Refer2QueryListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    auto refer_to_query_list = context.GetStringListCommonAttr("refer_to_query_list")
                                    .value_or(std::vector<absl::string_view>());
    RetrievalInfo sug_refer_to_query_list;
    for (int i = 0; i < refer_to_query_list.size(); i++) {
      std::string refer_to_query = std::string(refer_to_query_list[i].data(), refer_to_query_list[i].size());
      auto r_item = sug_refer_to_query_list.add_item();
      r_item->set_item_id(refer_to_query);
    }
    sug_refer_to_query_list.SerializeToString(&serialized_str);
    context.SetStringCommonAttr("refer_to_query_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool Refer2NextQueryListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_refer_to_next_query_list = 0;
    auto refer_to_next_query_list_str =
      context.GetStringCommonAttr("refer_to_next_query_list_str").value_or("");
    auto refer_to_next_query_list_string =
      std::string(refer_to_next_query_list_str.data(), refer_to_next_query_list_str.size());
    RetrievalInfo sug_refer_to_next_query_list;
    std::vector<std::string> refer_to_next_query_list;
    if (!refer_to_next_query_list_str.empty() &&
      sug_refer_to_next_query_list.ParseFromString(refer_to_next_query_list_string)) {
      for (int j = 0; j < sug_refer_to_next_query_list.item_size(); j++) {
        auto item_id = sug_refer_to_next_query_list.item(j).item_id();
        refer_to_next_query_list.push_back(item_id);
      }
      has_refer_to_next_query_list = 1;
    }
    context.SetStringListCommonAttr("refer_to_next_query_list", std::move(refer_to_next_query_list));
    context.SetIntCommonAttr("has_refer_to_next_query_list", std::move(has_refer_to_next_query_list));
    return true;
  }

  static bool Refer2NextQueryListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int> refer_next_query_exist;
    std::vector<std::string> sug_refer_to_next_query_vec;
    auto refer_to_next_query_list = context.GetStringListCommonAttr("refer_to_next_query_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 p2q2q_refer2query_match_pv_threshold =
        context.GetIntCommonAttr("p2q2q_refer2query_match_pv_threshold").value_or(0);
    RetrievalInfo sug_refer_to_next_query_list;
    RetrievalInfo sug_refer_to_next_query_list_merge;
    std::vector<std::pair<std::string, double>> refer_to_next_query_score_pair_list;
    for (int i = 0; i < refer_to_next_query_list.size(); i++) {
      std::string next_query_message =
        std::string(refer_to_next_query_list[i].data(), refer_to_next_query_list[i].size());
      if (sug_refer_to_next_query_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_refer_to_next_query_list.item_size(); j++) {
          auto item_id = sug_refer_to_next_query_list.item(j).item_id();
          auto score = sug_refer_to_next_query_list.item(j).score();
          if (refer_next_query_exist.find(item_id) == refer_next_query_exist.end()) {  // 去重
            refer_next_query_exist[item_id] = score;
          } else {
            refer_next_query_exist[item_id] += score;
          }
        }
      }
    }
    for (auto &pair : refer_next_query_exist) {
      auto item_id = pair.first;
      auto score = pair.second;
      if (score > p2q2q_refer2query_match_pv_threshold) {
        refer_to_next_query_score_pair_list.push_back(std::pair<std::string, float>(item_id, score));
      }
    }
    std::sort(refer_to_next_query_score_pair_list.begin(), refer_to_next_query_score_pair_list.end(),
          [](const std::pair<std::string, float> &a, const std::pair<std::string, float> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < refer_to_next_query_score_pair_list.size(); i++) {
      auto item_id = refer_to_next_query_score_pair_list[i].first;
      sug_refer_to_next_query_vec.push_back(item_id);
      auto r_item = sug_refer_to_next_query_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_refer_to_next_query_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("refer_to_next_query_list", std::move(sug_refer_to_next_query_vec));
    context.SetStringCommonAttr("refer_to_next_query_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool P2QRecallOutputMerge(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recall_num =
        context.GetIntCommonAttr("refer2query_recall_num").value_or(0);
    std::vector<std::string> profiles_name = {"p2q_eti", "p2q_sla", "p2q_av_realtime", "p2q_nht",
    "p2q_nht_gnl", "p2q_bst", "p2q_ava_inner_v4", "p2q_sla_inner", "p2q_sli", "p2q_av_realtime_ann",
    "p2q_author_recall", "p2q_magic_recall", "p2q_music_recall", "p2q_food_recall", "p2q_game_recall",
     "p2q_refer_base_recall", "p2q_xj_epd", "p2q_mnl", "p2q_hashtag_v2", "p2q_news_gen",
     "p2q_high_sense_recall", "p2q_food_realtime", "p2q_ava_inner_v1", "p2q_eti_inner",
     "p2q_ava_inner_v3", "p2q_base_recall", "p2q_hashtag_recall", "p2q_ava_inner_ann",
     "p2q_general_generate", "p2q_hashtag_ann_recall", "p2q_gds", "p2q_goods_v2", "p2q_gds_live",
     "p2q_avi_new", "p2q_avi_inner", "entity_recall_realtime", "p2q_ext_news", "p2q_ext_news_v1",
     "p2q_gen_v1", "p2q_gen_ann", "p2q_gen_mt5", "p2q_gen_llama", "p2q_gen_mt5_test", "p2q_gen_llama_test",
     "p2q_gen_llama_rl", "p2q_gen_llama_rl_v2", "p2q_mm_ann_v1", "p2q_sli_q2q_extend_recall",
     "p2q_phrase_extend_recall", "p2q_query_extend", "p2q_blip_embedding_recall_v2",
     "p2q_blip_embedding_recall", "p2q_q2p_hi_recall", "p2q_ann_recall", "p2q_sar_active_v1",
     "blip_emb_ann_recall_realtime", "p2q_perks_ann_recall", "p2q_gen", "p2q_ava_inner_v4_filter",
     "p2q_plc_entity_recall", "p2q_perks_ann_recall_v2", "p2q_recall_exp_v1", "p2q_recall_exp_v2",
     "p2q_recall_exp_v3", "p2q_recall_exp_v4", "p2q_recall_exp_v5", "p2q_recall_exp_v6", "p2q_recall_exp_v7",
     "p2q_recall_exp_v8", "p2q_recall_exp_v9", "p2q_recall_exp_v10", "p2q_recall_exp_v11",
     "p2q_recall_exp_v12", "p2q_recall_exp_v13", "p2q_recall_exp_v14", "p2q_recall_exp_v15",
     "p2q_recall_exp_v16", "p2q_recall_exp_v17", "p2q_recall_exp_v18", "p2q_recall_exp_v19",
     "p2q_recall_exp_v20", "p2q_movie_entity_extend_recall", "p2q_vfs_realtime",
     "blip_emb_ann_recall_realtime_v2", "p2q_deepseek_gen"};
    std::vector<std::string> output_profiles_attr_list;
    for (int i = 0; i < profiles_name.size(); i++) {
      output_profiles_attr_list.push_back(profiles_name[i] + "_refer2query_queries");
    }
    std::vector<std::string> refer_to_query_list;
    std::vector<std::pair<std::string, double>> refer_to_query_score_pair_list;
    std::unordered_map<std::string, int> refer_query_exist;
    for (auto output_profiles_attr : output_profiles_attr_list) {
      auto output_profiles_attr_queries =
      context.GetStringListCommonAttr(output_profiles_attr).value_or(std::vector<absl::string_view>());
      for (int j = 0; j < output_profiles_attr_queries.size(); j++) {
        auto refer_to_query_list_message =
          std::string(output_profiles_attr_queries[j].data(), output_profiles_attr_queries[j].size());
        RetrievalInfo p2q_recall_queries;
        if (p2q_recall_queries.ParseFromString(refer_to_query_list_message)) {
          for (int j = 0; j < p2q_recall_queries.item_size(); j++) {
            auto item_id = p2q_recall_queries.item(j).item_id();
            double score = p2q_recall_queries.item(j).score();
            refer_to_query_score_pair_list.push_back(std::pair<std::string, float>(item_id, score));
          }
        }
      }
    }
    std::sort(refer_to_query_score_pair_list.begin(), refer_to_query_score_pair_list.end(),
          [](const std::pair<std::string, float> &a, const std::pair<std::string, float> &b) -> bool {
            return a.second > b.second;
          });
    int recall_num_count = 0;
    for (int i = 0; i < refer_to_query_score_pair_list.size(); i++) {
      auto item_id = refer_to_query_score_pair_list[i].first;
      auto score = refer_to_query_score_pair_list[i].second;
      if (refer_query_exist.find(item_id) == refer_query_exist.end()) {
        refer_to_query_list.push_back(item_id);
        refer_query_exist[item_id] = 1;
        recall_num_count++;
      }
      if (recall_num_count >= recall_num) {
        break;
      }
    }
    context.SetStringListCommonAttr("refer_to_query_list", std::move(refer_to_query_list));
    return true;
  }

  static bool MultiRefer2QueryListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_refer_to_query_list = 0;
    auto refer_to_query_list_str =
      context.GetStringCommonAttr("multi_refer_to_query_list_str").value_or("");
    auto refer_to_query_list_string =
      std::string(refer_to_query_list_str.data(), refer_to_query_list_str.size());
    RetrievalInfo sug_refer_to_query_list;
    std::vector<std::string> refer_to_query_list;
    if (!refer_to_query_list_str.empty() &&
      sug_refer_to_query_list.ParseFromString(refer_to_query_list_string)) {
      for (int j = 0; j < sug_refer_to_query_list.item_size(); j++) {
        auto item_id = sug_refer_to_query_list.item(j).item_id();
        // double score = sug_refer_to_query_list.item(j).score();
        refer_to_query_list.push_back(item_id);
      }
      has_refer_to_query_list = 1;
    }
    context.SetStringListCommonAttr("multi_refer_to_query_list", std::move(refer_to_query_list));
    context.SetIntCommonAttr("has_multi_refer_to_query_list", std::move(has_refer_to_query_list));
    return true;
  }

  static bool MultiP2QRecallOutputMerge(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recall_num =
        context.GetIntCommonAttr("refer2query_recall_num").value_or(0);
    std::vector<std::string> profiles_name = {"p2q_eti", "p2q_sla", "p2q_av_realtime", "p2q_nht",
    "p2q_nht_gnl", "p2q_bst", "p2q_ava_inner_v4", "p2q_sla_inner", "p2q_sli", "p2q_av_realtime_ann",
    "p2q_author_recall", "p2q_magic_recall", "p2q_music_recall", "p2q_food_recall", "p2q_game_recall",
     "p2q_refer_base_recall", "p2q_xj_epd", "p2q_mnl", "p2q_hashtag_v2", "p2q_news_gen",
     "p2q_high_sense_recall", "p2q_food_realtime", "p2q_ava_inner_v1", "p2q_eti_inner",
     "p2q_ava_inner_v3", "p2q_base_recall", "p2q_hashtag_recall", "p2q_ava_inner_ann",
     "p2q_general_generate", "p2q_hashtag_ann_recall", "p2q_gds", "p2q_goods_v2", "p2q_gds_live",
     "p2q_avi_new", "p2q_avi_inner", "entity_recall_realtime", "p2q_ext_news", "p2q_ext_news_v1",
     "p2q_gen_v1", "p2q_gen_ann", "p2q_gen_mt5", "p2q_gen_llama", "p2q_gen_mt5_test", "p2q_gen_llama_test",
     "p2q_gen_llama_rl", "p2q_gen_llama_rl_v2", "p2q_mm_ann_v1", "p2q_sli_q2q_extend_recall",
     "p2q_phrase_extend_recall", "p2q_query_extend", "p2q_blip_embedding_recall_v2",
     "p2q_blip_embedding_recall", "p2q_q2p_hi_recall", "p2q_ann_recall", "p2q_sar_active_v1",
     "blip_emb_ann_recall_realtime", "p2q_perks_ann_recall", "p2q_gen", "p2q_ava_inner_v4_filter",
     "p2q_plc_entity_recall", "p2q_perks_ann_recall_v2", "p2q_recall_exp_v1", "p2q_recall_exp_v2",
     "p2q_recall_exp_v3", "p2q_recall_exp_v4", "p2q_recall_exp_v5", "p2q_recall_exp_v6", "p2q_recall_exp_v7",
     "p2q_recall_exp_v8", "p2q_recall_exp_v9", "p2q_recall_exp_v10", "p2q_recall_exp_v11",
     "p2q_recall_exp_v12", "p2q_recall_exp_v13", "p2q_recall_exp_v14", "p2q_recall_exp_v15",
     "p2q_recall_exp_v16", "p2q_recall_exp_v17", "p2q_recall_exp_v18", "p2q_recall_exp_v19",
     "p2q_recall_exp_v20", "p2q_movie_entity_extend_recall", "p2q_vfs_realtime",
     "blip_emb_ann_recall_realtime_v2", "p2q_deepseek_gen"};
    std::vector<std::string> output_profiles_attr_list;
    for (int i = 0; i < profiles_name.size(); i++) {
      output_profiles_attr_list.push_back(profiles_name[i] + "_multirefer2query_queries");
    }
    std::vector<std::string> refer_to_query_list;
    std::vector<std::pair<std::string, double>> refer_to_query_score_pair_list;
    std::unordered_map<std::string, int> refer_query_exist;
    for (auto output_profiles_attr : output_profiles_attr_list) {
      auto output_profiles_attr_queries =
      context.GetStringListCommonAttr(output_profiles_attr).value_or(std::vector<absl::string_view>());
      for (int j = 0; j < output_profiles_attr_queries.size(); j++) {
        auto refer_to_query_list_message =
          std::string(output_profiles_attr_queries[j].data(), output_profiles_attr_queries[j].size());
        RetrievalInfo p2q_recall_queries;
        if (p2q_recall_queries.ParseFromString(refer_to_query_list_message)) {
          for (int j = 0; j < p2q_recall_queries.item_size(); j++) {
            auto item_id = p2q_recall_queries.item(j).item_id();
            double score = p2q_recall_queries.item(j).score();
            refer_to_query_score_pair_list.push_back(std::pair<std::string, float>(item_id, score));
          }
        }
      }
    }
    std::sort(refer_to_query_score_pair_list.begin(), refer_to_query_score_pair_list.end(),
          [](const std::pair<std::string, float> &a, const std::pair<std::string, float> &b) -> bool {
            return a.second > b.second;
          });
    int recall_num_count = 0;
    for (int i = 0; i < refer_to_query_score_pair_list.size(); i++) {
      auto item_id = refer_to_query_score_pair_list[i].first;
      auto score = refer_to_query_score_pair_list[i].second;
      if (refer_query_exist.find(item_id) == refer_query_exist.end()) {
        refer_to_query_list.push_back(item_id);
        refer_query_exist[item_id] = 1;
        recall_num_count++;
      }
      if (recall_num_count >= recall_num) {
        break;
      }
    }
    context.SetStringListCommonAttr("multi_refer_to_query_list", std::move(refer_to_query_list));
    return true;
  }

  static bool MultiRefer2QueryListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    auto refer_to_query_list = context.GetStringListCommonAttr("multi_refer_to_query_list")
                                    .value_or(std::vector<absl::string_view>());
    RetrievalInfo sug_refer_to_query_list;
    for (int i = 0; i < refer_to_query_list.size(); i++) {
      std::string refer_to_query = std::string(refer_to_query_list[i].data(), refer_to_query_list[i].size());
      auto r_item = sug_refer_to_query_list.add_item();
      r_item->set_item_id(refer_to_query);
    }
    sug_refer_to_query_list.SerializeToString(&serialized_str);
    context.SetStringCommonAttr("multi_refer_to_query_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool SugMultiRefer2Query(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto sug_cross_session_id_attr = context.GetStringCommonAttr("sug_cross_session_id").value_or("");
    std::string sug_cross_session_id =
        std::string(sug_cross_session_id_attr.data(), sug_cross_session_id_attr.size());
    auto referVideoId_attr =
        context.GetStringCommonAttr("referVideoId").value_or("");
    std::string referVideoId = std::string(referVideoId_attr.data(), referVideoId_attr.size());
    auto client_action_list_pids =
        context.GetIntListCommonAttr("client_action_list_pids").value_or(absl::Span<const int64>());
    auto reco_click_list_photo_id =
        context.GetIntListCommonAttr("reco_click_list_photo_id").value_or(absl::Span<const int64>());
    auto sug_refer_add_client_num = context.GetIntCommonAttr("sug_refer_add_client_num").value_or(0);
    std::vector<int64_t> referVideoId_list;
    std::unordered_map<int64_t, int> refer_video_id_exist;
    int count = 0;
    int64_t vid = 0;
    if (!referVideoId.empty()) {
      ::base::StringToInt64(referVideoId, &vid);
      if (refer_video_id_exist.find(vid) == refer_video_id_exist.end()) {
        referVideoId_list.push_back(vid);
        refer_video_id_exist[vid] = 1;
        count += 1;
      }
    }
    if (count < sug_refer_add_client_num && !client_action_list_pids.empty() &&
      client_action_list_pids.size() > 0) {
      int client_action_list_pids_size = client_action_list_pids.size();
      for (int i = 0; i < client_action_list_pids_size && count < sug_refer_add_client_num; i++) {
        int idx = client_action_list_pids_size - i - 1;  // 倒序
        vid = client_action_list_pids[idx];
        if (refer_video_id_exist.find(vid) == refer_video_id_exist.end()) {
          referVideoId_list.push_back(vid);
          refer_video_id_exist[vid] = 1;
          count += 1;
        }
      }
    }
    if (count < sug_refer_add_client_num && !reco_click_list_photo_id.empty() &&
      reco_click_list_photo_id.size() > 0) {
      int reco_click_list_photo_id_size = reco_click_list_photo_id.size();
      for (int i = 0; i < reco_click_list_photo_id_size && count < sug_refer_add_client_num; i++) {
        vid = reco_click_list_photo_id[i];  // 正序
        if (refer_video_id_exist.find(vid) == refer_video_id_exist.end()) {
          referVideoId_list.push_back(vid);
          refer_video_id_exist[vid] = 1;
          count += 1;
        }
      }
    }
    std::string refer_to_query_key = "client_to_query_" + sug_cross_session_id;
    context.SetIntListCommonAttr("multi_referVideoId_list", std::move(referVideoId_list));
    context.SetStringCommonAttr("multi_refer_to_query_key", std::move(refer_to_query_key));
    return true;
  }

  static bool His2NextQueryListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_his_to_next_query_list = 0;
    auto his_to_next_query_list_str =
      context.GetStringCommonAttr("his_to_next_query_list_str").value_or("");
    auto his_to_next_query_list_string =
      std::string(his_to_next_query_list_str.data(), his_to_next_query_list_str.size());
    RetrievalInfo sug_his_to_next_query_list;
    std::vector<std::string> his_to_next_query_list;
    if (!his_to_next_query_list_str.empty() &&
      sug_his_to_next_query_list.ParseFromString(his_to_next_query_list_string)) {
      for (int j = 0; j < sug_his_to_next_query_list.item_size(); j++) {
        auto item_id = sug_his_to_next_query_list.item(j).item_id();
        his_to_next_query_list.push_back(item_id);
      }
      has_his_to_next_query_list = 1;
    }
    context.SetStringListCommonAttr("his_to_next_query_list", std::move(his_to_next_query_list));
    context.SetIntCommonAttr("has_his_to_next_query_list", std::move(has_his_to_next_query_list));
    return true;
  }

  static bool His2NextQueryListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int64> his_next_query_exist;
    std::vector<std::string> sug_his_to_next_query_vec;
    auto his_to_next_query_list = context.GetStringListCommonAttr("his_to_next_query_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2query_match_pv_threshold =
          context.GetIntCommonAttr("his2query_match_pv_threshold").value_or(0);
    double his2query_match_max_weight =
           context.GetDoubleCommonAttr("his2query_match_max_weight").value_or(1.0);
    RetrievalInfo sug_his_to_next_query_list;
    RetrievalInfo sug_his_to_next_query_list_merge;
    std::vector<std::pair<std::string, int64>> his_to_next_query_score_pair_list;
    int weight = his_to_next_query_list.size()-1;  // his2query_match_max_weight;
    for (int i = 0; i < his_to_next_query_list.size(); i++) {  // 历史词数量
      std::string next_query_message =
        std::string(his_to_next_query_list[i].data(), his_to_next_query_list[i].size());
      if (sug_his_to_next_query_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_his_to_next_query_list.item_size(); j++) {  // 历史词_前缀的关联词数量
          auto item_id = sug_his_to_next_query_list.item(j).item_id();
          if (sug_his_to_next_query_list.item(j).score() > his2query_match_pv_threshold) {
            int64 score = sug_his_to_next_query_list.item(j).score() *
                        std::pow(his2query_match_max_weight, weight);
            if (his_next_query_exist.find(item_id) == his_next_query_exist.end()) {  // 去重
              his_next_query_exist[item_id] = score;
            } else {
              his_next_query_exist[item_id] += score;
            }
          }
        }
      }
      weight--;
    }
    for (auto &pair : his_next_query_exist) {
      auto item_id = pair.first;
      int64 score = pair.second;
      his_to_next_query_score_pair_list.push_back(std::pair<std::string, int64>(item_id, score));
    }
    std::sort(his_to_next_query_score_pair_list.begin(), his_to_next_query_score_pair_list.end(),
          [](const std::pair<std::string, int64> &a, const std::pair<std::string, int64> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < his_to_next_query_score_pair_list.size(); i++) {
      auto item_id = his_to_next_query_score_pair_list[i].first;
      sug_his_to_next_query_vec.push_back(item_id);
      auto r_item = sug_his_to_next_query_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_his_to_next_query_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("his_to_next_query_list", std::move(sug_his_to_next_query_vec));
    context.SetStringCommonAttr("his_to_next_query_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool SugLongSearchedQueryList(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto searched_query_list = context.GetStringListCommonAttr("searched_query_list")
                                    .value_or(std::vector<absl::string_view>());
    auto searched_query_timestamp_list = context.GetIntListCommonAttr("searched_query_timestamp_list")
                                    .value_or(absl::Span<const int64>());
    auto his2nextquery_recall_trigger_num =
        context.GetIntCommonAttr("his2nextquery_recall_trigger_num").value_or(0);
    int64 his2nextquery_time_threshold_min =
        context.GetIntCommonAttr("his2nextquery_time_threshold_min").value_or(0);
    auto his2hashtag_trigger_num =
        context.GetIntCommonAttr("his2hashtag_trigger_num").value_or(0);
    int64 his2hashtag_time_threshold_min =
        context.GetIntCommonAttr("his2hashtag_time_threshold_min").value_or(0);
    auto his2hashtag2hashtag_trigger_num =
        context.GetIntCommonAttr("his2hashtag2hashtag_trigger_num").value_or(0);
    int64 his2hashtag2hashtag_time_threshold_min =
        context.GetIntCommonAttr("his2hashtag2hashtag_time_threshold_min").value_or(0);
    std::unordered_map<std::string, int> his_query_exist;
    std::unordered_map<std::string, int> his_tag_exist;
    std::unordered_map<std::string, int> his_tag2tag_exist;
    std::vector<std::string> his_searched_query_list;
    std::vector<std::string> his_searched_query_list_his2tag;
    std::vector<std::string> his_searched_query_list_his2tag2tag;
    int64 cur_timestamp_ms = base::GetTimestamp() / 1000;
    for (int i = 0; i < searched_query_list.size() && i < searched_query_timestamp_list.size(); i++) {
      auto time_interval = (cur_timestamp_ms - searched_query_timestamp_list[i]) / 60000;  // 分钟间隔
      auto item_id = std::string(searched_query_list[i].data(), searched_query_list[i].size());
      if ((his2nextquery_recall_trigger_num == 0 ||
        his_searched_query_list.size() < his2nextquery_recall_trigger_num) &&
        (his2nextquery_time_threshold_min == 0 || time_interval <= his2nextquery_time_threshold_min) &&
        his_query_exist.find(item_id) == his_query_exist.end()) {
          his_searched_query_list.push_back(item_id);
          his_query_exist[item_id] = 1;
      }
      if ((his2hashtag_trigger_num == 0 ||
        his_searched_query_list_his2tag.size() < his2hashtag_trigger_num) &&
        (his2hashtag_time_threshold_min == 0 || time_interval <= his2hashtag_time_threshold_min) &&
        his_tag_exist.find(item_id) == his_tag_exist.end()) {
          his_searched_query_list_his2tag.push_back(item_id);
          his_tag_exist[item_id] = 1;
      }
      if ((his2hashtag2hashtag_trigger_num == 0 ||
        his_searched_query_list_his2tag2tag.size() < his2hashtag2hashtag_trigger_num) &&
        (his2hashtag2hashtag_time_threshold_min == 0 ||
        time_interval <= his2hashtag2hashtag_time_threshold_min) &&
        his_tag2tag_exist.find(item_id) == his_tag2tag_exist.end()) {
          his_searched_query_list_his2tag2tag.push_back(item_id);
          his_tag2tag_exist[item_id] = 1;
      }
    }
    context.SetStringListCommonAttr("his_searched_query_list", std::move(his_searched_query_list));
    context.SetStringListCommonAttr("his_searched_query_list_his2tag",
                                    std::move(his_searched_query_list_his2tag));
    context.SetStringListCommonAttr("his_searched_query_list_his2tag2tag",
                                    std::move(his_searched_query_list_his2tag2tag));
    return true;
  }

  static bool SugHisPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto recall_name = context.GetStringCommonAttr("recall_name").value_or("");
    auto his_to_next_query_list = context.GetStringListCommonAttr("his_to_next_query_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2nextquery_recall_num = context.GetIntCommonAttr("his2nextquery_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_his2nextquery_list_exist;
    std::vector<std::string> prefix_match_his2nextquery_list;
    std::vector<std::string> prefix_match_his2nextquery_list_py;
    std::vector<int64_t> prefix_match_his2nextquery_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < his_to_next_query_list.size(); i++) {
      if (prefix_match_his2nextquery_list.size() >= his2nextquery_recall_num) break;
      auto item_id = std::string(his_to_next_query_list[i].data(), his_to_next_query_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_his2nextquery_list_exist.find(item_id) ==
          prefix_match_his2nextquery_list_exist.end()) {
        std::string key = item_id + std::string(recall_name.data(), recall_name.size());
        prefix_match_his2nextquery_list.push_back(item_id);
        prefix_match_his2nextquery_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_his2nextquery_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_his2nextquery_list.clear();
      prefix_match_his2nextquery_list_key.clear();
      prefix_match_his2nextquery_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec,
                          prefix_match_his2nextquery_list_py, chinese_end_pos, his2nextquery_recall_num);
      for (int i = 0; i < prefix_match_his2nextquery_list_py.size(); i++) {
        auto item_id = prefix_match_his2nextquery_list_py[i];
        if (prefix_match_his2nextquery_list_exist.find(item_id) ==
            prefix_match_his2nextquery_list_exist.end()) {
          std::string key = item_id + std::string(recall_name.data(), recall_name.size());
          prefix_match_his2nextquery_list.push_back(item_id);
          prefix_match_his2nextquery_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_his2nextquery_list_exist[item_id] = 1;
        }
      }
    }

    context.SetStringListCommonAttr("prefix_match_his2nextquery_list",
                                    std::move(prefix_match_his2nextquery_list));
    context.SetIntListCommonAttr("prefix_match_his2nextquery_list_key",
                                 std::move(prefix_match_his2nextquery_list_key));
    return true;
  }

  static bool SugRefer2QueryPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto recall_name = context.GetStringCommonAttr("p2q_recall_name").value_or("");
    auto multi_refer_to_query_list = context.GetStringListCommonAttr("multi_refer_to_query_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 refer2query_match_recall_num = context.GetIntCommonAttr("refer2query_match_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_p2q_query_list_exist;
    std::vector<std::string> prefix_match_p2q_query_list;
    std::vector<std::string> prefix_match_p2q_query_list_py;
    std::vector<int64_t> prefix_match_p2q_query_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < multi_refer_to_query_list.size(); i++) {
      if (prefix_match_p2q_query_list.size() >= refer2query_match_recall_num) break;
      auto item_id = std::string(multi_refer_to_query_list[i].data(), multi_refer_to_query_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_p2q_query_list_exist.find(item_id) ==
          prefix_match_p2q_query_list_exist.end()) {
        std::string key = item_id + std::string(recall_name.data(), recall_name.size());
        prefix_match_p2q_query_list.push_back(item_id);
        prefix_match_p2q_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_p2q_query_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_p2q_query_list.clear();
      prefix_match_p2q_query_list_key.clear();
      prefix_match_p2q_query_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec, prefix_match_p2q_query_list_py,
                                                        chinese_end_pos, refer2query_match_recall_num);
      for (int i = 0; i < prefix_match_p2q_query_list_py.size(); i++) {
        auto item_id = prefix_match_p2q_query_list_py[i];
        if (prefix_match_p2q_query_list_exist.find(item_id) == prefix_match_p2q_query_list_exist.end()) {
          std::string key = item_id + std::string(recall_name.data(), recall_name.size());
          prefix_match_p2q_query_list.push_back(item_id);
          prefix_match_p2q_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_p2q_query_list_exist[item_id] = 1;
        }
      }
    }
    context.SetStringListCommonAttr("prefix_match_p2q_query_list", std::move(prefix_match_p2q_query_list));
    context.SetIntListCommonAttr("prefix_match_p2q_query_list_key",
                                 std::move(prefix_match_p2q_query_list_key));
    return true;
  }

    static bool SugRefer2Query2QueryPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto recall_name = context.GetStringCommonAttr("p2q2q_recall_name").value_or("");
    auto refer_to_next_query_list = context.GetStringListCommonAttr("refer_to_next_query_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 p2q2q_refer2query_match_recall_num =
        context.GetIntCommonAttr("p2q2q_refer2query_match_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_p2q2q_query_list_exist;
    std::vector<std::string> prefix_match_p2q2q_query_list;
    std::vector<std::string> prefix_match_p2q2q_query_list_py;
    std::vector<int64_t> prefix_match_p2q2q_query_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < refer_to_next_query_list.size(); i++) {
      if (prefix_match_p2q2q_query_list.size() >= p2q2q_refer2query_match_recall_num) break;
      auto item_id = std::string(refer_to_next_query_list[i].data(), refer_to_next_query_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_p2q2q_query_list_exist.find(item_id) ==
          prefix_match_p2q2q_query_list_exist.end()) {
        std::string key = item_id + std::string(recall_name.data(), recall_name.size());
        prefix_match_p2q2q_query_list.push_back(item_id);
        prefix_match_p2q2q_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_p2q2q_query_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_p2q2q_query_list.clear();
      prefix_match_p2q2q_query_list_key.clear();
      prefix_match_p2q2q_query_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec, prefix_match_p2q2q_query_list_py,
                                                        chinese_end_pos, p2q2q_refer2query_match_recall_num);
      for (int i = 0; i < prefix_match_p2q2q_query_list_py.size(); i++) {
        auto item_id = prefix_match_p2q2q_query_list_py[i];
        if (prefix_match_p2q2q_query_list_exist.find(item_id) == prefix_match_p2q2q_query_list_exist.end()) {
          std::string key = item_id + std::string(recall_name.data(), recall_name.size());
          prefix_match_p2q2q_query_list.push_back(item_id);
          prefix_match_p2q2q_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_p2q2q_query_list_exist[item_id] = 1;
        }
      }
    }
    context.SetStringListCommonAttr("prefix_match_p2q2q_query_list",
                                    std::move(prefix_match_p2q2q_query_list));
    context.SetIntListCommonAttr("prefix_match_p2q2q_query_list_key",
                                 std::move(prefix_match_p2q2q_query_list_key));
    return true;
  }

  static bool Refer2NextHashtagListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_refer_to_next_hashtag_list = 0;
    auto refer_to_next_hashtag_list_str =
      context.GetStringCommonAttr("refer_to_next_hashtag_list_str").value_or("");
    auto refer_to_next_hashtag_list_string =
      std::string(refer_to_next_hashtag_list_str.data(), refer_to_next_hashtag_list_str.size());
    RetrievalInfo sug_refer_to_next_hashtag_list;
    std::vector<std::string> refer_to_next_hashtag_list;
    if (!refer_to_next_hashtag_list_str.empty() &&
      sug_refer_to_next_hashtag_list.ParseFromString(refer_to_next_hashtag_list_string)) {
      for (int j = 0; j < sug_refer_to_next_hashtag_list.item_size(); j++) {
        auto item_id = sug_refer_to_next_hashtag_list.item(j).item_id();
        refer_to_next_hashtag_list.push_back(item_id);
      }
      has_refer_to_next_hashtag_list = 1;
    }
    context.SetStringListCommonAttr("refer_to_next_hashtag_list", std::move(refer_to_next_hashtag_list));
    context.SetIntCommonAttr("has_refer_to_next_hashtag_list", std::move(has_refer_to_next_hashtag_list));
    return true;
  }

  static bool Refer2NextHashtagListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int> refer_next_hashtag_exist;
    std::vector<std::string> sug_refer_to_next_hashtag_vec;
    auto refer_to_next_hashtag_list = context.GetStringListCommonAttr("refer_to_next_hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    auto multi_refer_to_hashtag_list = context.GetStringListCommonAttr("multi_refer_to_hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 p2q2hashtag_refer2query_match_pv_threshold =
        context.GetIntCommonAttr("p2q2hashtag_refer2query_match_pv_threshold").value_or(0);
    RetrievalInfo sug_refer_to_next_hashtag_list;
    RetrievalInfo sug_refer_to_next_hashtag_list_merge;
    std::vector<std::pair<std::string, double>> refer_to_next_hashtag_score_pair_list;
    double max_score = 0.0;
    for (int i = 0; i < refer_to_next_hashtag_list.size(); i++) {
      std::string next_hashtag_message =
        std::string(refer_to_next_hashtag_list[i].data(), refer_to_next_hashtag_list[i].size());
      if (sug_refer_to_next_hashtag_list.ParseFromString(next_hashtag_message)) {
        for (int j = 0; j < sug_refer_to_next_hashtag_list.item_size(); j++) {
          auto item_id = sug_refer_to_next_hashtag_list.item(j).item_id();
          auto score = sug_refer_to_next_hashtag_list.item(j).score();
          if (refer_next_hashtag_exist.find(item_id) == refer_next_hashtag_exist.end()) {  // 去重
            refer_next_hashtag_exist[item_id] = score;
          } else {
            refer_next_hashtag_exist[item_id] += score;
          }
          if (max_score < refer_next_hashtag_exist[item_id]) {
            max_score = refer_next_hashtag_exist[item_id];
          }
        }
      }
    }
    // 保证 multi_refer_to_hashtag_list 优先级最高
    max_score = max_score + multi_refer_to_hashtag_list.size();
    for (int i = 0; i < multi_refer_to_hashtag_list.size(); i++) {
      std::string item_id =
        std::string(multi_refer_to_hashtag_list[i].data(), multi_refer_to_hashtag_list[i].size());
      refer_next_hashtag_exist[item_id] = max_score;  // 去重，且只以 p2hash 的顺序为准
      max_score = max_score - 1;
    }
    for (auto &pair : refer_next_hashtag_exist) {
      auto item_id = pair.first;
      auto score = pair.second;
      if (score > p2q2hashtag_refer2query_match_pv_threshold) {
        refer_to_next_hashtag_score_pair_list.push_back(std::pair<std::string, float>(item_id, score));
      }
    }
    std::sort(refer_to_next_hashtag_score_pair_list.begin(), refer_to_next_hashtag_score_pair_list.end(),
          [](const std::pair<std::string, float> &a, const std::pair<std::string, float> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < refer_to_next_hashtag_score_pair_list.size(); i++) {
      auto item_id = refer_to_next_hashtag_score_pair_list[i].first;
      sug_refer_to_next_hashtag_vec.push_back(item_id);
      auto r_item = sug_refer_to_next_hashtag_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_refer_to_next_hashtag_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("refer_to_next_hashtag_list", std::move(sug_refer_to_next_hashtag_vec));
    context.SetStringCommonAttr("refer_to_next_hashtag_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool SugRefer2Query2HashtagPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto recall_name = context.GetStringCommonAttr("p2q2hashtag_recall_name").value_or("");
    auto refer_to_next_hashtag_list = context.GetStringListCommonAttr("refer_to_next_hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 p2q2hashtag_refer2query_match_recall_num =
        context.GetIntCommonAttr("p2q2hashtag_refer2query_match_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_p2q2hashtag_query_list_exist;
    std::vector<std::string> prefix_match_p2q2hashtag_query_list;
    std::vector<std::string> prefix_match_p2q2hashtag_query_list_py;
    std::vector<int64_t> prefix_match_p2q2hashtag_query_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < refer_to_next_hashtag_list.size(); i++) {
      if (prefix_match_p2q2hashtag_query_list.size() >= p2q2hashtag_refer2query_match_recall_num) break;
      auto item_id = std::string(refer_to_next_hashtag_list[i].data(), refer_to_next_hashtag_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_p2q2hashtag_query_list_exist.find(item_id) ==
          prefix_match_p2q2hashtag_query_list_exist.end()) {
        std::string key = item_id + std::string(recall_name.data(), recall_name.size());
        prefix_match_p2q2hashtag_query_list.push_back(item_id);
        prefix_match_p2q2hashtag_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_p2q2hashtag_query_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_p2q2hashtag_query_list.clear();
      prefix_match_p2q2hashtag_query_list_key.clear();
      prefix_match_p2q2hashtag_query_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec,
                                                      prefix_match_p2q2hashtag_query_list_py,
                                                      chinese_end_pos,
                                                      p2q2hashtag_refer2query_match_recall_num);
      for (int i = 0; i < prefix_match_p2q2hashtag_query_list_py.size(); i++) {
        auto item_id = prefix_match_p2q2hashtag_query_list_py[i];
        if (prefix_match_p2q2hashtag_query_list_exist.find(item_id) ==
            prefix_match_p2q2hashtag_query_list_exist.end()) {
          std::string key = item_id + std::string(recall_name.data(), recall_name.size());
          prefix_match_p2q2hashtag_query_list.push_back(item_id);
          prefix_match_p2q2hashtag_query_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_p2q2hashtag_query_list_exist[item_id] = 1;
        }
      }
    }
    context.SetStringListCommonAttr("prefix_match_p2q2hashtag_query_list",
                                    std::move(prefix_match_p2q2hashtag_query_list));
    context.SetIntListCommonAttr("prefix_match_p2q2hashtag_query_list_key",
                                 std::move(prefix_match_p2q2hashtag_query_list_key));
    return true;
  }

  static bool His2HashtagListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_his2hashtag_list = 0;
    auto his2hashtag_list_str =
      context.GetStringCommonAttr("his2hashtag_list_str").value_or("");
    auto his2hashtag_list_string =
      std::string(his2hashtag_list_str.data(), his2hashtag_list_str.size());
    RetrievalInfo sug_his2hashtag_list;
    std::vector<std::string> his2hashtag_list;
    if (!his2hashtag_list_str.empty() &&
      sug_his2hashtag_list.ParseFromString(his2hashtag_list_string)) {
      for (int j = 0; j < sug_his2hashtag_list.item_size(); j++) {
        auto item_id = sug_his2hashtag_list.item(j).item_id();
        his2hashtag_list.push_back(item_id);
      }
      has_his2hashtag_list = 1;
    }
    context.SetStringListCommonAttr("his2hashtag_list", std::move(his2hashtag_list));
    context.SetIntCommonAttr("has_his2hashtag_list", std::move(has_his2hashtag_list));
    return true;
  }

  static bool His2HashtagListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int64> his2hashtag_exist;
    std::vector<std::string> sug_his2hashtag_vec;
    auto his2hashtag_list = context.GetStringListCommonAttr("his2hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2hashtag_match_pv_threshold =
          context.GetIntCommonAttr("his2hashtag_match_pv_threshold").value_or(0);
    double his2hashtag_match_max_weight =
           context.GetDoubleCommonAttr("his2hashtag_match_max_weight").value_or(1.0);
    RetrievalInfo sug_his2hashtag_list;
    RetrievalInfo sug_his2hashtag_list_merge;
    std::vector<std::pair<std::string, int64>> his2hashtag_score_pair_list;
    int weight = his2hashtag_list.size() - 1;  // his2hashtag_match_max_weight;
    for (int i = 0; i < his2hashtag_list.size(); i++) {
      std::string next_query_message =
        std::string(his2hashtag_list[i].data(), his2hashtag_list[i].size());
      if (sug_his2hashtag_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_his2hashtag_list.item_size(); j++) {
          auto item_id = sug_his2hashtag_list.item(j).item_id();
          if (sug_his2hashtag_list.item(j).score() > his2hashtag_match_pv_threshold) {
            int64 score = sug_his2hashtag_list.item(j).score() *
                        std::pow(his2hashtag_match_max_weight, weight);
            if (his2hashtag_exist.find(item_id) == his2hashtag_exist.end()) {  // 去重
              his2hashtag_exist[item_id] = score;
            } else {
              his2hashtag_exist[item_id] += score;
            }
          }
        }
      }
      weight--;
    }
    for (auto &pair : his2hashtag_exist) {
      auto item_id = pair.first;
      int64 score = pair.second;
      his2hashtag_score_pair_list.push_back(std::pair<std::string, int64>(item_id, score));
    }
    std::sort(his2hashtag_score_pair_list.begin(), his2hashtag_score_pair_list.end(),
          [](const std::pair<std::string, int64> &a, const std::pair<std::string, int64> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < his2hashtag_score_pair_list.size(); i++) {
      auto item_id = his2hashtag_score_pair_list[i].first;
      sug_his2hashtag_vec.push_back(item_id);
      auto r_item = sug_his2hashtag_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_his2hashtag_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("his2hashtag_list", std::move(sug_his2hashtag_vec));
    context.SetStringCommonAttr("his2hashtag_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool SugHis2HashtagPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto his2hashtag_recall_name = context.GetStringCommonAttr("his2hashtag_recall_name").value_or("");
    auto his2hashtag_list = context.GetStringListCommonAttr("his2hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2hashtag_recall_num = context.GetIntCommonAttr("his2hashtag_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_his2hashtag_list_exist;
    std::vector<std::string> prefix_match_his2hashtag_list;
    std::vector<std::string> prefix_match_his2hashtag_list_py;
    std::vector<int64_t> prefix_match_his2hashtag_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < his2hashtag_list.size(); i++) {
      if (prefix_match_his2hashtag_list.size() >= his2hashtag_recall_num) break;
      auto item_id = std::string(his2hashtag_list[i].data(), his2hashtag_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_his2hashtag_list_exist.find(item_id) ==
          prefix_match_his2hashtag_list_exist.end()) {
        std::string key = item_id + std::string(his2hashtag_recall_name.data(),
                                                his2hashtag_recall_name.size());
        prefix_match_his2hashtag_list.push_back(item_id);
        prefix_match_his2hashtag_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_his2hashtag_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_his2hashtag_list.clear();
      prefix_match_his2hashtag_list_key.clear();
      prefix_match_his2hashtag_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec,
                          prefix_match_his2hashtag_list_py, chinese_end_pos, his2hashtag_recall_num);
      for (int i = 0; i < prefix_match_his2hashtag_list_py.size(); i++) {
        auto item_id = prefix_match_his2hashtag_list_py[i];
        if (prefix_match_his2hashtag_list_exist.find(item_id) ==
            prefix_match_his2hashtag_list_exist.end()) {
          std::string key = item_id + std::string(his2hashtag_recall_name.data(),
                                                  his2hashtag_recall_name.size());
          prefix_match_his2hashtag_list.push_back(item_id);
          prefix_match_his2hashtag_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_his2hashtag_list_exist[item_id] = 1;
        }
      }
    }

    context.SetStringListCommonAttr("prefix_match_his2hashtag_list",
                                    std::move(prefix_match_his2hashtag_list));
    context.SetIntListCommonAttr("prefix_match_his2hashtag_list_key",
                                 std::move(prefix_match_his2hashtag_list_key));
    return true;
  }

  static bool His2Hashtag2HashtagListParseString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    int has_his2hashtag2hashtag_list = 0;
    auto his2hashtag2hashtag_list_str =
      context.GetStringCommonAttr("his2hashtag2hashtag_list_str").value_or("");
    auto his2hashtag2hashtag_list_string =
      std::string(his2hashtag2hashtag_list_str.data(), his2hashtag2hashtag_list_str.size());
    RetrievalInfo sug_his2hashtag2hashtag_list;
    std::vector<std::string> his2hashtag2hashtag_list;
    if (!his2hashtag2hashtag_list_str.empty() &&
      sug_his2hashtag2hashtag_list.ParseFromString(his2hashtag2hashtag_list_string)) {
      for (int j = 0; j < sug_his2hashtag2hashtag_list.item_size(); j++) {
        auto item_id = sug_his2hashtag2hashtag_list.item(j).item_id();
        his2hashtag2hashtag_list.push_back(item_id);
      }
      has_his2hashtag2hashtag_list = 1;
    }
    context.SetStringListCommonAttr("his2hashtag2hashtag_list", std::move(his2hashtag2hashtag_list));
    context.SetIntCommonAttr("has_his2hashtag2hashtag_list", std::move(has_his2hashtag2hashtag_list));
    return true;
  }

  static bool His2TopkHashtagMerge(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int64> his2top_hashtag_exist;
    std::vector<std::string> sug_his2top_hashtag_vec;
    auto his2top_hashtag_list = context.GetStringListCommonAttr("his2top_hashtag_list_str")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2top_hashtag_match_pv_threshold =
          context.GetIntCommonAttr("his2top_hashtag_match_pv_threshold").value_or(0);
    double his2top_hashtag_match_max_weight =
           context.GetDoubleCommonAttr("his2top_hashtag_match_max_weight").value_or(1.0);
    int64 his2top_hashtag_num = context.GetIntCommonAttr("his2top_hashtag_num").value_or(0);
    RetrievalInfo sug_his2top_hashtag_list;
    RetrievalInfo sug_his2top_hashtag_list_merge;
    std::vector<std::pair<std::string, int64>> his2top_hashtag_score_pair_list;
    int weight = his2top_hashtag_list.size() - 1;  // his2top_hashtag_match_max_weight;
    for (int i = 0; i < his2top_hashtag_list.size(); i++) {  // 历史词数量
      std::string next_query_message =
        std::string(his2top_hashtag_list[i].data(), his2top_hashtag_list[i].size());
      if (sug_his2top_hashtag_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_his2top_hashtag_list.item_size(); j++) {  // 每个历史词的 topk 个 hashtag
          auto item_id = sug_his2top_hashtag_list.item(j).item_id();
          if (sug_his2top_hashtag_list.item(j).score() > his2top_hashtag_match_pv_threshold) {
            int64 score = sug_his2top_hashtag_list.item(j).score() *
                        std::pow(his2top_hashtag_match_max_weight, weight);
            if (his2top_hashtag_exist.find(item_id) == his2top_hashtag_exist.end()) {  // 去重
              his2top_hashtag_exist[item_id] = score;
            } else {
              his2top_hashtag_exist[item_id] += score;
            }
          }
        }
      }
      weight--;
    }
    for (auto &pair : his2top_hashtag_exist) {
      auto item_id = pair.first;
      auto score = pair.second;
      his2top_hashtag_score_pair_list.push_back(std::pair<std::string, int64>(item_id, score));
    }
    // 每个历史词内部的 100 个已经按照 pv*weight 排序
    std::sort(his2top_hashtag_score_pair_list.begin(), his2top_hashtag_score_pair_list.end(),
          [](const std::pair<std::string, int64> &a, const std::pair<std::string, int64> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < his2top_hashtag_score_pair_list.size() && i < his2top_hashtag_num; i++) {
      auto item_id = his2top_hashtag_score_pair_list[i].first;
      sug_his2top_hashtag_vec.push_back(item_id);
      auto r_item = sug_his2top_hashtag_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_his2top_hashtag_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("his2top_hashtag_list", std::move(sug_his2top_hashtag_vec));
    return true;
  }

  static bool His2Hashtag2HashtagListWriteToRedis(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    std::string serialized_str;
    std::unordered_map<std::string, int64> his2hashtag2hashtag_exist;
    std::vector<std::string> sug_his2hashtag2hashtag_vec;
    auto his2hashtag2hashtag_list = context.GetStringListCommonAttr("his2hashtag2hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2hashtag2hashtag_match_pv_threshold =
          context.GetIntCommonAttr("his2hashtag2hashtag_match_pv_threshold").value_or(0);
    double his2hashtag2hashtag_match_max_weight =
           context.GetDoubleCommonAttr("his2hashtag2hashtag_match_max_weight").value_or(1.0);
    RetrievalInfo sug_his2hashtag2hashtag_list;
    RetrievalInfo sug_his2hashtag2hashtag_list_merge;
    std::vector<std::pair<std::string, int64>> his2hashtag2hashtag_score_pair_list;
    int weight = his2hashtag2hashtag_list.size() - 1;
    for (int i = 0; i < his2hashtag2hashtag_list.size(); i++) {
      std::string next_query_message =
        std::string(his2hashtag2hashtag_list[i].data(), his2hashtag2hashtag_list[i].size());
      if (sug_his2hashtag2hashtag_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_his2hashtag2hashtag_list.item_size(); j++) {
          auto item_id = sug_his2hashtag2hashtag_list.item(j).item_id();
          if (sug_his2hashtag2hashtag_list.item(j).score() > his2hashtag2hashtag_match_pv_threshold) {
            int64 score = sug_his2hashtag2hashtag_list.item(j).score() *
                        std::pow(his2hashtag2hashtag_match_max_weight, weight);
            if (his2hashtag2hashtag_exist.find(item_id) == his2hashtag2hashtag_exist.end()) {  // 去重
              his2hashtag2hashtag_exist[item_id] = score;
            } else {
              his2hashtag2hashtag_exist[item_id] += score;
            }
          }
        }
      }
      weight--;
    }
    for (auto &pair : his2hashtag2hashtag_exist) {
      auto item_id = pair.first;
      int64 score = pair.second;
      his2hashtag2hashtag_score_pair_list.push_back(std::pair<std::string, int64>(item_id, score));
    }
    std::sort(his2hashtag2hashtag_score_pair_list.begin(), his2hashtag2hashtag_score_pair_list.end(),
          [](const std::pair<std::string, int64> &a, const std::pair<std::string, int64> &b) -> bool {
            return a.second > b.second;
          });
    for (int i = 0; i < his2hashtag2hashtag_score_pair_list.size(); i++) {
      auto item_id = his2hashtag2hashtag_score_pair_list[i].first;
      sug_his2hashtag2hashtag_vec.push_back(item_id);
      auto r_item = sug_his2hashtag2hashtag_list_merge.add_item();
      r_item->set_item_id(item_id);
    }
    sug_his2hashtag2hashtag_list_merge.SerializeToString(&serialized_str);
    context.SetStringListCommonAttr("his2hashtag2hashtag_list", std::move(sug_his2hashtag2hashtag_vec));
    context.SetStringCommonAttr("his2hashtag2hashtag_list_serialized_str", std::move(serialized_str));
    return true;
  }

  static bool SugHis2Hashtag2HashtagPrefixMatch(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto query_str = context.GetStringCommonAttr("query").value_or("");
    std::string query = std::string(query_str.data(), query_str.size());
    auto his2hashtag2hashtag_recall_name =
        context.GetStringCommonAttr("his2hashtag2hashtag_recall_name").value_or("");
    auto his2hashtag2hashtag_list = context.GetStringListCommonAttr("his2hashtag2hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    int64 his2hashtag2hashtag_recall_num =
        context.GetIntCommonAttr("his2hashtag2hashtag_recall_num").value_or(0);
    std::vector<std::string> candidate_vec;
    std::unordered_map<std::string, int> prefix_match_his2hashtag2hashtag_list_exist;
    std::vector<std::string> prefix_match_his2hashtag2hashtag_list;
    std::vector<std::string> prefix_match_his2hashtag2hashtag_list_py;
    std::vector<int64_t> prefix_match_his2hashtag2hashtag_list_key;
    int chinese_end_pos = ks::search::ziya_sort::chinese_split(query);
    bool tail_has_pinyin = ((chinese_end_pos >= 0) && (chinese_end_pos < query.size()));
    for (int i = 0; i < his2hashtag2hashtag_list.size(); i++) {
      if (prefix_match_his2hashtag2hashtag_list.size() >= his2hashtag2hashtag_recall_num) break;
      auto item_id = std::string(his2hashtag2hashtag_list[i].data(), his2hashtag2hashtag_list[i].size());
      candidate_vec.push_back(item_id);
      bool match = ::base::StartsWith(item_id, query, false);
      if (!tail_has_pinyin && match && prefix_match_his2hashtag2hashtag_list_exist.find(item_id) ==
          prefix_match_his2hashtag2hashtag_list_exist.end()) {
        std::string key = item_id + std::string(his2hashtag2hashtag_recall_name.data(),
                                                his2hashtag2hashtag_recall_name.size());
        prefix_match_his2hashtag2hashtag_list.push_back(item_id);
        prefix_match_his2hashtag2hashtag_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
        prefix_match_his2hashtag2hashtag_list_exist[item_id] = 1;
      }
    }
    if (tail_has_pinyin) {
      prefix_match_his2hashtag2hashtag_list.clear();
      prefix_match_his2hashtag2hashtag_list_key.clear();
      prefix_match_his2hashtag2hashtag_list_exist.clear();
      ks::search::ziya_sort::prefix_match_with_pinyin(query, candidate_vec,
                          prefix_match_his2hashtag2hashtag_list_py, chinese_end_pos,
                          his2hashtag2hashtag_recall_num);
      for (int i = 0; i < prefix_match_his2hashtag2hashtag_list_py.size(); i++) {
        auto item_id = prefix_match_his2hashtag2hashtag_list_py[i];
        if (prefix_match_his2hashtag2hashtag_list_exist.find(item_id) ==
            prefix_match_his2hashtag2hashtag_list_exist.end()) {
          std::string key = item_id + std::string(his2hashtag2hashtag_recall_name.data(),
                                                  his2hashtag2hashtag_recall_name.size());
          prefix_match_his2hashtag2hashtag_list.push_back(item_id);
          prefix_match_his2hashtag2hashtag_list_key.push_back(::base::CityHash64(key.c_str(), key.size()));
          prefix_match_his2hashtag2hashtag_list_exist[item_id] = 1;
        }
      }
    }

    context.SetStringListCommonAttr("prefix_match_his2hashtag2hashtag_list",
                                    std::move(prefix_match_his2hashtag2hashtag_list));
    context.SetIntListCommonAttr("prefix_match_his2hashtag2hashtag_list_key",
                                 std::move(prefix_match_his2hashtag2hashtag_list_key));
    return true;
  }

  static bool MultiP2HashtagRecallOutputMerge(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recall_num =
        context.GetIntCommonAttr("client_refer2hashtag_recall_num").value_or(0);
    double client_refer2hashtag_max_weight =
           context.GetDoubleCommonAttr("client_refer2hashtag_max_weight").value_or(1.0);
    auto client_refer2hashtag_list = context.GetStringListCommonAttr("client_refer2hashtag_list")
                                    .value_or(std::vector<absl::string_view>());
    RetrievalInfo sug_client_refer2hashtag_list;
    std::vector<std::string> multi_refer_to_hashtag_list;
    std::vector<std::pair<std::string, int64>> client_refer2hashtag_score_pair_list;
    std::unordered_map<std::string, int64> client_refer2hashtag_exist;
    int weight = client_refer2hashtag_list.size() - 1;
    for (int i = 0; i < client_refer2hashtag_list.size(); i++) {
      std::string next_query_message =
        std::string(client_refer2hashtag_list[i].data(), client_refer2hashtag_list[i].size());
      if (sug_client_refer2hashtag_list.ParseFromString(next_query_message)) {
        for (int j = 0; j < sug_client_refer2hashtag_list.item_size(); j++) {
          auto item_id = sug_client_refer2hashtag_list.item(j).item_id();
          int64 score = std::pow(client_refer2hashtag_max_weight, weight);
          if (client_refer2hashtag_exist.find(item_id) == client_refer2hashtag_exist.end()) {  // 去重
            client_refer2hashtag_exist[item_id] = score;
            client_refer2hashtag_score_pair_list.push_back(std::pair<std::string, int64>(item_id, score));
          }
        }
      }
      weight--;
    }
    std::sort(client_refer2hashtag_score_pair_list.begin(), client_refer2hashtag_score_pair_list.end(),
          [](const std::pair<std::string, int64> &a, const std::pair<std::string, int64> &b) -> bool {
            return a.second > b.second;
          });
    int recall_num_count = 0;
    for (int i = 0; i < client_refer2hashtag_score_pair_list.size(); i++) {
      auto item_id = client_refer2hashtag_score_pair_list[i].first;
      multi_refer_to_hashtag_list.push_back(item_id);
      recall_num_count++;
      if (recall_num_count >= recall_num) {
        break;
      }
    }
    context.SetStringListCommonAttr("multi_refer_to_hashtag_list", std::move(multi_refer_to_hashtag_list));
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(SeRecoLightFunctionSet);
};
}  // namespace platform
}  // namespace ks
