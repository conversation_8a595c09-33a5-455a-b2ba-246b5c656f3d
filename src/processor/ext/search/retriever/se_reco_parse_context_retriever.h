#pragma once
#include <memory>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/module/traceback_util.h"
#include "dragon/src/processor/base/common_reco_base_retriever.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "redis_proxy_client/redis_pipeline.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "third_party/abseil/absl/container/flat_hash_set.h"

namespace ks {
namespace platform {
class SeRecoParseContextRetriever : public CommonRecoBaseRetriever {
 public:
  SeRecoParseContextRetriever() {}
  void Retrieve(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    auto *extract_common_attrs = config()->Get("extract_common_attrs");
    RecoUtil::ExtractStringSetFromJsonConfig(extract_common_attrs, &extract_common_attrs_);

    auto *extract_item_attrs = config()->Get("extract_item_attrs");
    RecoUtil::ExtractStringSetFromJsonConfig(extract_item_attrs, &extract_item_attrs_);

    auto *extract_item_results = config()->Get("extract_item_results");
    if (extract_item_results && extract_item_results->IsBoolean()) {
      extract_item_results_ = extract_item_results->BooleanValue(extract_item_attrs_.size() > 0);
    } else {
      // 在未填写的情况下，以 extract_item_attrs size 为默认值
      extract_item_results_ = extract_item_attrs_.size() > 0;
    }

    if (extract_common_attrs_.empty() && extract_item_results_ == false) {
      LOG(ERROR) << "SeRecoParseContextRetriever init failed! \"extract_common_attrs\"  and "
                    "\"extract_item_results\" cannot be both empty!";
      return false;
    }
    parse_from_attr_ = config()->GetString("parse_from_attr", "");
    if (parse_from_attr_.empty()) {
      LOG(ERROR) << "SeRecoParseContextRetriever init failed! \"parse_from_attr\" cannot "
                 << "be empty!";
      return false;
    }

    data_format_ = config()->GetString("data_format", "step_info");
    if (data_format_ != "step_info" && data_format_ != "data_table") {
      LOG(ERROR)
          << "SeRecoParseContextRetriever init failed! \"data_format\" must be step_info or data_table! ";
    }
    return true;
  }

  bool IsKeyInList(const std::vector<int>& ptrList, int key);

  void ParseFromDataTable(AddibleRecoContextInterface *context, absl::string_view ret_val);


  static bool ExtractPackedItemAttrToContext(ItemAttr *attr_accessor, const PackedItemAttrValue &attr_value,
                                              const CommonRecoResult &result,
                                              const std::vector<int> &item_index_valid_flag,
                                              const std::vector<int> &item_index_map, int orig_item_num,
                                              bool append_value = false) {
      std::vector<CommonRecoResult> items;
      items.push_back(result);
      auto static_info = ExtractPackedItemAttrToContext(attr_accessor, attr_value,
                                                       &items, item_index_valid_flag, item_index_map,
                                                       orig_item_num, append_value);
      if (static_info.count()) {
          return true;
      } else {
          return false;
      }
  }
  static StatisticInfo ExtractPackedItemAttrToContext(ItemAttr *attr_accessor,
                                                      const PackedItemAttrValue &attr_value,
                                                      const std::vector<CommonRecoResult> *p_items,
                                                      const std::vector<int> &item_index_valid_flag,
                                                      const std::vector<int> &item_index_map,
                                                      int orig_item_num,
                                                      bool append_value = false,
                                                      bool check_multi_table = false
                                                      ) {
    StatisticInfo stat;
    if (!p_items) return stat;

    const auto &items = *p_items;
    base::ConstArray<int64> int_list;
    base::ConstArray<double> double_list;
    bool invalid_packed_value = false;
    if (attr_value.type() == kuiba::CommonSampleEnum::INT_ATTR ||
        attr_value.value_type() == PackedItemAttrValue_ValueType_INT64) {
      int_list.SetData(attr_value.value());
      int i = 0;
      for (int64 val : int_list) {
        while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) {
          ++i;
        }
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }
        if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
          if (item_index_map[i] < items.size()) {
            items[item_index_map[i]].SetIntAttr(attr_accessor, val, false, true, check_multi_table);
          }
        }
        stat.AddValue(static_cast<double>(val));
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::FLOAT_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64) {
      double_list.SetData(attr_value.value());
      int i = 0;
      for (auto val : double_list) {
        while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) {
          ++i;
        }
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }

        if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
          if (item_index_map[i] < items.size()) {
            items[item_index_map[i]].SetDoubleAttr(attr_accessor, val, false, true, check_multi_table);
          }
        }
        stat.AddValue(static_cast<double>(val));
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::STRING_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_STRING) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
            if (item_index_map[i] < items.size()) {
              items[item_index_map[i]].SetStringAttr(attr_accessor,
                                                     attr_value.value().substr(offset, len),
                                                    false, true, check_multi_table);
            }
          }
          stat.IncrCount();
          offset += len;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::INT_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_INT64_LIST) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          int data_size = len * sizeof(int64);
          int_list.SetData(attr_value.value().data() + offset, data_size);
          if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
            std::vector<int64> val(int_list.begin(), int_list.end());
            if (item_index_map[i] < items.size()) {
              items[item_index_map[i]].SetIntListAttr(attr_accessor, std::move(val),
                                                      false, true, check_multi_table);
            }
          }
          stat.IncrCount();
          offset += data_size;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::FLOAT_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64_LIST) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          int data_size = len * sizeof(double);
          double_list.SetData(attr_value.value().data() + offset, data_size);
          if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
            std::vector<double> val(double_list.begin(), double_list.end());
            if (item_index_map[i] < items.size()) {
              items[item_index_map[i]].SetDoubleListAttr(attr_accessor,
                                                         std::move(val),
                                                         false, true, check_multi_table);
            }
          }
          stat.IncrCount();
          offset += data_size;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::STRING_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_STRING_LIST) {
      int i = 0;
      int k = 0;
      int offset = 0;
      while (k < attr_value.value_length_size()) {
        if (i >= orig_item_num) {
          invalid_packed_value = true;
          break;
        }
        int len = attr_value.value_length(k++);
        if (len >= 0) {
          if (item_index_valid_flag[i] == 1 && i < item_index_map.size()) {
            std::vector<std::string> val;
            val.reserve(len);
            while (len--) {
              int str_len = attr_value.value_length(k++);
              val.emplace_back(attr_value.value().substr(offset, str_len));
              offset += str_len;
            }
            if (item_index_map[i] < items.size()) {
              items[item_index_map[i]].SetStringListAttr(attr_accessor,
                                                        std::move(val),
                                                        false, true, check_multi_table);
            }
          }
          stat.IncrCount();
        }
        ++i;
      }
    }

    if (invalid_packed_value) {
      CL_LOG_EXCEPTION("[DANGER!!!] inconsistent packed_item_attr size: " + attr_accessor->name())
          << "[DANGER!!!] inconsistent packed_item_attr size. attr name: " << attr_accessor->name()
          << ", results size = " << items.size();
    }

    return stat;
  }

 private:
  absl::flat_hash_set<std::string> extract_common_attrs_;
  absl::flat_hash_set<std::string> extract_item_attrs_;
  bool extract_item_results_ = false;
  std::string data_format_;
  std::string parse_from_attr_;
  DISALLOW_COPY_AND_ASSIGN(SeRecoParseContextRetriever);
};
}  // namespace platform
}  // namespace ks
