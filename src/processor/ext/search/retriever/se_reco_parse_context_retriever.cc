#include "dragon/src/processor/ext/search/retriever/se_reco_parse_context_retriever.h"
#include <numeric>
#include <utility>

namespace ks {
namespace platform {

namespace {
template <typename T>
constexpr absl::Span<const T> BytesToArray(const std::string &bytes) {
  return absl::Span<const T>(reinterpret_cast<const T *>(bytes.data()), bytes.size() / sizeof(T));
}
}  // namespace

bool SeRecoParseContextRetriever::IsKeyInList(const std::vector<int>& ptrList, int key) {
    return std::find(ptrList.begin(), ptrList.end(), key) != ptrList.end();
}

void SeRecoParseContextRetriever::ParseFromDataTable(AddibleRecoContextInterface *context,
                                                         absl::string_view ret_val) {
  thread_local ks::platform::DataTable item_table;
  item_table.Clear();
  if (!item_table.ParseFromArray(ret_val.data(), ret_val.size())) {
    CL_LOG_ERROR("parse_context_se", "pb str parse error")
        << "parse_context_se error: parse pb str error from attr: " << parse_from_attr_;
    return;
  }

  std::vector<CommonRecoResult> items;
  items.reserve(item_table.item_list_size());
  int orig_item_num = item_table.item_list_size();

  std::vector<int> item_index_valid_flag;
  std::vector<int> item_index_map;
  std::vector<int> orig_sample_pid_list;
  item_index_valid_flag.resize(orig_item_num, 0);
  item_index_map.resize(orig_item_num, 0);
  auto orig_pid_attr = context->GetIntListCommonAttr("orig_sample_pid_list");
  if (orig_pid_attr) {
    for (auto iter : *orig_pid_attr) {
      orig_sample_pid_list.emplace_back(iter);
    }
  }
  int k = 0;
  for (int i = 0; i < item_table.item_list_size(); i++) {
    const auto &item = item_table.item_list(i);
    item_index_map[i] = k;
    if (orig_sample_pid_list.size() > 0) {
        if (IsKeyInList(orig_sample_pid_list, item.item_key())) {
            continue;
        }
    }
    k++;
    item_index_valid_flag[i] = 1;
    items.emplace_back(
        context->AddCommonRecoResult(item.item_key(), item.reason(), item.score()));
  }

  for (const auto &attr_value : *(item_table.mutable_columns())) {
    if (extract_item_attrs_.find(attr_value.name()) == extract_item_attrs_.end()) {
      continue;
    }
    auto *attr_accessor = context->GetItemAttrAccessor(attr_value.name());
    ExtractPackedItemAttrToContext(attr_accessor, attr_value, &items,
                                   item_index_valid_flag, item_index_map, orig_item_num);
  }
}

void SeRecoParseContextRetriever::Retrieve(AddibleRecoContextInterface *context) {
  auto ret_val = context->GetStringCommonAttr(parse_from_attr_);
  if (!ret_val || ret_val->empty()) {
    return;
  }

  if (data_format_ == "data_table") {
    ParseFromDataTable(context, ret_val.value());
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoParseContextRetriever, SeRecoParseContextRetriever);
}  // namespace platform
}  // namespace ks
