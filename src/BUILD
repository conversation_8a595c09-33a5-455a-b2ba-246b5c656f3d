import os
import json

# ---------------------------------
# region - system env controls
# ---------------------------------
enable_mio_tf_gpu = os.getenv("ENABLE_MIO_TENSORFLOW_GPU", 'False').lower() == "true"
use_tf_15 = os.getenv("USE_TF_15", 'False').lower() in ['true', '1']
use_tf_2 = os.getenv("USE_TF_2", 'False').lower() in ['true', '1']
is_kai_python_worker = os.getenv("ENABLE_KAI_LEARNER", 'False').lower() in ['true', '1'] \
    and ((os.getenv("ENABLE_KAI_LEARNER_V2", 'False').lower() in ['true', '1'] \
          and os.getenv("KAI_WITH_STREAM", 'False').lower() not in ['true', '1']) \
         or (os.getenv("ENABLE_KAI_LEARNER_V2", 'False').lower() not in ['true', '1'] \
             and os.getenv("IS_KAI_PYTHON_WORKER", 'False').lower() in ['true', '1']))
use_compatible_abseil = os.environ.get("ABSEIL_COMPATBLE_WITH_BASE", "false").lower() == "true"
fix_kai_duplicate_op = is_kai_python_worker and use_tf_2

# AD(Advertise) uses different protocol with common leaf. To reuse
# common_leaf based infer server for ad, some modifications are
# required, e.g., protocol conversion, add depends of ad codebase,
# skip some unnecessary processors that are conflict with ad, etc.
# In order to make these modifications not influencing existing BUILD,
# here we use the environment variable `BUILD_AD_INFER_SERVER`
# to indicate compiling the infer server for ad, and adapt the BUILD
# file according to it.
reco_infer_for_ad = os.getenv("BUILD_AD_INFER_SERVER", 'False').lower() in ['true', '1']
# BUILD_AD_WITH_DRAGON=TRUE when dragon is built within ad predict server pipelines
build_ad_with_dragon = os.getenv("BUILD_AD_WITH_DRAGON", 'False').lower() in ['true', '1']
# AD_KAI_SERVING_RANK=TRUE when dragon is built ad ad predict server
ad_kai_serving_rank = os.getenv("AD_KAI_SERVING_RANK", 'False').lower() in ['true', '1']
# 是否编译mio hdfs fetcher，由于mio hdfs fetcher默认依赖mpi，编译runner时需要同时加上USE_MPI_STUB取消mpi依赖
# 编译kai、mio已经默认带上mio hdfs fetcher，无需打开此开关
build_mio_hdfs_with_dragon = os.getenv("BUILD_MIO_HDFS_WITH_DRAGON", 'False').lower() in ['true', '1']

ad_use_uni_predict_v2 = os.getenv("AD_USE_UNI_PREDICT_V2", 'False').lower() in ['true', '1']

ldflags = []
if os.environ.get("COMMON_RECO_LEAF_LINK_MKL", "false") == "true":
  ldflags.append("-lmkl_rt")
if os.environ.get("COMMON_RECO_LEAF_LINK_PYTHON", "false") == "true":
  ldflags.append("-lpython3.6m")

embed_calc_deps = []
if reco_infer_for_ad or build_ad_with_dragon:
  embed_calc_deps = [":common_processors",
                   "//ks/reco/bt_embedding_server/BUILD:embedding_table",
                   ]
  embed_calc_excludes = ["processor/ext/embed_calc/enricher/*_test.cc",
                         "processor/ext/embed_calc/enricher/tower_fetch_gpu_index_attr_enricher.cc"]
else:
  embed_calc_deps = [
    ":common_processors",
    "//ks/reco/bt_embedding_server/BUILD:embedding_table",
    "//ks/reco_proto/gpu_retr/BUILD:proto",
    "//serving_base/memtable/BUILD:memtable",
  ]
  embed_calc_excludes = ["processor/ext/embed_calc/enricher/*_test.cc"]


if os.environ.get("COMMON_RECO_LEAF_LINK_FPGA", "false") == "true":
  ldflags.append("-lxilinxopencl -Wl,-Bdynamic -lboost_filesystem -L./teams/hetero/third_party/xilinx/OpenCL/lib")
  ldflags.append("-lgmp -lIp_floating_point_v7_0_bitacc_cmodel -Lteams/hetero/third_party/xilinx/ap/lnx64/tools/fpo_v7_0/")
  embed_calc_deps.append("//ks/reco/fpga_workflow_wrapper/BUILD:fpga_workflow_wrapper")
else:
  embed_calc_excludes.append("processor/ext/embed_calc/enricher/*fpga*.cc")

cppflags = [
  "-Wno-unknown-warning-option",
]

if os.environ.get("COMMON_RECO_LEAF_BUILD_STRICT", "false") == "true":
  cppflags.extend(["-Wall", "-Wextra",
                   "-Wno-sign-compare", # TOO many
                   "-Wno-unused-local-typedefs", # TOO many errors because of MACROs of glog
                   "-Wno-unused-parameter", # TOO many errors because of protobuf
                   "-Wno-class-memaccess", # F14Map
                   "-Wno-error=parentheses",  # boost
                   "-Wno-error=unused-variable",  # TOO many
                   ])

use_ks_reco_feature = os.environ.get("COMMON_LEAF_USE_KS_RECO_FEATURE", "false") == "true"
use_gpu = os.getenv("USE_GPU", 'False').lower() in ['true', '1']

# NOTE(zhaoyang09): clang11 环境暂时移除 ksib_mix_rank && kwai_i18n_ad_proto
use_pb319 = os.environ.get("CLANG11_PB319", "false") == "true"

if os.getenv("ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA", "False").lower() == "true":
  cppflags = cppflags + ["-DDRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA"]

if os.getenv("DISABLE_FLAT_INDEX", "False").lower() == "true":
  cppflags = cppflags + ["-DDISABLE_FLAT_INDEX"]

matx_deps = list()

if os.getenv("DRAGON_MATX_DYNAMIC_DEPLOY", "false").lower() == "true":
  # 动态编译，在 dragon 运行时需要链接 libmatx.so 等库，该选项主要用于 playground。
  matx_deps = ["//third_party/matx/BUILD:libmatx"]
else:
  # 静态编译，将 matx 的源代码与 dragon 一起编译，dragon 运行时不需要其他依赖，用于线上环境部署。
  matx_deps = ["//third_party/matx/BUILD:matx"]

module_to_files = {}
dragon_path = os.getenv("JOB_HOST_DIR", os.getenv("DRAGON_REPO_PATH", ""))
if os.path.exists(dragon_path + '/dragon/src/module_to_files.json'):
  with open(dragon_path + '/dragon/src/module_to_files.json', 'r') as file:
    module_to_files = json.load(file)

# endregion

# ---------------------------------
# region - core modules
# ---------------------------------
cc_library(
  name = "util",
  srcs = ["util/*.cc"],
  deps = [
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/perfutil/BUILD:perfutil",
    "//infra/perfutil-coarse/BUILD:perfutil_coarse",
    "//third_party/glog/BUILD:glog",
    "//base/common/BUILD:base",
    "//ks/serving_util/BUILD:kess_helper",
    "//ks/serving_util/BUILD:serving_util",
    "//serving_base/util/BUILD:util",
    "//third_party/numactl/BUILD:libnuma",
    "//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto",
    "//ks/numa_aware/BUILD:numa_util",
    "//third_party/xxhash/BUILD:xxhash",
  ],
  excludes=["util/*_test.cc"],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "core",
  srcs = ["core/*.cc"],
  deps = [
    ":util",
    ":async_task_thread_pool_mod",
    ":table_api",
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/encoding/BUILD:encoding",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/perfutil/BUILD:perfutil",
    "//infra/kess_core/BUILD:kess",
    "//infra/ktrace/BUILD:ktrace",
    "//infra/location/BUILD:location",
    "//third_party/protobuf_v3/BUILD:protobuf",
    "//third_party/glog/BUILD:glog",
    "//third_party/boost/BUILD:boost",
    "//third_party/grpc-v1100/BUILD:grpc++",
    "//third_party/zstd/BUILD:zstd",
    "//ks/base/abtest/BUILD:common",
    "//ks/common_reco/util/BUILD:key_sign",
    "//ks/common_reco/util/BUILD:util",
    "//ks/reco/common/browse_set/BUILD:browse_set",
    "//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto",
    "//ks/serving_util/BUILD:kess_helper",
    "//infra/dynamic_kafka_client/BUILD:dynamic_kafka_client",
    "//infra/kess_grpc-v1100/BUILD:kess-brpc",
    "//infra/kess_grpc-v1100/BUILD:kess-rpc",
    "//infra/kess_grpc-v1100/BUILD:kess-krpc",
    "//infra/memcached_client/BUILD:memcached_client",
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//infra/perfutil/BUILD:perfutil",
    "//infra/perfutil-coarse/BUILD:perfutil_coarse",
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//third_party/folly/BUILD:folly",
    "//third_party/gflags/BUILD:gflags",
    "//ks/reco_pub/reco/distributed_photo_info/protoutil/BUILD:attr_kv_photo_store_item",
    "//third_party/croaring/BUILD:croaring",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
  ],
  excludes=["core/*_test.cc"],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "interop",
  srcs = [
    "interop/*.cc",
  ],
  excludes = [
    "interop/*_test.cc",
  ],
  deps = [
    ":core",
    "//base/strings/BUILD:strings",
    "//third_party/jsoncpp/BUILD:jsoncpp",
    "//ks/reco_proto/feature_pipe/BUILD:proto",
    "//learning/kuiba/sample_reader/BUILD:attr_dict_api",
    "//teams/reco-arch/colossusdb/common/BUILD:field_replace_conf",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "async_task_thread_pool_mod",
  srcs = [
    "module/async_task_thread_pool.cc",
  ],
  deps = [
    ":util",
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//infra/ktrace/BUILD:ktrace",
    "//ks/base/perfutil/BUILD:perfutil",
    "//third_party/folly/BUILD:folly",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "query_mod",
  srcs = [
    "module/common_reco_build_query.cc",
  ],
  deps = [
    ":core",
    "//serving_base/jansson/BUILD:json",
    "//ks/common_reco/index/BUILD:common_index",
    "//teams/reco-arch/colossusdb/common/BUILD:field_replace_conf",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "pb_reg_mod",
  srcs = [
    "module/protobuf_reg.inc.cc",
  ],
  deps = [
    "//ks/ds/relation/relation_lib/proto/BUILD:relation_api_proto",
    "//ks/ds/relation/relation_lib/proto/BUILD:user_relation_photo_upload_service_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:edge_reco_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:log_client_log_client_log__proto",
    "//ks/reco_proto/distributed_photo_info/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:comment_rank_info",
    "//ks/reco_proto/proto/BUILD:platform_coupon_req",
    "//ks/reco_proto/proto/BUILD:ids",
    "//ks/reco_proto/proto/BUILD:follow_live",
    "//ks/reco_proto/proto/BUILD:reco_distribute_search_request",
    "//ks/reco_proto/proto/BUILD:reco_distribute_ner",
    "//ks/reco_proto/proto/BUILD:reco_galaxy_retr_service",
    "//ks/reco_proto/proto/BUILD:reco_tag_photo",
    "//ks/reco_proto/proto/BUILD:user_interest_server_proto",
    "//ks/reco_proto/proto/BUILD:interest_model_service",
    "//ks/reco_proto/proto/BUILD:reco_nr_fr_pxtr",
    "//ks/reco_proto/proto/BUILD:reco_rank_ncee_score",
    "//ks/reco_proto/proto/BUILD:reco_tnu_rank_xtr_log",
    "//ks/reco_proto/proto/BUILD:reco_nr",
    "//ks/reco_proto/proto/BUILD:entity_tag_service",
    "//ks/reco_proto/proto/BUILD:nearby_hotspot_service",
    "//ks/reco_proto/proto/BUILD:high_value_info",
    "//ks/reco_proto/proto/BUILD:author_chase_value_info",
    "//ks/reco_proto/proto/BUILD:reco_nr_poi_service",
    "//ks/reco_proto/action/BUILD:proto",
    "//ks/reco_proto/sample_log/BUILD:sample_log",
    "//ks/reco_proto/rodis_v2_proto/BUILD:rodis_v2_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:dragon_ad_union_train_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_explore_pic_joint_reco_log__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_eyeshot_joint_reco_log__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_eyeshot_joint_reco_tiny_log__proto",
    "//ks/reco/fm/fm_action_server/BUILD:fm_action_service",
    "//ks/reco/plateco-dsalog/ics/shop_customer_service/BUILD:merchant_search_mmu_proto",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:ksib_proto",
    "//ks/reco_proto/proto/BUILD:reco_rpc_percentile_service",
    "//ks/reco_proto/merchantreco/BUILD:kwaishop_marketing_coupon_query_service__proto",
    "//ks/reco_proto/merchantreco/BUILD:kwaishop_product_shelf_index_reco_search_rpc_service__proto",
    "//ks/reco_proto/merchantreco/BUILD:merchant_delivery_address_list__proto",
    "//ks/reco_proto/merchantreco/BUILD:merchantreco",
    "//ks/reco_proto/proto/BUILD:reco_interest_explore",
    "//ks/reco_proto/tdm_retr/BUILD:tdm_proto",
    "//ks/reco_proto/rta_proto/BUILD:proto",
    "//ks/reco_proto/rtb_proto/BUILD:proto",
    "//ks/reco_proto/ug_feature/BUILD:proto",
    "//ks/common_reco/common_kv/BUILD:proto",
    "//ks/reco_proto/live/BUILD:proto",
    "//ks/reco_proto/search/BUILD:proto",
    "//ks/reco_proto/nearby/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:reco_local_life",
    "//ks/reco_proto/proto/BUILD:mmulac_service",
    "//ks/reco_proto/proto/BUILD:reco_rl",
    "//ks/reco_proto/big_river/BUILD:big_river",
    "//teams/reco-arch/colossusdb/colossusdb-proto/BUILD:label_list",
    "//teams/reco-arch/colossusdb/clotho-sdk/proto/BUILD:clotho_proto",
    "//ks/reco_proto/proto/BUILD:puji_proto",
    "//ks/reco_proto/proto/BUILD:merchant_bh_huopan_retr__proto",
    "//ks/reco_proto/proto/BUILD:reco_browse_set_service",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:newsmodel_mix_rank_log_hive_schema__proto",
    "//algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/BUILD:kaiworks_data_proto",
    "//ks/reco_proto/proto/BUILD:reco_eyeshot_rl_proto",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "score_calc_mod",
  srcs = [
    "module/common_reco_dynamic_param_evaluator.cc",
  ],
  deps = [
    ":core",
    "//serving_base/retrieval/BUILD:retrieval",
    "//base/strings/BUILD:strings",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "photo_store_mod",
  srcs = [
    "module/photo_store_manager.cc",
  ],
  deps = [
    "//ks/photo_store/BUILD:photo_store",
    "//ks/util/BUILD:util",
    "//base/thread/BUILD:thread",
    "//ks/reco_pub/reco/distributed_photo_info/common/BUILD:photo_info_fetcher",
    "//ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/BUILD:photo_map_cache_api",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

cc_library(
  name = "table_api",
  srcs = [
    "table_api/table.cc",
  ],
  deps = [
    "//third_party/abseil/BUILD:abseil",
    "//third_party/glog/BUILD:glog",
    "//ks/algo-engine-proto/BUILD:dragonfly_data",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)


if not use_pb319:
  proto_library(
      name="kwai_i18n_ad_proto",
      enable_grpc=True,
      group="ad",
      artifact="kwai-i18n-ad-proto",
      use_remote_srcs=True,
      enable_kess=True,
      use_grpc_ver="v1100",
  )

ad_index_cppflag = []
ad_index_deps = []
action_proto_deps = ["//ks/reco_proto/action/BUILD:proto"]
pb_reg_mod_deps = [":pb_reg_mod"]
if reco_infer_for_ad:
  ad_index_cppflag = ["-DBUILD_AD_INFER_SERVER"]
  ad_index_deps = ["//teams/ad/ad_proto/kuaishou/BUILD:dragon_ad_union_train_proto_hack"]
  action_proto_deps = []
if build_ad_with_dragon:
  # ad 线上不会使用pb反射相关功能，pb_reg_mod包含的pb比较多，容易冲突
  pb_reg_mod_deps = []

common_srcs = [
  "processor/base/*.cc",
  "processor/common/arranger/*.cc",
  "processor/common/enricher/*.cc",
  "processor/common/observer/*.cc",
  "processor/common/retriever/*.cc",
  "processor/common/mixer/*.cc",
]

if module_to_files and "common_processors" in module_to_files:
  common_srcs = sorted([str(item) for item in module_to_files["common_processors"]])

cc_library(
  name = "common_processors",
  srcs = common_srcs,
  excludes = [
    "processor/common/arranger/*_test.cc",
    "processor/common/enricher/*_test.cc",
    "processor/common/observer/*_test.cc",
    "processor/common/retriever/*_test.cc",
    "processor/common/mixer/*_test.cc",
    "processor/common/enricher/common_reco_tensorflow_attr_enricher.cc",
    "processor/common/enricher/common_reco_tf_local_predict_enricher.cc",
    "processor/common/enricher/common_reco_tf_serving_predict_enricher.cc",
    "processor/common/enricher/common_reco_python_attr_enricher.cc",
    "processor/common/enricher/common_reco_local_ann_embedding_attr_enricher.cc",
    "processor/common/retriever/common_reco_local_ann_retriever.cc",
    "processor/common/retriever/common_reco_embedding_retriever.cc",
  ] + ([
    "processor/common/enricher/common_reco_common_predict_item_attr_enricher.cc",
    "processor/common/enricher/common_reco_predict_fetcher_item_attr_enricher.cc",
  ] if build_ad_with_dragon else []),
  deps = [
    ":core",
    ":interop",
    ":query_mod",
    ":score_calc_mod",
    ":photo_store_mod",
    ":matx_module",
    ":matx_common_custom_classes",
    "//ks/common_reco/ann_retrieve/client/BUILD:client",
    "//learning/kuiba/predict_base/BUILD:predict_base",
    "//ks/common_reco/ann_retrieve/util/BUILD:util",
    "//ks/photo_store/BUILD:photo_store",
    "//ks/reco_pub/reco/predict/base/BUILD:flags",
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/encoding/BUILD:encoding",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/perfutil/BUILD:perfutil",
    "//serving_base/server_base/BUILD:server_base",
    "//serving_base/cross_idc_comm/BUILD:send_message_client",
    "//infra/ktrace/BUILD:ktrace",
    "//infra/kenv/BUILD:kenv",
    "//infra/location/BUILD:location",
    "//infra/btq_client/BUILD:dynamic_bt_queue_client",
    "//infra/utility/BUILD:net_utils",
    "//third_party/boost/BUILD:boost",
    "//learning/kuiba/parameter/BUILD:parameter_base",
    "//infra/kconf/BUILD:kconf",
    "//ks/action/BUILD:photo_store_item",
    "//ks/action/BUILD:sample_join_client",
    "//ks/action/BUILD:sample_list_kess_client",
    "//ks/base/abtest/BUILD:common",
    "//ks/base/realtime_metric/BUILD:realtime_metric",
    "//ks/common_reco/util/BUILD:redis_idpool_proxy",
    "//ks/common_reco/action_log/BUILD:proto",
    "//ks/common_reco/common_cluster/BUILD:client",
    "//ks/common_reco/index/BUILD:common_query_client",
    "//ks/common_reco/config/BUILD:config",
    "//ks/common_reco/index/BUILD:common_index",
    "//ks/common_reco/model_retrieve/BUILD:common_model_retrieval_client",
    "//ks/reco/bt_embedding_server/BUILD:embedding_table",
    "//ks/reco/sphinx/client/BUILD:sphinx_client",
    "//ks/reco_pub/rpc_idpool_proxy/BUILD:rpc_idpool_proxy",
    "//ks/reco_pub/rpc_idpool_proxy/BUILD:btq_idpool",
    "//ks/reco_pub/reco/distributed_photo_info/attr_index/BUILD:attr_index_util",
    "//ks/reco_pub/reco/distributed_photo_info/protoutil/BUILD:attr_kv_photo_store_item",
    "//ks/reco_pub/reco/predict/clients/BUILD:common_reco_predict_client",
    "//ks/reco_pub/reco/util/BUILD:predict_util",
    "//ks/reco_proto/proto/BUILD:reco_user_profile",
    "//serving_base/utility/BUILD:buffered_btq_producer",
    "//serving_base/kv_client_wrapper/BUILD:kv_client_wrapper",
    "//teams/reco-arch/colossusdb/client/BUILD:embedding_client",
    "//teams/reco-arch/colossusdb/client/BUILD:dragon_client",
    "//teams/reco-arch/colossusdb/clotho-sdk/cpp/BUILD:clotho_gateway_sdk",
    "//teams/reco-arch/colossusdb/common/BUILD:field_replace_conf",
    "//teams/reco-arch/fdk/cpp/BUILD:fdk",
    "//teams/reco-arch/kgnn/src/proto/BUILD:gnn_kv_service",
    "//third_party/abseil/BUILD:abseil",
    "//third_party/lua-5.4.4/BUILD:lua",
    "//infra/utility/BUILD:encrypted_id_cipher",
    "//third_party/wasmtime-cpp/BUILD:wasmtime-cpp",
    "//ks/algo-engine/formula1/BUILD:formula1",
    "//ks/reco/metric_reporter/BUILD:metric_reporter",
    "//ks/reco_pub/author_abtest_util/BUILD:author_abtest_parameter_util",
    "//ks/ds/user_profile/photo_count_client/BUILD:photo_count",
    "//ks/ds/proto/BUILD:userprofile_proto",
    "//ks/ds/user_profile/rodis_sdk/BUILD:rodis_sdk",
    "//infra/geoinfo/BUILD:geoinfo",
    "//ks/reco_proto/proto/BUILD:merchant_profile",
    "//ks/reco_proto/live/BUILD:proto",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    "//third_party/libcurl/BUILD:libcurl",
  ] + ([
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
  ] if not build_ad_with_dragon else []) + ad_index_deps + action_proto_deps + pb_reg_mod_deps,
  cppflags=cppflags + ad_index_cppflag + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "common_reco_pipeline_executor",
  srcs = [
    "module/common_reco_pipeline_executor.cc",
  ],
  deps = [
    ":common_processors",
    ":extra_processors",
    ":framework_base",
    "//serving_base/jansson/BUILD:json",
    "//infra/kess_grpc-v1100/BUILD:kess-framework",
    "//third_party/abseil/BUILD:abseil",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "common_reco_rpc_mocker",
  srcs = [
    "module/common_reco_rpc_mocker.cc",
  ],
  deps = [
    ":common_processors",
    ":extra_processors",
    ":framework_base",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "common_reco_redis_mocker",
  srcs = [
    "module/common_reco_redis_mocker.cc",
  ],
  deps = [
    ":common_processors",
    ":extra_processors",
    ":framework_base",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "common_reco_kafka_mocker",
  srcs = [
    "module/common_reco_kafka_mocker.cc",
  ],
  deps = [
    ":common_processors",
    ":extra_processors",
    ":framework_base",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "framework_base",
  srcs = [
    "common_reco_handler.cc",
    "common_reco_flatten_handler.cc",
    "common_reco_web_service.cc",
  ],
  deps = [
    ":common_processors",
    "//ks/util/BUILD:util",
    "//ks/base/kafka/BUILD:kafka",
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/ranking/BUILD:ranking",
    "//frontend/ctemplate/BUILD:ctemplate",
    "//ks/base/abtest/BUILD:common",
    "//ks/reco_proto/common_reco/leaf/BUILD:common_reco_flatten_proto",
    "//ks/serving_util/BUILD:serving_util",
    "//ks/base/perfutil/BUILD:perfutil",
    "//infra/kess_grpc-v1100/BUILD:kess-framework",
    "//infra/perfutil-coarse/BUILD:perfutil_coarse",
    "//infra/ccbase/BUILD:ccbase",
    "//teams/reco-arch/colossusdb/common/BUILD:health_check_utils",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "framework_base_with_service",
  srcs = [
    "common_reco_grpc_service.cc",
    "common_reco_handler_queue.cc",
  ],
  deps = [":framework_base"]
)

cc_library(
  name = "kuiba_predict",
  srcs = [
    "common_reco_with_kuiba_predict_grpc_service.cc",
  ],
  deps = [
    ":common_reco_pipeline_executor",
    "//learning/kuiba/proto/BUILD:common_prediction",
    "//infra/kess_grpc-v1100/BUILD:kess-framework",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)


ad_infer_cppflags = []
ad_infer_excludes = []
ad_infer_depends = []
if reco_infer_for_ad:
  cc_library(
    name = "ad_infer_pb_converter",
    srcs = [
      "pb_converter/ad_infer_pb_converter.cc",
    ],
    deps = [
      "//teams/ad/ad_proto/kuaishou/BUILD:dragon_ad_union_train_proto_hack",
      "//ks/reco_proto/common_reco/leaf/BUILD:common_reco_flatten_proto",
      "//ks/serving_util/BUILD:serving_util",
      "//infra/kess_grpc-v1100/BUILD:kess-framework",
      "//base/encoding/BUILD:encoding",
      "//base/strings/BUILD:strings",
      "//base/time/BUILD:time",
      "//ks/common_reco/util/BUILD:key_sign",
      "//third_party/lz4/BUILD:lz4"
    ],
    cppflags=cppflags + ad_infer_cppflags,
    link_all_symbols = True,
  )
  ad_infer_depends = [":ad_infer_pb_converter"]

if reco_infer_for_ad:
  ad_infer_cppflags = ["-DBUILD_AD_INFER_SERVER"]
else:
  ad_infer_excludes = ["common_reco_with_ad_grpc_service.cc"]

cc_library(
  name = "framework",
  srcs = [
    "*.cc",
  ],
  excludes = [
    "*_test.cc",
    "common_reco_with_kuiba_predict_grpc_service.cc",
    "common_reco_handler.cc",
    "common_reco_flatten_handler.cc",
    "common_reco_web_service.cc",
    "paas_common_reco_retrieve.cc",
    "paas_common_reco_runner.cc",
    "paas_common_reco_server.cc",
    "paas_embedding_infer_server.cc",
    "common_reco_grpc_service.cc",
    "common_reco_handler_queue.cc",
  ] + ad_infer_excludes,
  deps = [
    ":framework_base_with_service",
    ":extra_processors",
    ":kuiba_predict",
    "//third_party/brpc-0.9.6/BUILD:brpc",
    "//infra/kafka_client/BUILD:kafka_client",
    "//infra/falcon_counter/BUILD:falcon_counter",
    "//ks/base/abtest/BUILD:common",
    "//ks/reco_proto/common_reco/leaf/BUILD:common_reco_flatten_proto",
    "//ks/serving_util/BUILD:serving_util",
    "//infra/kess_grpc-v1100/BUILD:kess-framework",
    "//infra/profiler_switch/BUILD:profiler_switch",
    "//infra/profiler_api/BUILD:profiler_api",
    "//infra/krpc/BUILD:krpc",
    "//infra/krpc/BUILD:krpc_thread",
    "//infra/perfutil/BUILD:perfutil",
    "//infra/perfutil-coarse/BUILD:perfutil_coarse",
    "//third_party/croaring/BUILD:croaring",
  ] + ad_infer_depends,
  cppflags=cppflags + ad_infer_cppflags + ["-std=gnu++17"],
  link_all_symbols = True,
)

cc_library(
  name = "arrow_module",
  srcs = [
    "module/arrow/*.cc",
  ],
  deps = [
    ":common_reco_pipeline_executor",
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:arrow",
    "//third_party/abseil/BUILD:abseil",
    "//algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/BUILD:kaiworks_data_proto",
  ],
  cppflags = cppflags
)

cc_library(
  name = "matx_module",
  srcs = [
    "module/matx/matx_dragon_context.cc",
    "module/matx/matx_dragon_util.cc",
    "module/matx/matx_function_manager.cc",
    "module/matx/matx_item_attr_getter.cc",
    "module/matx/matx_item_attr_setter.cc"
  ],
  deps = [
    ":core",
    "//third_party/folly/BUILD:folly",
  ] + matx_deps,
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True
)

cc_library(
  name = "matx_common_custom_classes",
  srcs = [
    "module/matx/custom_classes/*.cc",
  ],
  deps = [
    ":core",
    "//third_party/folly/BUILD:folly",
  ] + matx_deps,
  cppflags=cppflags + ["-std=gnu++17"],
  link_all_symbols = True
)

# endregion

# ---------------------------------
# region - extra processors
# ---------------------------------

# build these default ext modules
default_extra_processors = [
  "ann_client",
  "cofea",
  "video_cold_start",
  "explore",
  "explore_life",
  "explore_offline",
  "eyeshot",
  "gsu",
  "nearby",
  "kkd",
  "ks_mix_rank",
  "ks_follow_mix_rank",
  "kuiba",
  "kwai_tv",
  "livestream",
  "merchant",
  "mmu",
  "news",
  "user_reco",
  "nr",
  "oversea_lr",
  "oversea",
  "partlive",
  "pdn",
  "qiliang",
  "push",
  "related",
  "relation_photo",
  "retrieval",
  "rfm",
  "slide",
  "snack_predict",
  "snack",
  "subdivision",
  "gamora_rerank",
  "interest_explore",
  "nebula_rerank",
  "tdm",
  "tensorflow",
  "tf_serving",
  "web",
  "yuzhou",
  "ug_rtb",
  "ug_feature",
  "embed_calc",
  "embedding",
  "mem_retrieval",
  "kfs",
  "se_sug",
  "ksib_mix_rank",
  "se_reco",
  "se_reco_predict",
  "local_life",
  "follow_leaf",
  "friend_leaf",
  "l2model",
  "ps_index",
  "kongming_metric",
  "merchant_arch",
]


if build_ad_with_dragon:
  # ad 默认去除 //ks/reco_pub/reco/predict/clients/BUILD:api 依赖，会导致下列 ext 无法编译
  default_extra_processors.remove("explore")
  default_extra_processors.remove("explore_life")
  default_extra_processors.remove("ks_mix_rank")
  default_extra_processors.remove("livestream")
  default_extra_processors.remove("news")
  default_extra_processors.remove("pdn")
  default_extra_processors.remove("push")
  default_extra_processors.remove("related")
  default_extra_processors.remove("subdivision")

if use_pb319:
  default_extra_processors.remove("ksib_mix_rank")


extra_processor_str = " ".join(default_extra_processors)
extra_processor_str = os.environ.get("COMMON_RECO_LEAF_EXTRA_PROCESSORS", extra_processor_str)
extra_processor_str = os.environ.get("DRAGON_EXT", extra_processor_str)
target_extra_processors = set(extra_processor_str.split())

cc_library(
  name = "mio_embedding",
  srcs = [
    "processor/ext/mio/enricher/embedding_cache.cc",
    "processor/ext/mio/enricher/embedding_cache_v2.cc",
  ],
  cppflags = [
    "-DUNI_PREDICT_V2" if "uni_predict_v2" in target_extra_processors else "",
  ] + cppflags,
  deps = [
    "//base/thread/BUILD:thread",
    "//teams/reco-arch/colossusdb/client/BUILD:embedding_client",
  ],
)

exclude_processor_str = ""
exclude_processor_str = os.environ.get("COMMON_RECO_LEAF_EXCLUDE_PROCESSORS", exclude_processor_str)
exclude_processor_str = os.environ.get("DRAGON_EXT_EXCLUDE", exclude_processor_str)
exclude_extra_processors = set(exclude_processor_str.split())

append_processor_str = os.environ.get("DRAGON_EXT_APPEND", "")
append_extra_processors = set(append_processor_str.split())

# 保证 ann_client 和 (local_ann, gpu_local_ann) 至少一个存在
if target_extra_processors and not (target_extra_processors & {"ann_client", "local_ann", "gpu_local_ann"}):
  target_extra_processors.add("ann_client")

if os.environ.get("COMMON_LEAF_PROD_BUILD", "false") == "true":
  # 这些模块很多人没有代码权限, 只在线上 build 中加入
  for mod in ["mio", "colossus_client", "kgnn"]:
    target_extra_processors.add(mod)

if os.environ.get("AD_BS_PROCESSOR", "false") == "true":
  # ad 的 batched samples 相关 processor, 仅打开时加入
  for mod in ["ad"]:
    target_extra_processors.add(mod)

if os.environ.get("BUILD_AD_TWINS_TOWER_WITH_DRAGON", "false").lower() in ["true", 1] and "ad_uni_predict_fused" not in target_extra_processors:
  target_extra_processors.add("ad_uni_predict_fused")

target_extra_processors.update(append_extra_processors)
target_extra_processors.difference_update(exclude_extra_processors)
# XXX(fangjianbing): mio_rpc 已被从 extra processors 中删掉, 但大量 custom.sh 中还保留了对 mio_rpc 的引入, 这里特殊处理忽略掉
if "mio_rpc" in target_extra_processors:
  target_extra_processors.remove("mio_rpc")

if build_ad_with_dragon or not use_compatible_abseil:
  if "se_reco" in target_extra_processors:
    target_extra_processors.remove("se_reco")

if os.environ.get("DRAGON_PARTITION_SERVER", "false") == "true":
  for mod in ["partition_lib", "local_ann"]:
    target_extra_processors.add(mod)

def gen_processor_name(x, print_name=False):
  if print_name:
    print("[Dragon Build] compile ext processor module: {}".format(x))
  return "{}_processors".format(x)

if module_to_files:
  target_extra_processors &= set(module_to_files.keys())
  target_extra_processors.discard("common_processors")

# 设置为 false 来修复电商对编译依赖顺序敏感的问题
extra_processor_deps = map(lambda x: ":{}".format(gen_processor_name(x, True)), target_extra_processors)
if os.environ.get("DRAGON_SORT_EXT_DEPS", "true") == "true":
  extra_processor_deps = sorted(extra_processor_deps)

cc_library(
  name = "extra_processors",
  srcs = [],
  deps = extra_processor_deps
)

available_extra_processors = set()


def def_extra_processor(name, **kwargs):
  global cppflags
  global target_extra_processors
  global gen_processor_name
  global available_extra_processors
  global module_to_files
  available_extra_processors.add(name)
  if name in target_extra_processors:
    if "cppflags" not in kwargs:
      kwargs["cppflags"] = cppflags
    if "deps" not in kwargs:
      kwargs["deps"] = []
    if ":common_processors" not in kwargs["deps"]:
      kwargs["deps"].append(":common_processors")
    if name in module_to_files and "srcs" in kwargs:
      processor_types = ['arranger', 'enricher', 'observer', 'retriever', 'mixer']
      to_remove = []
      for file_path in kwargs["srcs"]:
        for pro_type in processor_types:
          if file_path.startswith('processor/ext/' + name + '/' + pro_type + '/'):
            to_remove.append(file_path)
      for file_path in to_remove:
        kwargs["srcs"].remove(file_path)
      kwargs["srcs"] += sorted([str(i) for i in module_to_files[name] if str(i) not in kwargs["srcs"]])
    return cc_library(
      name=gen_processor_name(name),
      link_all_symbols=True,
      **kwargs
    )

def_extra_processor(
  name = "colossus_client",
  srcs = [
    "processor/ext/colossus/retriever/common_reco_colossus_retriever.cc",
  ],
  deps = [
    "//teams/reco-arch/colossus/BUILD:client",
  ],
)

tensorflow_deps = []
eigen3_lib = ""
if use_tf_2:
  tf_version = os.getenv("TF_VERSION", "2.4")
  if tf_version.startswith("2.10"):
    eigen3_lib = "//third_party/tensorflow-2.10.0/BUILD:eigen"
    if enable_mio_tf_gpu:
       tensorflow_deps = ["//third_party/tensorflow-2.10.0/BUILD:tensorflow_cc_gpu"]
    else:
      tensorflow_deps = ["//third_party/tensorflow-2.10.0/BUILD:tensorflow_cc"]
  else:
    eigen3_lib = "//third_party/tensorflow-2.4.1/BUILD:eigen"
    if enable_mio_tf_gpu:
       tensorflow_deps = ["//third_party/tensorflow-2.4.1/BUILD:tensorflow_cc_gpu_all"]
    else:
      tensorflow_deps = ["//third_party/tensorflow-2.4.1/BUILD:tensorflow_cc"]
elif use_tf_15:
  eigen3_lib = "//third_party/tf_15_eigen3/BUILD:eigen"
  if enable_mio_tf_gpu:
    tensorflow_deps = ["//third_party/tensorflow-1.15/BUILD:tensorflow_cc_gpu"]
  else:
    tensorflow_deps = ["//third_party/tensorflow-1.15/BUILD:tensorflow_cc"]
else:
  eigen3_lib = "//third_party/eigen3/BUILD:eigen"
  if enable_mio_tf_gpu:
    tensorflow_deps = [
      "//third_party/tensorflow-gpu/BUILD:tensorflow_cc_gpu",
      "//third_party/tensorflow-gpu/BUILD:tf_contrib"
    ]
  else:
    tensorflow_deps = [
      "//third_party/tensorflow/BUILD:tensorflow_cc",
      "//third_party/tensorflow/BUILD:tf_contrib",
    ]

def_extra_processor(
  name = "tensorflow",
  srcs = [
    "processor/common/enricher/common_reco_tensorflow_attr_enricher.cc",
  ],
  deps = tensorflow_deps
)

def_extra_processor(
  name = "tf_local_predict",
  srcs = [
    "processor/common/enricher/common_reco_tf_local_predict_enricher.cc",
  ],
  deps = [
    "//third_party/tensorflow/BUILD:tensorflow_cc",
    "//third_party/tensorflow/BUILD:tf_contrib",
    "//learning/kuiba_tf/BUILD:kuiba_tf_op_infer_server",
  ],
)

def_extra_processor(
  name = "tf_serving",
  srcs = [
    "processor/common/enricher/common_reco_tf_serving_predict_enricher.cc",
  ],
  deps = [
    "//learning/kuiba/predict_base/BUILD:tf_serving_predict_client",
  ],
)

def_extra_processor(
  name = "python",
  srcs = [
    "processor/common/enricher/common_reco_python_attr_enricher.cc",
  ],
  deps = [
    "//third_party/pybind11/BUILD:pybind11",
  ],
  cppflags = [
    "-Ithird_party/prebuilt/include/python3.6m/",
  ] + cppflags,
)

def_extra_processor(
  name = "local_ann",
  srcs = [
    "processor/common/retriever/common_reco_local_ann_retriever.cc",
    "processor/common/enricher/common_reco_local_ann_embedding_attr_enricher.cc",
  ],
  deps = [
    "//ks/common_reco/ann_retrieve/server/BUILD:ann_retrieve",
    "//serving_base/ann/cpu/BUILD:ann",
  ],
)

def_extra_processor(
  name="gpu_local_ann",
  srcs=[
    "processor/common/retriever/common_reco_local_ann_retriever.cc",
    "processor/common/enricher/common_reco_local_ann_embedding_attr_enricher.cc",
  ],
  deps=[
    "//ks/common_reco/ann_retrieve/server/BUILD:ann_retrieve",
    "//serving_base/ann/gpu/BUILD:ann",
  ],
)

def_extra_processor(
  name = "ann_client",
  srcs = [
    "processor/common/retriever/common_reco_embedding_retriever.cc",
  ],
  deps = [
    "//learning/kuiba/predict_base/BUILD:common_predict_client",
    "//ks/common_reco/ann_retrieve/client/BUILD:client",
  ],
)

#NOTE(zhaoyang09): 解决 ad 与 se 中 ad_common_enums__proto 编译冲突
gsu_more_excludes = ["processor/ext/gsu/enricher/picasso_ad_sim_enricher.cc"]
gsu_more_deps = []
if build_ad_with_dragon or reco_infer_for_ad:
  gsu_more_excludes = []
  gsu_more_deps = ["//teams/ad/picasso/BUILD:picasso_sdk"]

def_extra_processor(
  name = "gsu",
  srcs = [
    "processor/ext/gsu/enricher/*.cc",
    "processor/ext/gsu/retriever/*.cc",
    "processor/ext/gsu/util/*.cc",
  ],
  excludes = ["processor/ext/gsu/enricher/*_test.cc",
      ] + gsu_more_excludes,
  cppflags = cppflags + ["-std=c++17"],
  deps = [
    "//ks/reco_proto/reco_base/BUILD:reco_base",
    "//ks/util/BUILD:util",
    "//base/thread/BUILD:thread",
    "//base/encoding/BUILD:encoding",
    "//base/hash_function/BUILD:hash_function",
    "//base/file/BUILD:file",
    "//infra/utility/BUILD:string_utils",
    "//serving_base/server_base/BUILD:server_base",
    "//third_party/boost/BUILD:boost",
    "//teams/reco-arch/colossus/BUILD:client",
    "//third_party/utf8proc/BUILD:utf8proc",
    "//teams/reco-arch/colossus/BUILD:checkpoint_helper",
    "//teams/reco-arch/colossus/BUILD:common",
    "//ks/reco_proto/proto/BUILD:merchant_profile",
    "//teams/reco-arch/colossusdb/client/BUILD:sim_client",
    "//teams/reco-arch/colossusdb/client/BUILD:kconf_client",
    "//infra/dynamic_kafka_client/BUILD:dynamic_kafka_client",
    "//teams/reco-arch/colossusdb/sim/BUILD:checkpoint",
    "//teams/reco-arch/colossusdb/sim/BUILD:item",
    "//teams/reco-arch/colossusdb/clotho-sdk/cpp/BUILD:clotho_gateway_sdk",
  ] + gsu_more_deps,
)

def_extra_processor(
  name = "nearby",
  srcs = [
    "processor/ext/nearby/enricher/*.cc",
    "processor/ext/nearby/arranger/*.cc",
    "processor/ext/nearby/retriever/*.cc",
    "processor/ext/nearby/module/*.cc",
    "processor/ext/nearby/module/**/*.cc",
    "processor/ext/nearby/util/*.cc",
  ],
  deps = [
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/hash_function/BUILD:hash_function",
    "//infra/ccbase/BUILD:ccbase",
    "//infra/geoinfo/BUILD:geoinfo",
    "//third_party/glog/BUILD:glog",
    "//third_party/boost/BUILD:boost",
    "//teams/reco-arch/colossus/BUILD:client",
    "//third_party/utf8proc/BUILD:utf8proc",
    "//teams/reco-arch/colossus/BUILD:checkpoint_helper",
    "//teams/ad/picasso/BUILD:picasso_sdk",
    "//ks/reco_proto/proto/BUILD:nearby_retrieval_service",
    "//ks/realtime_reco/util/BUILD:util",
    "//teams/aiplatform/inference_sdk/src/BUILD:client_library",
    "//ks/reco_pub/reco/model_rank_score/BUILD:model_rank_score",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "rfm",
  srcs = [
    "processor/ext/rfm/enricher/*.cc",
    "processor/ext/rfm/util/*.cc",
   # "processor/ext/rfm/retriever/*.cc",
  ],
  deps = [
    "//teams/reco-arch/colossus/BUILD:client",
    "//third_party/utf8proc/BUILD:utf8proc",
    "//teams/reco-arch/colossus/BUILD:checkpoint_helper",
    "//teams/ad/picasso/BUILD:picasso_sdk",
    "//ks/reco_proto/live/BUILD:proto",
  ],
  cppflags = [
    "-std=gnu++17",
  ],
)

def_extra_processor(
  name = "nr",
  srcs = [
    "processor/ext/nr/arranger/*.cc",
    "processor/ext/nr/enricher/*.cc",
    "processor/ext/nr/retriever/*.cc",
    "processor/ext/nr/observer/*.cc",
    "processor/ext/nr/module/*.cc",
    "processor/ext/nr/module/**/*.cc",
    "processor/ext/nr/util/*.cc",
  ],
  deps = [
    "//infra/ccbase/BUILD:ccbase",
    "//infra/kenv/BUILD:kenv",
    "//teams/reco-arch/colossus/BUILD:client",
    "//third_party/utf8proc/BUILD:utf8proc",
    "//ks/reco_proto/proto/BUILD:causal_param_proto",
    "//ks/reco/collab_filter/graph_collab_retrieval_server/BUILD:graph_collab_service",
    "//ks/reco/longterm_icf/BUILD:proto",
    "//ks/reco/pandora/proto/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:reco_rank_ncee_score",
    "//infra/dynamic_kafka_client/BUILD:dynamic_kafka_client",
    "//ks/reco_proto/proto/BUILD:reco_nr",
    eigen3_lib,
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "slide",
  srcs = [
    "processor/ext/slide/enricher/*.cc",
    "processor/ext/slide/arranger/*.cc",
    "processor/ext/slide/util/*.cc",
  ],
  deps = [
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//third_party/jsoncpp/BUILD:jsoncpp",
    "//third_party/folly/BUILD:folly",
    "//third_party/abseil/BUILD:abseil",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    "//ks/base/abtest2/BUILD:abtest2_proto",
  ],
)

def_extra_processor(
  name = "qiliang",
  srcs = [
    "processor/ext/qiliang/enricher/*.cc",
    "processor/ext/qiliang/arranger/*.cc",
    "processor/ext/qiliang/util/*.cc",
  ],
  deps = [
    "//infra/geoinfo/BUILD:geoinfo",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//third_party/jsoncpp/BUILD:jsoncpp",
  ]
)

def_extra_processor(
  name = "embed_calc",
  srcs = [
    "processor/ext/embed_calc/enricher/*.cc",
    "processor/ext/embed_calc/retriever/*.cc",
  ],
  excludes = embed_calc_excludes,
  deps = embed_calc_deps,
)

def_extra_processor(
  name = "embedding",
  srcs = [
    "processor/ext/embedding/enricher/*.cc",
  ],
  deps = [
    ":mio_embedding",
    "//teams/reco-arch/colossusdb/client/BUILD:embedding_client",
  ],
)

def_extra_processor(
  name = "swing",
  srcs = [
    "processor/ext/swing/enricher/*.cc",
  ],
  deps = [
    "//third_party/folly/BUILD:folly",
  ]
)

# ks/reco_proto/mix_rank/BUILD:mix_rank is conflict with ad proto,
# so exclude it and the processors use it when build_ad_with_dragon is true.
def_extra_processor(
  name = "merchant",
  srcs = [
    "processor/ext/merchant/enricher/*.cc",
    "processor/ext/merchant/retriever/*.cc",
    "processor/ext/merchant/arranger/*.cc",
    "processor/ext/merchant/observer/*.cc",
    "processor/ext/merchant/mixer/*.cc",
    "processor/ext/merchant/util/*.cc",
    "processor/ext/merchant/util/RecallWithEarlyStop/src/*.cc",
    "processor/ext/merchant/common/*.cc",
  ],
  excludes = [
    "processor/ext/merchant/util/*_test.cc",
    "processor/ext/merchant/retriever/uni_recall_result_retriever.cc",
    "processor/ext/merchant/enricher/full_recall_map_item_id_to_gather_index_enricher.cc",
    "processor/ext/merchant/enricher/inc_updater_enricher.cc",
  ] + ([
    "processor/ext/merchant/enricher/merchant_mix_ad_xtr_enricher.cc",
    "processor/ext/merchant/enricher/merchant_mix_ad_trace_log_enricher.cc",
    "processor/ext/merchant/enricher/merchant_follow_cross_feature_enricher.cc",
    "processor/ext/merchant/enricher/merchant_get_preview_adinfo_enricher.cc",
    "processor/ext/merchant/enricher/merchant_set_adresult_info_enricher.cc",
  ] if build_ad_with_dragon else []),
  deps = [
    "//base/strings/BUILD:strings",
    "//serving_base/server_base/BUILD:server_base",
    "//third_party/brpc-0.9.6/BUILD:brpc",
    "//ks/reco/plateco-dsalog/ics/shop_customer_service/BUILD:proto",
    "//ks/merchant/BUILD:gbdt_predictor",
    "//ks/merchant/BUILD:merchant_profile_client",
    "//ks/reco_proto/gpu_retr/BUILD:proto",
    "//ks/reco_proto/merchantreco/BUILD:merchantreco",
    "//ks/ds/proto/BUILD:userprofile_proto",
    "//ks/reco_proto/cofea/rodis/BUILD:rodis_ltv_proto",
    "//teams/aiplatform/inference_sdk/src/BUILD:client_library",
    "//teams/reco-arch/colossus/BUILD:client",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    ":util",
    "//ks/reco/merchant_seller_service/BUILD:merchant_bid_service",
    "//ks/merchant/BUILD:merchant_strategy__proto",
    "//ks/reco_proto/proto/BUILD:mmu_commodity_retrieval_server",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_reco_common__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_browseset_reco_browse_set__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:reco_reco_user_profile_base__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_leaf_info__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_info__proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_quality_photo_info__proto",
    "//ks/reco_proto/ad/ad_proto/common/BUILD:common",
    "//ks/reco_proto/action/BUILD:sample_list_service",
    eigen3_lib,
  ] + ([] if build_ad_with_dragon else ["//ks/reco_proto/mix_rank/BUILD:mix_rank",]),
  cppflags = cppflags + ["-std=gnu++17"],
)

def_extra_processor(
  name = "kongming_metric",
  srcs = [
    "processor/ext/kongming_metric/observer/*.cc",
    "processor/ext/kongming_metric/enricher/*.cc",
    "processor/ext/kongming_metric/util/*.cc",
  ],
  excludes = ["processor/ext/kongming_metric/observer/*_test.cc"],
  deps = [
    "//ks/reco/metric_reporter/BUILD:metric_reporter",
    "//ks/merchant/BUILD:epcenter_export__proto",
    "//ks/reco/merchant_ep_center/BUILD:merchant_ep",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

def_extra_processor(
  name = "merchant_arch",
  srcs = [
    "processor/ext/merchant_arch/arranger/*.cc",
    "processor/ext/merchant_arch/common/*.cc",
    "processor/ext/merchant_arch/enricher/*.cc",
  ],
  excludes = [
    "processor/ext/merchant_arch/arranger/*_test.cc",
    "processor/ext/merchant_arch/*_test.cc",
    "processor/ext/merchant_arch/enricher/*_test.cc",
  ],
  deps = [
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

def_extra_processor(
  name = "mio_trt",
  srcs = [
    "processor/ext/mio/enricher/tensorrt_predict_item_attr_enricher.cc",
  ],
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
    "-DUSE_TENSORRT_PREDICT=1",
    "-DGOOGLE_LOGGING",
  ] + cppflags,
  deps = [
    "//ks/reco_proto/proto/BUILD:predict_kess_service",
    "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
  ],
)

mio_embedding_use_kvs = os.getenv("MIO_EMBEDDING_USE_KVS", "true") == "true"
mio_depends = []
mio_feature_dep = ["//ks/reco_pub/reco/feature/BUILD:feature"]  if use_ks_reco_feature else ["//teams/reco-model/base/feature/sign/BUILD:sign_feature","//teams/reco-model/base/feature/dense/BUILD:dense_feature"]
if reco_infer_for_ad:
  mio_depends = [
    "//ks/reco_proto/proto/BUILD:predict_kess_service",
    "//ks/reco_pub/embedding_util/BUILD:emb_compressor",
    "//base/thread/BUILD:thread",
    "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
    "//third_party/hetero_acc/kvs/BUILD:kvs_client" if mio_embedding_use_kvs else "",
    "//teams/reco-arch/colossus/BUILD:feature_client",
    "//teams/reco-arch/colossus/BUILD:proto",
    "//teams/reco-arch/colossusdb/client/BUILD:embedding_client",
    "//third_party/redis_cache_ext/BUILD:redis_cache_ext",
    "//serving_base/hdfs_read/BUILD:hdfs_read",
    "//base/file/BUILD:file",
    "//third_party/yaml-cpp/BUILD:yaml-cpp"
  ] + mio_feature_dep
else:
  mio_depends = mio_feature_dep + [
    "//ks/reco_proto/proto/BUILD:predict_kess_service",
    "//ks/reco_proto/gpu_retr/BUILD:proto",
    "//ks/reco_pub/embedding_util/BUILD:emb_compressor",
    "//base/thread/BUILD:thread",
    # "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
    "//third_party/hetero_acc/kvs/BUILD:kvs_client" if mio_embedding_use_kvs else "",
    "//teams/reco-arch/colossus/BUILD:feature_client",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:ksib_proto",
    "//teams/reco-arch/colossus/BUILD:proto",
    "//teams/reco-arch/colossusdb/client/BUILD:embedding_client",
    "//third_party/redis_cache_ext/BUILD:redis_cache_ext",
    "//serving_base/hdfs_read/BUILD:hdfs_read",
    "//base/file/BUILD:file",
    "//third_party/yaml-cpp/BUILD:yaml-cpp"]
  if not fix_kai_duplicate_op:
    mio_depends += [
      "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
      "//teams/reco-model/base/mio-tensorflow/BUILD:executor",
      "//third_party/yaml-cpp/BUILD:yaml-cpp"
    ]
mio_depends += ["//teams/reco-model/base/mio-benchmark/BUILD:proto"] if use_tf_15 else []

mio_excludes = []
if reco_infer_for_ad:
    mio_excludes = ["processor/ext/mio/enricher/tensorrt_predict_item_attr_enricher.cc",
                    "processor/ext/mio/enricher/uni_predict_item_attr_enricher.cc",
                    "processor/ext/mio/retriever/uni_live_retriver.cc",
                    "processor/ext/mio/enricher/mio_update_commonspec_enricher.cc",
                    "processor/ext/mio/enricher/kai_predict_item_attr_enricher.cc" if not use_tf_15 else "",
                    "processor/ext/mio/enricher/mio_predict_item_attr_opt_enricher.cc" if not use_tf_15 else "",
                    "processor/ext/mio/retriever/mio_live_retriever.cc" if not enable_mio_tf_gpu else ""]
else:
     mio_excludes = ["processor/ext/mio/enricher/tensorrt_predict_item_attr_enricher.cc",
                     "processor/ext/mio/enricher/uni_predict_item_attr_enricher.cc",
                     "processor/ext/mio/retriever/uni_live_retriver.cc",
                     "processor/ext/mio/enricher/kai_predict_item_attr_enricher.cc" if not use_tf_15 else "",
                     "processor/ext/mio/enricher/mio_predict_item_attr_opt_enricher.cc" if not use_tf_15 else "",
                     "processor/ext/mio/retriever/mio_live_retriever.cc" if not enable_mio_tf_gpu else "",
                     ]
     if fix_kai_duplicate_op:
       mio_excludes += [
         "processor/ext/mio/enricher/mio_predict_item_attr_enricher.cc",
         "processor/ext/mio/enricher/kai_predict_item_attr_enricher.cc",
         "processor/ext/mio/retriever/mio_live_retriever.cc",
         "processor/ext/mio/enricher/mio_feature_embedding_attr_enricher.cc",
         "processor/ext/mio/enricher/mio_local_embedding_attr_enricher.cc",
         "processor/ext/mio/enricher/mio_local_embedding_attr_opt_enricher.cc",
         "processor/ext/mio/enricher/mio_embedding_attr_enricher.cc",
         "processor/ext/mio/enricher/mio_embedding_attr_opt_enricher.cc",
         "processor/ext/mio/enricher/mio_embedding_attr_lite_enricher.cc",
       ]

assert not ("uni_predict_v2" in target_extra_processors and "uni_predict" in target_extra_processors), "uni_predict_v2 and uni_predict can not be compiled at the same time"
def_extra_processor(
  name = "mio",
  srcs = [
    "processor/ext/mio/enricher/*.cc",
    "processor/ext/mio/retriever/*.cc",
  ],
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
    "-DUSE_TENSORRT_PREDICT=1" if "mio_trt" in target_extra_processors else "",
    "-DGOOGLE_LOGGING",
    "-DUSE_TF_15" if use_tf_15 else "",
    "-DMIO_EMBEDDING_USE_KVS" if mio_embedding_use_kvs else "",
    "-DUNI_PREDICT_V2" if "uni_predict_v2" in target_extra_processors else "",
  ] + cppflags,
  excludes = mio_excludes + [
    "processor/ext/mio/enricher/embedding_cache.cc",
    "processor/ext/mio/enricher/embedding_cache_v2.cc",
  ],
  deps = mio_depends + (["//ks/reco_pub/embedding_manager/BUILD:gpu_live_embedding_manager"] if enable_mio_tf_gpu else []) + ["//ks/ksib_reco/util/BUILD:util", ":mio_embedding"] + tensorflow_deps,
)

def_extra_processor(
  name = "snack_predict",
  srcs = [
    "processor/ext/snack/enricher/snack_fetch_user_info_enricher.cc",
    "processor/ext/snack/enricher/snack_fish_predict_item_attr_enricher.cc",
    "processor/ext/snack/enricher/snack_graph_enricher.cc",
    "processor/ext/snack/enricher/snack_patch_reco_result_enricher.cc",
    "processor/ext/snack/enricher/snack_patch_photo_info_enricher.cc",
    "processor/ext/snack/enricher/snack_user_repr_photo_enricher.cc",
    "processor/ext/snack/retriever/snack_per_random_retriever.cc"
  ],
  deps = [
    "//base/time/BUILD:time",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/server_base/BUILD:server_base",
    "//ks/ksib_reco/snack_proto/BUILD:snack_proto",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:snack_proto",
  ],
)

def_extra_processor(
  name = "snack",
  srcs = [
    "processor/ext/snack/enricher/*.cc",
    "processor/ext/snack/retriever/*.cc",
    "processor/ext/snack/observer/*.cc",
    "processor/ext/snack/util/*.cc",
  ],
  excludes = [
    "processor/ext/snack/enricher/snack_fetch_user_info_enricher.cc",
    "processor/ext/snack/enricher/snack_fish_predict_item_attr_enricher.cc",
    "processor/ext/snack/enricher/snack_graph_enricher.cc",
    "processor/ext/snack/enricher/snack_patch_reco_result_enricher.cc",
    "processor/ext/snack/enricher/snack_patch_photo_info_enricher.cc",
    "processor/ext/snack/enricher/snack_user_repr_photo_enricher.cc",
    "processor/ext/snack/retriever/snack_per_random_retriever.cc"
  ],
  deps = [
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//third_party/glog/BUILD:glog",
  ]
)

def_extra_processor(
  name = "oversea",
  srcs = [
    "processor/ext/oversea/retriever/*.cc",
    "processor/ext/oversea/enricher/*.cc",
    "processor/ext/oversea/arranger/*.cc",
    "processor/ext/oversea/observer/*.cc",
    "processor/ext/oversea/util/*.cc"
  ],
  cppflags = [
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
  ] + cppflags,
  deps = [
    "//base/thread/BUILD:thread",
    "//base/strings/BUILD:strings",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/jansson/BUILD:json",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:kwai_proto",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:ksib_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_ad_predict_service__proto",
    "//ks/ds/proto/BUILD:userprofile_proto",
    "//third_party/abseil/BUILD:abseil",
    "//teams/reco-model/base/feature/core/BUILD:ks_sign_feature_core",
    eigen3_lib,
    #"//ks/reco_pub/reco/feature/BUILD:feature" if use_ks_reco_feature else "//teams/reco-model/base/feature/feature/BUILD:feature",
  ],
  excludes = [
    "processor/ext/oversea/enricher/oversea_online_mio_feature_enricher.cc",
  ]
)

def_extra_processor(
  name = "kuiba",
  srcs = [
    "processor/ext/kuiba/enricher/*.cc",
    "processor/ext/kuiba/retriever/*.cc",
    "processor/ext/kuiba/observer/*.cc",
  ],
  excludes = [
    "processor/ext/kuiba/enricher/kuiba_predict_item_attr_enricher.cc",
    "processor/ext/kuiba/enricher/kuiba_calc_user_embedding_attr_enricher.cc",
  ],
  deps = [
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/time/BUILD:time",
    "//learning/kuiba/base/BUILD:base",
    "//ks/reco_pub/kuiba/parameter/BUILD:parameter",
    "//learning/kuiba/storage/BUILD:storage",
    "//ks/realtime_reco/kuiba/BUILD:kuiba",
    "//teams/ad/ad_nn/feature_extract/BUILD:slot_sign_remap_util",
  ],
)

def_extra_processor(
  name = "se_sug",
  srcs = [
    "processor/ext/se_sug/retriever/*.cc",
  ],
  excludes = [
    #"processor/ext/kuiba/enricher/kuiba_predict_item_attr_enricher.cc",
    #"processor/ext/kuiba/enricher/kuiba_calc_user_embedding_attr_enricher.cc",
  ],
  deps = [
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/time/BUILD:time",
    "//learning/kuiba/base/BUILD:base",
    "//ks/reco_pub/kuiba/parameter/BUILD:parameter",
    "//learning/kuiba/storage/BUILD:storage",
    "//ks/realtime_reco/kuiba/BUILD:kuiba",
  ],
)

def_extra_processor(
  name = "kuiba_tf",
  srcs = [
    "processor/ext/kuiba/enricher/kuiba_predict_item_attr_enricher.cc",
  ],
  deps = [
    "//learning/kuiba/tensorflow/predict/BUILD:tf_predict",
    "//ks/krp/c++/kuiba/kuiba_predict/BUILD:predict_service",
  ],
)

def_extra_processor(
  name = "kuiba_tower",
  srcs = [
    "processor/ext/kuiba/enricher/kuiba_calc_user_embedding_attr_enricher.cc",
  ],
  deps = [
    "//learning/kuiba/tensorflow/predict/BUILD:tf_predict",
    "//ks/krp/c++/kuiba/kuiba_predict/BUILD:predict_service",
  ],
)

mio_hdfs_deps = []
if build_mio_hdfs_with_dragon:
  mio_hdfs_deps = ["//teams/reco-model/train/mio-learner-tf/BUILD:mio_record_channel_observer"]

def_extra_processor(
  name = "offline",
  srcs = [
    "processor/ext/offline/enricher/*.cc",
    "processor/ext/offline/observer/*.cc",
    "processor/ext/offline/retriever/*.cc",
    "processor/ext/offline/util/*.cc",
  ],
  excludes = [
    "processor/ext/offline/enricher/*_test.cc",
  ],
  cppflags = [
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
    "-Wno-overloaded-virtual",
  ] + cppflags,
  deps = [
    "//serving_base/fault_tolerant/BUILD:reco_fault_tolerant",
    "//ks/common_reco/util/BUILD:lite_photo_map",
    "//ks/reco/needle/BUILD:needle",
    "//serving_base/utility/BUILD:buffered_btq_producer",
    "//serving_base/bt_shm_kv/BUILD:kess_service",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:ksib_proto",
    "//learning/kuiba/proto/BUILD:proto",
    "//teams/reco-model/serve/predict_dict/BUILD:proto",
    "//teams/reco-arch/colossus/BUILD:proto",
    "//teams/reco-arch/colossus/BUILD:client",
    "//teams/reco-arch/colossus/BUILD:checkpoint_helper",
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:arrow",
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:parquet",
    "//third_party/hdfscppclient/BUILD:libhdfs3",
    "//ks/reco_proto/proto/BUILD:reco_rl",

  ] + mio_feature_dep + mio_hdfs_deps,
)

def_extra_processor(
  name = "cofea",
  srcs = [
    "processor/ext/cofea/enricher/*.cc",
    "processor/ext/cofea/retriever/*.cc",
    "processor/ext/cofea/observer/*.cc",
  ],
  deps = [
    "//base/strings/BUILD:strings",
    "//base/encoding/BUILD:encoding",
    "//base/file/BUILD:file",
    "//serving_base/hdfs_read/BUILD:hdfs_read",
    "//third_party/boost/BUILD:boost",
    "//ks/cofea/BUILD:client",
    "//ks/reco_proto/feature_pipe/BUILD:proto",
    "//teams/reco-arch/colossus/BUILD:feature_client",
  ],
)

def_extra_processor(
  name = "explore",
  srcs = [
    "processor/ext/explore/arranger/*.cc",
    "processor/ext/explore/enricher/*.cc",
    "processor/ext/explore/retriever/*.cc",
    "processor/ext/explore/util/*.cc",
    "processor/ext/explore/module/*.cc",
    "processor/ext/explore/module/**/*.cc",
  ],
  deps = [
    "//ks/reco/common/retrieval_client/BUILD:user_info_custom_tailor",
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/itemcf/BUILD:itemcf_client",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    eigen3_lib,
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "explore_life",
  srcs = [
    "processor/ext/explore_life/arranger/*.cc",
    "processor/ext/explore_life/enricher/*.cc",
    "processor/ext/explore_life/observer/*.cc",
    "processor/ext/explore_life/module/**/*.cc",
    "processor/ext/explore_life/util/*.cc",
  ],
  deps = [
    "//ks/reco/common/retrieval_client/BUILD:user_info_custom_tailor",
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/itemcf/BUILD:itemcf_client",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    eigen3_lib,
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "slide_leaf",
  srcs = [
  ],
  deps = [
    "//slide-leaf-dragon/binary/BUILD:slide_leaf",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

# 电商rerank模块
def_extra_processor(
  name = "merchant_rerank_leaf",
  srcs = [
  ],
  deps = [
    "//merchant_rerank_leaf/binary/BUILD:merchant_rerank_leaf",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

# 电商rerank模块
def_extra_processor(
  name = "slide_merchant_mix_rank",
  srcs = [
  ],
  deps = [
    "//ks/reco/merchant-engine/merchant_strategy_platform/leaf/rerank_leaf/binary/BUILD:merchant_rerank_leaf",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

#
def_extra_processor(
  name = "reco_access_layer",
  srcs = [
  ],
  deps = [
    "//ks/reco/merchant-engine/merchant_strategy_platform/leaf/reco_access_layer/binary/BUILD:reco_access_layer",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

# NOTE(xudongyi) 仅单列近线策略分析项目独立流水线使用，其他业务请勿添加此依赖
def_extra_processor(
  name = "slide_nearline_analysis",
  srcs = [
  ],
  deps = [
    "//slide-nearline-analysis/binary/BUILD:slide_nearline_analysis",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "explore_model",
  srcs = [
    "processor/ext/explore_model/enricher/*.cc",
    "processor/ext/explore_model/retriever/*.cc",
    "processor/ext/explore_model/module/**/*.cc",
    "processor/ext/explore_model/util/*.cc",
  ],
  deps = [
    "//infra/perfutil/BUILD:perfutil"
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "explore_offline",
  srcs = [
    "processor/ext/explore_offline/enricher/*.cc",
    "processor/ext/explore_offline/util/*.cc",
  ],
  deps = [
    "//ks/reco/common/retrieval_client/BUILD:user_info_custom_tailor",
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/itemcf/BUILD:itemcf_client",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    eigen3_lib,
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "slide_onerec",
  srcs = [
  ],
  deps = [
    "//onerec_online/binary/BUILD:slide_onerec",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

se_parent_src = [
  "processor/ext/search/parent/common/*.cc",
  "processor/ext/search/parent/retriever/*.cc",
  "processor/ext/search/parent/enricher/*.cc",
  "processor/ext/search/parent/util/*.cc",
  "processor/ext/search/urs/goods/*.cc",
  "processor/ext/search/ufs/*.cc",
  "processor/ext/search/ufs/goods/enricher/*.cc",
]

se_parent_deps = [
  '//se/txt2vid_se/query/proto/BUILD:query_rewriter',
  '//se/txt2vid_se/query/proto/BUILD:mmu_video_search_rewriter',
  '//se/txt2vid_se/indexing/parent/internal/BUILD:rpc_client',
  '//se/txt2vid_se/proto/BUILD:general_servlet_rpc',
  '//se/txt2vid_se/query/rewriters/internal/BUILD:query_rewriter_util',
  '//se/txt2vid_se/util/dict_manager/BUILD:dict_manager',
  '//se/txt2vid_se/util/debug_logger/BUILD:debug_logger',
  '//se/txt2vid_se/util/data_client/BUILD:data_client',
  '//se/txt2vid_se/model/rkfs/src/proto/BUILD:r_kfs_service_proto',
  '//se/txt2vid_se/model/kfs/src/proto/BUILD:kfs_service_proto',
  '//se/txt2vid_se/ranking/proto/BUILD:ranking_doc_info',
  '//se/txt2vid_se/indexing/rank_index/manage/BUILD:multi_index_manager',
  '//third_party/taskflow2/BUILD:taskflow',
  '//se/txt2vid_se/query/utils/BUILD:segmenter',
  '//se/txt2vid_se/nlp/perks_tokenizer_word/BUILD:bert_tokenizer',
  '//se/txt2vid_se/nlp/tokenizer/BUILD:ks_tokenizer',
  '//third_party/se_hnswlib/BUILD:starry_hnswlib',
]


def_extra_processor(
  name = "search",
  srcs = [
    "processor/ext/search/base/*.cc",
    "processor/ext/search/retriever/*.cc",
    "processor/ext/search/enricher/*.cc",
    "processor/ext/search/util/*.cc",
    "processor/ext/search/arranger/*.cc",
  ] + (se_parent_src if use_compatible_abseil else []),
  deps = [
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/server_base/BUILD:server_base",
    "//third_party/boost/BUILD:boost",
    "//se/dedup/batch_retrieval/client/BUILD:se_gas_client",
    "//se/txt2vid_se/profile/client3/BUILD:client3-no-search-flag",
    '//infra/utility/BUILD:string_utils',
    '//base/strings/BUILD:strings',
    '//se/txt2vid_se/base/BUILD:base',
    '//third_party/flatbuffers/BUILD:flatbuffers',
    "//learning/kuiba/proto/BUILD:proto",
    '//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto',
    '//ks/reco_proto/common_reco/leaf/BUILD:common_reco_flatten_proto',
    '//ks/reco/bt_embedding_server/BUILD:proto',
    "//infra/kess_grpc-v1100/BUILD:kess-rpc",
    "//serving_base/util/BUILD:util",
    "//serving_base/server_base/BUILD:server_base",
    "//teams/reco-arch/colossus/BUILD:client",
    "//infra/perfutil/BUILD:perfutil",
    "//se/online_service/protos/BUILD:live_goods_feat",
    "//se/online_service/protos/BUILD:search_coupon_query",
    '//se/txt2vid_se/proto/BUILD:distribute_user_item_query_service',
    '//se/txt2vid_se/nlp/segmenter/public/BUILD:segmenter',
    '//se/txt2vid_se/nlp/perks_tokenizer_word/BUILD:bert_tokenizer',
    '//se/txt2vid_se/poi_search/proto/BUILD:poi_parent_log',
    "//teams/aiplatform/inference_sdk/src/BUILD:client_library",
    '//se/online_service/protos/BUILD:kwaishop_marketing_quote_item_service',
    eigen3_lib,
  ] + (se_parent_deps if use_compatible_abseil else []),
  cppflags=["-std=c++17"],
)

def_extra_processor(
  name = "subdivision",
  srcs = [
    "processor/ext/subdivision/arranger/*.cc",
    "processor/ext/subdivision/enricher/*.cc",
    "processor/ext/subdivision/retriever/*.cc",
    "processor/ext/subdivision/observer/*.cc",
  ],
  deps = [
    "//ks/common_reco/ann_retrieve/client/BUILD:client",
    "//ks/reco/tag/BUILD:tag",
    "//ks/reco_proto/tower/BUILD:proto",
    "//ks/common_reco/ann_retrieve/util/BUILD:util",
    "//ks/reco/neo/model_util/BUILD:feature_recolog_util",
    "//ks/reco_pub/reco/predict/base/BUILD:flags",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/kenv/BUILD:kenv",
    "//third_party/boost/BUILD:boost",
    "//serving_base/retrieval/BUILD:retrieval",
    "//se/txt2vid_se/vision_search/proto/BUILD:gas",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:rpc_result_proto",
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/deep_cross/BUILD:client",
    "//ks/reco/itemcf/BUILD:itemcf_service",
    "//ks/reco/itemcf/BUILD:itemcf_client",
    "//ks/reco/mcf_server/BUILD:client",
    "//ks/reco_pub/reco/model_rank_score/BUILD:model_rank_score",
    "//ks/reco/common/retrieval_client/BUILD:retrieve_client",
    "//se/dedup/batch_retrieval/proto/BUILD:mmu_batch_retrieval_proto",
    "//se/dedup/batch_retrieval/client/BUILD:se_gas_client",
    "//ks/reco/hh_online_u2u_server/BUILD:client",
    "//ks/reco/fm/fm_action_server/BUILD:fm_action_client",
    "//ks/reco_proto/mix_rank/BUILD:mix_rank",
    "//ks/ds/relation/relation_lib/proto/BUILD:relation_api_proto",
    "//ks/ds/relation/relation_lib/client/BUILD:relation_api_client",
    "//ks/merchant/BUILD:gbdt_predictor",
    "//teams/aiplatform/kml/predict_server/protos/BUILD:kml_predict_server_proto",
    "//ks/reco_proto/common_reco/leaf/BUILD:reco_third_retrieval_proto",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "nebula_rerank",
  srcs = [
    "processor/ext/nebula_rerank/enricher/*.cc",
    "processor/ext/nebula_rerank/observer/*.cc",
  ],
  deps = [
    "//ks/reco/tag/BUILD:tag",
    "//ks/reco_proto/tower/BUILD:proto",
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/kenv/BUILD:kenv",
    "//third_party/boost/BUILD:boost",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "gamora_rerank",
  srcs = [
    "processor/ext/gamora_rerank/enricher/*.cc",
    "processor/ext/gamora_rerank/util/*.cc",
  ],
  deps = [
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/encoding/BUILD:encoding",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/kenv/BUILD:kenv",
    "//third_party/boost/BUILD:boost",
    "//third_party/folly/BUILD:folly",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad_pack_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:rpc_result_proto",
    "//ks/reco_proto/mix_rank/BUILD:mix_rank",
    "//teams/reco-arch/kgnn/src/client/BUILD:rgraph_client",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "interest_explore",
  srcs = [
    "processor/ext/interest_explore/enricher/*.cc",
    "processor/ext/interest_explore/util/*.cc",
  ],
  deps = [
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/encoding/BUILD:encoding",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/kenv/BUILD:kenv",
    "//third_party/boost/BUILD:boost",
    "//third_party/folly/BUILD:folly",
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:photo_author_service",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "oversea_lr",
  srcs = [
    "processor/ext/oversea_lr/retriever/*.cc",
    "processor/ext/oversea_lr/enricher/*.cc",
  ],
  deps = [
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/deep_cross/BUILD:client",
    "//ks/ksib_reco/ksib_proto/proto/BUILD:kwai_proto",
    "//third_party/krp_faiss/BUILD:faiss",
  ],
)

def_extra_processor(
  name = "eyeshot",
  srcs = [
    "processor/ext/eyeshot/arranger/*.cc",
    "processor/ext/eyeshot/enricher/*.cc",
    "processor/ext/eyeshot/retriever/*.cc",
    "processor/ext/eyeshot/util/*.cc",
  ],
  deps = [
    "//ks/util/BUILD:util",
    "//base/time/BUILD:time",
    "//serving_base/server_base/BUILD:server_base",
    "//ks/reco/deep_cross/BUILD:nn_service",
    "//ks/reco/deep_cross/BUILD:client",
    "//ks/reco/itemcf/BUILD:itemcf_service",
    "//ks/reco/mcf_server/BUILD:client",
    "//ks/reco_pub/reco/model_rank_score/BUILD:model_rank_score",
    "//ks/reco/common/retrieval_client/BUILD:retrieve_client",
    "//se/dedup/batch_retrieval/client/BUILD:se_gas_client",
    "//ks/reco/hh_online_u2u_server/BUILD:client",
    "//ks/reco/fm/fm_action_server/BUILD:fm_action_client",
    "//ks/reco_pub/embedding_util/BUILD:emb_compressor",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "news",
  srcs = [
    "processor/ext/news/retriever/*.cc",
    "processor/ext/news/enricher/*.cc",
    "processor/ext/news/observer/*.cc"
  ],
  excludes = [
    "*_test.cc",
  ],
  deps = [
    "//ks/photo_store/BUILD:photo_store",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/ranking/BUILD:ranking",
    "//infra/perfutil/BUILD:perfutil",
    "//ks/ds/relation/relation_lib/client/BUILD:relation_api_client",
    "//ks/ds/relation/relation_lib/client/BUILD:kwaigraph_client",
    "//ks/ds/relation/relation_lib/proto/BUILD:ds_news_reco_proto",
    "//ks/ds/relation/relation_lib/proto/BUILD:ds_news_message_friend_reco_proto",
    "//ks/ds/relation/relation_lib/proto/BUILD:user_relation_photo_upload_service_proto",
    "//ks/realtime_reco/live_recommend/util/BUILD:util",
    "//ks/ds/relation/relation_lib/proto/BUILD:relation_exp_isotope_log_proto",
    "//teams/aiplatform/inference_sdk/src/BUILD:client_library",
    "//ks/ds/relation/relation_lib/client/BUILD:user_reco_api_client",
    "//ks/ds/relation/relation_lib/client/BUILD:friend_material_recommend_client",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
  ]
)

def_extra_processor(
  name = "user_reco",
  srcs = [
    "processor/ext/user_reco/retriever/*.cc",
    "processor/ext/user_reco/enricher/*.cc",
  ],
  excludes = [
    "*_test.cc",
  ],
  deps = [
    "//ks/photo_store/BUILD:photo_store",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/ranking/BUILD:ranking",
    "//infra/perfutil/BUILD:perfutil",
    "//ks/ds/relation/relation_lib/client/BUILD:relation_api_client",
    "//ks/ds/relation/relation_lib/client/BUILD:kwaigraph_client",
    "//ks/ds/relation/relation_lib/client/BUILD:user_reco_filter_client",
    "//ks/ds/relation/relation_lib/client/BUILD:user_reco_predict_client",
    "//ks/ds/relation/relation_lib/client/BUILD:user_reco_api_client",
    "//ks/ds/relation/relation_lib/client/BUILD:global_pymk_hate_filter_client",
    "//ks/ds/relation/relation_lib/proto/BUILD:relation_exp_isotope_log_proto",
    "//teams/aiplatform/inference_sdk/src/BUILD:client_library",
    "//ks/ds/relation/relation_lib/proto/BUILD:user_reco_feature_proto",
    "//ks/ds/relation/relation_lib/proto/BUILD:user_reco_graph_service",
    "//ks/ds/relation/relation_lib/client/BUILD:graph_client",
  ]
)

def_extra_processor(
  name = "relation_photo",
  srcs = [
    "processor/ext/relation_photo/retriever/*.cc",
    "processor/ext/relation_photo/enricher/*.cc",
  ],
  deps = [
    "//ks/photo_store/BUILD:photo_store",
    "//base/encoding/BUILD:encoding",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/traffic_record/BUILD:grpc_traffic_record",
    "//ks/ds/relation/relation_lib/client/BUILD:relation_feed_client",
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/common_reco/index/BUILD:common_query_client",
  ],
)

def_extra_processor(
  name = "kap",
  srcs = [
    "processor/ext/kap/observer/*.cc",
    "processor/ext/kap/enricher/*.cc",
    "processor/ext/kap/retriever/*.cc",
  ],
  deps = [
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:arrow",
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:parquet",
    "//teams/aiplatform/common_train_api/BUILD:api"
  ],
)

def_extra_processor(
  name = "push",
  srcs = [
    "processor/ext/push/enricher/*.cc",
    "processor/ext/push/arranger/*.cc",
    "processor/ext/push/util/*.cc",
  ],
  deps = [
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
  ]
)

def_extra_processor(
  name = "mem_retrieval",
  srcs = [
    "processor/ext/mem_retrieval/enricher/*.cc",
    "processor/ext/mem_retrieval/retriever/*.cc",
  ],
)

def_extra_processor(
  name = "related",
  srcs = [
    "processor/ext/related/enricher/*.cc",
    "processor/ext/related/retriever/*.cc",
    "processor/ext/related/arranger/*.cc"
  ],
  excludes = [
    "processor/ext/related/retriever/related_search_rpc_retriever.cc",
  ],
  deps=[
    "//ks/util/BUILD:util",
    "//base/strings/BUILD:strings",
    "//base/encoding/BUILD:encoding",
    "//serving_base/server_base/BUILD:server_base",
    "//third_party/boost/BUILD:boost",
    "//ks/reco/itemcf/BUILD:itemcf_service",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    #"//ks/reco_proto/proto/BUILD:reco_related_search_proto",
  ]
)

def_extra_processor(
  name = "mmu",
  srcs = [
    "processor/ext/mmu/retriever/*.cc",
    "processor/ext/mmu/enricher/*.cc",
  ],
  deps = [
    "//se/dedup/batch_retrieval/client/BUILD:mmu_batch_retrieval_client",
    "//se/dedup/batch_retrieval/client/BUILD:mmu_user_search_query_client",
    "//se/dedup/batch_retrieval/client/BUILD:mmu_related_video_trigger_client",
    "//se/dedup/batch_retrieval/client/BUILD:mmu_related_video_search_client"
  ],
)

def_extra_processor(
  name = "retrieval",
  srcs = [
    "processor/ext/retrieval/arranger/*.cc",
    "processor/ext/retrieval/enricher/*.cc",
    "processor/ext/retrieval/retriever/*.cc",
  ],
  deps = [
    "//base/random/BUILD:random",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
  ],
)

uni_predict_exclude_srcs = []
if use_gpu:
  uni_predict_exclude_srcs = []
else:
  uni_predict_exclude_srcs = [
    "processor/ext/uni_predict/retriever/*.cc",
    "processor/ext/uni_predict/enricher/live_retrieve_predict_item_attr_enricher.cc",
    "processor/ext/uni_predict/embedding_merger/batch_gpu_embedding_merger_v2.cc",
  ]

uni_predict_v2_exclude_srcs = []
if use_gpu:
  uni_predict_v2_exclude_srcs = []
else:
  uni_predict_v2_exclude_srcs = [
    "processor/ext/uni_predict_v2/retriever/*.cc",
    "processor/ext/uni_predict_v2/enricher/live_retrieve_predict_item_attr_enricher.cc",
    "processor/ext/uni_predict_v2/embedding_merger/batch_gpu_embedding_merger_v2.cc",
    "processor/ext/uni_predict_v2/dynamic_embedding_fetcher/*.cc",
  ]

def_extra_processor(
  name = "uni_predict",
  srcs = [
    "processor/ext/uni_predict/enricher/*.cc",
    "processor/ext/uni_predict/retriever/*.cc",
    "processor/ext/uni_predict/embedding_fetcher/*.cc",
    "processor/ext/uni_predict/embedding_merger/*.cc",
    "processor/ext/uni_predict/embedding_manager/*.cc",
  ],
  excludes = [
    "processor/ext/uni_predict/enricher/*_test.cc",
    "processor/ext/uni_predict/retriever/*_test.cc",
    "processor/ext/uni_predict/embedding_fetcher/*_test.cc",
    "processor/ext/uni_predict/embedding_merger/*_test.cc",
    "processor/ext/uni_predict/embedding_manager/*_test.cc",
  ] + uni_predict_exclude_srcs,
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
    "-DUSE_TENSORRT_PREDICT=1",
    "-DGOOGLE_LOGGING",
    "-DUSE_GPU" if use_gpu else "",
  ] + cppflags,
  deps = [
    "//teams/reco-arch/uni-predict/src/BUILD:uni_predict",
    "//ks/reco_proto/proto/BUILD:predict_kess_service",
    "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
    "//ks/reco/bt_embedding_server/BUILD:embedding_table",
    "//ks/common_reco/util/BUILD:embedding_table_util",
  ],
)


def_extra_processor(
  name = "uni_predict_v2",
  srcs = [
    "processor/ext/uni_predict_v2/enricher/*.cc",
    "processor/ext/uni_predict_v2/retriever/*.cc",
    "processor/ext/uni_predict_v2/embedding_fetcher/*.cc",
    "processor/ext/uni_predict_v2/dynamic_embedding_fetcher/*.cc",
    "processor/ext/uni_predict_v2/embedding_merger/*.cc",
    "processor/ext/uni_predict_v2/embedding_manager/*.cc",
    "processor/ext/uni_predict_v2/embedding_cache/*.cc",
  ],
  excludes = [
    "processor/ext/uni_predict_v2/enricher/*_test.cc",
    "processor/ext/uni_predict_v2/retriever/*_test.cc",
    "processor/ext/uni_predict_v2/embedding_fetcher/*_test.cc",
    "processor/ext/uni_predict_v2/embedding_merger/*_test.cc",
    "processor/ext/uni_predict_v2/embedding_manager/*_test.cc",
  ] + uni_predict_v2_exclude_srcs,
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-DCOMMON_LEAF_USE_KS_RECO_FEATURE" if use_ks_reco_feature else "",
    "-DUSE_TENSORRT_PREDICT=1",
    "-DGOOGLE_LOGGING",
    "-DUSE_GPU" if use_gpu else "",
  ] + cppflags,
  deps = [
    "//teams/reco-arch/uni-predict-v2/src/BUILD:uni_predict_v2",
    "//ks/reco_proto/proto/BUILD:predict_kess_service",
    "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
    "//ks/reco/bt_embedding_server/BUILD:embedding_table",
    "//ks/common_reco/util/BUILD:embedding_table_util",
  ],
)

def_extra_processor(
  name = "recollm",
  srcs = [
    "processor/ext/recollm/enrich/*.cc",
  ],
  deps = [
    "//teams/reco-arch/recollm/src/proto/BUILD:proto",
    "//infra/kess_grpc-v1100/BUILD:kess-framework",
  ],
  cppflags = ["-std=c++17"] + cppflags
)

def_extra_processor(
  name = "yuzhou",
  srcs = [
    "processor/ext/yuzhou/arranger/*.cc",
    "processor/ext/yuzhou/enricher/*.cc",
    "processor/ext/yuzhou/retriever/*.cc",
    "processor/ext/yuzhou/observer/*.cc",
  ],
  deps = [
    "//ks/reco_proto/new_prod/BUILD:wenyuan_proto",
    "//ks/reco_proto/new_prod/BUILD:music_proto",
    "//base/random/BUILD:random",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/hash_function/BUILD:hash_function",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/location/BUILD:location",
    "//serving_base/region/BUILD:region",
    "//infra/location/BUILD:location",
    "//third_party/jsoncpp/BUILD:jsoncpp",
  ]
)

def_extra_processor(
  name = "ug_rtb",
  srcs = [
    "processor/ext/ug_rtb/util/*.cc",
    "processor/ext/ug_rtb/arranger/*.cc",
    "processor/ext/ug_rtb/enricher/*.cc",
  ],
  deps = [
    "//serving_base/server_base/BUILD:server_base"
  ]
)

def_extra_processor(
  name = "se_video_parent",
  srcs = [
  ],
  deps = [
    ':common_processors',
    ':util',
    '//se/se_dragon/processors/BUILD:video_parent',
  ],
)

def_extra_processor(
  name = "ug_feature",
  srcs = [
    "processor/ext/ug_feature/util/*.cc",
    "processor/ext/ug_feature/enricher/*.cc",
  ],
  deps = [
    "//serving_base/server_base/BUILD:server_base",
    "//teams/reco-arch/colossusdb/clotho-sdk/cpp/BUILD:clotho_gateway_sdk",
    "//teams/reco-arch/colossusdb/flatkv/BUILD:flatkv_client",
    "//teams/reco-arch/colossusdb/union_proxy/BUILD:feasury_handler",
    "//serving_base/util/BUILD:util",
  ],
  cppflags=cppflags + ["-std=c++17"],
)

def_extra_processor(
  name = "video_cold_start",
  srcs = [
    "processor/ext/video_cold_start/retriever/*.cc",
    "processor/ext/video_cold_start/arranger/*.cc",
    "processor/ext/video_cold_start/enricher/*.cc",
    "processor/ext/video_cold_start/observer/*.cc",
  ],
  deps = [
   "//serving_base/server_base/BUILD:server_base",
    "//ks/reco/online_match/per_random_server/server/BUILD:per_random_client",
    "//base/zip/BUILD:zip"
  ],
)

def_extra_processor(
  name = "web",
  srcs = [
    "processor/ext/web/retriever/*.cc",
    "processor/ext/web/enricher/*.cc",
  ],
  deps = [
    "//third_party/boost/BUILD:boost",
    "//serving_base/server_base/BUILD:server_base",
    "//ks/reco_proto/proto/BUILD:reco_eyeshot_proto",
    "//ks/reco_proto/proto/BUILD:reco_web_follow_proto",
  ],
)

def_extra_processor(
  name = "livestream",
  srcs = [
    "processor/ext/livestream/retriever/*.cc",
    "processor/ext/livestream/util/*.cc",
    "processor/ext/livestream/enricher/*.cc",
    "processor/ext/livestream/arranger/*.cc",
    "processor/ext/livestream/observer/*.cc",
    "processor/ext/livestream/mixer/*.cc",
    "processor/ext/livestream/module/*.cc",
    "processor/ext/livestream/module/**/*.cc",
  ],
  deps = [
    "//ks/common_reco/ann_retrieve/util/BUILD:util",
    "//ks/util/BUILD:util",
    "//base/thread/BUILD:thread",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//serving_base/server_base/BUILD:server_base",
    "//ks/reco/itemcf/BUILD:itemcf_client",
    "//ks/reco/fm/fm_server/BUILD:fm_client",
    "//ks/reco/live_itemcf/BUILD:live_itemcf_client",
    "//ks/common_reco/ann_retrieve/client/BUILD:client",
    "//ks/reco/embedding_retr/BUILD:common_embedding_kess_client",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/reco_proto/proto/BUILD:realtime_live_reco_service",
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:athena_proto",
    "//ks/reco_proto/big_river/BUILD:big_river",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/reco_pub/reco/util/BUILD:util",
    "//ks/reco_pub/reco/grade_distribute/BUILD:grade_distribute",
    "//ks/reco_proto/cofea/rodis/BUILD:rodis_ltv_proto",
    "//ks/realtime_reco/ad_service_proto/BUILD:ad_service_proto",
  ],
)

def_extra_processor(
  name = "partlive",
  srcs = [
    "processor/ext/partlive/enricher/*.cc",
    "processor/ext/partlive/arranger/*.cc",
  ],
  deps = [
    "//ks/reco_pub/reco/tdm_retr/BUILD:client",
    "//base/random/BUILD:random",
    "//serving_base/server_base/BUILD:server_base",
    "//ks/reco_proto/proto/BUILD:proto",
    "//third_party/folly/BUILD:folly",
  ],
)

enable_new_faiss = os.environ.get("ENABLE_FAISS_GPU", "false") == "true"
tdm_use_gpu = os.getenv("TDM_USE_GPU", str(use_gpu)).lower() in ['true', '1']
if tdm_use_gpu:
  if enable_new_faiss:
    faiss_kmeans_dep = ["//third_party/faiss-1.7.3/BUILD:faiss"]
  else:
    faiss_kmeans_dep = ["//third_party/krp_faiss-gpu/BUILD:faiss",]
else:
  faiss_kmeans_dep = ["//third_party/krp_faiss/BUILD:faiss",]

def_extra_processor(
  name = "tdm",
  srcs = [
    "processor/ext/tdm/retriever/*.cc",
    "processor/ext/tdm/enricher/*.cc",
    "processor/ext/tdm/arranger/*.cc",
  ],
  excludes = [
    "processor/ext/tdm/retriever/*_test.cc",
  ],
  cppflags = [
    "-DUSE_GPU" if tdm_use_gpu else "",
  ] + cppflags,
  deps = [
    "//ks/reco_pub/reco/tdm_retr/BUILD:client",
    "//ks/reco_proto/tdm_retr/BUILD:proto",
    "//ks/reco_proto/tdm_retr/BUILD:tdm_proto",
    "//ks/serving_util/BUILD:kess_helper",
  ] + faiss_kmeans_dep,
)

if use_gpu:
  tdm_item_emb_dep = ["//ks/reco_pub/embedding_manager/BUILD:tdm_item_embedding_manager",]
else:
  tdm_item_emb_dep = []

def_extra_processor(
  name = "tdm_server",
  srcs = [
    "processor/ext/tdm_server/retriever/*.cc",
    "processor/ext/tdm_server/enricher/*.cc",
    "processor/ext/tdm_server/arranger/*.cc",
    "processor/ext/tdm_server/observer/*.cc",
  ],
  excludes = [
    "processor/ext/tdm_server/retriever/*_test.cc",
    "processor/ext/tdm_server/enricher/*_test.cc",
    "processor/ext/tdm_server/arranger/*_test.cc",
    "processor/ext/tdm_server/observer/*_test.cc",
  ],
  cppflags = [
    "-DUSE_GPU" if use_gpu else "",
  ] + cppflags,
  deps = [
    "//ks/reco_pub/reco/dynamic_photo_map/BUILD:dynamic_photo_map",
    "//ks/reco_pub/reco/tdm_retr/BUILD:client",
    "//ks/reco_proto/tdm_retr/BUILD:tdm_proto",
    "//ks/serving_util/BUILD:kess_helper",
    "//teams/reco-model/serve/predict_dict/BUILD:predict_dict",
    "//teams/reco-model/serve/tdm_project/tree_server/server/BUILD:common_reco_tree_server",
    "//teams/reco-model/serve/tdm_project/tree_calc_server/server/BUILD:common_reco_tree_calc_server",
    "//third_party/hdfscppclient/BUILD:libhdfs3",
    "//teams/reco-model/serve/tdm_project/tree_server/src/embedding_cache/BUILD:embedding_cache",
  ] + tdm_item_emb_dep,
)

def_extra_processor(
  name = "ks_follow_mix_rank",
  srcs = [
    "processor/ext/ks_follow_mix_rank/*.cc",
    "processor/ext/ks_follow_mix_rank/base/*.cc",
  ],
  deps = [
    "//ks/reco_proto/mix_rank/BUILD:mix_rank",
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:reco_rank_ncee_score",
    "//ks/reco/debug_system/BUILD:debug_system",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
  ]
)

def_extra_processor(
  name = "se_urs",
  srcs = [
  ],
  deps = [
    '//se/se_dragon/processors/BUILD:urs', # 代码在当前库里
  ],
)

def_extra_processor(
  name = "se_video_matchserver",
  srcs = [
  ],
  deps = [
    ':common_processors',
    ':util',
    '//se/se_dragon/processors/BUILD:video_matchserver',
  ],
)

def_extra_processor(
  name = "ks_mix_rank",
  srcs = [
    # "processor/ext/ks_mix_rank/retriever/*.cc",
    # "processor/ext/ks_mix_rank/enricher/*.cc",
    "processor/ext/ks_mix_rank/util/*.cc",
    # 分界线，以下是支持多个业务后新的目录划分
    "processor/ext/ks_mix_rank/base/*.cc",
    "processor/ext/ks_mix_rank/common_mix_ranker/*.cc",
    "processor/ext/ks_mix_rank/hot_mix_ranker/*.cc",
    "processor/ext/ks_mix_rank/slide_mix_ranker/*.cc",
    "processor/ext/ks_mix_rank/load_controller/*.cc"
  ],
  deps = [
    "//ks/util/BUILD:util",
    "//base/random/BUILD:random",
    "//base/thread/BUILD:thread",
    "//base/strings/BUILD:strings",
    "//base/time/BUILD:time",
    "//base/encoding/BUILD:encoding",
    "//serving_base/jansson/BUILD:json",
    "//serving_base/server_base/BUILD:server_base",
    "//infra/kenv/BUILD:kenv",
    "//infra/btq_client/BUILD:dynamic_bt_queue_client",
    "//third_party/boost/BUILD:boost",
    "//ks/reco_proto/mix_rank/BUILD:mix_rank",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad_info_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad_pack_proto",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:rpc_result_proto",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/reco_proto/proto/BUILD:reco_eyeshot_mix_proto",
    "//ks/reco/common/retrieval_client/BUILD:common_attr_helper",
    "//third_party/folly/BUILD:folly",
    "//ks/reco/sphinx/client/BUILD:sphinx_client",
    "//teams/reco-arch/colossus/BUILD:proto",
    "//ks/reco/sphinx/proto/BUILD:proto",
    "//ks/reco/memory_data_map/BUILD:memory_data_map"
  ],
)

def_extra_processor(
  name = "ks_slide_mix_rank",
  srcs = [
  ],
  deps = [
    "//kuaishou-slide-mix-rank/binary/BUILD:slide_mix_rank",
  ],
  cppflags=cppflags + ["-std=c++17"],
)


cc_library(
  name = "ad_union_utility",
  srcs = [
      "processor/ext/ad_union_train/enricher/remap_slot_sign_feature.cc"
    ],
  deps = [
    ":core",
    "//base/strings/BUILD:strings",
  ],
  link_all_symbols = True,
)

def_extra_processor(
  name = "ad_union_utility",
  srcs = [
  ],
  excludes = [],
  deps = [
    "//base/common/BUILD:base",
    ":ad_union_utility",
  ],
)
ad_union_train_deps = [":ad_union_utility"]
ad_union_train_excludes = []
ad_union_train_cppflags = []
if reco_infer_for_ad:
  ad_union_train_deps += [
    "//teams/ad/ad_proto/kuaishou/BUILD:dragon_ad_union_train_proto_hack",
    "//teams/ad/picasso/BUILD:picasso_sdk",
    "//teams/ad/ad_algorithm/feature/BUILD:fast_feature",
    "//base/encoding/BUILD:encoding",
    "//teams/reco-ad-union/ad_algorithm/feature/BUILD:opt_ad_log_wrapper",
    "//teams/reco-ad-union/ad_algorithm/pb_adaptor/BUILD:opt_pb_adaptor",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
    ]
  ad_union_train_excludes = [
    "processor/ext/ad_union_train/enricher/adlog_parameter_attr_enricher.cc",
    "processor/ext/ad_union_train/enricher/klearner_online_parameter_attr_enricher.cc",
    "processor/ext/ad_union_train/enricher/klearner_parameter_attr_enricher.cc",
  ]
  ad_union_train_cppflags =[
    "-DBUILD_AD_INFER_SERVER",
    "-std=gnu++17",
  ]
else:
  ad_union_train_deps += [
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:dragon_ad_union_train_proto",
    "//teams/reco-ad-union/ad_algorithm/feature/BUILD:fast_feature",
    "//ks/reco_proto/sample_log/BUILD:sample_log",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
  ]
  ad_union_train_excludes = [
    "processor/ext/ad_union_train/enricher/adlog_online_parameter_attr_enricher.cc",
    "processor/ext/ad_union_train/enricher/opt_adlog_online_parameter_attr_enricher.cc",
    "processor/ext/ad_union_train/observer/dump_online_ad_log_observer.cc"
  ]

def_extra_processor(
  name = "ad_union_train",
  srcs = [
    "processor/ext/ad_union_train/retriever/*.cc",
    "processor/ext/ad_union_train/enricher/*.cc",
    "processor/ext/ad_union_train/observer/*.cc",
  ],
  excludes = [
    "processor/ext/ad_union_train/arranger/*_test.cc",
    "processor/ext/ad_union_train/enricher/*_test.cc",
    "processor/ext/ad_union_train/observer/*_test.cc",
    "processor/ext/ad_union_train/retriever/*_test.cc",
    "processor/ext/ad_union_train/enricher/remap_slot_sign_feature.cc",
  ] + ad_union_train_excludes,
  deps = ad_union_train_deps,
  cppflags = ad_union_train_cppflags
)

def_extra_processor(
  name = "ad_gsu",
  srcs = [
    "processor/ext/ad/gsu/enricher/*.cc",
    "processor/ext/ad/gsu/retriever/*.cc",
    ],
  cppflags = [],
  excludes = [
    "processor/ext/ad/gsu/enricher/multimodal_seq_cosine_similarity_extract_enricher.cc",
    "processor/ext/ad/gsu/enricher/ad_common_picasso_enricher.cc",
    "processor/ext/ad/gsu/retriever/ad_common_picasso_enricher.cc",
    "processor/ext/ad/gsu/enricher/ad_live_v2_goods_click_seq_concurrent_enricher.cc"
  ],
  deps = [
    "//teams/ad/picasso/BUILD:picasso_sdk",
    "//teams/ad/ad_algorithm/live_model/BUILD:picasso_item_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_reco_service",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_sim_pb",
    "//ks/cofea/BUILD:proto",
    "//base/common/BUILD:base",
    "//base/strings/BUILD:strings",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
    "//teams/reco-arch/colossus/BUILD:client",
    ]
)

def_extra_processor(
  name = "ad_uni_predict_fused",
  srcs = [
    "processor/ext/uni_predict_ad/embedding_fetcher/*.cc",
    "processor/ext/uni_predict_ad/embedding_loader/*.cc",
    "processor/ext/uni_predict_ad/enricher/ad_uni_predict_fused_enricher.cc",
  ],
  cppflags = [
    "-DGOOGLE_LOGGING"
  ],
  deps = [
    "//ks/reco_proto/proto/BUILD:model",
    "//infra/falcon_counter/BUILD:falcon_counter",
    "//base/common/BUILD:base",
    "//base/file/BUILD:file",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_online_feature__proto",
    "//teams/ad/ad_nn/feature/BUILD:feature_file",
    "//teams/ad/ad_nn/model/BUILD:ad_uni_predict_embedding",
    "//teams/ad/ad_nn/common_idt_server/BUILD:common_idt",
    "//teams/reco-model/base/mio/mio/BUILD:archive",
    "//teams/reco-model/base/mio/mio/BUILD:fs",
    "//teams/reco-arch/embedding_manager/utils/BUILD:btq_wrapper",
    "//teams/reco-arch/embedding_manager/embedding_data/BUILD:embedding_store_manager",
    "//teams/reco-arch/uni-predict-v2/src/BUILD:uni_predict_v2",
  ],
)

def_extra_processor(
  name = "ad_twin_towers",
  srcs = [
    "processor/ext/ad/uni/twin_towers/result_handler/*.cc",
    "processor/ext/ad/uni/twin_towers/*.cc",
  ],
  cppflags = [
    "-DGOOGLE_LOGGING"
  ],
  deps = [
    ":util",
    "//third_party/glog/BUILD:glog",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
    "//teams/ad/ad_nn/utils/BUILD:utils",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_sim_pb",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_fea_util",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_log",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_fast_feature",
    "//teams/ad/ad_nn/feature/BUILD:feature_preprocessor",
    "//teams/ad/ad_nn/feature_store/BUILD:feature_store",
    "//teams/ad/ad_nn/user_info_merge/BUILD:get_bs_user_info",
    "//teams/ad/ad_nn/twin_towers_ps/BUILD:twin_towers_target",
    "//teams/ad/ad_nn/flatten_raw_feature/BUILD:flatten_helper",
    "//teams/ad/ad_nn/feature_extract/BUILD:ad_extract_framework",
    "//teams/ad/ad_nn/feature_extract/BUILD:bs_kafka_feature_handler",
    "//teams/ad/ad_nn/kconf_manager/BUILD:twin_towers_kconf_manager",
    "//teams/reco-arch/embedding_manager/embedding_data/BUILD:embedding_store_manager",
  ] + tensorflow_deps,
)

def_extra_processor(
  name = "ad",
  srcs = [
    "processor/ext/ad/retriever/*.cc",
    "processor/ext/ad/enricher/*.cc",
    "processor/ext/ad/common/retriever/*.cc",
    "processor/ext/ad/common/enricher/*.cc",
    "processor/ext/ad/bs_kafka_feature/enricher/*.cc",
  ],
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-Iteams/ad/ad_nn/feature_extract/",
  ],
  deps = [
    "//teams/ad/ad_nn/flatten_raw_feature/BUILD:flatten_helper",
    "//teams/ad/ad_nn/feature_extract/BUILD:ad_extract_framework",
    "//teams/ad/ad_nn/feature_extract/BUILD:bs_kafka_feature_handler",
    "//third_party/zeromq/BUILD:zeromq",
    "//third_party/yaml-cpp/BUILD:yaml-cpp",
    "//ks/reco_pub/reco/tdm_retr/BUILD:client",
    "//ks/reco_proto/tdm_retr/BUILD:proto",
    "//ks/reco_proto/tdm_retr/BUILD:tdm_proto",
    "//base/hash_function/BUILD:hash_function",
    "//ks/serving_util/BUILD:kess_helper",
    "//teams/ad/bucket_join/BUILD:bucket_join",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_fast_feature",
    "//teams/reco-model/base/mio/mio/BUILD:archive",
    "//teams/ad/ad_nn/service/BUILD:service_kconf",
    "//teams/ad/ad_algorithm/label_centre/BUILD:data_generate",
    "//teams/ad/ad_algorithm/label_centre/BUILD:label_centre",
  ],
)

ad_union_online_deps = []
if ad_kai_serving_rank:
  ad_union_online_deps += [
    "//teams/ad/ad_nn/feature_dragon/BUILD:feature_dragon",
  ]
def_extra_processor(
  name = "ad_union_online",
  srcs = [
    "processor/ext/ad/predict/observer/*.cc",
    "processor/ext/ad/predict/enricher/*.cc",
    "processor/ext/ad/common/retriever/*.cc",
    "processor/ext/ad/common/enricher/*.cc",
  ],
  excludes = [
    "processor/ext/ad/predict/enricher/ad_infer_result_enricher.cc",
    "processor/ext/ad/predict/enricher/ad_uni_predict_enricher.cc",
    "processor/ext/ad/predict/enricher/ad_uni_predict_internal_enricher.cc",
    "processor/ext/ad/predict/enricher/ad_uni_predict_fused_enricher.cc",
  ],
  deps = [
    "//teams/ad/ad_nn/router/utils/BUILD:router_utils",
    "//teams/ad/ad_nn/flatten_raw_feature/BUILD:adlog_helper",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_sim_pb",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_fea_util",
    "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_log",
    "//teams/ad/ad_nn/bs_field_helper/BUILD:bs_field_helper",
    "//teams/ad/ad_nn/feature_store/BUILD:feature_store",
    "//teams/ad/ad_algorithm/live_model/BUILD:picasso_item_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_reco_service",
    "//ks/cofea/BUILD:proto",
    "//base/common/BUILD:base",
    "//base/strings/BUILD:strings",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
    "//teams/ad/ad_nn/feature/BUILD:feature_preprocessor",
    "//teams/ad/ad_nn/flow_copier/BUILD:flow_copier",
    "//teams/ad/ad_algorithm/label_centre/BUILD:data_generate",
    "//teams/ad/ad_algorithm/label_centre/BUILD:label_centre",
    "//teams/ad/ad_nn/service/BUILD:debug_sample",
    "//teams/reco-arch/embedding_manager/embedding_data/BUILD:embedding_store_manager",
    "//teams/reco-arch/embedding_manager/utils/BUILD:btq_wrapper",
    ":ad_union_utility",
  ] + ad_union_online_deps,
  cppflags = ["-DGOOGLE_LOGGING"] + cppflags,
)

ad_uni_predict_deps = []
if ad_use_uni_predict_v2:
  ad_uni_predict_deps += [
    "//teams/reco-arch/uni-predict-v2/src/BUILD:uni_predict_v2",
  ]
else:
  ad_uni_predict_deps += [
    "//teams/reco-arch/uni-predict/src/BUILD:uni_predict",
  ]

def_extra_processor(
  name = "ad_uni_predict",
  srcs = [
    "processor/ext/ad/predict/enricher/ad_infer_result_enricher.cc",
    "processor/ext/ad/predict/enricher/ad_uni_predict_enricher.cc",
    "processor/ext/ad/predict/enricher/ad_uni_predict_internal_enricher.cc",
  ] + (["processor/ext/ad/predict/enricher/ad_uni_predict_fused_enricher.cc"] if ad_use_uni_predict_v2 else []),
  deps = [
    "//base/common/BUILD:base",
    "//base/strings/BUILD:strings",
    "//ks/cofea/BUILD:proto",
    "//teams/ad/ad_nn/model/BUILD:ad_uni_predict_embedding",
  ] + ad_uni_predict_deps,
  cppflags = [
    "-DUNI_PREDICT_V2" if ad_use_uni_predict_v2 else "",
    "-DGOOGLE_LOGGING",
    "-DUSE_GPU" if use_gpu else "",
  ] + cppflags,
)

def_extra_processor(
  name = "data_trans",
  srcs = [
    "processor/ext/data_trans/common/*.cc",
    "processor/ext/data_trans/observer/*.cc",
    "processor/ext/data_trans/enricher/*.cc",
  ],
  deps = [
    ":util",
    "//teams/ad/ad_nn/common_idt_server/BUILD:common_idt",
    "//teams/reco-arch/embedding_manager/embedding_data/BUILD:embedding_store_manager",
    "//teams/reco-arch/embedding_manager/message_queue/BUILD:mq",
    "//teams/reco-arch/embedding_manager/dumper/BUILD:matrix_dumper"
  ],
  cppflags = [
  "-DGOOGLE_LOGGING",
  "-DUNI_PREDICT_V2" if ad_use_uni_predict_v2 else "",
  ] + cppflags
)

data_trans_func_srcs = [
  "processor/ext/data_trans/common/functions.cc",
]
data_trans_func_deps = [
  ":util",
  "//teams/reco-arch/embedding_manager/embedding_data/BUILD:embedding_store_manager",
  "//teams/reco-arch/embedding_manager/message_queue/BUILD:mq",
]

def_extra_processor(
  name = "merchant_full_recall",
  srcs = [
    "processor/ext/data_trans/enricher/streaming_receiver_enricher.cc",
    "processor/ext/merchant/enricher/inc_updater_enricher.cc",
    "processor/ext/merchant/retriever/uni_recall_result_retriever.cc",
    "processor/ext/merchant/enricher/full_recall_map_item_id_to_gather_index_enricher.cc"
  ] + data_trans_func_srcs,
  deps = [
    "//teams/reco-arch/embedding_manager/dumper/BUILD:matrix_dumper",
    "//teams/reco-arch/uni-predict-v2/src/BUILD:uni_predict_v2"
  ] + data_trans_func_deps,
  cppflags = ["-DFULL_RECALL_INC_UPDATE_CACHE", "-std=gnu++17", "-DUNI_PREDICT_V2", "-DGOOGLE_LOGGING"] + cppflags,
)

def_extra_processor(
  name = "kgnn",
  srcs = [
    "processor/ext/kgnn/enricher/*.cc",
    "processor/ext/kgnn/observer/*.cc",
    "processor/ext/kgnn/util/*.cc"
  ],
  deps = [
    "//teams/reco-arch/kgnn/src/client/BUILD:rgraph_client",
    "//teams/reco-arch/kgnn/src/proto/BUILD:gnn_kv_service",
    "//teams/reco-arch/kgnn/src/base/BUILD:graph_base",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//teams/reco-arch/colossusdb/client/BUILD:kgnn_client",
    "//teams/reco-arch/colossusdb/partition_server/biz/kgnn/src/proto/BUILD:gnn_kv_service_proto",
    "//teams/reco-arch/colossusdb/partition_server/biz/kgnn/src/base/BUILD:graph_base",
  ],
  cppflags=cppflags + ["-std=gnu++17"],
)

def_extra_processor(
  name = "kkd",
  srcs = [
    "processor/ext/kkd/retriever/*.cc",
    "processor/ext/kkd/enricher/*.cc",
  ],
  deps = [
    "//base/encoding/BUILD:encoding",
    "//serving_base/server_base/BUILD:server_base",
    "//third_party/boost/BUILD:boost",
    "//ks/reco_proto/proto/BUILD:reco_kkd_service_proto",
    "//ks/reco_proto/proto/BUILD:kkd_itemcf_service",
    "//ks/reco_proto/proto/BUILD:kkd_grpc_u2u_service",
  ],
)

def_extra_processor(
  name = "kwai_tv",
  srcs = [
    "processor/ext/kwai_tv/enricher/*.cc",
    "processor/ext/kwai_tv/arranger/*.cc",
  ],
  deps = [
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//ks/ds/user_profile/rodis_client/BUILD:rodis_client",
    "//ks/reco_proto/new_prod/BUILD:kwai_tv_proto",
    eigen3_lib,
  ],
)

def_extra_processor(
  name = "kfs",
  srcs = [
    "processor/ext/kfs/enricher/*.cc",
  ],
  excludes = [
    # "processor/ext/kfs/enricher/jubao_enricher.cc",
    "processor/ext/kfs/enricher/kfs_feature_enricher.cc",
  ],
  deps = [
    "//base/file/BUILD:file",
    # "//se/txt2vid_se/model/rkfs/src/lib/BUILD:librkfs_deps",
    # "//se/txt2vid_se/model/rkfs/src/proto_lite/BUILD:r_kfs_service_proto",
    "//ks/serving_util/BUILD:kess_helper",
    "//se/txt2vid_se/model/kfs/src/proto/BUILD:feature_proto",
    "//se/txt2vid_se/profile/proto/BUILD:profile_kfs_feature",
    "//se/txt2vid_se/jubao/proto/BUILD:jubao_proto",
  ],
  cppflags = cppflags + ["-std=gnu++17"],
)

def_extra_processor(
  name = "pdn",
  srcs = [
    "processor/ext/pdn/enricher/*.cc",
    "processor/ext/pdn/observer/*.cc",
    "processor/ext/pdn/retriever/*.cc",
    "processor/ext/pdn/arranger/*.cc",
    "processor/ext/pdn/util/*.cc",
  ],
  deps = [
    "//base/thread/BUILD:thread",
    "//base/hash_function/BUILD:hash_function",
    "//ks/common_reco/common_kv/BUILD:proto",
    "//ks/reco_proto/proto/BUILD:merchant_profile",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/reco_proto/gpu_retr/BUILD:proto",
    "//ks/reco/plateco-dsalog/ics/shop_customer_service/BUILD:merchant_search_mmu_proto",
    "//ks/reco_proto/neg_sample_bank/BUILD:proto",
    "//teams/reco-arch/colossus/BUILD:client",
  ]
)

def_extra_processor(
  name = "se_reco",
  srcs = [
    "processor/ext/se_reco/arranger/*.cc",
    "processor/ext/se_reco/enricher/*.cc",
    "processor/ext/se_reco/retriever/*.cc",
    "processor/ext/se_reco/mixer/*.cc",
    "processor/ext/se_reco/util/*.cc",
    "processor/ext/se_reco/observer/*.cc",
  ],
  deps = [
    "//se/txt2vid_se/data_flow/proto/BUILD:data_flow_proto",
    "//se/txt2vid_se/util/cache2/BUILD:lru_cache",
    "//se/txt2vid_se/base/BUILD:base",
    "//se/txt2vid_se/profile/client3/BUILD:client3-no-search-flag",
    "//se/txt2vid_se/ziya_sort/src/utils/BUILD:protobuf_reg_mod",
    "//se/txt2vid_se/ziya_sort/src/base/BUILD:ziya_src_base_dragon",
    "//se/txt2vid_se/splatform/proto/BUILD:search_api",
    "//se/txt2vid_se/util/id_mapping_client/BUILD:id_mapping_client",
    "//third_party/brpc-0.9.6/BUILD:brpc",
    "//third_party/aws-cpp-sdk/BUILD:aws_cpp_sdk_s3",
  ],
  cppflags = cppflags + ["-std=gnu++17", "-DCONTEXT_NAMESPACE=combo_sugg"],
)

def_extra_processor(
  name = "se_reco_predict",
  srcs = [
    "processor/ext/se_reco_predict/enricher/*.cc",
    "processor/ext/se_reco_predict/util/*.cc",
  ],
  deps = [
    "//se/txt2vid_se/ziya_sort/src/base/BUILD:se_reco_predict_ziya_plugin",
  ],
  cppflags = cppflags + ["-std=gnu++17"],
)

if not use_pb319:
  def_extra_processor(
    name = "ksib_mix_rank",
    srcs = [
      "processor/ext/ksib_mix_rank/retriever/*.cc",
      "processor/ext/ksib_mix_rank/enricher/*.cc",
      "processor/ext/ksib_mix_rank/enricher/rank/*.cc",
      "processor/ext/ksib_mix_rank/arranger/*.cc",
      "processor/ext/ksib_mix_rank/utils/*.cc",
      "processor/ext/ksib_mix_rank/utils/common/*.cc",
      "processor/ext/ksib_mix_rank/utils/dynamic_adpos/*.cc",
    ],
    deps = [
      ":kwai_i18n_ad_proto",
      "//ks/util/BUILD:util",
      "//base/random/BUILD:random",
      "//base/thread/BUILD:thread",
      "//base/strings/BUILD:strings",
      "//base/time/BUILD:time",
      "//base/encoding/BUILD:encoding",
      "//ks/reco/memory_data_map/BUILD:memory_data_map",
      "//ks/base/perfutil/BUILD:perfutil",
      "//serving_base/jansson/BUILD:json",
      "//serving_base/server_base/BUILD:server_base",
      "//infra/kenv/BUILD:kenv",
      "//teams/ad/ad_base/src/auto_param/BUILD:auto_param",
    ],
  )

def_extra_processor(
  name = "arrow",
  srcs = [
    "processor/ext/arrow/enricher/*.cc",
    "processor/ext/arrow/retriever/*.cc",
    "processor/ext/arrow/observer/*.cc",
    "processor/ext/arrow/utils/*.cc",
  ],
  excludes=["processor/ext/arrow/enricher/*_test.cc"],
  cppflags = cppflags,
  deps = [
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:arrow",
    "//third_party/folly/BUILD:folly",
  ],
)

def_extra_processor(
  name = "arrow_for_ad",
  srcs = [
    "processor/ext/arrow/enricher/*.cc",
    "processor/ext/arrow/retriever/*.cc",
    "processor/ext/arrow/observer/*.cc",
    "processor/ext/arrow/utils/*.cc",
  ],
  excludes=[
    "processor/ext/arrow/utils/ad_label_protobuf_reg.inc.cc",
    "processor/ext/arrow/enricher/*_test.cc",
    "processor/ext/arrow/retriever/batch_row_retriever.cc",
    "processor/ext/arrow/enricher/batch_row_enricher.cc",
  ],
  cppflags = cppflags,
  deps = [
    "//third_party/apache-arrow/arrow-8.0.1/cpp/BUILD:arrow",
    "//third_party/folly/BUILD:folly",
  ],
)

def_extra_processor(
  name = "local_life",
  srcs = [
    "processor/ext/local_life/arranger/*.cc",
    "processor/ext/local_life/observer/*.cc",
    "processor/ext/local_life/enricher/*.cc",
    "processor/ext/local_life/mixer/*.cc",
    "processor/ext/local_life/retriever/*.cc",
    "processor/ext/local_life/module/*.cc",
    "processor/ext/local_life/module/**/*.cc",
    "processor/ext/local_life/util/*.cc",
  ],
  deps = [
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    "//base/time/BUILD:time",
  ],
)
def_extra_processor(
  name = "kai_fg",
  srcs = [
    "processor/ext/kai_fg/enricher/*.cc",
    "processor/ext/kai_fg/adaptor/*.cc",
    "processor/ext/kai_fg/observer/*.cc",
    "processor/ext/kai_fg/adaptor/io/*.cc",
    "processor/ext/kai_fg/utils/*.cc",
  ],
  excludes = [
    "processor/ext/kai_fg/enricher/*_test.cc",
    "processor/ext/kai_fg/adaptor/test_*.cc",
    "processor/ext/kai_fg/adaptor/*_test.cc",
    "processor/ext/kai_fg/adaptor/io/*_test.cc",
  ] + [
    "processor/ext/kai_fg/adaptor/to_velox.cc",
    "processor/ext/kai_fg/enricher/kaifg_extract_slot_sign.cc",
  ],
  cppflags = [
    "-Ithirtd_party",
    "-Iteams/reco-arch/kaifg/csrc",
    "-Wno-overloaded-virtual",
  ] + cppflags + ["-std=gnu++17"],
  deps = [
    "//teams/reco-arch/kaifg/csrc/BUILD:fg",
    "//teams/reco-arch/kaifg/csrc/BUILD:fg_proto",
    "//teams/reco-arch/colossusdb/flatkv/BUILD:flatkv_client",
    "//third_party/abseil/BUILD:abseil",
  ],
)

if use_gpu:
  l2model_exclude_srcs = []
  l2model_deps = ["//ks/reco_pub/embedding_manager/BUILD:local_life_embedding_manager",]
else:
  l2model_exclude_srcs = [
    "processor/ext/l2model/enricher/local_life_prepare_item_embedding_enricher.cc",
  ]
  l2model_deps = []

def_extra_processor(
  name = "l2model",
  srcs = [
    "processor/ext/l2model/mixer/*.cc",
    "processor/ext/l2model/enricher/*.cc",
    "processor/ext/l2model/util/*.cc",
  ],
  excludes = [
  ] + l2model_exclude_srcs,
  cppflags = [
    "-Iteams/reco-model/base/mio/mio/include/",
    "-Iteams/reco-model/base/mio/mio-table/include/",
    "-Iteams/reco-model/base/mio-dnn/include/",
    "-DUSE_GPU" if use_gpu else "",
    "-DGOOGLE_LOGGING",
  ] + cppflags,
  deps = [
    "//base/time/BUILD:time",
    "//teams/reco-model/serve/dnn-lib/BUILD:dnn_predict",
  ] + l2model_deps,
)

def_extra_processor(
  name = "follow_leaf",
  srcs = [
    "processor/ext/follow_leaf/retriever/*.cc",
    "processor/ext/follow_leaf/arranger/*.cc",
    "processor/ext/follow_leaf/arranger/filter_op/*.cc",
    "processor/ext/follow_leaf/context/*.cc",
    "processor/ext/follow_leaf/enricher/*.cc",
    "processor/ext/follow_leaf/enricher/*/*.cc",
    "processor/ext/follow_leaf/observer/*.cc",
    "processor/ext/follow_leaf/util/*.cc"
  ],
  deps = [
    "//ks/reco_proto/proto/BUILD:proto",
    "//ks/reco_pub/reco/predict/clients/BUILD:api",
    "//ks/common_reco/index/BUILD:common_query_client",
    "//ks/reco/memory_data_map/BUILD:memory_data_map",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:ad",
    "//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:fanstop",
    '//ks/reco_proto/ad/ad_proto/kuaishou/BUILD:athena_proto',
    "//ks/realtime_reco/ad_service_proto/BUILD:ad_service_proto",
    "//ks/realtime_reco/ad_service_proto/BUILD:realtime_reco_ad",
    "//teams/reco-arch/colossus/BUILD:proto",
    eigen3_lib,
  ]
)

def_extra_processor(
  name = "friend_leaf",
  srcs = [
    "processor/ext/friend_leaf/enricher/*.cc",
  ],
  deps = [
    "//ks/reco_proto/proto/BUILD:proto",
  ]
)


def_extra_processor(
  name = "live_cluster",
  srcs = [
    "processor/ext/live_cluster/retriever/*.cc"
  ],
  deps = [
    "//third_party/cuda/BUILD:cuda",
    "//base/common/BUILD:base",
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//base/strings/BUILD:strings",
    "//third_party/faiss-1.7.0/BUILD:faiss",
    "//third_party/boost/BUILD:boost",
    "//third_party/boost/BUILD:boost_system",
    "//base/time/BUILD:time",
  ],

  cppflags = [
    "-Ithird_party/faiss-1.7.0"
  ]
)

def_extra_processor(
  name = "ps_index",
  srcs = [
    "processor/ext/ps_index/enricher/*.cc",
    "processor/ext/ps_index/retriever/*.cc",
  ],
  deps = [
    "//teams/reco-arch/colossusdb/client/BUILD:ps_client",
    "//teams/reco-arch/colossusdb/client/BUILD:kconf_client",
    "//teams/reco-arch/colossusdb/common/BUILD:flexbuffers",
    "//ks/reco_proto/big_river/BUILD:big_river",
    "//teams/reco-arch/colossusdb/colossusdb-proto/BUILD:label_list",
  ]
)

def_extra_processor(
  name = "partition_lib",
  srcs = [
    "processor/ext/partition_lib/enricher/*.cc",
    "processor/ext/partition_lib/retriever/*.cc",
  ],
  deps = [
    "//ks/reco/merchant-engine/recall_platform/BUILD:recall_platform",
    "//teams/reco-arch/colossusdb/partition_server/partition_lib/BUILD:partition_lib",
    "//teams/reco-arch/colossusdb/client/BUILD:ps_client",
    "//teams/reco-arch/colossusdb/client/BUILD:kconf_client",
    "//teams/reco-arch/colossusdb/common/BUILD:flexbuffers",
  ]
)

def_extra_processor(
  name = "id_mapping",
  srcs = [
    "processor/ext/id_mapping/enricher/*.cc",
    "processor/ext/id_mapping/util/*.cc",
  ],
  deps = [
    "//third_party/folly/BUILD:folly",
    "//third_party/abseil/BUILD:abseil",
    "//ks/base/abtest2/BUILD:abtest2_proto",
    "//ks/ds/proto/BUILD:userprofile_proto",
  ],
  cppflags=["-std=gnu++17"],
)

# 统一特征服务(特征抽取) 相关算子
def_extra_processor(
  name = "reco_feature_server",
  srcs = [
    "processor/ext/reco_feature_server/util/*.cc",
    "processor/ext/reco_feature_server/enricher/*.cc",
    "processor/ext/reco_feature_server/observer/*.cc",
  ],
  deps = [
  ],
  cppflags=["-std=gnu++17"],
)

# endregion

# ---------------------------------
# region - unit tests
# ---------------------------------
enable_cc_test = os.getenv("ENABLE_CC_TEST", "false").lower() == "true"
if enable_cc_test:
  cc_test(
    name = "common_processors_tests",
    srcs = [
      "processor/common/enricher/*_test.cc",
      "processor/common/retriever/*_test.cc",
    ],
    excludes = [
    ],
    deps = [
      ":framework_base",
      ":common_processors",
      "//third_party/gtest/BUILD:gtest",
    ],
    cppflags=cppflags,
    ldflags = ldflags,
  )

  cc_test(
    name = "frame_tests",
    srcs = [
      "core/*_test.cc",
      "module/*_test.cc",
      "interop/*_test.cc",
    ],
    deps = [
      ":framework_base",
      ":core",
      ":interop",
      ":util",
      ":async_task_thread_pool_mod",
      "//third_party/gtest/BUILD:gtest",
    ],
    cppflags=cppflags,
    ldflags = ldflags,
  )

  if "offline" in target_extra_processors:
    cc_test(
      name = "offline_processors_tests",
      srcs = [
        "processor/ext/offline/enricher/*_test.cc",
      ],
      deps = [
        ":framework",
        ":offline_processors",
        "//third_party/gtest/BUILD:gtest",
      ],
      cppflags=cppflags,
      ldflags = ldflags,
    )

  if "ad_union_train" in target_extra_processors:
    cc_test(
      name = "ad_union_train_processors_tests",
      srcs = [
        "processor/ext/ad_union_train/enricher/*_test.cc",
      ],
      deps = [
        ":framework",
        ":ad_union_train_processors",
      ],
      cppflags = cppflags,
      ldflags = ldflags,
    )

  if "gsu" in target_extra_processors:
    cc_test(
      name = "gsu_processors_tests",
      srcs = [
        "processor/ext/gsu/enricher/*_test.cc",
      ],
      deps = [
        ":framework",
        ":gsu_processors",
      ],
      cppflags = cppflags,
      ldflags = ldflags,
    )

  if "uni_predict" in target_extra_processors:
    uni_predict_test_gpu_deps = [
      "//third_party/cuda/BUILD:cuda",
      "//teams/reco-arch/uni-predict/src/BUILD:utils_cuda"
    ] if use_gpu else []
    cc_test(
      name = "uni_predict_processors_tests",
      srcs = [
        "processor/ext/uni_predict/embedding_merger/embedding_merger_test.cc",
        "processor/ext/uni_predict/enricher/*_test.cc",
      ],
      deps = [
        ":framework_base",
        ":util",
        "//ks/reco_pub/embedding_util/BUILD:emb_compressor",
        "//teams/reco-arch/uni-predict/src/BUILD:utils_fp16",
        "//third_party/gtest/BUILD:gtest",
      ] + uni_predict_test_gpu_deps,
      cppflags = [
        "-DUSE_GPU" if use_gpu else "",
      ] + cppflags
    )

  if "uni_predict_v2" in target_extra_processors:
    uni_predict_v2_test_gpu_deps = [
      "//third_party/cuda/BUILD:cuda",
      "//teams/reco-arch/uni-predict-v2/src/BUILD:utils_cuda"
    ] if use_gpu else []
    cc_test(
      name = "uni_predict_v2_processors_tests",
      srcs = [
        "processor/ext/uni_predict_v2/embedding_merger/embedding_merger_test.cc",
        "processor/ext/uni_predict_v2/enricher/*_test.cc",
      ],
      deps = [
        ":framework_base",
        ":util",
        "//ks/reco_pub/embedding_util/BUILD:emb_compressor",
        "//teams/reco-arch/uni-predict-v2/src/BUILD:utils_fp16",
        "//third_party/gtest/BUILD:gtest",
      ] + uni_predict_v2_test_gpu_deps,
      cppflags = [
        "-DUSE_GPU" if use_gpu else "",
      ] + cppflags
    )


  if "arrow" in target_extra_processors:
    cc_test(
      name = "arrow_processors_tests",
      srcs = [
        "processor/ext/arrow/enricher/*_test.cc",
      ],
      deps = [
        ":framework",
        ":arrow_processors",
        ":common_reco_pipeline_executor",
      ],
      cppflags = cppflags,
    )

  if "kai_fg" in target_extra_processors:
    cc_test(
      name = "kai_fg_processors_tests",
      srcs = [
        "processor/ext/kai_fg/enricher/*_test.cc",
        # "processor/ext/kai_fg/adaptor/io/*_test.cc"
      ],
      excludes = [
        "processor/ext/kai_fg/enricher/kaifg_extract_slot_sign_test.cc",
      ],
      deps = [
        ":framework_base",
        ":util",
        ":kai_fg_processors",
        "//third_party/gtest/BUILD:gtest",
      ],
      cppflags = [
        "-Ithird_party",
        "-Iteams/reco-arch/kaifg/csrc",
        "-Wno-overloaded-virtual",
      ] + cppflags,
    )

  if "merchant" in target_extra_processors:
    cc_test(
      name = "model_family_tests",
      srcs = [
        "processor/ext/merchant/util/model_family_test.cc",
        "processor/ext/merchant/util/infer_threshold_manager_test.cc",
        "processor/ext/merchant/util/model_family.cc",
        "processor/ext/merchant/util/infer_threshold_manager.cc"
      ],
      deps = [
        "//third_party/gtest/BUILD:gtest",
        "//third_party/folly/BUILD:folly",
        "//base/testing/BUILD:testing",
        "//base/testing/BUILD:test_main",
        "//serving_base/jansson/BUILD:json",
        "//third_party/boost/BUILD:boost",
        "//serving_base/perfutil/BUILD:perfutil"
       ],
      cppflags = cppflags + ["-std=gnu++17"],
      ldflags = ldflags,
    )
# endregion


# ---------------------------------
# region - param check at last
# ---------------------------------
unknown_processors = target_extra_processors - available_extra_processors
assert not unknown_processors, "[Dragon Build] please remove unknown ext processor modules in env DRAGON_EXT or COMMON_RECO_LEAF_EXTRA_PROCESSORS: {}".format(sorted(unknown_processors))
# endregion
